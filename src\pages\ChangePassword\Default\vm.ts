import { useCallback } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { changeEmployeePassword } from "services/crm/employee";

export const PasswordChangePageVm = () => {
  const [changeEmployeePasswordExec, changeEmployeePasswordState] = useAsync(
    changeEmployeePassword,
    {
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
      onSuccess: useCallback(
        () =>
          toastSingleMode({
            type: "success",
            message: "Đ<PERSON><PERSON> mật khẩu thành công",
          }),
        []
      ),
    }
  );

  return {
    changeEmployeePasswordExec,
    changeEmployeePasswordState,
  };
};
