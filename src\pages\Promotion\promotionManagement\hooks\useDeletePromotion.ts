import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import { DeletePromotionDto } from "../dtos/promotion.dto";
import promotionServices from "../services/promotion.service";

export const useDeletePromotion = () => {
  const [deletePromotionExe] = useAsync(
    useCallback(
      (dto: DeletePromotionDto) => promotionServices.deletePromotion(dto),
      []
    )
  );

  return { deletePromotionExe };
};
