import pick from "object.pick";

import { cleanEmptyString } from "helpers/object";
import { createMapper, force, fromSchema } from "libs/adapters/dto";
import { mergeSchema } from "libs/domain";

import { StorageSchema } from "../entities";

export const updateStorageDto = createMapper(
  fromSchema(
    mergeSchema(pick(StorageSchema, ["name", "code", "displayOrder"]))
  ),
  force((data) => ({
    ...cleanEmptyString(data),
  }))
);

export type UpdateStorageDtoType = ReturnType<typeof updateStorageDto>;
