import React from "react";

import { <PERSON>, <PERSON>a } from "@storybook/react/types-6-0";

import { <PERSON>, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|utils/Link",
  component: Link,
} as Meta;

const Template: Story<Props> = (args) => <Link {...args} />;

export const Normal = Template.bind({});
Normal.args = {
  children: "Link (depends on here is MPA or SPA)",
  to: "/somewhere",
  state: { a: 123 },
};
