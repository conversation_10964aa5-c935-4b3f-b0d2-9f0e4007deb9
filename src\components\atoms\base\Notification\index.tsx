import { useEffect } from "react";

import { notification } from "antd";
import store, { useAppSelector } from "store/index";
import { NotificationState, setNotification } from "store/system/systemSlice";

export const NotificationComponent = () => {
  const [api, contextHolder] = notification.useNotification();
  const state = useAppSelector((s) => s.system.notication);

  useEffect(() => {
    if (state) {
      const { type, ...rest } = state;
      api[type]({ ...rest });
    }
  }, [state]);

  return contextHolder;
};

export const showNotification = (opts: NotificationState) => {
  store.dispatch(setNotification(opts));
};
