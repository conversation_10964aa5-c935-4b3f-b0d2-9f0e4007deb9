.a-autocomplete {
	$root: &;
	position: relative;

	&_wrapped-input {
		position: relative;
	}

	&_input {
		width: 100%;
		height: rem(48);
		padding: 0 rem(12);
		font-size: rem(14);
		line-height: rem(16);
		color: $COLOR-QUARTZ;
		background-color: $COLOR-WHITE;
		border: 1px solid $COLOR-PLATINUM-4;
		border-radius: rem(2);
		outline: none;
		transition: all 0.3s ease-in-out;
		appearance: none;

		&:hover,
		&:focus {
			color: $COLOR-QUARTZ;
			border-color: $COLOR-QUARTZ;
		}
	}

	&_icon {
		position: absolute;
		top: 50%;
		right: rem(14);
		width: rem(20);
		height: rem(20);
		cursor: pointer;
		transition: opacity 0.3s ease-in-out;
		transform: translateY(-50%);

		&:hover {
			opacity: 0.7;
		}
	}

	&_options {
		position: absolute;
		z-index: z("autocomplete", "option");
		width: 100%;
		margin-top: rem(6);
		list-style: none;
		box-shadow: 0 0 0 1px rgba($COLOR-BLA<PERSON>K, 0.1),
			0 rem(4) rem(11) rgba($COLOR-BLACK, 0.1);
	}

	&_option-item {
		display: flex;
		align-items: center;
		height: rem(37);
		padding: rem(8) rem(12);
		background-color: $COLOR-WHITE;
		&:not(.empty):hover {
			color: $COLOR-WHITE;
			background-color: $COLOR-DENIM;
		}
		&.empty {
			justify-content: center;
		}
	}
}
