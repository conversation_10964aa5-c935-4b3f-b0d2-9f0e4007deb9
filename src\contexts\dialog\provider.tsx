import React, { useCallback, useReducer } from "react";

import produce from "immer";

import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Text } from "components/atoms/text";
import { Col, Row } from "components/organisms/grid";
import { Modal } from "components/organisms/modal";

interface PromiseRef {
  resolve: () => void;
  reject: () => void;
}

export interface DialogServiceOption {
  title?: string;
  contents?: React.ReactNode;
  okLabel: string;
  cancelLabel?: string;
}

export interface DialogServiceState extends DialogServiceOption {
  open: boolean;
}

export const DialogContext = React.createContext<
  (options: DialogServiceOption) => Promise<void>
>(Promise.reject);

type ActionType =
  | {
      type: "OPEN";
      payload: DialogServiceOption;
    }
  | {
      type: "CLOSE";
    };

const initialState: DialogServiceState = {
  open: false,
  okLabel: "",
};

const reducer = produce((draft: DialogServiceOption, action: ActionType) => {
  switch (action.type) {
    case "OPEN": {
      return {
        ...draft,
        ...action.payload,
        open: true,
      };
    }
    case "CLOSE": {
      return initialState;
    }
    default: {
      return { ...draft, open: false };
    }
  }
});

const DialogServiceProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const [dialogServiceState, dispatch] = useReducer(reducer, initialState);
  const promiseRef = React.useRef<PromiseRef>();

  const openDialog = useCallback((options: DialogServiceOption) => {
    dispatch({ type: "OPEN", payload: options });
    return new Promise<void>((resolve, reject) => {
      promiseRef.current = { resolve, reject };
    });
  }, []);

  const handleCloseDialog = useCallback(() => {
    dispatch({ type: "CLOSE" });
  }, []);

  const handleOk = useCallback(() => {
    if (promiseRef.current && dialogServiceState.okLabel) {
      promiseRef.current.resolve();
    }
    handleCloseDialog();
  }, [dialogServiceState.okLabel, handleCloseDialog]);

  const handleCancel = useCallback(() => {
    if (promiseRef.current && dialogServiceState.cancelLabel) {
      promiseRef.current.reject();
    }
    handleCloseDialog();
  }, [dialogServiceState.cancelLabel, handleCloseDialog]);

  const { open, title, okLabel, cancelLabel, contents } =
    dialogServiceState as DialogServiceState;

  return (
    <>
      <DialogContext.Provider value={openDialog}>
        {children}
      </DialogContext.Provider>
      <Modal
        isOpen={open}
        isClosable={false}
        shouldCloseOnEsc={false}
        shouldCloseOnOverlayClick={false}
        style={{
          content: {
            maxWidth: 620,
          },
        }}
      >
        {title && (
          <Heading type="h1" centered>
            {title}
          </Heading>
        )}
        {contents && (
          <div className="u-mt-10 u-mb-20">
            <Text centered>{contents}</Text>{" "}
          </div>
        )}
        <Row>
          {cancelLabel && (
            <Col>
              <Button modifiers="secondary" fullwidth onClick={handleCancel}>
                {cancelLabel}
              </Button>
            </Col>
          )}
          <Col>
            <Button fullwidth onClick={handleOk}>
              {okLabel}
            </Button>
          </Col>
        </Row>
      </Modal>
    </>
  );
};

export default DialogServiceProvider;
