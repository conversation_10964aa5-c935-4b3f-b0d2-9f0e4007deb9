import { useCallback } from "react";

import * as Yup from "yup";

import { But<PERSON> } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { FormContainer } from "helpers/form";
import * as navigationHelper from "helpers/navigation";
import { EmployeePasswordChangePayload } from "services/crm/employee";

import { PasswordChangePageVm } from "./vm";

const inputValidationSchema = Yup.object({
  oldPassword: Yup.string()
    .required("Mật khẩu cũ là bắt buộc")
    .min(8, "Mật khẩu vui lòng sử dụng 8 ký tự trở lên"),
  newPassword: Yup.string()
    .required("Mật khẩu mới là bắt buộc")
    .min(8, "<PERSON><PERSON>t khẩu vui lòng sử dụng 8 ký tự trở lên"),
  confirmNewPassword: Yup.string()
    .required("Nhập lại mật khẩu mới là bắt buộc")
    .oneOf([Yup.ref("newPassword"), ""], "Mật khẩu không khớp"),
});

const ChangePassword = () => {
  const { changeEmployeePasswordExec, changeEmployeePasswordState } =
    PasswordChangePageVm();

  const onSubmitPasswordChange = useCallback(
    ({ newPassword, oldPassword }: EmployeePasswordChangePayload) =>
      changeEmployeePasswordExec({ newPassword, oldPassword }),
    [changeEmployeePasswordExec]
  );

  return (
    <General>
      <title>Đổi mật khẩu</title>
      <Section>
        <Heading type="h1" modifiers="primary">
          ĐỔI MẬT KHẨU
        </Heading>
        <Section>
          <FormContainer
            validationSchema={inputValidationSchema}
            onSubmit={onSubmitPasswordChange}
          >
            <Row>
              <Col xs="12" className="u-mb-15">
                <Formfield label="Mật khẩu cũ" name="oldPassword">
                  <TextfieldHookForm
                    name="oldPassword"
                    placeholder="Nhập mật khẩu cũ"
                    type="password"
                  />
                </Formfield>
              </Col>
              <Col lg="6" className="u-mb-15 u-mb-lg-0">
                <Formfield label="Mật khẩu mới" name="newPassword">
                  <TextfieldHookForm
                    name="newPassword"
                    placeholder="Nhập mật khẩu mới"
                    type="password"
                  />
                </Formfield>
              </Col>
              <Col lg="6">
                <Formfield
                  label="Nhập lại mật khẩu mới"
                  name="confirmNewPassword"
                >
                  <TextfieldHookForm
                    name="confirmNewPassword"
                    placeholder="Nhập lại mật khẩu mới"
                    type="password"
                  />
                </Formfield>
              </Col>
            </Row>
            <div className="d-flex justify-content-end u-mt-20">
              <Button
                modifiers="secondary"
                buttonType="outline"
                onClick={navigationHelper.goBack}
              >
                QUAY LẠI
              </Button>
              <div className="u-ml-15">
                <Button
                  type="submit"
                  isLoading={changeEmployeePasswordState.loading}
                >
                  LƯU
                </Button>
              </div>
            </div>
          </FormContainer>
        </Section>
      </Section>
    </General>
  );
};

export default ChangePassword;
