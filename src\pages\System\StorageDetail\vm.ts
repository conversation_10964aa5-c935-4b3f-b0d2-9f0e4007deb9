import { useEffect, useCallback } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { storageDetailDto, StorageDetailDtoType } from "modules/location/dtos";
import {
  updateStorageDto,
  UpdateStorageDtoType,
} from "modules/location/dtos/updateStorageDetail";
import { getStorageById, updateStorageById } from "services/crm/system";

interface StorageDetailPageVmProps {
  storageId: number;
}

export type StorageItem = StorageDetailDtoType;

export const StorageDetailPageVm = ({
  storageId,
}: StorageDetailPageVmProps) => {
  const [getStorageByIdExec, getStorageByIdState] = useAsync(
    useCallback(
      (params: { storageId: number }) =>
        getStorageById({ ...params }).then((res) =>
          storageDetailDto(res.data.data)
        ),
      []
    )
  );

  const [updateStorageDetailServiceExec, updateStorageDetailState] = useAsync(
    updateStorageById,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Cập nhật thành công" });
      }, []),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      onFailed: useCallback((error: any) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const storageDetail = getStorageByIdState.data;

  const handleUpdateStorageDetail = useCallback(
    (rawPayload: Partial<UpdateStorageDtoType>) => {
      if (!storageDetail?.storageId) return;
      const updateStoragePayload = updateStorageDto({
        ...storageDetail,
        ...rawPayload,
      });

      updateStorageDetailServiceExec(
        storageDetail.storageId,
        updateStoragePayload
      );
    },
    [updateStorageDetailServiceExec, storageDetail]
  );

  useEffect(() => {
    getStorageByIdExec({ storageId });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [storageId]);

  return {
    loading: getStorageByIdState.loading,
    storageData: getStorageByIdState.data,
    updateStorageDetailState,
    handleUpdateStorageDetail,
  };
};
