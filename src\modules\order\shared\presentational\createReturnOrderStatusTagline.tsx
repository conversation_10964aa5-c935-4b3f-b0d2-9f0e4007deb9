import * as Yup from "yup";

import { Button } from "components/atoms/button";
import { ColorPicker } from "components/atoms/colorpicker";
import { Heading } from "components/atoms/heading";
import { NumberfieldHookForm } from "components/atoms/numberfield";
import { PulldownHookForm } from "components/atoms/pulldown";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Modal } from "components/organisms/modal";
import { Section } from "components/organisms/section";
import { FormContainer } from "helpers/form";
import { useAsync } from "hooks/useAsync";
import {
  orderReturnStatusOption,
  OrderReturnStatusDetailDtoType,
} from "modules/order";
import { CreateReturnOrderStatusTaglineFormPayload } from "pages/Order/ListReturnStatusTagline/constant";

export interface CreateReturnOrderStatusTaglineModalProps {
  open: boolean;
  submitValidationSchema: Yup.ObjectSchema<Yup.AnyObject>;
  onChangeColor: (color: string) => void;
  returnOrderStatus?: OrderReturnStatusDetailDtoType;
  onClose?: () => void;
  handleSubmitCreateReturnOrderStatusTagline: (
    FormData: CreateReturnOrderStatusTaglineFormPayload
  ) => Promise<void>;
}

export const CreateReturnOrderStatusTaglineModal = ({
  onClose,
  open,
  submitValidationSchema,
  returnOrderStatus,
  onChangeColor,
  handleSubmitCreateReturnOrderStatusTagline,
}: CreateReturnOrderStatusTaglineModalProps) => {
  const [handleSubmit, handleSubmitState] = useAsync(
    handleSubmitCreateReturnOrderStatusTagline
  );

  return (
    <Modal
      style={{ content: { maxWidth: 700 } }}
      isOpen={open}
      onCloseModal={onClose}
      isClosable={false}
    >
      <Heading type="h1" centered>
        TẠO MỚI TAGLINE ĐỔI TRẢ
      </Heading>
      <Section>
        <FormContainer
          validationSchema={submitValidationSchema}
          onSubmit={handleSubmit}
        >
          <Formfield label="Tên trạng thái" name="returnOrderStatus">
            <PulldownHookForm
              defaultValue={orderReturnStatusOption(returnOrderStatus)}
              name="returnOrderStatus"
              placeholder="Trạng thái"
              isDisabled
            />
          </Formfield>
          <Formfield label="Tên tagline đổi trả" name="name">
            <TextfieldHookForm name="name" placeholder="Tên tagline" />
          </Formfield>
          <Formfield label="Màu sắc" name="color">
            <ColorPicker onChangeClolor={onChangeColor} />
          </Formfield>
          <Formfield label="Mô tả" name="description">
            <TextareafieldHookForm name="description" placeholder="Mô tả" />
          </Formfield>
          <Formfield label="Thứ tự hiển thị" name="displayOrder">
            <NumberfieldHookForm
              name="displayOrder"
              placeholder="Thứ tự hiển thị"
            />
          </Formfield>
          <div className="d-flex justify-content-end u-mt-20">
            <Button
              buttonType="outline"
              modifiers="secondary"
              onClick={onClose}
            >
              HỦY
            </Button>
            <div className="u-ml-15">
              <Button
                type="submit"
                disabled={handleSubmitState.loading}
                isLoading={handleSubmitState.loading}
              >
                LƯU
              </Button>
            </div>
          </div>
        </FormContainer>
      </Section>
    </Modal>
  );
};
