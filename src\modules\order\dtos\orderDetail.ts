import { fromSchema, createMapper, merge } from "libs/adapters/dto";
import {
  ExtendSchema,
  Number,
  Array,
  mergeSchema,
  Mixed,
  String,
  DateType,
} from "libs/domain";
import {
  CustomerProfileSchema,
  ShippingAddressSchema,
} from "modules/customer/entities";
import { EmployeeSchema } from "modules/employee";
import { ColorSchema, SizeSchema, SkuSchema } from "modules/product/entities";

import {
  CreateOrderItemSchema,
  DeliveryPartnerSchema,
  OrderItemSchema,
  OrderPersonalInfoSchema,
  OrderReturnStatusSchema,
  OrderReturnStatusTaglineSchema,
  OrderStatusSchema,
  OrderStatusTaglineSchema,
} from "../entities";
import { paymentDisplayMethod } from "../entities/orderPayment";
import { OrderSourceSchema } from "../entities/orderSource";

export const orderItemDetailDto = createMapper(
  ExtendSchema(CreateOrderItemSchema, {
    unitPrice: Number(),
    totalAmount: Number(),
    sku: mergeSchema(SkuSchema, {
      color: ColorSchema,
      size: SizeSchema,
    }),
  })
);

export const orderDetailDto = createMapper(
  fromSchema(
    mergeSchema(OrderItemSchema, {
      items: Array(orderItemDetailDto),
      assignedEmployee: Mixed(EmployeeSchema, {
        optional: true,
      }),
      customer: CustomerProfileSchema,
      orderStatus: Mixed(OrderStatusSchema, { optional: true }),
      shippingAddress: fromSchema(ShippingAddressSchema),
      returnOrderStatus: Mixed(OrderReturnStatusSchema, {
        optional: true,
      }),
      shippingDetail: Mixed(DeliveryPartnerSchema, {
        optional: true,
      }),
      returnOrderStatusTagline: Mixed(OrderReturnStatusTaglineSchema, {
        optional: true,
      }),
      orderStatusTagline: Mixed(OrderStatusTaglineSchema, { optional: true }),
      orderSource: Mixed(OrderSourceSchema, { optional: true }),
      persionalInfo: Mixed(OrderPersonalInfoSchema, { optional: true }),

      createdBy: String(),
      updatedBy: String(),
      confirmedBy: String(),

      // FIELD DATE //
      unConfirmDate: String(),
      confirmDate: String(),
      deliveryDate: String(),
      returnDate: String(),
      completeDate: String(),
      cancelDate: String(),
      exchangeOrderDate: String(),
      shopSentDate: String(),
    })
  ),
  merge((data) => ({
    payment: {
      displayName: data.payment && paymentDisplayMethod[data.payment.method],
    },
  }))
);

export type OrderDetailDtoType = ReturnType<typeof orderDetailDto>;
