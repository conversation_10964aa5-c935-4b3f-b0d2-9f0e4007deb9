import { use<PERSON><PERSON>back, useMemo } from "react";

import { useSearchParams, useParams } from "react-router";

import { Button } from "components/atoms/button";
import { ColorPicker } from "components/atoms/colorpicker";
import { Heading } from "components/atoms/heading";
import { NumberfieldHookForm } from "components/atoms/numberfield";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { Textfield, TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import { FormContainer } from "helpers/form";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";

import { ChildrenPage } from "../types";
import {
  inputValidationSchema,
  UpdateOrderStatusTaglineFormData,
} from "./constant";
import { OrderStatusTaglineDetailPageVm } from "./vm";

const TaglineEditPage = () => {
  const [searchUrlPath] = useSearchParams();
  const { orderStatusId, taglineId } =
    useParams<PageParamsType<ChildrenPage["orderStatusTaglineDetail"]>>();

  const pageActionType = searchUrlPath.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  const {
    loading,
    orderStatusTaglineDetail,
    handleUpdateOrderStatusTagline,
    updateOrderStatusTaglineState,
    setSelectedColor,
    selectedColorRef,
  } = OrderStatusTaglineDetailPageVm({
    orderStatusId: Number(orderStatusId),
    taglineId: Number(taglineId),
  });

  const handleSubmitOrderStatusTaglineUpdate = useCallback(
    (formData: UpdateOrderStatusTaglineFormData) =>
      handleUpdateOrderStatusTagline({
        ...formData,
        color: selectedColorRef.current,
      }),
    [handleUpdateOrderStatusTagline, selectedColorRef]
  );

  return (
    <SpinnerContainer animating={loading}>
      <General>
        <title key="title">Sửa thông tin tagline</title>
        <Section>
          <Heading type="h1" modifiers="primary">
            SỬA THÔNG TIN TAGLINE ĐƠN HÀNG
          </Heading>
          <Section>
            {orderStatusTaglineDetail && (
              <FormContainer
                validationSchema={inputValidationSchema}
                onSubmit={handleSubmitOrderStatusTaglineUpdate}
              >
                <Row>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Tên tagline" name="name">
                      <TextfieldHookForm
                        name="name"
                        placeholder="Tên tagline"
                        disabled={viewMode}
                        defaultValue={orderStatusTaglineDetail.name}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Tên trạng thái" name="status">
                      <Textfield
                        name="status"
                        placeholder="Tên trạng thái"
                        disabled
                        defaultValue={
                          orderStatusTaglineDetail.orderStatus?.name
                        }
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Mô tả" name="description">
                      <TextareafieldHookForm
                        name="description"
                        placeholder="Mô tả"
                        row={10}
                        disabled={viewMode}
                        defaultValue={orderStatusTaglineDetail.description}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Màu sắc" name="color">
                      <ColorPicker
                        disabled={viewMode}
                        defaultColor={orderStatusTaglineDetail.color}
                        onChangeClolor={setSelectedColor}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Thứ tự hiển thị" name="displayOrder">
                      <NumberfieldHookForm
                        name="displayOrder"
                        placeholder="Thứ tự hiển thị"
                        disabled={viewMode}
                        defaultValue={orderStatusTaglineDetail.displayOrder}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="12" className="u-mb-15">
                    <div className="d-flex justify-content-end">
                      <Button
                        modifiers="secondary"
                        buttonType="outline"
                        onClick={navigationHelper.goBack}
                      >
                        QUAY LẠI
                      </Button>
                      <div className="u-ml-15">
                        <Button
                          type="submit"
                          isLoading={updateOrderStatusTaglineState.loading}
                          disabled={updateOrderStatusTaglineState.loading}
                        >
                          LƯU
                        </Button>
                      </div>
                    </div>
                  </Col>
                </Row>
              </FormContainer>
            )}
          </Section>
        </Section>
      </General>
    </SpinnerContainer>
  );
};

export default TaglineEditPage;
