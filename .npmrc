registry=https://tfs.dptsolution.vn:40001/DPT/React_Pack/_packaging/packages/npm/registry/ 
                        
always-auth=true

; begin auth token
//tfs.dptsolution.vn:40001/DPT/React_Pack/_packaging/packages/npm/registry/:username=DPT
//tfs.dptsolution.vn:40001/DPT/React_Pack/_packaging/packages/npm/registry/:_password=cnRlcGNzY3RubWk2cTc3bnVpa291YjR6c3Rtc3pieW5wbXgydTNvZmV2Y2pjNW1zd3BtYQ==
//tfs.dptsolution.vn:40001/DPT/React_Pack/_packaging/packages/npm/registry/:email=npm requires email to be set but doesn't use the value
//tfs.dptsolution.vn:40001/DPT/React_Pack/_packaging/packages/npm/:username=DPT
//tfs.dptsolution.vn:40001/DPT/React_Pack/_packaging/packages/npm/:_password=cnRlcGNzY3RubWk2cTc3bnVpa291YjR6c3Rtc3pieW5wbXgydTNvZmV2Y2pjNW1zd3BtYQ==
//tfs.dptsolution.vn:40001/DPT/React_Pack/_packaging/packages/npm/:email=npm requires email to be set but doesn't use the value
; end auth token