import crmDriverV2 from "services/nest-crm/crm-driver-v2";

export type GetCommentDto = {
  _id: string;
  postId: string;
  content: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
};

export const GetCommentContext = (username: string, site: string) => {
  const getCommentExe = (postId: string, offset: number) => {
    return crmDriverV2.get<GetCommentDto[]>(
      `/post/comment/${postId}/${offset}`
    );
  };
  return { getCommentExe };
};

export const SendCommentContext = (username: string, site: string) => {
  const sendCommentExe = (postId: string, text: string) => {
    return crmDriverV2.post(`/post/comment/${postId}`, {
      content: text,
      createdBy: username,
      site,
    });
  };
  return { sendCommentExe };
};
