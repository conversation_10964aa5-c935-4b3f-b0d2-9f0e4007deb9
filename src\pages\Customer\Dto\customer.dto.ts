import { PaginationDTOType } from "modules/common/pagination";
import { MetaTotalRecords } from "pages/TickerManagement/StatusTicket/dto/get-status-ticket.dto";

export interface CustomerDto {
  name: string;
  phoneNumber: string;
  phoneNumber2: string;
  email: string;
  gender: number;
  birthDay: string;
  customerGroup: string;
  customerType: string;
  dontSendSMS: string;
  cityId: number;
  districtId: number;
  wardId: number;
  weight: string;
  height: string;
  waist: string;
  address: string;
  note: string;
}

export interface CustomerResponse {
  customerId: number;
  name: string;
  gender: number;
  birthDay: string; // ISO date string
  accountType: number;
  isVerified: boolean;
  memberPoints: number;
  facebookPID: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  weight: string;
  height: string;
  waist: string;
  // phoneNumbers: CustomerPhoneNumber[];
  phoneNumber: string;
  phoneNumber2: string;
  emails: CustomerEmail[];
  address: CustomerAddress[];
}

export interface CustomerPhoneNumber {
  customerPhoneNumberId: number;
  customerId: number;
  phoneNumber: string;
  description: string | null;
  isPrimary: boolean;
  from: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  phoneNumber2: string;
}

export interface CustomerEmail {
  customerEmailId: number;
  customerId: number;
  email: string;
  description: string | null;
  isPrimary: boolean;
  from: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface CustomerAddress {
  shippingAddressId: number;
  customerId: number;
  name: string;
  phoneNumber: string;
  address: string;
  wardId: number;
  districtId: number;
  cityId: number;
  countryId: number;
  detail: string;
  isDefault: boolean;
  type: number;
  companyName: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface pagination {
  pageSize?: number;
  pageNum?: number;
}

export interface GetPointCustomerDto {
  customerId: number;
  pageSize: number;
  pageNum: number;
}
export interface GetPointCustomerResponseDto extends MetaTotalRecords {
  data: CustomerPoint[];
  link: PaginationDTOType;
}

export interface CustomerPoint {
  customerPointId: number;
  customerId: number;
  orderId: number;
  point: number;
  type: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
