export const formatCurrencyWithoutSuffix = (value: number | string) => {
  if (value === null || value === undefined || Number.isNaN(Number(value)))
    return "";
  return new Intl.NumberFormat("vi-VN").format(Number(value));
};

export const formatCurrencyWithSuffix = (
  value: number | string,
  suffix = "đ"
) => {
  if (value === null || value === undefined || Number.isNaN(Number(value)))
    return "";
  return `${new Intl.NumberFormat("vi-VN").format(Number(value))} ${suffix}`;
};
