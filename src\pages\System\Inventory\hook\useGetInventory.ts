import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import { InvertoryDto } from "../dto/inventory.dto";
import InventoryService from "../service/invertory.service";

export const UseGetInventory = () => {
  const [fetchData, inventoryData] = useAsync(
    useCallback((dto: InvertoryDto) => InventoryService.getInventory(dto), [])
  );
  return {
    fetchData,
    inventoryData: inventoryData.data?.data,
    loading: inventoryData.loading,
  };
};
