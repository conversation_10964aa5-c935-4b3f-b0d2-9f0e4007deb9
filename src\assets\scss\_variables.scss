/*
* https://hexcol.com/
*	description: Use this tool to get the Color-Name
*/

/* #region COLOR-BASE */
$COLOR-TRANSPARENT: transparent;
$COLOR-DPT-BLUE: rgb(28, 134, 200);
$COLOR-BLACK: rgb(0, 0, 0);
$COLOR-BLACK-1: rgba(153, 153, 153, 0.15);
$COLOR-BLACK-2: rgba(153, 153, 153, 0.3);
$COLOR-BLACK-3: rgba($COLOR-BLACK, 0.25);
$COLOR-WHITE: rgb(255, 255, 255);
$COLOR-DENIM: rgb(22, 132, 185);
$COLOR-DENIM-2: rgba(22, 132, 185, 0.7);
$COLOR-DENIM-3: rgba(22, 132, 185, 0.3);
$COLOR-DENIM-4: rgba(22, 132, 185, 0.1);
$COLOR-DODGER-BLUE: rgb(24, 144, 255);
$COLOR-QUARTZ: rgb(74, 74, 74);
$COLOR-PLATINUM: rgb(225, 225, 225);
$COLOR-PLATINUM-2: rgb(229, 229, 234);
$COLOR-PLATINUM-3: rgb(222, 226, 230);
$COLOR-PLATINUM-4: rgb(232, 232, 232);
$COLOR-PLATINUM-5: rgb(229, 229, 229);
$COLOR-PLATINUM-6: rgb(219, 219, 219);
$COLOR-PASTEL-ORANGE: rgb(242, 175, 74);
$COLOR-ISABELLINE: rgb(239, 239, 239);
$COLOR-WHITE-SMOKE: rgb(245, 245, 245);
$COLOR-WHITE-SMOKE-2: rgb(248, 248, 248);
$COLOR-LIGHT-GRAY: rgb(207, 207, 207);
$COLOR-CHARLESTON-GREEN: rgb(32, 39, 45);
$COLOR-CHARLESTON-GREEN-2: rgb(40, 40, 41);
$COLOR-PASTEL-GRAY: rgb(204, 204, 204);
$COLOR-EERIE-BLACK: rgb(24, 24, 24);
$COLOR-SPANISH-GRAY: rgb(153, 153, 153);
$COLOR-ANTIQUE-WHITE: rgb(250, 238, 214);
$COLOR-ROMAN-SILVER: rgb(139, 142, 144);
$COLOR-MANATEE: rgba(149, 157, 165, 0.2);
$COLOR-CINNABAR: rgb(241, 67, 54);
$COLOR-CINNABAR-2: rgb(153, 43, 45);
$COLOR-AZURE: rgb(0, 122, 255);
$COLOR-LAVENDER-BLUE: rgb(197, 220, 250);
$COLOR-LAVENDER-BLUE-1: rgba($COLOR-LAVENDER-BLUE, 0.5);
$COLOR-AZURE-1: rgba($COLOR-AZURE, 0.1);

/* #endregion */

/* #region FONT-FAMILY */
$FONTFAMILY-BASIC: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
  "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif,
  "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
$FONTFAMILY-ARIAL: "Arial";
$FONTFAMILY-ROBOTO: "Roboto";
$FONTFAMILY-POPPINS: "Poppins";
/* #endregion */

/* #region FONT-SIZE html */
$FONT-SIZE-HTML-VALUE: 14;
$FONT-SIZE-HTML: 14px;
/* #endregion */

/* #region SIZE-SCREEN */
$WIDTH-PC-LARGE: 1900px;
$WIDTH-PC-MEDIUM: 1366px;
$WIDTH-PC: 1200px;
$WIDTH-TAB: 992px;
$WIDTH-SM: 768px;
$WIDTH-XS: 576px;
/* #endregion */
