import * as Yup from "yup";

import {
  orderReturnStatusOption,
  createReturnOrderStatusTaglineDtoType,
} from "modules/order";

export const CreateOrderExchangeValidationSchema = Yup.object({
  name: Yup.string().required("Vui lòng nhập tên tagline"),
});

export type CreateReturnOrderStatusTaglineFormPayload = Pick<
  createReturnOrderStatusTaglineDtoType,
  "color" | "description" | "displayOrder" | "name"
> & {
  returnOrderStatus: ReturnType<typeof orderReturnStatusOption>;
};
