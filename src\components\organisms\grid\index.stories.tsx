import React from "react";

import { Story, Meta } from "@storybook/react/types-6-0";
import { ContainerProps } from "react-bootstrap";

import { Container, Row, Col } from ".";

export default {
  title: "Components|organisms/Grid",
} as Meta;

const Template: Story<ContainerProps> = ({
  as,
  bsPrefix,
  className,
  fluid,
}) => (
  <Container fluid={fluid} as={as} bsPrefix={bsPrefix} className={className}>
    <Row noGutters>
      <Col>
        <div
          style={{
            paddingTop: ".75rem",
            paddingBottom: ".75rem",
            backgroundColor: "#bbeffd",
            border: "1px solid #61dafb",
          }}
        >
          1 of 2
        </div>
      </Col>
      <Col>
        <div
          style={{
            paddingTop: ".75rem",
            paddingBottom: ".75rem",
            backgroundColor: "#bbeffd",
            border: "1px solid #61dafb",
          }}
        >
          2 of 2
        </div>
      </Col>
    </Row>
    <Row noGutters>
      <Col>
        <div
          style={{
            paddingTop: ".75rem",
            paddingBottom: ".75rem",
            backgroundColor: "#bbeffd",
            border: "1px solid #61dafb",
          }}
        >
          1 of 3
        </div>
      </Col>
      <Col>
        <div
          style={{
            paddingTop: ".75rem",
            paddingBottom: ".75rem",
            backgroundColor: "#bbeffd",
            border: "1px solid #61dafb",
          }}
        >
          2 of 3
        </div>
      </Col>
      <Col>
        <div
          style={{
            paddingTop: ".75rem",
            paddingBottom: ".75rem",
            backgroundColor: "#bbeffd",
            border: "1px solid #61dafb",
          }}
        >
          3 of 3
        </div>
      </Col>
    </Row>
  </Container>
);

export const Normal = Template.bind({});

export const Fluid = Template.bind({});

Fluid.args = {
  fluid: true,
};
