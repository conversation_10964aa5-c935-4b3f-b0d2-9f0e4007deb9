import { useCallback, useMemo } from "react";

import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { OrderStatusModel, orderStatusOption } from "modules/order";
import { getOrderStatus } from "services/crm/order";

export interface PulldownOrderStatusFunctionalOptions {
  excludePending: boolean;
  searchText?: string;
}

export const usePulldownOrderStatus = ({
  excludePending = false,
  searchText,
}: PulldownOrderStatusFunctionalOptions) => {
  const [fetchOrderStatus, fetchOrderStatusState] = useAsync(
    useCallback(
      () =>
        getOrderStatus({
          searchText,
        }).then((res) => OrderStatusModel.createMap(res.data.data)),
      [searchText]
    ),
    {
      excludePending,
    }
  );

  const orderStatuses = useMemo(
    () => fetchOrderStatusState.data || [],
    [fetchOrderStatusState.data]
  );

  const {
    options: orderStatusesOptions,
    getOptionByValue,
    formatOption: formatOrderStatusOption,
  } = usePulldownHelper({
    dataSource: orderStatuses,
    optionCreator: orderStatusOption,
    valueTrans: Number,
  });

  const isDefaultOrderStatus = fetchOrderStatusState?.data?.find(
    (status) => status.isDefault
  );

  return {
    orderStatusesOptions,
    isDefaultOrderStatus,
    orderStatuses,
    fetchOrderStatus,
    getOptionByValue,
    formatOrderStatusOption,
    fetchOrderStatusState,
  };
};
