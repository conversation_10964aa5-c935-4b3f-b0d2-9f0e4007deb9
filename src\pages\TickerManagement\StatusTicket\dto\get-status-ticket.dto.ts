import { PaginationDTOType } from "modules/common/pagination";

export interface GetListStatusTicketDto {
  pageNum: number;
  pageSize: number;
  searchText: string;
}

export interface GetListStatusTicketResponseDto extends MetaTotalRecords {
  data: StatusTicketDto[];
  links: PaginationDTOType;
}

export interface MetaTotalRecords {
  meta: {
    totalRecords: number;
  };
}

export interface StatusTicketDto {
  color: string;
  createdAt: string;
  deletedAt: string | null;
  displayOrder: number;
  name: string;
  statusTicketId: number;
  updatedAt: string;
}
