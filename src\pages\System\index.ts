import { lazy } from "react";

import PATHS from "constants/paths";
import { createAppPage } from "libs/react";

import { ChildrenPage } from "./types";

export default createAppPage<{}, ChildrenPage>({
  name: "SystemManagement",
  path: PATHS.SYSTEM,
  childrenPages: {
    listStorage: createAppPage({
      name: "ListStorage",
      path: "/danh-sach-kho",
      page: () => lazy(() => import("./ListStorage")),
    }),
    storageDetail: createAppPage({
      name: "StorageDetail",
      path: "/danh-sach-kho/chi-tiet-kho/:storageId",
      page: () => lazy(() => import("./StorageDetail")),
    }),
    listShop: createAppPage({
      name: "ListShop",
      path: "/danh-sach-cua-hang",
      page: () => lazy(() => import("./ListShop")),
    }),
    shopDetail: createAppPage({
      name: "ShopDetail",
      path: "/danh-sach-cua-hang/chi-tiet-shop/:shopId",
      page: () => lazy(() => import("./ShopDetail")),
    }),
    listBankAccount: createAppPage({
      name: "ListBankAccount",
      path: "/danh-sach-tai-khoan-ngan-hang",
      page: () => lazy(() => import("./ListBankAccount")),
    }),
    bankAccountDetail: createAppPage({
      name: "BankAccountDetail",
      path: "/danh-sach-tai-khoan-ngan-hang/chi-tiet-tai-khoan-ngan-hang/:bankAccountId",
      page: () => lazy(() => import("./BankAccountDetail")),
    }),
    shopDateilV2: createAppPage({
      name: "ShopDetailV2",
      path: "/danh-sach-cua-hang/tao-moi",
      page: () => lazy(() => import("./ShopDetail/shopDetailV2")),
    }),
    shopDetailV2: createAppPage({
      name: "ShopDetailV2",
      path: "/danh-sach-cua-hang/chi-tiet-shop-v2/:shopId",
      page: () => lazy(() => import("./ShopDetail/shopDetailV2")),
    }),
    inventory: createAppPage({
      name: "Inventory",
      path: "/ton-kho",
      page: () => lazy(() => import("./Inventory/inventory")),
    }),
  },
});
