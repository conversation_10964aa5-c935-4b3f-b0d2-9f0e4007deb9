import { createMapper, fromSchema } from "libs/adapters/dto";
import { Array, mergeSchema } from "libs/domain";

import { ItemStockLocationSchema } from "../entities";
import { productDetailDto } from "./productDetail";
import { skuDetailDto } from "./skuDetail";

export const itemStockLocationDto = createMapper(
  fromSchema(
    mergeSchema(ItemStockLocationSchema, {
      sku: skuDetailDto,
      product: productDetailDto,
    })
  )
);

export type ItemStockLocationDtoType = ReturnType<typeof itemStockLocationDto>;
