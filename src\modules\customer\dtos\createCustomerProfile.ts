import pick from "object.pick";

import { createMapper, fromSchema } from "libs/adapters/dto";

import { CustomerProfileSchema } from "../entities";

export const createCustomerProfileDto = createMapper(
  fromSchema(
    pick(CustomerProfileSchema, [
      "name",
      "phoneNumber",
      "email",
      "gender",
      "birthDay",
    ])
  )
);

export type CreateCustomerProfileDtoType = ReturnType<
  typeof createCustomerProfileDto
>;
