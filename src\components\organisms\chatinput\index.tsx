import React, { useCallback, useRef, useState } from "react";

import { EditableInput } from "components/atoms/editableinput";
import { useEditableInput } from "components/atoms/editableinput/hook";
import {
  ClipboardFile,
  Register as RegisterEditableInput,
} from "components/atoms/editableinput/types";
import { Icon } from "components/atoms/icon";
import { mapModifiers } from "helpers/component";
import { createUUId } from "helpers/creator";
import { blobFromFiles, FileMetadataData } from "helpers/file";

import { Register } from "./hook";
import PreviewItem from "./previewitem";

/* eslint-disable */
export const ErrorMessage = {
	OVER_SIZE: "Vui lòng tải lên file có kích thước nhỏ hơn: ",
	OVER_COUNT: "Chỉ được phép tải lên: ",
};

export type FileStateType = {
	id: string;
	file: File;
	blob: Blob;
	metadata?: FileMetadataData;
};

export interface Props {
	onSendMessage?: (text: string, files: FileStateType[]) => void;
	register?: (registerValue: Register) => void;
	onUploadFileError?: (error: Error) => void;
	onSelectPreviewFile?: (file: FileStateType) => void;
	fileSizeLimit?: number;
	fileCountLimit?: number;
}

interface State {
	files: FileStateType[];
}

export const ChatInput: React.FC<Props> = ({
	onSendMessage,
	register: registerProps,
	fileSizeLimit = 25,
	fileCountLimit = 5,
	onUploadFileError,
}) => {
	const { register, reset, getValue, setValue } = useEditableInput();

	const [state, setState] = useState<State>({
		files: [],
	});
	const uploadFileInputRef = useRef<HTMLInputElement>(null);

	const handleSendMessage = useCallback(() => {
		const textMessage = getValue();
		const uploadedFiles = state.files;
		if (!textMessage?.trim() && uploadedFiles.length === 0) return;

		if (onSendMessage) {
			onSendMessage(textMessage || "", uploadedFiles);
		}

		reset();
	}, [getValue, reset, state.files, onSendMessage]);

	const addUploadFile = useCallback(
		(files: Omit<FileStateType, "id">[]) => {
			const hasOverSizeFile = files.some(
				(file) => file.file.size > 1000000 * fileSizeLimit
			);
			if (hasOverSizeFile) {
				const outSizeError = new Error(
					`${ErrorMessage.OVER_SIZE}${fileSizeLimit}MB`
				);
				if (onUploadFileError) {
					onUploadFileError(outSizeError);
				}
				return;
			}
			const filesWithId = files.map((item) => ({
				...item,
				id: createUUId("_file_"),
			}));

			const nextFilesState = [...state.files, ...filesWithId];
			if (nextFilesState.length > fileCountLimit) {
				const overCountError = new Error(
					`${ErrorMessage.OVER_COUNT}${fileCountLimit} tập tin`
				);

				if (onUploadFileError) {
					onUploadFileError(overCountError);
				}
				return;
			}

			setState((prevState) => ({
				...prevState,
				files: nextFilesState,
			}));
		},
		[fileSizeLimit, fileCountLimit, onUploadFileError, state.files]
	);

	const onRequestUploadButton = (options?: { imageOnly?: boolean }) => {
		const uploadInput = uploadFileInputRef.current;
		if (!uploadInput) return;
		if (options?.imageOnly) uploadInput.accept = "image/*";
		else {
			uploadInput.accept = "*";
		}

		uploadInput.click();
	};

	const handleInputUploadChange = async (
		event: React.ChangeEvent<HTMLInputElement>
	) => {
		if (!event.target.files) return;
		const files = await blobFromFiles(Array.from(event.target.files));
		addUploadFile(files);
		if (uploadFileInputRef.current) uploadFileInputRef.current.value = "";
	};

	const removeUploadFileByIndex = (index: number) => {
		const listFiles = [...state.files];
		listFiles.splice(index, 1);

		setState((prevState) => ({
			...prevState,
			files: listFiles,
		}));
	};

	const onPasteFile = useCallback(
		(clipboardFiles: ClipboardFile[]) => {
			addUploadFile(clipboardFiles);
		},
		[addUploadFile]
	);

	const handleRegister = useCallback(
		(registerValue: RegisterEditableInput) => {
			register(registerValue);
			if (registerProps)
				registerProps({
					setter: () => ({
						setValue: ({ files, text }) => {
							setValue(text);
							setState((prevState) => ({
								...prevState,
								files: [...files],
							}));
						},
					}),
					getter: () => ({
						text: getValue() || "",
						files: state.files,
					}),
				});
		},
		[getValue, register, registerProps, setValue, state.files]
	);

	return (
		<div className={mapModifiers("o-chatinput")}>
			<EditableInput
				register={handleRegister}
				onEnter={handleSendMessage}
				onPasteFile={onPasteFile}
			/>
			<div className="o-chatinput_utils">
				<div className="o-chatinput_preview">
					{state.files.map((file, index) => (
						<PreviewItem
							key={file.id}
							file={file}
							onRemove={() => removeUploadFileByIndex(index)}
						/>
					))}
				</div>
				<div className="o-chatinput_lefticons">
					<Icon iconName="emoji" />
					<Icon
						iconName="file-upload"
						onClick={() => onRequestUploadButton()}
					/>
					<Icon
						iconName="image-upload"
						onClick={() => onRequestUploadButton({ imageOnly: true })}
					/>
					<Icon iconName="send-message" onClick={handleSendMessage} />
					<input
						ref={uploadFileInputRef}
						name="upload-file"
						type="file"
						hidden
						multiple
						onChange={handleInputUploadChange}
					/>
				</div>
			</div>
		</div>
	);
};
