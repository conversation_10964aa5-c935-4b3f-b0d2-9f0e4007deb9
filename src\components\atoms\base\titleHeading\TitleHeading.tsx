interface ITitleHeader {
  title: string;
}
export default function TitleHeading({ title }: ITitleHeader) {
  return (
    <div
      className="title"
      style={{
        borderBottom: "1px solid #1c86c8",
        display: "flex",
        alignItems: "center",
        width: "100%",
        background: "#eee",
        padding: "4px",
      }}
    >
      <span className="e-icons e-align-right" />
      <p className="pl-2">{title}</p>
    </div>
  );
}
