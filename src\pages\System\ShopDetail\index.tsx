/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from "react";
import { DeleteFilled, PlusOutlined } from "@ant-design/icons";
import { Checkbox, Col, Flex, Form, Input, Row, Table, Tooltip } from "antd";
import { ColumnsType } from "antd/es/table";
import { useParams, useSearchParams } from "react-router";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { showLoading } from "components/atoms/base/Spinner";
import { Heading } from "components/atoms/heading";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";
import { EmployeeSelectedModal } from "modules/employee/shared/presentational/selectListEmployeeModal";
import { useLocationSelect } from "modules/location";
import { BankAccountSelectedModal } from "modules/location/shared/presentational/selectListBankAccountModal";

import { ChildrenPage } from "../types";
import { optionsShopType, ShopItemFormType } from "./constant";
import { ShopDetailPageVM } from "./vm";

interface Employee {
  employeeId: number;
  name: string;
}

interface BankAccount {
  bankAccountId: number;
  accountNumber: string;
  owner: string;
  bankName: string;
}

type FormValueType = Partial<Omit<ShopItemFormType, "storageIds">> & {
  storageIds?: string[];
};

function ShopDetailV2() {
  const [form] = Form.useForm<FormValueType>();
  const [searchParams] = useSearchParams();
  const [convertCityOption, setConvertCityOption] = useState<any>([]);
  const [convertDistrictOption, setConvertDistrictOption] = useState<any>([]);
  const [convertWardOption, setConvertWardOption] = useState<any>([]);
  const { shopId } = useParams<PageParamsType<ChildrenPage["shopDetail"]>>();

  const {
    loading,
    shopDetailData,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    storageOptions,
    removeEmployees,
    removeBankAccounts,
    employeeSelected,
    bankAccountSelected,
    updateShopDetailState,
    handleUpdateShopDetail,
    onSaveModalEmployees,
    onSaveModalBankAccounts,
  } = ShopDetailPageVM({ shopId: Number(shopId) });

  const {
    cityOptions,
    districtOptions,
    wardOptions,
    setFilter: setLocationFilter,
    setFilters: setLocationFilters,
    batchUpdate: batchUpdateLocation,
  } = useLocationSelect();

  const pageActionType = searchParams.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  // Set form values when data is loaded
  useEffect(() => {
    if (shopDetailData) {
      form.setFieldsValue({
        shopId: shopDetailData.shopId,
        name: shopDetailData.name,
        hotline: shopDetailData.hotline,
        type: shopDetailData.type,
        address: shopDetailData.address,
        cityId: shopDetailData.cityId,
        districtId: shopDetailData.districtId,
        wardId: shopDetailData.wardId,
        displayOrder: shopDetailData.displayOrder,
        isFranchisedShop: shopDetailData.isFranchisedShop,
        storageIds: shopDetailData.storages?.map((s) => s.storageId.toString()),
      });
    }
  }, [shopDetailData, form]);

  // Update location filters when shop data loads

  useEffect(() => {
    const convetCity = cityOptions.map((item) => ({
      label: item.label,
      value: Number(item.value),
    }));
    const convertDistric = districtOptions.map((item) => ({
      label: item.label,
      value: Number(item.value),
    }));
    const convertWard = wardOptions.map((item) => ({
      label: item.label,
      value: Number(item.value),
    }));

    setConvertDistrictOption(convertDistric);
    setConvertWardOption(convertWard);
    setConvertCityOption(convetCity);
  }, [cityOptions.length, districtOptions.length, wardOptions.length]);

  useEffect(() => {
    if (shopDetailData) {
      batchUpdateLocation(() => {
        setLocationFilters({
          cityId: Number(shopDetailData?.cityId),
          districtId: Number(shopDetailData?.districtId),
          wardId: Number(shopDetailData?.wardId),
        });
      });
    }
  }, [batchUpdateLocation, setLocationFilters, shopDetailData]);

  // Show loading when updating
  useEffect(() => {
    if (updateShopDetailState.loading) {
      showLoading(true);
    } else {
      showLoading(false);
    }
  }, [updateShopDetailState.loading]);

  const onFinish = (values: FormValueType) => {
    handleUpdateShopDetail({
      ...values,
      cityId: values.cityId,
      districtId: values.districtId,
      wardId: values.wardId,
      type: values.type,
      storageId: values.storageId,
      bankAccountIds: bankAccountSelected.map((item) => item.bankAccountId),
      employeeIds: employeeSelected.map((item) => item.employeeId),
      isFranchisedShop: values.isFranchisedShop || false,
    });
  };

  // Employee table columns
  const employeeColumns: ColumnsType<Employee> = [
    {
      title: "Mã nhân viên",
      dataIndex: "employeeId",
      key: "employeeId",
      align: "center",
    },
    {
      title: "Tên nhân viên",
      dataIndex: "name",
      key: "name",
      align: "center",
    },
    ...(editMode
      ? [
          {
            title: "Thao tác",
            key: "action",
            align: "center" as const,
            render: (_: any, record: Employee) => (
              <Tooltip title="Xóa">
                <BaseButton
                  type="primary"
                  bgColor={COLOR.RED[500]}
                  hoverColor={COLOR.RED[700]}
                  icon={<DeleteFilled rev={undefined} />}
                  onClick={() => removeEmployees(record as any)} // eslint-disable-line @typescript-eslint/no-explicit-any
                />
              </Tooltip>
            ),
          },
        ]
      : []),
  ];

  // Bank account table columns
  const bankAccountColumns: ColumnsType<BankAccount> = [
    {
      title: "Số tài khoản",
      dataIndex: "accountNumber",
      key: "accountNumber",
      align: "center",
    },
    {
      title: "Chủ tài khoản",
      dataIndex: "owner",
      key: "owner",
      align: "center",
    },
    {
      title: "Ngân hàng",
      dataIndex: "bankName",
      key: "bankName",
      align: "center",
    },
    ...(editMode
      ? [
          {
            title: "Thao tác",
            key: "action",
            align: "center" as const,
            render: (_: any, record: BankAccount) => (
              <Tooltip title="Xóa">
                <BaseButton
                  type="primary"
                  bgColor={COLOR.RED[500]}
                  hoverColor={COLOR.RED[700]}
                  icon={<DeleteFilled rev={undefined} />}
                  onClick={() => removeBankAccounts(record as any)} // eslint-disable-line @typescript-eslint/no-explicit-any
                />
              </Tooltip>
            ),
          },
        ]
      : []),
  ];

  if (loading) {
    return (
      <General>
        <div className="flex justify-center items-center h-64">
          <div>Đang tải...</div>
        </div>
      </General>
    );
  }

  return (
    <General>
      <title key="title">CHI TIẾT SHOP</title>
      <Section>
        <div className="flex flex-col gap-4 p-3">
          <Heading type="h1" modifiers="primary">
            THÔNG TIN SHOP: {shopDetailData?.name || ""}
          </Heading>

          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            disabled={viewMode}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Form.Item label="Mã shop" name="shopId">
                  <Input placeholder="Mã shop" disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Tên shop"
                  name="name"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng nhập tên shop",
                    },
                  ]}
                >
                  <Input placeholder="Tên shop" readOnly={viewMode} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Hotline"
                  name="hotline"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng nhập hotline",
                    },
                  ]}
                >
                  <Input placeholder="Hotline" readOnly={viewMode} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Loại shop"
                  name="type"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng chọn loại shop",
                    },
                  ]}
                >
                  <BaseSelect
                    placeholder="Chọn loại shop"
                    options={optionsShopType}
                    disabled={viewMode}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Form.Item label="Kho" name="storageIds">
                  <BaseSelect
                    mode="multiple"
                    placeholder="Chọn kho"
                    options={storageOptions}
                    disabled={viewMode}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Địa chỉ"
                  name="address"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng nhập địa chỉ",
                    },
                  ]}
                >
                  <Input placeholder="Địa chỉ" readOnly={viewMode} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Tỉnh/Thành phố"
                  name="cityId"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng chọn tỉnh/thành phố",
                    },
                  ]}
                >
                  <BaseSelect
                    placeholder="Chọn tỉnh/thành phố"
                    options={convertCityOption}
                    onChange={(value: any) => {
                      setLocationFilter("cityId", value);

                      // Reset district and ward when city changes
                      form.setFieldsValue({
                        districtId: undefined,
                        wardId: undefined,
                      });
                    }}
                    disabled={!convertCityOption.length || viewMode}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Quận/Huyện"
                  name="districtId"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng chọn quận/huyện",
                    },
                  ]}
                >
                  <BaseSelect
                    placeholder="Chọn quận/huyện"
                    options={convertDistrictOption}
                    onChange={(value: any) => {
                      setLocationFilter("districtId", value);
                      // Reset ward when district changes
                      form.setFieldsValue({
                        wardId: undefined,
                      });
                    }}
                    disabled={!convertDistrictOption.length || viewMode}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Phường/Xã"
                  name="wardId"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng chọn phường/xã",
                    },
                  ]}
                >
                  <BaseSelect
                    placeholder="Chọn phường/xã"
                    options={convertWardOption}
                    onChange={(value: any) =>
                      setLocationFilter("wardId", value)
                    }
                    disabled={!convertWardOption.length || viewMode}
                  />
                </Form.Item>
              </Col>
            </Row>
            {/* Employee Section */}
            <div className="mt-8">
              <div className="flex justify-between items-center mb-4">
                <Heading>NHÂN VIÊN SHOP</Heading>
                {editMode && (
                  <BaseButton
                    type="primary"
                    bgColor={COLOR.BLUE[500]}
                    hoverColor={COLOR.BLUE[700]}
                    icon={<PlusOutlined rev={undefined} />}
                    onClick={() => handleOpenModalByType("employeeSelected")}
                  >
                    Thêm nhân viên mới
                  </BaseButton>
                )}
              </div>
              <Table
                columns={employeeColumns}
                dataSource={employeeSelected}
                rowKey="employeeId"
                size="small"
                bordered
                pagination={false}
              />
            </div>

            {/* Bank Account Section */}
            <div className="mt-8">
              <div className="flex justify-between items-center mb-4">
                <Heading>TÀI KHOẢN NGÂN HÀNG</Heading>
                {editMode && (
                  <BaseButton
                    type="primary"
                    bgColor={COLOR.BLUE[500]}
                    hoverColor={COLOR.BLUE[700]}
                    icon={<PlusOutlined rev={undefined} />}
                    onClick={() => handleOpenModalByType("bankAccountSelected")}
                  >
                    Thêm tài khoản mới
                  </BaseButton>
                )}
              </div>
              <Table
                columns={bankAccountColumns}
                dataSource={bankAccountSelected}
                rowKey="bankAccountId"
                size="small"
                bordered
                pagination={false}
              />
            </div>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  label="Thứ tự hiển thị"
                  name="displayOrder"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng nhập thứ tự hiển thị",
                    },
                  ]}
                >
                  <Input
                    type="number"
                    placeholder="Thứ tự hiển thị"
                    readOnly={viewMode}
                    min={0}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Shop nhượng quyền"
                  name="isFranchisedShop"
                  valuePropName="checked"
                >
                  <Checkbox disabled={viewMode}>Nhượng quyền</Checkbox>
                </Form.Item>
              </Col>
            </Row>

            <Flex align="center" justify="end" gap={16} className="mt-6">
              <BaseButton type="default" onClick={navigationHelper.goBack}>
                Quay lại
              </BaseButton>
              {editMode && (
                <BaseButton
                  htmlType="submit"
                  type="primary"
                  bgColor={COLOR.BLUE[500]}
                  hoverColor={COLOR.BLUE[700]}
                  disabled={updateShopDetailState.loading}
                  loading={updateShopDetailState.loading}
                >
                  Lưu
                </BaseButton>
              )}
            </Flex>
          </Form>
        </div>
      </Section>

      {/* Employee Selection Modal */}
      <EmployeeSelectedModal
        open={modalTypeIsOpen("employeeSelected")}
        employeeSelected={employeeSelected}
        onSave={(value) => onSaveModalEmployees(value)}
        onClose={handleCloseModal}
      />

      {/* Bank Account Selection Modal */}
      <BankAccountSelectedModal
        open={modalTypeIsOpen("bankAccountSelected")}
        bankAccountSelected={bankAccountSelected}
        onSave={(value) => onSaveModalBankAccounts(value)}
        onClose={handleCloseModal}
      />
    </General>
  );
}

export default ShopDetailV2;
