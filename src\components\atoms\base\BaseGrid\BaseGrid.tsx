import { forwardRef, PropsWithChildren, useMemo } from "react";
import {
  AggregateColumnDirective,
  AggregateColumnsDirective,
  AggregateDirective,
  AggregatesDirective,
  ColumnDirective,
  ColumnsDirective,
  Filter,
  GridComponent,
  GridModel,
  Inject,
  Resize,
  Sort,
} from "@syncfusion/ej2-react-grids";

export type BaseGridProps = PropsWithChildren<
  GridModel & {
    injectServices?: Object[];
    className?: string;
  }
>;

export type BaseGridRef = GridComponent;

export const BaseGrid = forwardRef<BaseGridRef, BaseGridProps>((props, ref) => {
  const { children, className, injectServices, ...rest } = props;

  const services = useMemo(
    () =>
      injectServices
        ? [Sort, Resize, Filter, ...injectServices]
        : [Sort, Resize, Filter],
    [injectServices]
  );

  return (
    <GridComponent
      allowResizing
      className={`${className}
        [&_.e-gridheader.e-lib.e-droppable]:!pr-0
        [&_.e-gridheader>_.e-headercontent>_.e-table>thead>tr>th]:!bg-sky-600
        [&_.e-gridheader>_.e-headercontent>div>_.e-table>thead>tr>th]:!bg-sky-600
        [&_.e-gridheader>_.e-headercontent>_.e-table>thead>tr>th]:!h-[32px]
        [&_.e-gridheader>_.e-headercontent>div>_.e-table>thead>tr>th]:!h-[32px]
        [&_.e-gridheader>_.e-headercontent>_.e-table>thead>tr>th]:!text-white
        [&_.e-gridheader>_.e-headercontent>div>_.e-table>thead>tr>th]:!text-white
        [&_.e-filtermenudiv.e-icons.e-icon-filter]:!text-white
        [&_.e-sortfilterdiv.e-icons]:!text-white
      `}
      allowFiltering
      allowSorting
      ref={ref}
      filterSettings={{ type: "Excel" }}
      height="100%"
      width="100%"
      {...rest}
    >
      {children}
      <Inject services={services} />
    </GridComponent>
  );
});

BaseGrid.displayName = "BaseGrid";

export const BaseGridCols = ColumnsDirective;
export const BaseGridCol = ColumnDirective;
export const BaseGridAggregates = AggregatesDirective;
export const BaseGridAggregate = AggregateDirective;
export const BaseGridColsAggregate = AggregateColumnsDirective;
export const BaseGridColAggregate = AggregateColumnDirective;
