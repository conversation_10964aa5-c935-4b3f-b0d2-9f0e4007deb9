import "./index.css";

interface InfoCardProps {
  title: string;
  figure: string;
  // eslint-disable-next-line
	unit?: string;
  icon: string;
  bg: string;
}
export default function InfoCard({
  title,
  figure,
  unit,
  icon,
  bg,
}: InfoCardProps) {
  return (
    <div style={{ position: "relative", height: "100%" }}>
      <div className="main-card" style={{ color: "#fff" }}>
        <div className="info-card">
          <p className="info-card__title mb-0">{title}</p>
          <div className="info-card__figure">
            <span
              className="figure"
              style={{ fontSize: "36px", marginRight: "3px" }}
            >
              {figure}
            </span>
            {unit ? <span style={{ fontSize: "16px" }}>{unit}</span> : ""}
          </div>
        </div>
        <div className="icon-card">
          <span className={icon} style={{ fontSize: "50px" }} />
        </div>
      </div>
      <div className="more-card" style={{ background: bg }}>
        <span style={{ marginRight: "5px" }}>More</span>
        <span className="e-small e-icons e-chevron-right-double" />
      </div>
    </div>
  );
}
