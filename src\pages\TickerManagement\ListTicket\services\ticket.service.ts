import crmDriverV1 from "services/crm/crm-driver-v1";
import {
  CreateTicketDto,
  GetListTicketDto,
  GetListTicketResponseDto,
  TicketDto,
  TicketResponse,
  UpdateTicketDto,
} from "../dto/ticket.dto";

const ticketServices = {
  getListTicket: (
    dto: GetListTicketDto
  ): Promise<{ data: GetListTicketResponseDto }> => {
    const url = `media/external/ticket/list`;
    return crmDriverV1.post(url, dto);
  },

  postCreateTicket: (dto: CreateTicketDto) => {
    const url = `media/external/ticket/create`;
    return crmDriverV1.post(url, dto);
  },

  putUpdateTicket: (dto: UpdateTicketDto) => {
    const { ticketId, ...restProps } = dto;
    const url = `media/external/ticket/update/${ticketId}`;
    return crmDriverV1.put(url, restProps);
  },

  deleteTicket: ({ ticketId }: { ticketId: number }) => {
    const url = `media/external/ticket/delete/${ticketId}`;
    return crmDriverV1.delete(url);
  },
  getTicketById: (ticketId: number): Promise<{ data: TicketResponse }> => {
    const url = `media/external/ticket/detail/${ticketId}`;
    return crmDriverV1.get(url);
  },
};

export default ticketServices;
