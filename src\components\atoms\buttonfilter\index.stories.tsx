import { Story, Meta } from "@storybook/react/types-6-0";

import { ButtonFilter, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|atoms/ButtonFilter",
  component: ButtonFilter,
} as Meta;

const Template: Story<Props> = ({ title, onClick }) => {
  return <ButtonFilter title={title} onClick={onClick} />;
};

export const Normal = Template.bind({});

Normal.args = {};
