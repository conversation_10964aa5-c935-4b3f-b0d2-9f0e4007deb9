import React, { CSSProperties, useRef } from "react";

import noDataImg from "assets/images/common/no-data.png";
import { Icon } from "components/atoms/icon";
import { Image } from "components/atoms/image";
import { mapModifiers } from "helpers/component";
import useDidMount from "helpers/react-hooks/useDidMount";

type ThModifier = "center";
type TdModifier = "center";
type Modifier = "borderdotted";

export interface TableProps {
  modofiers?: Modifier | Array<Modifier>;
  scroll?: { x?: number; y?: number };
  fixedHeader?: boolean;
  loading?: boolean;
  hasData?: boolean;
}

export interface ThProps {
  colSpan?: number;
  rowSpan?: number;
  modifiers?: ThModifier | Array<ThModifier>;
  stickyLeft?: true;
  stickyRight?: true;
  isSortable?: true;
  style?: CSSProperties;
  onSort?: () => void;
}

export interface TdProps {
  colSpan?: number;
  rowSpan?: number;
  modifiers?: TdModifier | Array<TdModifier>;
  stickyLeft?: true;
  stickyRight?: true;
  style?: React.CSSProperties;
}

export const Table: React.FC<React.PropsWithChildren<TableProps>> = ({
  modofiers,
  scroll,
  children,
  fixedHeader,
  loading,
  hasData,
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);

  useDidMount(() => {
    const elem = containerRef.current;
    if (!elem) return undefined;

    const triggerBoxShadow = () => {
      const hasStickyLeft = elem.classList.contains(
        "o-table_container-hasstickyleft"
      );

      const hasStickyRight = elem.classList.contains(
        "o-table_container-hasstickyright"
      );

      if (elem.scrollLeft > 0 && !hasStickyLeft) {
        elem.classList.add("o-table_container-hasstickyleft");
      } else if (elem.scrollLeft === 0 && hasStickyLeft) {
        elem.classList.remove("o-table_container-hasstickyleft");
      }

      const isBoxShadowRight =
        Math.round(elem.clientWidth + elem.scrollLeft) === elem.scrollWidth;

      if (!isBoxShadowRight && !hasStickyRight) {
        elem.classList.add("o-table_container-hasstickyright");
      } else if (isBoxShadowRight && hasStickyRight) {
        elem.classList.remove("o-table_container-hasstickyright");
      }
    };

    triggerBoxShadow();
    elem.addEventListener("scroll", triggerBoxShadow);

    return () => {
      elem.removeEventListener("scroll", triggerBoxShadow);
    };
  });

  return (
    <div
      className={mapModifiers(
        "o-table",
        modofiers,
        fixedHeader && "fixedheader",
        loading && "loading"
      )}
    >
      <div
        ref={containerRef}
        className="o-table_container"
        style={{
          height: scroll && scroll.y ? scroll.y : undefined,
        }}
      >
        <table
          className="o-table_table"
          style={{
            minWidth: scroll && scroll.x ? scroll.x : 1140,
          }}
        >
          {children}
        </table>
        {typeof hasData !== "undefined" && !hasData && (
          <div className="o-table_empty">
            <div className="o-table_emptycontent">
              <div className="o-table_image">
                <Image src={noDataImg} alt="alt" />
              </div>
              <span className="o-table_description">Không có dữ liệu</span>
            </div>
          </div>
        )}
      </div>

      {loading && (
        <div className="o-table_wraploading">
          <Icon iconName="loading-blue" />
        </div>
      )}
    </div>
  );
};

export const Thead: React.FC<React.PropsWithChildren> = ({ children }) => (
  <thead className="o-table_thead">{children}</thead>
);

export const Tbody: React.FC<React.PropsWithChildren> = ({ children }) => (
  <tbody className="o-table_tbody">{children}</tbody>
);

export const Tr: React.FC<React.PropsWithChildren> = ({ children }) => (
  <tr className="o-table_tr">{children}</tr>
);

export const Th: React.FC<React.PropsWithChildren<ThProps>> = ({
  colSpan,
  rowSpan,
  modifiers,
  stickyLeft,
  stickyRight,
  isSortable,
  children,
  style,
  onSort,
}) => (
  <th
    colSpan={colSpan}
    rowSpan={rowSpan}
    className={mapModifiers(
      "o-table_th",
      modifiers,
      stickyLeft && "stickyleft",
      stickyRight && "stickyright",
      isSortable && "sortable"
    )}
    style={style}
  >
    <div className="o-table_th_content">
      <div className="o-table_th_wrapcontent">{children}</div>

      {isSortable && (
        <div className="o-table_th_wrapsort">
          <Icon iconName="sort-ascending" onClick={onSort} />
        </div>
      )}
    </div>
  </th>
);

export const Td: React.FC<React.PropsWithChildren<TdProps>> = ({
  colSpan,
  rowSpan,
  modifiers,
  stickyLeft,
  stickyRight,
  children,
  style,
}) => (
  <td
    colSpan={colSpan}
    rowSpan={rowSpan}
    className={mapModifiers(
      "o-table_td",
      modifiers,
      stickyLeft && "stickyleft",
      stickyRight && "stickyright"
    )}
    style={style}
  >
    {children}
  </td>
);
