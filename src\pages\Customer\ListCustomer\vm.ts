/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useState } from "react";

import produce from "immer";

import { useRadioProvider } from "components/atoms/radio";
import { toastSingleMode } from "components/atoms/toastify";
import { useCalendarProvider } from "components/molecules/calendar";
import dayjs from "helpers/dayjs";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import {
  createCustomerProfileDto,
  CreateCustomerProfileDtoType,
  customerDTO,
  Gender,
} from "modules/customer";
import {
  createCustomerProfile,
  getCustomerByFilter,
  getListCustomer,
  UseFindAllByFilter,
} from "services/crm/customer";

import RootPageRouter from "..";

type ModalType = "customerCreation";

type FilterValueType = {
  email?: string;
  name?: string;
  birthDay?: string;
  inputSearch?: string;
  city?: number;
  district?: number;
  ward?: number;
  customerType?: boolean;
  gender?: number;
  groupCustomer?: number;
  // NEW FIELDS
  successDateChecked?: boolean;
  fromDateSuccess?: Date;
  toDateSuccess?: Date;
  monthOfBirth?: number;
  dateOfBirth?: number;
  purchaseFrom?: Date;
  purchaseTo?: Date;
  totalAmountFrom?: number;
  totalAmountTo?: number;

  // TO DO: ADD FIELDS IF NEEDED
};

interface State {
  pageSize: number;
  modalState: {
    open: boolean;
    type?: ModalType;
  };
  filterValue?: FilterValueType;
}

export const CustomerPageVm = () => {
  const [state, setState] = useState<State>({
    pageSize: 10,
    modalState: {
      open: false,
      type: undefined,
    },
    filterValue: undefined,
  });

  const [getDatatableCustomersExec, getDatatableCustomerState] = useAsync(
    useCallback(
      (options: {
        pageNum: number;
        pageSize: number;
        filterValue?: FilterValueType;
      }) =>
        getCustomerByFilter({
          pageNum: Number(options.pageNum),
          pageSize: Number(options.pageSize),
          ...options.filterValue,
        }).then((res) => ({
          data: mapFrom(res?.data?.data, customerDTO),
          pagination: paginationDTO(res?.data?.links),
          total: res?.data?.meta?.totalRecords,
        })),
      []
    )
  );

  const [customerData, setCustomerdata] = useState<any>([]);
  const handleOpenModalByType = useCallback(
    (type: ModalType) =>
      setState(
        produce((draft) => {
          draft.modalState.open = true;
          draft.modalState.type = type;
        })
      ),
    []
  );

  const handleCloseModal = useCallback(
    () =>
      setState(
        produce((draft) => {
          draft.modalState.open = false;
          draft.modalState.type = undefined;
        })
      ),
    []
  );

  const modalTypeIsOpen = useCallback(
    (type: ModalType) =>
      state.modalState.open && state.modalState.type === type,
    [state.modalState.open, state.modalState.type]
  );

  const { gotoPage, ...customerPaginationState } = usePagination({
    pageSize: state.pageSize,
    actionOnPageChange: ({ page, pageSize }) => {
      console.log("actionOnPageChange", page, pageSize);
      return getDatatableCustomersExec({
        pageSize,
        pageNum: page,
        filterValue: state.filterValue,
      });
    },
  });

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pageSize = pageSize;
      })
    );
  }, []);

  const gotoCustomerDetailPage = useCallback(
    (customerId: number) =>
      RootPageRouter.gotoChild("customerDetail", {
        params: { customerId: customerId.toString() },
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  const gotoCustomerDetail = useCallback(
    () => RootPageRouter.gotoChild("addCustomer"),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const gotoCustomerPage = useCallback(
    ({
      customerId,
      action,
    }: {
      customerId: number | string;
      action?: string;
    }) =>
      RootPageRouter.gotoChild("customerDetailV2", {
        params: { customerId: customerId.toString() },
        queryString: action ? `?action=${action}` : undefined,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const { sortedData: dataCustomer, toggleSortState: toggleSortOrderBy } =
    useSortable({
      data: getDatatableCustomerState.data?.data,
      sortBy: {
        name: (data) => data.name,
        updatedAt: (data) => data.updatedAt,
      },
    });

  const { getDate, setDate } =
    useCalendarProvider<{
      birthDay: Date;
    }>();

  const { bindSilent, getCheckedValueGroup, setCheckedValue } =
    useRadioProvider<{
      gender: Gender;
    }>();

  useEffect(() => {
    setCustomerdata(dataCustomer);
  }, [dataCustomer]);

  const [createCustomerProfileExec, createCustomerProfileState] = useAsync(
    createCustomerProfile,
    {
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
      onSuccess: useCallback(() => {
        toastSingleMode({
          type: "success",
          message: "Tạo mới khách hàng thành công",
        });
        handleCloseModal();
      }, [handleCloseModal]),
    }
  );

  const handleSubmitCreateCustomerProfile = useCallback(
    (formData: CreateCustomerProfileDtoType) => {
      const customerProfilePayload = createCustomerProfileDto({
        ...formData,
        gender: Number(getCheckedValueGroup("gender")!),
        birthDay: dayjs(getDate("birthDay")).format("YYYY-MM-DD"),
      });

      createCustomerProfileExec(customerProfilePayload);
    },
    [createCustomerProfileExec, getCheckedValueGroup, getDate]
  );

  const updateFilterValue = useCallback((newFilter: FilterValueType) => {
    setState(
      produce((draft) => {
        draft.filterValue = {
          ...draft.filterValue,
          ...newFilter,
        };
      })
    );
  }, []);

  useEffect(() => {
    gotoPage(1);
  }, [state.filterValue]);

  const handleFindProfile = (payload: any) => {
    updateFilterValue(payload);
  };

  useEffect(() => {
    setCheckedValue("gender", 1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    customerPaginationState,
    gotoPage,
    customerData: customerData || [],
    loading: getDatatableCustomerState.loading,
    totalRecords: getDatatableCustomerState?.data?.total || 0,
    refetch: getDatatableCustomersExec,
    handleChangePageSize,
    pageSize: state.pageSize,
    gotoCustomerDetailPage,
    gotoCustomerPage,
    toggleSortOrderBy,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    pageState: state,
    handleSubmitCreateCustomerProfile,
    bindSilent,
    createCustomerProfileState,
    setDate,
    gotoCustomerDetail,
    handleFindProfile,
  };
};
