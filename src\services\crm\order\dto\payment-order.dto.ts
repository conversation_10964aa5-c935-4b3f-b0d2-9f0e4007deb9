import { ShopDetailDtoType } from "modules/shop";

export interface PaymentOrderPrimaryKeyDto {
  paymentOrderId: number;
}

export interface CreatePaymentOrderDto {
  orderId: number;
  orderCode: string;
  paymentMethod: string;
  paymentDate: string; // ISO date string
  employeeId: number;
  bankAccountId?: number; // Optional, used for bank transfers
  applyPoint?: number; // Optional, points to apply
  // applyPointValue?: number; // Optional, value of points applied
}

export interface DeletePaymentOrderDto extends PaymentOrderPrimaryKeyDto {}

export interface GetListPaymentOrderDto {
  orderId: number;
}

export interface GetListPaymentOrderByOrderIdDto {
  data: PaymentOrderDto[];
}

export interface PaymentOrderDto {
  paymentOrderId: number;
  orderId: number;
  orderCode: string;
  paymentMethod: string;
  amount: number;
  paymentDate: string;
  employeeId: number;
  bankAccountId: number;
  applyPoint: number;
  // applyPointValue: number;
  createdBy: string;
  updatedBy: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  employee: EmployeeDto;
}

export interface EmployeeDto {
  employeeId: number;
  name: string;
  phoneNumber: string;
  email: string;
  createdAt: string;
  updatedAt: string;
  employeeGroups: EmployeeGroup[];
  shops: ShopDetailDtoType[];
}

export interface EmployeeGroup {
  employeeGroupId: number;
  name: string;
  createdAt: string;
  updatedAt: string;
}
