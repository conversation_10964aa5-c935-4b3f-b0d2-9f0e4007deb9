import { FormEvent, useCallback } from "react";

import { FilterOutlined } from "@ant-design/icons";
import { Empty, Modal, Typography } from "antd";
import noDataImg from "assets/images/common/no-data.png";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { Button } from "components/atoms/button";
import { Divider } from "components/atoms/divider";
import { Heading } from "components/atoms/heading";
import { Phonefield } from "components/atoms/phonefield";
import { Formfield } from "components/molecules/formfield";
import { InfiniteScrollable } from "components/utils/infinitescrollable";
import { COLOR } from "constants/color";
import { MemorizePresenter } from "helpers/memorize";
import { useInfinityParams } from "hooks/useInfinityParams";
import { CustomerProfileModel } from "modules/customer";
import { CustomerProfileEntityType } from "modules/customer/entities";
import { getListCustomer } from "services/crm/customer";

export interface CustomerPickerByPhoneProps {
  open: boolean;
  onClose?: () => void;
  onChooseCustomer?: (customer: CustomerProfileEntityType) => void;
}

export const CustomerPickerByPhone = ({
  open,
  onChooseCustomer,
  onClose,
}: CustomerPickerByPhoneProps) => {
  const {
    loadMore,
    loadMoreWithParams,
    reset,
    state: { data: customers, loading: getCustomersPending },
  } = useInfinityParams<CustomerProfileEntityType[]>(
    async (params: Parameters<typeof getListCustomer>[0]) =>
      getListCustomer(params).then((res) => ({
        ...res,
        data: {
          ...res.data,
          data: CustomerProfileModel.createMap(res.data.data),
        },
      })),
    { pageSize: 15 }
  );

  const handleOnModalClose = useCallback(() => {
    if (onClose) {
      onClose();
      reset();
    }
  }, [onClose, reset]);

  const handleOnFormSubmit = useCallback(
    (event: FormEvent<HTMLFormElement>) => {
      event.preventDefault();
      const formData = new FormData(event.currentTarget);
      const phoneInputValue = formData.get("phone");
      if (phoneInputValue) {
        loadMoreWithParams({ phoneNumber: phoneInputValue });
      }
    },
    [loadMoreWithParams]
  );

  const handleOnChooseCustomer = useCallback(
    (customer: CustomerProfileEntityType) => {
      // console.log(customer, "customer selected");

      if (onChooseCustomer) {
        onChooseCustomer(customer);
        handleOnModalClose();
      }
    },
    [onChooseCustomer, handleOnModalClose]
  );

  return (
    <Modal
      open={open}
      styles={{
        content: {
          width: 500,
          minHeight: 600,
        },
      }}
      centered
      footer={null}
      maskClosable={false}
      onCancel={handleOnModalClose}
    >
      <Heading type="h2" centered>
        CHỌN KHÁCH HÀNG
      </Heading>
      <div className="my-4">
        <form onSubmit={handleOnFormSubmit}>
          <div className="grid grid-cols-[1fr_max-content] items-end gap-4">
            <Formfield name="phone" label="Số điện thoại">
              <Phonefield name="phone" placeholder="Nhập số điện thoại" />
            </Formfield>
            <BaseButton
              htmlType="submit"
              type="primary"
              style={{
                height: "40px",
              }}
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              loading={getCustomersPending}
              icon={<FilterOutlined />}
            >
              Tìm kiếm
            </BaseButton>
          </div>
        </form>
        <div />
      </div>

      <div style={{ height: 400 }}>
        {!getCustomersPending && !customers?.length && (
          <div
            className="flex u-align-center"
            style={{ width: 200, height: "100%", margin: "0px auto" }}
          >
            <Empty description="Không có dữ liệu" />
          </div>
        )}
        {customers?.length && (
          <InfiniteScrollable
            height="100%"
            width="100%"
            scrollType="bottom"
            className="u-pl-12 u-pr-12"
            onTrigger={loadMore}
          >
            {customers?.map((customer) => (
              <MemorizePresenter
                key={customer.customerId}
                memorizeValue={[customer, handleOnChooseCustomer]}
              >
                <div className="u-mt-10">
                  <div className="flex u-align-center u-justify-between u-mb-8">
                    <div className="flex">
                      <b>{customer.name}</b>
                      <span>&nbsp;({customer?.emails?.[0]?.email})</span>
                    </div>
                    <Button
                      type="button"
                      buttonType="textbutton"
                      onClick={() => handleOnChooseCustomer(customer)}
                    >
                      Chọn
                    </Button>
                  </div>
                  <Divider type="dash" />
                </div>
              </MemorizePresenter>
            ))}
          </InfiniteScrollable>
        )}
      </div>
    </Modal>
  );
};
