.a-toastify {
	$root: &;

	&_message {
		display: flex;
		align-items: center;
		padding: rem(5);
		font-size: rem(12);
	}

	&_header {
		@include u-fw-bold;
	}

	&_description {
		font-size: rem(12);
	}

	.a-icon {
		width: rem(16);
		height: rem(16);
	}

	&_content {
		width: calc(100% - #{rem(16)});
		padding-left: rem(12);
	}

	.Toastify {
		&__toast {
			@include sp {
				margin-bottom: rem(5);
			}

			&-container {
				width: 300px;
			}

			&--default {
				color: $COLOR-WHITE;
				background-color: $COLOR-DENIM-2;
				border-radius: 0;
			}
		}

		&__progress-bar--default {
			background: $COLOR-BLACK-2;
		}

		&__progress-bar {
			background-color: $COLOR-BLACK-3;
		}

		&__close-button--default {
			opacity: 1;

			&:focus,
			&:hover {
				opacity: 0.3;
			}
		}

		&__close-button > svg {
			fill: $COLOR-WHITE;
		}
	}

	&-center {
		.Toastify__toast-container--top-center {
			top: 50%;
			left: 50%;
			max-width: calc(100% - 20px);
			transform: translate(-50%, -50%);
		}

		.Toastify__toast-body {
			margin: 0 auto;
		}
	}
}
