import { EmployeeEntityType, GroupEmployeeEntityType } from "../entities";

export const assignEmployeeOption = (employee?: EmployeeEntityType) => ({
  label: employee?.name || "",
  value: employee?.employeeId?.toString() || "",
});

export const employeeGroupOption = (
  employeeGroup?: GroupEmployeeEntityType
) => ({
  label: employeeGroup?.name || "",
  value: employeeGroup?.employeeGroupId?.toString() || "",
});
