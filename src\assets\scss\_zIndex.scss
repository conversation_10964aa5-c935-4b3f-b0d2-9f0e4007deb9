/*
z-index manager

usage:
1. add z-index definition into $layers
2. call it as follows
   z-index: z("example"); /=> 1
   z-index: z("example-group", "base"); /=> 1
--------------------------------------------------------- */

$layers: (
	modal: (
		base: 9000,
		close: 8999,
	),
	imagewrapper: (
		close: 2,
		input: 2,
	),
	table: (
		sticky: 1,
		fixed: 2,
		fixedsticky: 3,
		afterloading: 4,
		loading: 5,
	),
	pulldown: (
		menu: 10,
	),
	calendar: (
		datecalendar: 1000,
		monthyearcalendar: 1000,
	),
	sidebar: (
		submenupopup: 1,
	),
	colorpicker: 11,
	spinner: (
		overlay: 900,
	),
	mainlayout: (
		wrapmenu: 999,
		menuprofile: 999,
	),
	pagechatbox: (
		tabavigation: 1,
	),
	autocomplete: (
		option: 10,
	),
);

@function map-deep-get($map, $keys...) {
	$value: $map;

	@each $key in $keys {
		$value: map-get($value, $key);
	}

	@return $value;
}

@function z($keys...) {
	@if not variable-exists(layers) {
		@error "`$layers` is not defined.";
	}

	@if not function-exists(map-deep-get) {
		@error "`map-deep-get()` is not available.";
	}

	@return map-deep-get($layers, $keys...);
}
