import { useCallback, useRef } from "react";

import { FileStateType } from ".";

export interface Register {
  getter: () => {
    text: string;
    files: FileStateType[];
  };
  setter: () => {
    setValue: (value: { text: string; files: FileStateType[] }) => void;
  };
}

export const useChatInput = () => {
  const registerRef = useRef<Register>();

  const register = useCallback((registerValue: Register) => {
    registerRef.current = registerValue;
  }, []);

  const getValue = useCallback(() => {
    const getter = registerRef.current?.getter;
    if (getter) {
      const value = getter();
      return value;
    }
    return null;
  }, []);

  const setValue = useCallback(
    (
      updateFn: (currentValue: { text: string; files: FileStateType[] }) => {
        text: string;
        files: FileStateType[];
      }
    ) => {
      const setter = registerRef.current?.setter;
      const getter = registerRef.current?.getter;
      if (setter && getter) {
        const { setValue: originSetter } = setter();
        const value = getter();

        originSetter(updateFn(value));
      }
    },
    []
  );

  const reset = useCallback(() => {
    const setter = registerRef.current?.setter;
    if (setter) {
      const { setValue: originSetter } = setter();
      originSetter({ text: "", files: [] });
    }
  }, []);

  return {
    register,
    setValue,
    reset,
    getValue,
  };
};
