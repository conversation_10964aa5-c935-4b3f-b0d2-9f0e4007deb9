/* eslint-disable @typescript-eslint/no-explicit-any */
import { SchemaTypeParser } from "../types";

export interface ArrayTypeOptions<R> {
  safeMode?: boolean;
  optional?: boolean;
  defaultValue?: Array<R>;
}

export function ArrayType<R>(
  parser: SchemaTypeParser<R>,
  options?: ArrayTypeOptions<R>
) {
  return (value: any, extraConfig?: any): Array<R> | undefined => {
    const mergeConfig = {
      ...extraConfig,
      ...options,
    };
    const dataFieldValueIsArray = Array.isArray(value);
    if (!dataFieldValueIsArray) {
      if (mergeConfig?.safeMode && !mergeConfig?.optional)
        return mergeConfig?.defaultValue || [];
      return undefined;
    }

    return value.map((itemValue: any) => parser(itemValue, mergeConfig));
  };
}
