import dayjs from "dayjs";
import { useParams } from "react-router";

import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Phonefield } from "components/atoms/phonefield";
import { Pulldown } from "components/atoms/pulldown";
import { Textareafield } from "components/atoms/textareafield";
import { Textfield } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";
import { primaryEmail, primaryPhone } from "modules/customer";
import { paymentDisplayMethod, paymentDisplayStatus } from "modules/order";

import { ChildrenPage } from "../types";
import { PaymentTransactionDetailVm } from "./vm";

const IndexPage = () => {
  const { paymentTransactionId } =
    useParams<PageParamsType<ChildrenPage["paymentTransactionDetail"]>>();

  const { loading, paymentData } = PaymentTransactionDetailVm({
    paymentId: Number(paymentTransactionId),
  });

  return (
    <SpinnerContainer animating={loading}>
      <General>
        <title key="title">THÔNG TIN GIAO DỊCH</title>
        {paymentData && (
          <Section>
            <Heading type="h1" modifiers="primary">
              THÔNG TIN GIAO DỊCH: {paymentData.orderCode}
            </Heading>
            <Section>
              <Heading type="h2">
                KHÁCH HÀNG:
                <span style={{ color: "rgb(22, 132, 185)" }}>
                  &nbsp;{paymentData.order?.customer?.name}
                </span>
              </Heading>
              <Section>
                <Row>
                  <Col lg={6}>
                    <Formfield label="Điện thoại" name="phone">
                      <Phonefield
                        name="phone"
                        disabled
                        defaultValue={primaryPhone(paymentData.order?.customer)}
                      />
                    </Formfield>
                  </Col>
                  <Col lg={6}>
                    <Formfield label="Email" name="email">
                      <Textfield
                        name="email"
                        disabled
                        defaultValue={primaryEmail(paymentData.order?.customer)}
                      />
                    </Formfield>
                  </Col>
                </Row>
              </Section>
            </Section>

            <Section>
              <Heading type="h2">GIAO DỊCH</Heading>
              <Section>
                <Row>
                  <Col lg={6} className="u-mb-15">
                    <Formfield label="Mã đơn hàng" name="orderId">
                      <Textfield
                        name="orderId"
                        disabled
                        defaultValue={paymentData.orderCode}
                      />
                    </Formfield>
                  </Col>
                  <Col lg={6} className="u-mb-15">
                    <Formfield label="Thời gian thanh toán" name="paymentTime">
                      <Textfield
                        name="paymentTime"
                        disabled
                        defaultValue={dayjs(paymentData.createdAt).format(
                          "DD/MM/YYYY"
                        )}
                      />
                    </Formfield>
                  </Col>
                  <Col lg={6} className="u-mb-15">
                    <Formfield
                      label="Phương thức thanh toán"
                      name="paymentMethod"
                    >
                      <Pulldown
                        name="paymentMethod"
                        isDisabled
                        defaultValue={[
                          {
                            label:
                              paymentDisplayMethod[paymentData.method] || "",
                            value: paymentData?.method.toString() || "",
                          },
                        ]}
                      />
                    </Formfield>
                  </Col>
                  <Col lg={6} className="u-mb-15">
                    <Formfield label="Trạng thái" name="status">
                      <Pulldown
                        name="status"
                        isDisabled
                        defaultValue={[
                          {
                            label:
                              paymentDisplayStatus[paymentData.status] || "",
                            value: paymentData.status.toString() || "",
                          },
                        ]}
                      />
                    </Formfield>
                  </Col>
                  <Col lg={12} className="u-mb-15">
                    <Formfield label="Tổng thanh toán" name="totalPayment">
                      <Textfield
                        name="totalPayment"
                        disabled
                        defaultValue={paymentData.amount}
                      />
                    </Formfield>
                  </Col>
                  <Col lg={12}>
                    <Formfield label="Ghi chú" name="note">
                      <Textareafield
                        name="note"
                        disabled
                        defaultValue={paymentData.order?.orderNote}
                      />
                    </Formfield>
                  </Col>
                </Row>
              </Section>
            </Section>

            <Section>
              <div className="d-flex justify-content-end">
                <Button
                  buttonType="outline"
                  modifiers="secondary"
                  onClick={navigationHelper.goBack}
                >
                  QUAY LẠI
                </Button>
              </div>
            </Section>
          </Section>
        )}
      </General>
    </SpinnerContainer>
  );
};

export default IndexPage;
