import {
  DeleteFilled,
  EditFilled,
  InfoCircleFilled,
  SearchOutlined,
} from "@ant-design/icons";
import { Col, Input, Row, Tooltip, Modal, Form, Typography } from "antd";
import Table, { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { Heading } from "components/atoms/heading";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import RootPageRouter from "..";
import { DeliveryPartnerListPageVm } from "./vm";

interface DeliveryPartner {
  deliveryPartnerId: number;
  code: string;
  name: string;
  updatedAt: string | Date;
}
const IndexPage = () => {
  const [form] = Form.useForm();
  const {
    gotoPage,
    handleChangePageSize,
    deliveryPartnerListData,
    deliveryPartnerListLoading,
    pageSize,
    deliveryPartnerListPaginationState,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    createDeliveryPartner,
    handleDeleteDeliveryPartner,
  } = DeliveryPartnerListPageVm();

  const onFinish = (values: { code: string; name: string }) => {
    createDeliveryPartner(values);
    form.resetFields();
  };

  const onCancel = () => {
    form.resetFields();
    handleCloseModal();
  };
  // Define table columns
  const columns: ColumnsType<DeliveryPartner> = [
    {
      title: "STT",
      key: "no.",
      render: (_, __, index) => index + 1,
      fixed: "left",
      align: "center",
    },
    {
      title: "Mã đơn vị vận chuyển",
      sorter: true,
      dataIndex: "code",
      key: "code",
      align: "center",
    },
    {
      title: "Tên đơn vị vận chuyển",
      sorter: true,
      dataIndex: "name",
      key: "name",
      align: "center",
    },
    {
      title: "Cập nhật cuối",
      dataIndex: "updatedAt",
      align: "center",
      key: "updatedAt",
      sorter: true,
      render: (value) => {
        return (
          <div className="flex justify-center">
            <span>{dayjs(value).format("DD/MM/YYYY HH:mm")}</span>
          </div>
        );
      },
    },
    {
      title: "Thao tác",
      align: "center",
      key: "action",
      fixed: "right",
      render: (_, record) => {
        const { deliveryPartnerId } = record ?? {};
        return (
          <div className="flex justify-center gap-3">
            <Tooltip title="Chi tiết">
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                icon={<InfoCircleFilled rev={undefined} />}
                onClick={() => {
                  RootPageRouter.gotoChild("deliveryPartnerDetail", {
                    params: {
                      deliveryPartnerId: deliveryPartnerId.toString(),
                    },
                  });
                }}
              />
            </Tooltip>
            <Tooltip title="Chỉnh sửa">
              <BaseButton
                type="primary"
                bgColor={COLOR.GREEN[500]}
                hoverColor={COLOR.GREEN[700]}
                icon={<EditFilled rev={undefined} />}
                onClick={() => {
                  RootPageRouter.gotoChild("deliveryPartnerDetail", {
                    params: {
                      deliveryPartnerId: deliveryPartnerId.toString(),
                    },
                    queryString: "?action=edit",
                  });
                }}
              />
            </Tooltip>

            <Tooltip title="Xóa">
              <BaseButton
                type="primary"
                bgColor={COLOR.RED[500]}
                hoverColor={COLOR.RED[700]}
                icon={<DeleteFilled rev={undefined} />}
                onClick={() => {
                  // TODO: Implement delete functionality

                  handleDeleteDeliveryPartner(deliveryPartnerId);
                }}
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return (
    <General>
      <title>Danh sách đơn vị vận chuyển</title>

      <Section>
        <div className="p-4">
          <div className="mb-6">
            <Typography.Title level={4}>
              DANH SÁCH ĐƠN VỊ VẬN CHUYỂN
            </Typography.Title>
          </div>

          <div className="mb-6">
            <Row gutter={[16, 16]}>
              <Col xs={{ span: 24, order: 2 }} lg={{ span: 6, order: 1 }}>
                <Input
                  placeholder="Tìm kiếm"
                  suffix={
                    <SearchOutlined className="text-xl" rev={undefined} />
                  }
                />
              </Col>
              <Col
                xs={{ span: 24, order: 1 }}
                lg={{ span: 2, order: 2, offset: 16 }}
              >
                <BaseButton
                  type="primary"
                  bgColor={COLOR.BLUE[500]}
                  hoverColor={COLOR.BLUE[700]}
                  className="w-full"
                  onClick={() => handleOpenModalByType("shippingPartner")}
                >
                  Tạo mới
                </BaseButton>
              </Col>
            </Row>
          </div>

          <div className="">
            <Table
              loading={deliveryPartnerListLoading}
              scroll={{ x: 1200 }}
              size="small"
              bordered
              columns={columns}
              dataSource={deliveryPartnerListData}
              rowKey="deliveryPartnerId"
              pagination={{
                current: deliveryPartnerListPaginationState?.currentPage || 1,
                total:
                  (deliveryPartnerListPaginationState?.totalPage || 0) *
                  pageSize,
                pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} của ${total} mục`,
                pageSizeOptions: ["5", "10", "15", "25", "30", "50", "100"],
                onChange: (page, size) => {
                  gotoPage(page);
                  if (size !== pageSize) {
                    handleChangePageSize(size);
                  }
                },
                onShowSizeChange: (_, size) => {
                  handleChangePageSize(size);
                  gotoPage(1); // Reset to first page when changing page size
                },
              }}
            />
          </div>
        </div>
      </Section>

      <Modal
        title="TẠO MỚI ĐƠN VỊ VẬN CHUYỂN"
        open={modalTypeIsOpen("shippingPartner")}
        onCancel={onCancel}
        footer={null}
        width={700}
      >
        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Form.Item
            label="Mã đơn vị vận chuyển"
            name="code"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập mã đơn vị vận chuyển",
              },
            ]}
          >
            <Input placeholder="GHTK" />
          </Form.Item>

          <Form.Item
            label="Tên đơn vị vận chuyển"
            name="name"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập tên đơn vị vận chuyển",
              },
            ]}
          >
            <Input placeholder="Giao hàng tiết kiệm" />
          </Form.Item>

          <div className="flex justify-end gap-3 mt-6">
            <BaseButton type="default" onClick={onCancel}>
              Hủy
            </BaseButton>
            <BaseButton
              htmlType="submit"
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
            >
              Lưu
            </BaseButton>
          </div>
        </Form>
      </Modal>
    </General>
  );
};

export default IndexPage;
