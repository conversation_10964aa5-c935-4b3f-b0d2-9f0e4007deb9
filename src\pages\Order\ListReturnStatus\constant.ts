import * as Yup from "yup";

import { orderReturnStatusOption } from "modules/order";

export const inputValidationSchema = Yup.object({
  name: Yup.string().required("Vui lòng nhập tên"),
});

export type CreateReturnOrderStatusFormPayload = {
  description: string;
  name: string;
  displayOrder: string;
  prevReturnOrderStatusIds: Array<ReturnType<typeof orderReturnStatusOption>>;
  color?: string;
  isDefault: boolean;
};
