import React from "react";

import {
  DeleteFilled,
  EditFilled,
  InfoCircleFilled,
  InfoCircleOutlined,
} from "@ant-design/icons";

import { Button } from "antd";
import { Icon } from "components/atoms/icon";
import { Tooltip } from "components/utils/tooltip";

type ActionType = {
  id: string;
  action?: () => void;
};

interface Props {
  moveAction?: ActionType & { description: string };
  infoAction?: ActionType;
  editAction?: ActionType;
  deleteAction?: ActionType;
}

const IndexSection: React.FC<Props> = ({
  moveAction,
  infoAction,
  editAction,
  deleteAction,
}) => (
  <div
    style={{
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      margin: "auto",
      cursor: "pointer",
      marginRight: "-5px",
    }}
  >
    {moveAction && (
      <div style={{ marginRight: 5 }}>
        <Tooltip
          id={moveAction.id}
          description={moveAction.description}
          place="top"
        >
          <Icon
            iconName="human"
            modifiers="small"
            onClick={moveAction.action}
          />
        </Tooltip>
      </div>
    )}
    {infoAction && (
      <div style={{ marginRight: 5 }}>
        <Tooltip id={infoAction.id} description="Chi tiết" place="top">
          <Button
            type="primary"
            icon={<InfoCircleFilled rev={undefined} />}
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
            onClick={infoAction.action}
          />
          {/* <Icon iconName="info" modifiers="small" onClick={infoAction.action} /> */}
        </Tooltip>
      </div>
    )}
    {editAction && (
      <div style={{ marginRight: 5 }}>
        <Tooltip id={editAction.id} description="Chỉnh sửa" place="top">
          <Button
            type="primary"
            icon={<EditFilled rev={undefined} />}
            style={{
              backgroundColor: "#32a852",
              borderColor: "#32a852",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
            onClick={editAction.action}
          />
          {/* <Icon
            iconName="pencil"
            modifiers="small"
            onClick={editAction.action}
          /> */}
        </Tooltip>
      </div>
    )}
    {deleteAction && (
      <div style={{ marginRight: 5 }}>
        <Tooltip id={deleteAction.id} description="Xoá" place="top">
          <Button
            type="primary"
            icon={<DeleteFilled rev={undefined} />}
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
            onClick={deleteAction.action}
            danger
          />
          {/* <Icon
            iconName="dust-bin"
            modifiers="small"
            onClick={deleteAction.action}
          /> */}
        </Tooltip>
      </div>
    )}
  </div>
);
export default IndexSection;
