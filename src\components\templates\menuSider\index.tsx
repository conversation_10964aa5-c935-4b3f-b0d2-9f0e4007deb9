/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useMemo, useState } from "react";
import { SearchOutlined } from "@ant-design/icons";
import { Input, InputProps, Layout, Menu, MenuProps } from "antd";
import "./index.css";
import { AxiosError } from "axios";
import { Link } from "react-router";
import debounce from "helpers/debounce";
import { useAppDispatch, useAppSelector } from "store/index";
import {
  setMenuCollapsedAction,
  setNotification,
} from "store/system/systemSlice";
import { MenuContext } from "../mainlayout/context";

const { Sider } = Layout;
type MenuItem = Required<MenuProps>["items"][number];

const getItems = (v: any) => {
  return {
    key: v.no,
    label: v.url ? (
      <Link to={v.url} style={{ textDecoration: "none" }}>
        {v.name}
      </Link>
    ) : (
      v.name
    ),
    icon: v.iconBase64 ? (
      <div className="menu-icon">
        <img src={v.iconBase64} alt={v.iconBase64} />
        <p>{v.name}</p>
      </div>
    ) : undefined,
  };
};

export const MenuSider = () => {
  const [menu, setMenu] = useState<any[]>([]);
  const [menuSearch, setMenuSearch] = useState<any[]>();
  const [key, setKey] = useState<string | undefined>();
  const collapsed = useAppSelector((state) => state.system.menuCollapsed);
  const employeeInfo = useAppSelector((state) => state.auth.employeeInfo);
  const { getMenuContextExe, getMenuContextState: menuContext } = MenuContext();
  const dispatch = useAppDispatch();

  const removeAccentss = (str: string) => {
    return str
      .toUpperCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/đ/g, "d")
      .replace(/Đ/g, "D");
  };
  const treeMenu = useMemo(() => {
    const tree: MenuItem[] = [];
    const mappedData: { [key: string]: MenuItem } = {};
    if (menu.length) {
      menu.forEach((v) => {
        mappedData[v.no] = getItems(v);
      });

      menu.forEach((v) => {
        if (v.parentMenu) {
          const parent = mappedData[v.parentMenu] as any;
          if (parent) {
            if (parent.children) {
              parent.children?.push(mappedData[v.no]);
            } else {
              parent.children = [mappedData[v.no]];
            }
          }
        } else {
          tree.push(mappedData[v.no]);
        }
      });
    }
    return {
      tree,
      mappedData,
    };
  }, [menu]);

  const open = useCallback(() => {
    dispatch(setMenuCollapsedAction(false));
  }, [dispatch]);

  const close = useCallback(() => {
    dispatch(setMenuCollapsedAction(true));
  }, [dispatch]);

  const clearSearch = useCallback(() => {
    setMenuSearch(undefined);
  }, []);

  const onSelect: MenuProps["onSelect"] = useCallback(
    (e) => {
      setKey(e.key);
      clearSearch();
      close();
    },
    [close, clearSearch]
  );

  const onSearch: InputProps["onChange"] = useCallback(
    debounce((e) => {
      const { value } = e.target;
      if (value) {
        setMenuSearch(() => {
          return menu.reduce<any[]>((p, c) => {
            if (
              c.url &&
              removeAccentss(c.name).includes(removeAccentss(value.trim()))
            ) {
              p.push(getItems(c));
            }
            return p;
          }, []);
        });
      } else {
        clearSearch();
      }
    }, 500),
    [menu]
  );

  useEffect(() => {
    const loadMenu = async () => {
      if (employeeInfo) {
        try {
          const result = await getMenuContextExe(
            employeeInfo?.site,
            employeeInfo.userName
          );

          const checkFormNo = result.data.map((v: any) => {
            if (
              v.formNo === "frmGoodReceiptNote_KATA" ||
              v.formNo === "frmGoodsDeliveryNote"
            ) {
              return {
                ...v,
                formNo: `${v.formNo}/${v.param1}`,
              };
            }
            return v;
          });

          const convertMenuIFdontHaveURL = checkFormNo.map((v: any) => {
            if (!v.url) {
              const findParentName = v.parentMenu
                ? result.data.find((p: any) => p.no === v.parentMenu)
                : null;
              if (findParentName) {
                const { name } = findParentName;
                return {
                  ...v,
                  url: v.formNo
                    ? `/${name.split(" ").join("")}/${v.formNo.replace(
                        "frm",
                        ""
                      )}`
                    : undefined,
                };
              }
              return {
                ...v,
                url: v.formNo ? `/${v.formNo.replace("frm", "")}` : undefined,
              };
            }
            return v;
          });
          setMenu(convertMenuIFdontHaveURL);
        } catch (err) {
          if (err instanceof AxiosError) {
            // dispatch(
            //   setNotification({
            //     type: "error",
            //     message: err.response?.data.message,
            //   })
            // );
          } else {
            // dispatch(
            //   setNotification({
            //     type: "error",
            //     message: (err as Error).message,
            //   })
            // );
          }
        }
      }
    };
    loadMenu();
  }, []);

  // fixed;   left-0   z-30
  // ${
  //                        collapsed
  //                          ? "translate-x-[-100%] opacity-0 invisible"
  //                          : "translate-x-0 opacity-100 visible"
  //                      }

  return (
    <Sider
      id="menu-sider"
      trigger={null}
      collapsible
      width={275}
      collapsed={collapsed}
      className={` 
                    [&_.ant-layout-sider-children]:!flex
                    [&_.ant-layout-sider-children]:!flex-col
                    h-[calc(100vh_-_54px)]
                `}
      theme="light"
    >
      <div
        className="flex items-center justify-center border-e border-e-[rgba(5,5,5,0.06)]
 px-2 py-3"
      >
        <Input
          key={key}
          placeholder="Tìm kiếm"
          onFocus={open}
          onChange={onSearch}
          suffix={<SearchOutlined rev={null} />}
        />
      </div>
      <Menu
        mode="inline"
        className="h-full overflow-auto"
        items={menuSearch || treeMenu.tree}
        onSelect={onSelect}
      />
    </Sider>
  );
};
