export const collapseSection = (element: HTMLElement) => {
  const sectionHeight = element.scrollHeight;

  const elementTransition = "all 0.25s cubic-bezier(0.2, 0, 0.4, 1)";
  element.style.transition = "";

  requestAnimationFrame(() => {
    element.style.height = `${sectionHeight}px`;
    element.style.transition = elementTransition;

    requestAnimationFrame(() => {
      element.style.height = `${0}px`;
    });
  });
};

export const expandSection = (element: HTMLElement) => {
  // have the element transition to the height of its inner content
  element.style.height = `auto`;

  function transitionendEvent() {
    // remove this event listener so it only gets triggered once
    element.removeEventListener("transitionend", transitionendEvent);
  }

  // when the next css transition finishes (which should be the one we just triggered)
  element.addEventListener("transitionend", transitionendEvent);
};

export const onTriggerCollapedFormConversation = (
  contentElm: HTMLElement | null,
  cb?: (isCollapsed: boolean) => void
) => {
  const isCollapsed = contentElm?.getAttribute("data-collapsed") === "true";
  if (contentElm) {
    contentElm.style.padding = isCollapsed ? "0 16px" : "16px";
    contentElm.style.overflow = isCollapsed ? "hidden" : "visible";
    contentElm.setAttribute("data-collapsed", isCollapsed ? "false" : "true");
    if (!isCollapsed) expandSection(contentElm);
    else collapseSection(contentElm);
  }
  if (cb) {
    cb(!isCollapsed);
  }
};
