import { forwardRef, useImperativeHandle, useState } from "react";

import style from "./index.module.css";

interface Props {
  enabled?: boolean;
}

export interface Loading {
  show(): void;
  hide(): void;
}

// eslint-disable-next-line
export const Loading = forwardRef<Loading, Props>(
  ({ enabled = false }, ref) => {
    const [load, setLoad] = useState(enabled);
    useImperativeHandle(ref, () => ({
      show: () => {
        setLoad(true);
      },
      hide: () => {
        setLoad(false);
      },
    }));
    return (
      <div
        className={style.overlay}
        style={{ display: load ? "flex" : "none" }}
      >
        <div className={style.lds_ring}>
          <div />
          <div />
          <div />
          <div />
        </div>
      </div>
    );
  }
);
