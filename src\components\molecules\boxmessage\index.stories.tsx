import { Story, Meta } from "@storybook/react/types-6-0";

import { BoxTextMessage, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|molecules/BoxTextMessage",
  component: BoxTextMessage,
} as Meta;

const Template: Story<Props> = ({ src, children, contentLeft }) => (
  <div style={{ padding: 20, backgroundColor: "#eeeeee" }}>
    <BoxTextMessage src={src} contentLeft={contentLeft}>
      {children}
    </BoxTextMessage>
  </div>
);

export const Normal = Template.bind({});

Normal.args = {
  contentLeft: false,
  src: "https://avatars.githubusercontent.com/u/18642063?s=64&v=4",
  children: "Trường Zzoro",
};
