import { Button } from "components/atoms/button";
import { Checkbox } from "components/atoms/checkbox";
import { Heading } from "components/atoms/heading";
import { Textfield } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Accordion } from "components/organisms/accordion";
import { Row, Col } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";

const IndexPage = () => {
  return (
    <General>
      <title>Quản lý trang Facebook</title>
      <Section>
        <Heading type="h1" modifiers="primary">
          QUẢN LÝ TRANG FACEBOOK
        </Heading>
        {Array(2)
          .fill(0)
          .map((_, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <Section key={index}>
              <Accordion
                title={`Tên trang ${index + 1}`}
                type="highlight"
                deletable
              >
                <Row>
                  <Col lg="6">
                    <Formfield label="Tên trang" name="pageName">
                      <Textfield name="name" placeholder="Nhập tên trang" />
                    </Formfield>
                  </Col>

                  <Col lg="6" className="u-mt-15 u-mt-lg-0">
                    <Formfield label="Page id" name="pageId">
                      <Textfield name="pageId" placeholder="Nhập page id" />
                    </Formfield>
                  </Col>

                  <Col lg="6" className="u-mt-15">
                    <Formfield label="URL" name="url">
                      <Textfield name="url" placeholder="Nhập url" />
                    </Formfield>
                  </Col>

                  <Col lg="6" className="u-mt-15">
                    <div className="d-none d-lg-block" style={{ height: 44 }} />
                    <div className="d-flex align-items-center">
                      <Checkbox defaultChecked>Kích hoạt</Checkbox>
                    </div>
                  </Col>
                </Row>
                <div className="d-flex justify-content-end u-mt-20">
                  <Button>CẬP NHẬT</Button>
                </div>
              </Accordion>
            </Section>
          ))}

        <Section>
          <div className="d-flex justify-content-end">
            <Button buttonType="outline" iconName="note" buttonSize="medium">
              THÊM TRANG
            </Button>
          </div>

          <div className="d-flex justify-content-end u-mt-20 ">
            <Button modifiers="secondary" buttonType="outline">
              HỦY
            </Button>

            <div className="u-ml-15">
              <Button>LƯU</Button>
            </div>
          </div>
        </Section>
      </Section>
    </General>
  );
};

export default IndexPage;
