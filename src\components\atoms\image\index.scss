.a-image {
	$root: &;
	position: relative;
	overflow: hidden;

	&_img {
		max-width: 100%;

		#{$root}-fixed &,
		#{$root}-oversize &,
		#{$root}-circled & {
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			display: block;
			height: 100%;
			margin: auto;
		}

		#{$root}-fixed & {
			object-fit: cover;
			width: 100%;
		}

		#{$root}-oversize & {
			object-fit: cover;
			object-position: top;
		}

		#{$root}-circled & {
			width: 100%;
			object-fit: cover;
			border-radius: 50%;
		}

		#{$root}-fullwidth & {
			width: 100%;
		}
	}

	&-center {
		text-align: center;
	}

	&-fixed {
		background-color: $COLOR-PLATINUM;
	}

	&-transparent {
		background-color: $COLOR-TRANSPARENT;
	}

	&-16x9 {
		@include aspectRatio(16, 9);
	}

	&-1x1 {
		@include aspectRatio(1, 1);
	}

	&-2x1 {
		@include aspectRatio(2, 1);
	}

	&-3x2 {
		@include aspectRatio(3, 2);
	}

	&-3x4 {
		@include aspectRatio(3, 4);
	}

	&-4x3 {
		@include aspectRatio(4, 3);
	}

	&-file {
		@include aspectRatio(139, 173);
	}
}
