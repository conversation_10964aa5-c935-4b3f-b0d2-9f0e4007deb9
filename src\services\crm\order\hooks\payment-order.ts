import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import {
  CreatePaymentOrderDto,
  DeletePaymentOrderDto,
  GetListPaymentOrderDto,
} from "../dto/payment-order.dto";
import paymentOrderServices from "../payment-order.service";

export const useGetListPaymentOrderByOrderId = (
  dto: GetListPaymentOrderDto
) => {
  const [paymentOrderRefetch, paymentOrderState] = useAsync(
    useCallback(() => paymentOrderServices.getListPaymentOrder(dto), [dto])
  );

  return {
    paymentOrderRefetch,
    paymentOrderData: paymentOrderState?.data?.data,
    paymentOrderLoading: paymentOrderState.loading,
  };
};

export const useDeletePaymentOrder = () => {
  const [deletePaymentOrderExe] = useAsync(
    useCallback(
      (dto: DeletePaymentOrderDto) => paymentOrderServices.delete(dto),
      []
    )
  );

  return {
    deletePaymentOrderExe,
  };
};

export const useCreatePaymentOrder = () => {
  const [createPaymentOrderExe] = useAsync(
    useCallback(
      (dto: CreatePaymentOrderDto) => paymentOrderServices.create(dto),
      []
    )
  );

  return {
    createPaymentOrderExe,
  };
};
