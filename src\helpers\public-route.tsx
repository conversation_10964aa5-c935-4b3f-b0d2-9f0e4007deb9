import { Navigate, Outlet, useLocation } from "react-router";
import PATHS from "constants/paths";
import { useAppSelector } from "store";

export const PublicRouteWrapper = () => {
  const status = useAppSelector((state) => state.auth.status);
  const location = useLocation();

  if (status === "AUTH") {
    return (
      <Navigate
        to={location.state?.previousLocation || PATHS.DASHBOARD}
        replace
      />
    );
  }

  return <Outlet />;
};
