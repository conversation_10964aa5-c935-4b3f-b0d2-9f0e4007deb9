import React, { useEffect, useMemo } from "react";
import { Form, Input, Flex, Row, Col } from "antd";
import { useSearchParams, useParams } from "react-router";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showLoading } from "components/atoms/base/Spinner";
import { Heading } from "components/atoms/heading";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";

import { ChildrenPage } from "../types";
import { DeliveryPartnerDetailFormType } from "./constant";
import { DeliveryPartnerDetailPageVm } from "./vm";

type FormValueType = Partial<DeliveryPartnerDetailFormType>;

function DeliveryPartnerDetailV2() {
  const [form] = Form.useForm<FormValueType>();
  const [searchParams] = useSearchParams();

  const { deliveryPartnerId } =
    useParams<PageParamsType<ChildrenPage["deliveryPartnerDetail"]>>();

  const {
    deliveryPartnerData,
    loading,
    updateDeliveryPartnerState,
    handleUpdateDeliveryPartnerDetail,
  } = DeliveryPartnerDetailPageVm({
    deliveryPartnerId: Number(deliveryPartnerId),
  });

  const pageActionType = searchParams.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  // Set form values when data is loaded
  useEffect(() => {
    if (deliveryPartnerData) {
      form.setFieldsValue({
        code: deliveryPartnerData.code,
        name: deliveryPartnerData.name,
      });
    }
  }, [deliveryPartnerData, form]);

  // Show loading when updating
  useEffect(() => {
    if (updateDeliveryPartnerState.loading) {
      showLoading(true);
    } else {
      showLoading(false);
    }
  }, [updateDeliveryPartnerState.loading]);

  const onFinish = (values: FormValueType) => {
    handleUpdateDeliveryPartnerDetail(values);
  };

  if (loading) {
    return (
      <General>
        <div className="flex justify-center items-center h-64">
          <div>Đang tải...</div>
        </div>
      </General>
    );
  }

  return (
    <General>
      <title>Thông tin đơn vị vận chuyển</title>
      <Section>
        <div className="flex flex-col gap-6 p-6">
          <Heading type="h1" modifiers="primary">
            THÔNG TIN ĐƠN VỊ VẬN CHUYỂN
          </Heading>

          <Form form={form} layout="vertical" onFinish={onFinish}>
            <Row gutter={[16, 24]}>
              <Col span={12}>
                <Form.Item
                  label="Mã đơn vị vận chuyển"
                  name="code"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng nhập mã đơn vị vận chuyển",
                    },
                  ]}
                >
                  <Input placeholder="GHTK" disabled={viewMode} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Tên đơn vị vận chuyển"
                  name="name"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng nhập tên đơn vị vận chuyển",
                    },
                  ]}
                >
                  <Input
                    placeholder="Giao hàng tiết kiệm"
                    disabled={viewMode}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Flex align="center" justify="end" gap={16} className="mt-6">
              <BaseButton type="default" onClick={navigationHelper.goBack}>
                Quay lại
              </BaseButton>
              {editMode && (
                <BaseButton
                  htmlType="submit"
                  type="primary"
                  bgColor={COLOR.BLUE[500]}
                  hoverColor={COLOR.BLUE[700]}
                  disabled={updateDeliveryPartnerState.loading}
                  loading={updateDeliveryPartnerState.loading}
                >
                  Lưu
                </BaseButton>
              )}
            </Flex>
          </Form>
        </div>
      </Section>
    </General>
  );
}

export default DeliveryPartnerDetailV2;
