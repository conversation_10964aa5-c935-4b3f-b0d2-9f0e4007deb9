import { createMapper, fromSchema } from "libs/adapters/dto";
import { mergeSchema, Number } from "libs/domain";

import { ColorSchema, SizeSchema, SkuSchema } from "../entities";

export const skuDetailDto = createMapper(
  fromSchema(
    mergeSchema(SkuSchema, {
      color: ColorSchema,
      size: SizeSchema,
      productId: Number(),
    })
  )
);

export type SkuDetailDtoType = ReturnType<typeof skuDetailDto>;
