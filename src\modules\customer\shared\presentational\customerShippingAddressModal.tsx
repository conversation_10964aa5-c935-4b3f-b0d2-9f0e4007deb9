// import { ChangeEvent, useRef, useMemo, useCallback, useEffect } from "react";

// import { Flex, Input, Modal } from "antd";
// import TextArea from "antd/es/input/TextArea";
// import * as Yup from "yup";

// import { BaseButton } from "components/atoms/base/button/BaseButton";
// import { BaseSelect } from "components/atoms/base/select/BaseSelect";
// import { Button } from "components/atoms/button";
// import { Checkbox } from "components/atoms/checkbox";
// import { Heading } from "components/atoms/heading";
// import { PhonefieldHookForm } from "components/atoms/phonefield";
// import { PulldownHookForm } from "components/atoms/pulldown";
// import { Radio, useRadioProvider } from "components/atoms/radio";
// import { TextareafieldHookForm } from "components/atoms/textareafield";
// import { TextfieldHookForm } from "components/atoms/textfield";
// import { Formfield } from "components/molecules/formfield";
// import { Col, Row } from "components/organisms/grid";
// import { Section } from "components/organisms/section";
// import { COLOR } from "constants/color";
// import { FormContainer } from "helpers/form";
// import { useAsync } from "hooks/useAsync";
// import {
//   CreateCustomerShippingAddressDtoType,
//   UpdateCustomerShippingAddressDtoType,
//   ShippingAddressDtoType,
// } from "modules/customer";
// import {
//   cityOption,
//   districtOption,
//   useLocationSelect,
//   wardOption,
// } from "modules/location";

// type ShippingAddress = ShippingAddressDtoType;

// export type CustomerShippingAddressFormData = Omit<
//   CreateCustomerShippingAddressDtoType | UpdateCustomerShippingAddressDtoType,
//   "countryId" | "cityId" | "districtId" | "wardId"
// > & {
//   city: ReturnType<typeof cityOption>;
//   district: ReturnType<typeof districtOption>;
//   ward: ReturnType<typeof wardOption>;
// };

// export type SubmitShippingAddressType =
//   | Partial<CreateCustomerShippingAddressDtoType>
//   | Partial<UpdateCustomerShippingAddressDtoType>;

// interface CustomerShippingModalProps {
//   open: boolean;
//   onClose: () => void;
//   customerShippingAddress?: ShippingAddress;
//   onSubmit: (formData: SubmitShippingAddressType) => Promise<void>;
// }

// export const CustomerShippingAddressModal = ({
//   open,
//   onClose,
//   customerShippingAddress,
//   onSubmit,
// }: CustomerShippingModalProps) => {
//   const defaultAddressCheckboxRef = useRef<HTMLInputElement | null>(null);

//   const [
//     submitCustomerShippingAddressExec,
//     submitCustomerShippingAddressState,
//   ] = useAsync(onSubmit);

//   const {
//     cityOptions,
//     districtOptions,
//     wardOptions,
//     selectedCity,
//     selectedDistrict,
//     selectedWard,
//     setFilter: setLocationFilter,
//     setFilters: setLocationFilters,
//     batchUpdate: batchUpdateLocation,
//     formatCityOption,
//     formatDistrictOption,
//     formatWardOption,
//   } = useLocationSelect();

//   const {
//     isChecked,
//     setCheckedValue,
//     getCheckedValueGroup,
//   } = useRadioProvider<{
//     shippingAddressType: number;
//   }>({
//     defaultValue: {
//       shippingAddressType: 0,
//     },
//   });

//   const shippingAddressTypeValue = getCheckedValueGroup("shippingAddressType");

//   const customerShippingSchema = useMemo(
//     () =>
//       Yup.object().shape({
//         name: Yup.string().required("Vui lòng nhập tên"),
//         phoneNumber: Yup.string()
//           .required("Vui long nhập số điện thoại")
//           .length(10, "Số điện thoại phải là 10 số"),
//         detail: Yup.string().required("Vui lòng nhập địa chỉ"),
//         city: Yup.object().nullable().required("Vui lòng chọn Tỉnh/Thành phố"),
//         district: Yup.object().nullable().required("Vui lòng chọn Quận/Huyện"),
//         ward: Yup.object().nullable().required("Vui lòng chọn Phường/Xã"),
//         companyName: Yup.string()
//           .typeError("Vui lòng nhập tên công ty")
//           .test(
//             "companyName",
//             "Vui lòng nhập tên công ty",
//             (value?: unknown) => {
//               if (
//                 shippingAddressTypeValue !== 1 ||
//                 (typeof value === "string" && value !== "")
//               )
//                 return true;
//               return false;
//             }
//           ),
//       }),
//     [shippingAddressTypeValue]
//   );

//   const onChangeShippingAddressType = useCallback(
//     (e: ChangeEvent<HTMLInputElement>) =>
//       setCheckedValue("shippingAddressType", Number(e.target.value)),
//     [setCheckedValue]
//   );

//   const handleSubmitCreateShippingAddress = useCallback(
//     async (formData: Partial<CustomerShippingAddressFormData>) => {
//       const { city, district, ward, ...rest } = formData;
//       submitCustomerShippingAddressExec({
//         ...rest,
//         isDefault: defaultAddressCheckboxRef.current?.checked,
//         type: shippingAddressTypeValue,
//         cityId: Number(city?.value),
//         districtId: Number(district?.value),
//         wardId: Number(ward?.value),
//       });
//     },
//     [shippingAddressTypeValue, submitCustomerShippingAddressExec]
//   );

//   useEffect(() => {
//     if (!customerShippingAddress) return;
//     setCheckedValue("shippingAddressType", customerShippingAddress.type);
//     batchUpdateLocation(() => {
//       setLocationFilters({
//         city_id: customerShippingAddress.cityId,
//         district_id: customerShippingAddress.districtId,
//         ward_id: customerShippingAddress.wardId,
//       });
//     });
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [customerShippingAddress]);

//   console.log(cityOptions, "aaaaaaaaaa");

//   return (
//     <Modal
//       width={780}
//       centered
//       open={open}
//       onCancel={onClose}
//       footer={
//         <>
//           <Flex justify="between">
//             <BaseButton
//               type="primary"
//               className="w-fit"
//               bgColor={COLOR.GRAY[500]}
//               hoverColor={COLOR.GRAY[600]}
//               onClick={onClose}
//             >
//               Hủy
//             </BaseButton>
//             <BaseButton
//               type="primary"
//               bgColor={COLOR.BLUE[500]}
//               hoverColor={COLOR.BLUE[600]}
//               disabled={submitCustomerShippingAddressState.loading}
//             >
//               Lưu
//             </BaseButton>
//           </Flex>
//         </>
//       }
//     >
//       <Section>
//         <Heading type="h1" centered>
//           THÔNG TIN GIAO HÀNG
//         </Heading>
//         <Section>
//           <FormContainer
//             validationSchema={customerShippingSchema}
//             onSubmit={handleSubmitCreateShippingAddress}
//           >
//             <Row>
//               <Col lg="12" className="u-mb-15">
//                 <Formfield label="Họ tên" name="name">
//                   <Input
//                     name="name"
//                     placeholder="Họ tên"
//                     defaultValue={customerShippingAddress?.name}
//                   />
//                 </Formfield>
//               </Col>
//               <Col lg="12" className="u-mb-15">
//                 <Formfield label="Số điện thoại" name="phoneNumber">
//                   <Input
//                     name="phoneNumber"
//                     placeholder="Số điện thoại"
//                     defaultValue={customerShippingAddress?.phoneNumber}
//                   />
//                 </Formfield>
//               </Col>
//               <Col lg="12" className="u-mb-15">
//                 <Formfield label="Địa chỉ" name="detail">
//                   <TextArea
//                     name="detail"
//                     placeholder="Địa chỉ"
//                     defaultValue={customerShippingAddress?.detail}
//                   />
//                 </Formfield>
//               </Col>
//               <Col lg="4" className="u-mb-15">
//                 <Formfield label="Tỉnh/Thành" name="city">
//                   <BaseSelect
//                     className="w-full"
//                     options={cityOptions}
//                     fieldNames={{ label: "label", value: "value" }}
//                     onChange={(value) =>
//                       setLocationFilter("city_id", Number(value?.value))
//                     }
//                     // defaultValue={
//                     //   customerShippingAddress &&
//                     //   formatCityOption(customerShippingAddress?.city)
//                     // }
//                     value={selectedCity || null}
//                   />
//                 </Formfield>
//               </Col>
//               <Col lg="4" className="u-mb-15">
//                 <Formfield label="Quận/Huyện" name="district">
//                   <BaseSelect
//                     className="w-full"
//                     options={districtOptions}
//                     fieldNames={{ label: "label", value: "value" }}
//                     onChange={(value) =>
//                       setLocationFilter("district_id", Number(value?.value))
//                     }
//                     // defaultValue={
//                     //   customerShippingAddress &&
//                     //   formatDistrictOption(customerShippingAddress?.district)
//                     // }
//                     value={selectedDistrict || null}
//                   />
//                 </Formfield>
//               </Col>
//               <Col lg="4" className="u-mb-15">
//                 <Formfield label="Phường/Xã" name="ward">
//                   <BaseSelect
//                     className="w-full"
//                     options={wardOptions}
//                     onChange={(value) =>
//                       setLocationFilter("ward_id", Number(value?.value))
//                     }
//                     // defaultValue={
//                     //   customerShippingAddress &&
//                     //   formatWardOption(customerShippingAddress?.ward)
//                     // }
//                     value={selectedWard || null}
//                   />
//                 </Formfield>
//               </Col>
//               <Col lg="12" className="u-mb-15">
//                 <Formfield label="Loại địa chỉ" name="type">
//                   <div className="d-flex align-items-center">
//                     <div className="d-flex u-pr-15">
//                       <Radio
//                         name="type"
//                         value={0}
//                         defaultChecked={isChecked("shippingAddressType", 0)}
//                         onChange={onChangeShippingAddressType}
//                       >
//                         Nhà riêng
//                       </Radio>
//                     </div>
//                     <Radio
//                       name="type"
//                       value={1}
//                       defaultChecked={isChecked("shippingAddressType", 1)}
//                       onChange={onChangeShippingAddressType}
//                     >
//                       Công ty
//                     </Radio>
//                   </div>
//                 </Formfield>
//               </Col>
//               {isChecked("shippingAddressType", 1) && (
//                 <Col lg="12" className="u-mb-15">
//                   <Formfield label="Tên công ty" name="companyName">
//                     <TextfieldHookForm
//                       name="companyName"
//                       placeholder="Tên công ty"
//                       defaultValue={customerShippingAddress?.companyName}
//                     />
//                   </Formfield>
//                 </Col>
//               )}
//               <Col lg="12" className="u-mb-15">
//                 <Checkbox
//                   ref={defaultAddressCheckboxRef}
//                   name="isDefault"
//                   defaultChecked={customerShippingAddress?.isDefault}
//                 >
//                   Đặt làm mặc định
//                 </Checkbox>
//               </Col>
//               <Col lg="12" className="d-flex justify-content-end u-mt-20" />
//             </Row>
//           </FormContainer>
//         </Section>
//       </Section>
//     </Modal>
//   );
// };

// CustomerShippingAddressModal.defaultProps = {
//   customerShippingAddress: undefined,
// };

import { ChangeEvent, useRef, useMemo, useCallback, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Checkbox,
  Radio,
  Typography,
  RadioChangeEvent,
  CheckboxRef,
  Flex,
} from "antd";
import * as Yup from "yup";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import BaseSelectCity from "components/atoms/base/select/shared/BaseSelectCity.share";
import BaseSelectDistrict from "components/atoms/base/select/shared/BaseSelectDistrict.share";
import { PhonefieldHookForm } from "components/atoms/phonefield";
import { PulldownHookForm } from "components/atoms/pulldown";
import { useRadioProvider } from "components/atoms/radio";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { COLOR } from "constants/color";
import { useAsync } from "hooks/useAsync";
import {
  CreateCustomerShippingAddressDtoType,
  UpdateCustomerShippingAddressDtoType,
  ShippingAddressDtoType,
} from "modules/customer";
import {
  cityOption,
  districtOption,
  useLocationSelect,
  wardOption,
} from "modules/location";

type ShippingAddress = ShippingAddressDtoType;

// export type CustomerShippingAddressFormData = Omit<
//   CreateCustomerShippingAddressDtoType | UpdateCustomerShippingAddressDtoType,
//   "countryId" | "cityId" | "districtId" | "wardId"
// > & {
//   city: ReturnType<typeof cityOption>;
//   district: ReturnType<typeof districtOption>;
//   ward: ReturnType<typeof wardOption>;
// };

export type CustomerShippingAddressFormData = Omit<
  CreateCustomerShippingAddressDtoType | UpdateCustomerShippingAddressDtoType,
  "country" | "city" | "district" | "ward"
> & {
  city: ReturnType<typeof cityOption>;
  district: ReturnType<typeof districtOption>;
  ward: ReturnType<typeof wardOption>;
};

export type SubmitShippingAddressType =
  | Partial<CreateCustomerShippingAddressDtoType>
  | Partial<UpdateCustomerShippingAddressDtoType>;

interface CustomerShippingModalProps {
  open: boolean;
  onClose: () => void;
  customerShippingAddress?: ShippingAddress;
  onSubmit: (formData: SubmitShippingAddressType) => Promise<void>;
}

export const CustomerShippingAddressModal = ({
  open,
  onClose,
  customerShippingAddress,
  onSubmit,
}: CustomerShippingModalProps) => {
  const defaultAddressCheckboxRef = useRef<CheckboxRef>(null);
  const [form] = Form.useForm();
  const [
    submitCustomerShippingAddressExec,
    submitCustomerShippingAddressState,
  ] = useAsync(onSubmit);
  const {
    // cityOptions,
    // districtOptions,
    // wardOptions,
    // selectedCity,
    // selectedDistrict,
    // selectedWard,
    // setFilter: setLocationFilter,
    setFilters: setLocationFilters,
    batchUpdate: batchUpdateLocation,
    // formatCityOption,
    // formatDistrictOption,
    // formatWardOption,
  } = useLocationSelect();

  const cityIdValue = Form.useWatch("cityId", form);

  const { isChecked, setCheckedValue, getCheckedValueGroup } =
    useRadioProvider<{
      shippingAddressType: number;
    }>({
      defaultValue: {
        shippingAddressType: 0,
      },
    });

  const shippingAddressTypeValue = getCheckedValueGroup("shippingAddressType");

  const customerShippingSchema = useMemo(
    () =>
      Yup.object().shape({
        name: Yup.string().required("Vui lòng nhập tên"),
        phoneNumber: Yup.string()
          .required("Vui long nhập số điện thoại")
          .length(10, "Số điện thoại phải là 10 số"),
        detail: Yup.string().required("Vui lòng nhập địa chỉ"),
        city: Yup.object().nullable().required("Vui lòng chọn Tỉnh/Thành phố"),
        district: Yup.object().nullable().required("Vui lòng chọn Quận/Huyện"),
        ward: Yup.object().nullable().required("Vui lòng chọn Phường/Xã"),
        companyName: Yup.string()
          .typeError("Vui lòng nhập tên công ty")
          .test(
            "companyName",
            "Vui lòng nhập tên công ty",
            (value?: unknown) => {
              if (
                shippingAddressTypeValue !== 1 ||
                (typeof value === "string" && value !== "")
              )
                return true;
              return false;
            }
          ),
      }),
    [shippingAddressTypeValue]
  );

  // const onChangeShippingAddressType = useCallback(
  //   (e: ChangeEvent<HTMLInputElement>) =>
  //     setCheckedValue("shippingAddressType", Number(e.target.value)),
  //   [setCheckedValue]
  // );
  const onChangeShippingAddressType = (e: RadioChangeEvent) => {
    setCheckedValue("shippingAddressType", Number(e.target.value));
  };

  const handleSubmitCreateShippingAddress = useCallback(
    async (formData: Partial<CustomerShippingAddressFormData>) => {
      form.validateFields();
      const { cityId, districtId, wardId, ...rest } = formData;
      submitCustomerShippingAddressExec({
        ...rest,
        address: formData.detail,
        isDefault: defaultAddressCheckboxRef.current?.input?.checked,
        type: shippingAddressTypeValue,
        cityId: Number(cityId),
        districtId: Number(districtId),
        wardId: Number(wardId),
        countryId: 1,
      });
    },
    [shippingAddressTypeValue, submitCustomerShippingAddressExec]
  );

  useEffect(() => {
    if (!customerShippingAddress) return;
    // setCheckedValue("shippingAddressType", customerShippingAddress.type);
    batchUpdateLocation(() => {
      setLocationFilters({
        cityId: customerShippingAddress.cityId,
        districtId: customerShippingAddress.districtId,
        wardId: customerShippingAddress.wardId,
      });
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customerShippingAddress]);

  const onCloseModal = () => {
    form.resetFields();
    setCheckedValue("shippingAddressType", 0);
    setLocationFilters({
      cityId: undefined,
      districtId: undefined,
      wardId: undefined,
    });
    if (onClose) {
      onClose();
    }
  };

  useEffect(() => {
    if (customerShippingAddress) {
      form.setFieldsValue({
        name: customerShippingAddress.name,
        phoneNumber: customerShippingAddress.phoneNumber,
        detail: customerShippingAddress.detail,
        cityId: customerShippingAddress.cityId,
        districtId: customerShippingAddress.districtId,
        wardId: customerShippingAddress.wardId,
        isDefault: customerShippingAddress.isDefault,
        type: customerShippingAddress.type,
        companyName: customerShippingAddress.companyName || "",
      });
    }
  }, [customerShippingAddress]);

  return (
    <Modal
      width={780}
      centered
      open={open}
      onCancel={onCloseModal}
      footer={
        <Flex justify="end" align="center" gap={10}>
          <BaseButton
            type="primary"
            className="w-fit"
            bgColor={COLOR.GRAY[500]}
            hoverColor={COLOR.GRAY[600]}
            onClick={onCloseModal}
          >
            Hủy
          </BaseButton>
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[600]}
            disabled={submitCustomerShippingAddressState.loading}
            onClick={() => {
              form.submit();
            }}
          >
            Lưu
          </BaseButton>
        </Flex>
      }
    >
      <Section>
        <Typography.Title level={1} style={{ textAlign: "center" }}>
          THÔNG TIN GIAO HÀNG
        </Typography.Title>
        <Form
          layout="vertical"
          form={form}
          name="customer-shipping-address"
          onFinish={handleSubmitCreateShippingAddress}
          // initialValues={customerShippingAddress}
          // validateMessages={customerShippingSchema.validateSync()}
        >
          <Row>
            <Col lg="12" className="u-mb-15">
              <Form.Item
                name="name"
                label="Họ tên"
                rules={[{ required: true, message: "Vui lòng nhập tên" }]}
              >
                <Input placeholder="Họ tên" />
              </Form.Item>
            </Col>
            <Col lg="12" className="u-mb-15">
              <Form.Item
                name="phoneNumber"
                label="Số điện thoại"
                rules={[
                  { required: true, message: "Vui long nhập số điện thoại" },
                  { len: 10, message: "Số điện thoại phải là 10 số" },
                ]}
              >
                <Input placeholder="Số điện thoại" />
              </Form.Item>
            </Col>
            <Col lg="12" className="u-mb-15">
              <Form.Item
                name="detail"
                label="Địa chỉ"
                rules={[{ required: true, message: "Vui lòng nhập địa chỉ" }]}
              >
                <Input.TextArea placeholder="Địa chỉ" />
              </Form.Item>
            </Col>
            <Col lg="6" className="u-mb-15">
              <Form.Item
                name="cityId"
                label="Tỉnh/Thành phố"
                rules={[
                  { required: true, message: "Vui lòng chọn Tỉnh/Thành phố" },
                ]}
              >
                <BaseSelectCity
                  placeholder="Tỉnh/Thành phố"
                  className="w-full"
                  onChange={(value) => {
                    setLocationFilters({ cityId: Number(value) });
                    form.setFieldsValue({
                      districtId: undefined,
                      wardId: undefined,
                    });
                  }}
                />
              </Form.Item>
            </Col>
            <Col lg="6" className="u-mb-15">
              <Form.Item
                name="districtId"
                label="Phường/Xã"
                rules={[{ required: true, message: "Vui lòng chọn Phường/Xã" }]}
              >
                <BaseSelectDistrict
                  cityId={cityIdValue}
                  placeholder="Phường/Xã"
                  className="w-full"
                />
              </Form.Item>
            </Col>
            {/* <Col lg="4" className="u-mb-15">
              <Form.Item
                name="ward"
                label="Phường/Xã"
                rules={[{ required: true, message: "Vui lòng chọn Phường/Xã" }]}
              >
                <Select
                  className="w-full"
                  options={wardOptions}
                  onChange={(value) =>
                    setLocationFilter("ward_id", Number(value?.value))
                  }
                  value={selectedWard || null}
                />
              </Form.Item>
            </Col> */}
            {/* <Col lg="12" className="u-mb-15">
              <Form.Item
                name="type"
                label="Loại địa chỉ"
                rules={[
                  { required: true, message: "Vui lòng chọn loại địa chỉ" },
                ]}
              >
                <Radio.Group
                  onChange={onChangeShippingAddressType}
                  value={shippingAddressTypeValue}
                >
                  <Radio value={0}>Nhà riêng</Radio>
                  <Radio value={1}>Công ty</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            {shippingAddressTypeValue === 1 && (
              <Col lg="12" className="u-mb-15">
                <Form.Item
                  name="companyName"
                  label="Tên công ty"
                  rules={[
                    { required: true, message: "Vui lòng nhập tên công ty" },
                  ]}
                >
                  <Input placeholder="Tên công ty" />
                </Form.Item>
              </Col>
            )} */}
            <Col lg="12" className="u-mb-15">
              <Form.Item name="isDefault" valuePropName="checked">
                <Checkbox ref={defaultAddressCheckboxRef}>
                  Đặt làm mặc định
                </Checkbox>
              </Form.Item>
            </Col>
            <Col lg="12" className="d-flex justify-content-end u-mt-20" />
          </Row>
        </Form>
      </Section>
    </Modal>
  );
};
