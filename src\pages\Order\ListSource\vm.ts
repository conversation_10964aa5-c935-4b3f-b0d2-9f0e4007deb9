import { useCallback, useEffect, useRef, useState } from "react";

import produce from "immer";

import { showNotification } from "components/atoms/base/Notification";
import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { paginationDTO } from "modules/common/pagination";
import {
  createOrderSourceDto,
  CreateOrderSourceDtoType,
  OrderSourceModel,
} from "modules/order";
import {
  getListOrderSource,
  createOrderSource as createOrderSourceService,
  deleteOrderSource,
} from "services/crm/order";

import RootPageRouter from "..";

type ModalType = "sourceCreation";
interface State {
  pageSize: number;
  modalState: {
    open: boolean;
    type?: ModalType;
  };
}
export const ListSourcePageVm = () => {
  const [state, setState] = useState<State>({
    pageSize: 10,
    modalState: {
      open: false,
      type: "sourceCreation",
    },
  });
  const pageSizeRef = useRef<number>(state.pageSize);

  const [getListOrderSourceExec, getListOrderSourceState] = useAsync(
    useCallback(
      (pageNum: number, pageSize: number) =>
        getListOrderSource({ pageNum, pageSize }).then((res) => ({
          listOrderSource: OrderSourceModel.createMap(res.data.data),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );
  const { gotoPage, ...listOrderSourcePaginationState } = usePagination({
    pageSize: state.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getListOrderSourceExec(page, pageSize),
  });

  const {
    sortedData: listOrderSource,
    toggleSortState: toggleListOrderSourceBy,
  } = useSortable({
    data: getListOrderSourceState.data?.listOrderSource,
    sortBy: {
      updatedAt: ({ updatedAt }) => updatedAt,
    },
  });

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pageSize = pageSize;
      })
    );
  }, []);

  const handleOpenModalByType = useCallback(
    (type: ModalType) =>
      setState(
        produce((draft) => {
          draft.modalState.open = true;
          draft.modalState.type = type;
        })
      ),
    []
  );

  const handleOnCloseModal = useCallback(
    () =>
      setState(
        produce((draft) => {
          draft.modalState.open = false;
          draft.modalState.type = undefined;
        })
      ),
    []
  );

  const gotoDetailOrderSource = useCallback(
    (id: number) =>
      RootPageRouter.gotoChild("orderSourceDetail", {
        params: { sourceId: id.toString() },
      }),
    []
  );

  const handleDeleteOrderSource = useCallback((id: number) => {
    deleteOrderSource(id).then((res) => {
      if (res.status === 200) {
        // Refresh the list after deletion
        showNotification({
          type: "success",
          message: "Xóa nguồn đơn thành công",
        });
        getListOrderSourceExec(1, pageSizeRef.current);
      }
    });
  }, []);

  const gotoEditOrderSource = useCallback(
    (id: number) =>
      RootPageRouter.gotoChild("orderSourceDetail", {
        params: { sourceId: id.toString() },
        queryString: "?action=edit",
      }),
    []
  );

  const [createOrderSourceExec, createOrderSourceState] = useAsync(
    createOrderSourceService,
    {
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
      onSuccess: useCallback(() => {
        handleOnCloseModal();
        toastSingleMode({
          type: "success",
          message: "Tạo mới thành công",
        });
        getListOrderSourceExec(1, pageSizeRef.current);
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, []),
    }
  );

  const createOrderSource = useCallback(
    (rawPayload: Partial<CreateOrderSourceDtoType>) => {
      const orderSourcePayload = createOrderSourceDto(rawPayload);
      createOrderSourceExec(orderSourcePayload);
    },
    [createOrderSourceExec]
  );

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    pageSizeRef.current = state.pageSize;
  }, [state.pageSize]);

  return {
    loading: getListOrderSourceState.loading,
    gotoPage,
    listOrderSourcePaginationState,
    listOrderSource: listOrderSource || [],
    toggleListOrderSourceBy,
    pageSize: state.pageSize,
    handleChangePageSize,
    modalState: state.modalState,
    handleOpenModalByType,
    handleOnCloseModal,
    gotoDetailOrderSource,
    gotoEditOrderSource,
    createOrderSource,
    handleDeleteOrderSource,
    createOrderSourceState,
  };
};
