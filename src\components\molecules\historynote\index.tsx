import React from "react";

export interface Props {
  author: string;
  time: string;
  note: string;
}

export const Historynote: React.FC<Props> = ({ author, time, note }) => (
  <div className="m-historynote">
    <div className="m-historynote_author">
      <span>Ngư<PERSON><PERSON> tạo:</span>
      <span>{author}</span>
    </div>
    <div className="m-historynote_time">
      <span>Thời gian:</span>
      <span>{time}</span>
    </div>
    <div className="m-historynote_note">{note}</div>
  </div>
);
