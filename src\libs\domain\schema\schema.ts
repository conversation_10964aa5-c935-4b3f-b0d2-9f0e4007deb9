/* eslint-disable @typescript-eslint/no-explicit-any */
import { Mixed } from "./schemaTypes/mixed";
import { IObject, SchemaReturnValue, SchemaType } from "./types";

/**
 * This class will contain a `schema` property of type `SchemaType`.
 * @class Schema
 * @description This is a `Schema` class
 * @param `o` - Object on which to generate the `schema`.
 * @return The instance of `Schema` class
 */
export class Schema<O extends { [key: string]: unknown }> {
  schema: SchemaType<O>;

  constructor(schema: SchemaType<O>) {
    this.schema = schema;
  }
}

/**
 * A function to get the `parserData` object
 * @function schemaParser
 * @description This is a function to get the `parserData` through the `schema`, `data` and `options`
 * @param schemaInstance - The instance of `Schema` class.
 * @param data - Any data
 * @param options
 * @return A `parserData` object
 */
export const schemaParser = <O extends IObject>(
  schemaInstance: Schema<O>,
  data: any,
  options?: { safeMode?: boolean }
) => {
  const parserData = {} as IObject;
  const { schema } = schemaInstance;
  // eslint-disable-next-line no-restricted-syntax
  for (const field of Object.keys(schema)) {
    const schemaTypeParser = schema[field];
    const dataFieldValue = data?.[field];
    if (typeof schemaTypeParser === "function") {
      parserData[field] = schemaTypeParser(dataFieldValue, options);
    } else {
      parserData[field] = Mixed(schemaTypeParser as SchemaType<IObject>, {
        ...options,
      })(dataFieldValue, { ...options });
    }
  }

  return parserData as {
    [key in keyof SchemaType<O>]: SchemaReturnValue<SchemaType<O>[key]>;
  };
};
