export interface StorageDto {
  storageId: number;
  name: string;
  code: string;
  displayOrder: number;
  // createdAt: string;
  updatedAt: string | Date;
  // deletedAt: string | null;
}

export interface bankAccountDto {
  bankAccountId: number;
  bankName: string;
  branchName: string;
  owner: string;
  shopId: number;
  accountNumber: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
export interface CityDto {
  cityId: number;
  name: string;
  countryId: number;
  createdAt: string;
  updatedAt: string;
}
export interface DistrictDto {
  districtId: number;
  name: string;
  cityId: number;
  createdAt: string;
  updatedAt: string;
}
