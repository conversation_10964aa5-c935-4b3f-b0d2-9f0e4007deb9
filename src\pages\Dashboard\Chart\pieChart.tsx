import {
  AccumulationChartComponent,
  AccumulationDataLabel,
  AccumulationLegend,
  AccumulationSeriesCollectionDirective,
  AccumulationSeriesDirective,
  AccumulationTooltip,
  Inject,
  PieSeries,
} from "@syncfusion/ej2-react-charts";

/* eslint-disable */
export default function PieChart({ id, title, dataPie }: any) {
	return (
		<AccumulationChartComponent
			id={id}
			title={title}
			titleStyle={{ size: "18px" }}
			tooltip={{ enable: true, format: "${point.x} : <b>${point.y}%</b>" }}
			enableSmartLabels
			style={{ height: "100%", width: "100%" }}
			legendSettings={{ visible: true, position: "Bottom" }}
		>
			<Inject
				services={[
					AccumulationTooltip,
					PieSeries,
					AccumulationLegend,
					AccumulationDataLabel,
				]}
			/>
			<AccumulationSeriesCollectionDirective>
				<AccumulationSeriesDirective
					dataSource={dataPie}
					pointColorMapping="color"
					xName="x"
					yName="y"
					border={{ color: "white", width: 1 }}
					radius="70%"
					dataLabel={{
						visible: true,
						position: "Inside",
						name: "text",
						font: {
							fontWeight: "600",
						},
					}}
					innerRadius="30%"
				/>
			</AccumulationSeriesCollectionDirective>
		</AccumulationChartComponent>
	);
}
