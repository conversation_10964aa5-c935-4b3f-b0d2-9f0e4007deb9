import React from "react";

import { Icon } from "components/atoms/icon";
import useUploadFile from "components/hooks/useUploadFile";
import { mapModifiers } from "helpers/component";

export interface Props {
  onChange?: (file: File) => void;
}

export const ImageWrapper: React.FC<Props> = ({ onChange }) => {
  const { refInput, fileUpload, handleClearFile, onChangeFileUpload } =
    useUploadFile({
      onChange,
      extensions: ["jpg", "jpeg", "png"],
      fileSize: 1,
    });

  return (
    <div
      className={mapModifiers("m-imagewrapper", !!fileUpload && "uploaded")}
      style={{
        backgroundImage: fileUpload
          ? `url(${URL.createObjectURL(fileUpload)})`
          : "none",
      }}
    >
      {fileUpload ? (
        <div className="m-imagewrapper_close">
          <Icon iconName="close" />
          <Icon iconName="close-blue" onClick={handleClearFile} />
        </div>
      ) : (
        <>
          <input
            className="m-imagewrapper_input"
            type="file"
            ref={refInput}
            onChange={onChangeFileUpload}
          />
          <div className="m-imagewrapper_wrapicon">
            <Icon iconName="upload" />
          </div>
        </>
      )}
    </div>
  );
};
