import React, {
  useCallback,
  useState,
  useRef,
  useEffect,
  useImperativeHandle,
} from "react";

import vi from "date-fns/locale/vi";
import DatePicker from "react-datepicker";

import { mapModifiers } from "helpers/component";

import { convertDateString } from "./utils";

type ProviderValue<V> = {
  [group in keyof V]: V[group];
};
export interface ProviderHookProps<V = {}> {
  defaultValue?: ProviderValue<V>;
}

export const useCalendarProvider = <V,>(props: ProviderHookProps<V> = {}) => {
  const date = useRef<ProviderValue<V>>(
    props.defaultValue || ({} as ProviderValue<V>)
  );

  const setDate = useCallback(
    <K extends keyof V>(group: K, selectedValue?: V[K]) => {
      date.current = {
        ...date.current,
        [group]: selectedValue,
      };
    },
    []
  );

  const getDate = useCallback(
    <K extends keyof V>(group: K) => date.current?.[group],
    []
  );

  return {
    setDate,
    getDate,
  };
};

export interface ExternalCalendarRegister {
  reset: () => void;
}

export interface Props {
  disabled?: boolean;
  defaultValue?: Date;
  value?: Date;
  valueEnd?: Date;
  isRangePicker?: boolean;
  placeholder?: string;
  onDateSelect?: (date: Date) => void;
  onDateChange?: (date?: Date) => void;
  onDateRangeChange?: (date?: [Date, Date]) => void;
  register?: React.MutableRefObject<ExternalCalendarRegister | undefined>;
}

type DateState = {
  startDate?: Date;
  endDate?: Date;
};

export const Calendar: React.FC<Props> = ({
  onDateSelect,
  onDateChange,
  onDateRangeChange,
  isRangePicker,
  defaultValue,
  value,
  valueEnd,
  disabled,
  placeholder,
  register,
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const datePickerRef = useRef<DatePicker | null>(null);
  const monthYearPickerRef = useRef<DatePicker | null>(null);
  const prevDateRef = useRef<DateState>({
    startDate: undefined,
    endDate: undefined,
  });
  const [dateState, setDateState] = useState<DateState>({
    startDate: isRangePicker ? undefined : defaultValue,
    endDate: undefined,
  });

  const setDatepickerPosition = useCallback(() => {
    const inputElm = containerRef.current?.querySelector(
      ".react-datepicker__input-container"
    ) as HTMLDivElement;

    if (!inputElm) return;

    const {
      top: inputTop,
      bottom: inputBottom,
      left: inputLeft,
    } = inputElm.getBoundingClientRect();
    const inputWidth = inputElm.clientWidth;
    const inputMiddle = inputLeft && inputWidth && inputLeft + inputWidth / 2;

    const dateCalendarElm = containerRef.current?.querySelector(
      ".m-calendar_datecalendar"
    ) as HTMLDivElement;

    if (!dateCalendarElm) return;

    const monthCalendarElm = containerRef.current?.querySelector(
      ".m-calendar_monthyearcalendar"
    ) as HTMLDivElement;

    if (
      inputBottom &&
      window.innerHeight - inputBottom > dateCalendarElm.clientHeight + 15
    ) {
      const dateCalendarPosition = {
        top: `${inputTop}px`,
        left: `${inputMiddle}px`,
        bottom: "unset",
      };
      Object.assign(dateCalendarElm.style, dateCalendarPosition);

      if (!monthCalendarElm) return;
      const monthCalendarPosition = {
        top: `${inputTop}px`,
        left: `${inputMiddle}px`,
      };
      Object.assign(monthCalendarElm.style, monthCalendarPosition);
    } else {
      const dateCalendarPosition = {
        top: "unset",
        left: `${inputMiddle}px`,
        bottom: `${window.innerHeight - inputTop + 45}px`,
      };
      Object.assign(dateCalendarElm.style, dateCalendarPosition);

      if (!monthCalendarElm) return;
      const monthCalendarPosition = {
        top: `${inputTop - 65 - dateCalendarElm.clientHeight}px`,
        left: `${inputMiddle}px`,
      };
      Object.assign(monthCalendarElm.style, monthCalendarPosition);
    }
  }, []);

  const onCalendarOpenDatePicker = useCallback(() => {
    setDatepickerPosition();

    const headerElm = containerRef.current?.querySelector(
      ".react-datepicker__current-month"
    );

    if (!headerElm) return;

    headerElm.addEventListener("click", () => {
      monthYearPickerRef.current?.setOpen(true);
      setDatepickerPosition();
    });
  }, [setDatepickerPosition]);

  const onChangeDatePicker = useCallback(
    (date: Date | [Date, Date] | null) => {
      if (date === null) return;

      if (date instanceof Date) {
        if (onDateChange) onDateChange(date);
        setDateState({ ...dateState, startDate: date });
      } else {
        setDateState((prevDate) => {
          prevDateRef.current = prevDate;
          return { startDate: date[0], endDate: date[1] };
        });

        if (date[1] !== null) {
          datePickerRef.current?.setOpen(false);

          if (onDateRangeChange) onDateRangeChange(date);
        }
      }
    },
    [dateState, onDateChange, onDateRangeChange]
  );

  const onClickOutSideDatePicker = useCallback(
    (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
      if (containerRef.current?.contains(event.target as Node)) {
        datePickerRef.current?.setOpen(true);
      } else if (
        isRangePicker &&
        dateState.startDate !== null &&
        dateState.endDate === null
      ) {
        setDateState(prevDateRef.current);
      }
    },
    [dateState, isRangePicker]
  );

  useEffect(() => {
    window.addEventListener("scroll", setDatepickerPosition, true);
    window.addEventListener("resize", setDatepickerPosition);

    return () => {
      window.removeEventListener("scroll", setDatepickerPosition, true);
      window.removeEventListener("resize", setDatepickerPosition);
    };
  }, [setDatepickerPosition]);

  useImperativeHandle(
    register,
    () => ({
      reset: () => {
        setDateState({});
        if (isRangePicker) {
          if (onDateRangeChange) onDateRangeChange(undefined);
          return;
        }
        if (onDateChange) onDateChange(undefined);
      },
    }),
    [isRangePicker, onDateChange, onDateRangeChange]
  );

  return (
    <div
      className={mapModifiers("m-calendar", disabled && "disabled")}
      ref={containerRef}
    >
      <DatePicker
        placeholderText={placeholder}
        onChangeRaw={(e) => e.preventDefault()}
        locale={vi}
        openToDate={dateState.startDate || undefined}
        disabledKeyboardNavigation
        disabled={disabled}
        ref={datePickerRef}
        selected={value || dateState.startDate}
        selectsEnd={false}
        startDate={isRangePicker ? value || dateState.startDate : undefined}
        endDate={isRangePicker ? valueEnd || dateState.endDate : undefined}
        onChange={onChangeDatePicker}
        onSelect={onDateSelect}
        dateFormat="dd/MM/yyyy"
        selectsRange={isRangePicker}
        shouldCloseOnSelect={!isRangePicker}
        popperClassName="m-calendar_datecalendar"
        customInput={
          isRangePicker ? (
            <div>
              <input
                type="text"
                value={convertDateString(
                  value || dateState.startDate,
                  valueEnd || dateState.endDate
                )}
              />
            </div>
          ) : undefined
        }
        onCalendarOpen={onCalendarOpenDatePicker}
        onClickOutside={onClickOutSideDatePicker}
      />

      {/* Month-Year Picker */}
      <DatePicker
        locale={vi}
        wrapperClassName="m-calendar_wrappermonthyear"
        showMonthYearPicker
        disabledKeyboardNavigation
        disabled={disabled}
        selected={dateState.startDate}
        ref={monthYearPickerRef}
        startDate={isRangePicker ? dateState.startDate : undefined}
        endDate={isRangePicker ? dateState.endDate : undefined}
        selectsRange={isRangePicker}
        onChange={onChangeDatePicker}
        popperClassName="m-calendar_monthyearcalendar"
      />
    </div>
  );
};
