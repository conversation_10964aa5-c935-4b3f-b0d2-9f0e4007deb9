import { Model, String, Number, ModelValue, ExtendSchema } from "libs/domain";
import { EmployeeSchema } from "modules/employee/entities";

export const LeadNoteSchema = {
  employeeId: Number(),
  content: String(),
  employee: ExtendSchema(EmployeeSchema),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  createdAt: (raw: any) => new Date(raw),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => new Date(raw),
};

export const LeadNoteSchemaModal = new Model(LeadNoteSchema);
export type LeadNoteSchemaType = ModelValue<typeof LeadNoteSchemaModal>;
