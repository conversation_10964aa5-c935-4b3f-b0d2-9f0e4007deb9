import { lazy } from "react";

import PATHS from "constants/paths";
import { createAppPage } from "libs/react";

import { ChildrenPage } from "./types";

export default createAppPage<{}, ChildrenPage>({
  name: "LeadManagement",
  path: PATHS.LEAD,
  childrenPages: {
    listLead: createAppPage({
      name: "ListLead",
      path: "/danh-sach-ban-nhap",
      page: () => lazy(() => import("./ListLead")),
    }),
    leadDetail: createAppPage({
      name: "LeadDetail",
      path: "/danh-sach-ban-nha/chi-tiet-ban-nhap/:leadId",
      page: () => lazy(() => import("./LeadDetail")),
    }),
  },
});
