import dayjs from "dayjs";

export const convertDateString = (startDate?: Date, endDate?: Date) => {
  let valueStart: string = "<PERSON><PERSON><PERSON> bắt đầu";
  let valueEnd: string = "<PERSON><PERSON><PERSON> kết thúc";

  if (startDate) {
    valueStart = dayjs(startDate?.toString()).format("DD/MM/YYYY");
  }

  if (endDate) valueEnd = dayjs(endDate?.toString()).format("DD/MM/YYYY");

  return `${valueStart} - ${valueEnd}`;
};
