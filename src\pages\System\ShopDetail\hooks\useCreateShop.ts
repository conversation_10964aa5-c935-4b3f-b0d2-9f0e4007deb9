/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import crmDriverV1 from "services/crm/crm-driver-v1";

export const UseCreateShop = () => {
  const [createShopExe] = useAsync(
    useCallback(
      (payload: any) => crmDriverV1.post("locations/external/shops", payload),
      []
    )
  );
  return {
    createShopExe,
  };
};
