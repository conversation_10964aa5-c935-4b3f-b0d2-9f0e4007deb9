/* eslint-disable react/no-array-index-key */
/* eslint-disable @typescript-eslint/no-explicit-any */
import "./index.css";
import Card from "./Card";
import { CardsData } from "./data";

const Cards = () => {
  return (
    <div className="Cards w-full">
      <p className="text-xl">Dashboard</p>
      <div className="flex w-full">
        {CardsData.map((card: any, id: any) => {
          return (
            <div className="parentContainer mx-2" key={id}>
              <Card
                title={card.title}
                color={card.color}
                barValue={card.barValue}
                value={card.value}
                png={card.png}
                series={card.series}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Cards;
