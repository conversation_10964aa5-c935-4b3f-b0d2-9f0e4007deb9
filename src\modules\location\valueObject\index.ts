import {
  CityEntityType,
  DistrictEntityType,
  WardEntityType,
  StorageEntityType,
  BankAccountEntityType,
} from "modules/location";

export const cityOption = (city: CityEntityType) => ({
  label: city?.name,
  value: city?.cityId?.toString(),
});

export const districtOption = (district: DistrictEntityType) => ({
  label: district?.name,
  value: district?.districtId?.toString(),
});

export const wardOption = (ward: WardEntityType) => ({
  label: ward?.name,
  value: ward?.wardId?.toString(),
});

export const bankAccountOption = (bankAccount: BankAccountEntityType) => ({
  label: bankAccount?.owner,
  value: bankAccount?.bankAccountId.toString(),
});

export const storageOption = (storage?: Partial<StorageEntityType>) => ({
  label: storage?.name || "",
  value: storage?.storageId?.toString() || "",
});
