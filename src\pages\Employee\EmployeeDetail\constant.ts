import * as Yup from "yup";

type DefaultProps = {
  role: string;
  permission: Array<{ name: string; isActive: boolean }>;
};

export const defaultDataPermission: DefaultProps[] = [
  {
    role: "QUẢN LÝ ĐƠN HÀNG",
    permission: [
      {
        name: "xem danh sách",
        isActive: false,
      },
      {
        name: "tạo",
        isActive: false,
      },
      {
        name: "chỉnh sửa",
        isActive: true,
      },
      {
        name: "xóa",
        isActive: true,
      },
    ],
  },
  {
    role: "QUẢN LÝ ĐƠN HÀNG",
    permission: [
      {
        name: "xem danh sách",
        isActive: false,
      },
      {
        name: "tạo",
        isActive: true,
      },
      {
        name: "chỉnh sửa",
        isActive: true,
      },
      {
        name: "xóa",
        isActive: false,
      },
    ],
  },
  {
    role: "QUẢN LÝ ĐƠN HÀNG",
    permission: [
      {
        name: "xem danh sách",
        isActive: false,
      },
      {
        name: "tạo",
        isActive: false,
      },
      {
        name: "chỉnh sửa",
        isActive: true,
      },
      {
        name: "xóa",
        isActive: true,
      },
    ],
  },
];

export const EmployeeUpdateValidationSchema = Yup.object({
  name: Yup.string().required("Tên là bắt buộc"),
  phoneNumber: Yup.string().required("Số điện thoại là bắt buộc"),
  email: Yup.string()
    .email("Vui lòng nhập email hợp lệ")
    .required("Email là bắt buộc"),
  roles: Yup.array()
    .min(1, "Vai trò truy cập là bắt buộc")
    .of(Yup.object().required()),
});
