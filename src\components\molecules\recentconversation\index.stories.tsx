import { Story, Meta } from "@storybook/react/types-6-0";

import { MessageModel } from "modules/chat";

import { RecentConversation, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|molecules/RecentConversation",
  component: RecentConversation,
} as Meta;

const Template: Story<Props> = ({ avatar, name, message, alt }) => (
  <RecentConversation avatar={avatar} alt={alt} name={name} message={message} />
);

export const Normal = Template.bind({});

Normal.args = {
  avatar: "http://via.placeholder.com/300x300&text=avatar",
  alt: "avatar",
  name: "TÊN KHÁCH HÀNG",
  message: {
    ...MessageModel.create({
      _id: "6081552575a3570e40ec00f3",
      messageId: "44be45ed-88ec-4e79-89ad-858a9ce475ba",
      sender: { id: "102496038607569" },
      recipient: { id: "4172256639491478" },
      content: {
        mid: "m_NXxWxb-GgxyFXiSMWvmggSnlTZVA-vSYVr98nFHhaUr65IkDikuxFS9PRCygG0z4rFGWVInuAGvtmszXmcszWw",
        text: "hihi",
        attachments: [],
        metadata: {
          fromSocketId: "gpy8_DcBcx0JB6inAAAJ",
          tempId: "_mid_89pv59jce",
          media: { type: "" },
        },
      },
      conversationId: "a0e709b7-be89-4e1b-9de0-eac7688a5d57",
      createdAt: new Date("2021-04-22T10:51:17.579Z"),
      updatedAt: new Date("2021-04-22T10:51:17.579Z"),
      creatorType: "employee",
      creatorAvatar: "/static/media/logo.b3bbb694.png",
    }),
    creatorType: "employee",
  },
};
