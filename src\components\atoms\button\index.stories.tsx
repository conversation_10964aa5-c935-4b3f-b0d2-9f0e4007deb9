import { <PERSON>, <PERSON>a } from "@storybook/react/types-6-0";

import { <PERSON><PERSON>, <PERSON><PERSON> } from ".";

export default {
  title: "Components|atoms/Button",
  component: But<PERSON>,
} as Meta;

const Template: Story<Props> = ({
  buttonType,
  modifiers,
  disabled,
  href,
  target,
  type,
  fullwidth,
  isLoading,
  onClick,
  buttonSize,
  iconName,
}) => (
  <Button
    modifiers={modifiers}
    buttonType={buttonType}
    disabled={disabled}
    href={href}
    onClick={onClick}
    target={target}
    fullwidth={fullwidth}
    isLoading={isLoading}
    type={type}
    buttonSize={buttonSize}
    iconName={iconName}
  >
    Button
  </Button>
);

export const Normal = Template.bind({});

Normal.args = {};
