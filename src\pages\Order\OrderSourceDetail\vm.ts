import { useCallback, useEffect } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import * as navigationHelper from "helpers/navigation";
import { useAsync } from "hooks/useAsync";
import {
  orderSourceDto,
  OrderSourceDtoType,
  updateOrderSourceDetailDto,
  UpdateOrderSourceDetailDtoType,
} from "modules/order";
import { getOrderSourceDetail, updateOrderSource } from "services/crm/order";

interface OrderSourceDetailPageVmProps {
  orderSourceId: number;
}

export type OrderSourceItem = OrderSourceDtoType;

export const OrderSourceDetailPageVm = ({
  orderSourceId,
}: OrderSourceDetailPageVmProps) => {
  const [getOrderSourceDetailExec, getOrderSourceDetailState] = useAsync(
    useCallback(
      (params: { orderSourceId: number }) =>
        getOrderSourceDetail({ ...params }).then((res) =>
          orderSourceDto(res.data.data)
        ),
      []
    )
  );

  const [updateOrderSourceExec, updateOrderSourceState] = useAsync(
    updateOrderSource,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Cập nhật thành công" });
        navigationHelper.goBack();
      }, []),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const orderSourceDetail = getOrderSourceDetailState.data;

  const handleUpdateOrderSource = useCallback(
    (rawPayload: Partial<UpdateOrderSourceDetailDtoType>) => {
      if (!orderSourceDetail?.orderSourceId) return;
      const updateOrderSourcePayload = updateOrderSourceDetailDto({
        ...orderSourceDetail,
        ...rawPayload,
      });

      updateOrderSourceExec(
        orderSourceDetail.orderSourceId,
        updateOrderSourcePayload
      );
    },
    [updateOrderSourceExec, orderSourceDetail]
  );

  useEffect(() => {
    getOrderSourceDetailExec({ orderSourceId });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderSourceId]);

  return {
    loading: getOrderSourceDetailState.loading,
    orderSourceData: getOrderSourceDetailState.data,
    updateOrderSourceState,
    handleUpdateOrderSource,
  };
};
