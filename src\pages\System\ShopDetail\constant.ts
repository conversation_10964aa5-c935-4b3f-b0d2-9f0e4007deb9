import * as Yup from "yup";

import {
  cityOption,
  districtOption,
  wardOption,
  storageOption,
} from "modules/location";
import { shopTypeOption } from "modules/shop";

import { ShopItem } from "./vm";

export const updateShopDetailValidationSchema = Yup.object({
  name: Yup.string().required("<PERSON>ui lòng nhập tên shop"),
  address: Yup.string().required("Vui lòng nhập địa chỉ"),
  hotline: Yup.string()
    .required("Vui lòng nhập số điện thoại")
    .length(10, "Số điện thoại phải là 10 số"),
  city: Yup.object().nullable().required("Vui lòng chọn Tỉnh/Thành phố"),
  district: Yup.object().nullable().required("Vui lòng chọn Quận/Huyện"),
  // ward: Yup.object().nullable().required("<PERSON>ui lòng chọn Phường/Xã"),
  // storageIds: Yup.array().min(1, "Kho phải ít nhất 1 item"),
});

export const optionsShopType = [
  {
    label: "offline",
    value: "offline",
  },
  {
    label: "online",
    value: "online",
  },
];
export type ShopItemFormType = Omit<ShopItem, "storageIds"> & {
  city?: ReturnType<typeof cityOption>;
  district?: ReturnType<typeof districtOption>;
  // ward?: ReturnType<typeof wardOption>;
  type?: ReturnType<typeof shopTypeOption>;
  // storageIds: Array<ReturnType<typeof storageOption>>;
};
