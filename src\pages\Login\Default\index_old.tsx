// /* eslint-disable */
// import { useCallback, useRef, useState } from "react";

// import { CheckBoxComponent } from "@syncfusion/ej2-react-buttons";
// import {
//   CheckBoxSelection,
//   Inject,
//   ListBoxComponent,
// } from "@syncfusion/ej2-react-dropdowns";
// import * as Yup from "yup";

// import { Button } from "components/atoms/button";
// import { Heading } from "components/atoms/heading";
// import { TextfieldHookForm } from "components/atoms/textfield";
// import { toastSingleMode } from "components/atoms/toastify";
// import { Formfield } from "components/molecules/formfield";
// import { Modal } from "components/organisms/modal";
// import { UnAuth } from "components/pages/unauth";
// import { BasePageProps } from "helpers/component";
// import { getErrorMessageViaErrCode } from "helpers/error-messages";
// import { FormContainer } from "helpers/form";
// import { useAsync } from "hooks/useAsync";
// import { useEmployeeAuthAction } from "modules/auth";
// import * as PAGES from "pages/pages";
// import "./index.css";
// import { GetSiteByUserContext } from "components/form/util/context/Common/Site";

// const loginValidationSchema = Yup.object({
//   emailOrPhone: Yup.string().required("Vui lòng nhập tên đăng nhập"),
//   password: Yup.string().required("Vui lòng nhập mật khẩu"),
// });

// const IndexPage: React.FC<BasePageProps> = () => {
//   const [isOpenSite, setIsOpenSite] = useState<boolean>(false);
//   const listRef = useRef<ListBoxComponent>(null);
//   const data = useRef<{ emailOrPhone: string; password: string }>(null!);
//   const {
//     getSiteByUserExe,
//     getSiteByUserState: listSite,
//   } = GetSiteByUserContext();
//   const { employeeLogin } = useEmployeeAuthAction();

//   const [employeeLoginExec, employeeLoginAsyncState] = useAsync(employeeLogin, {
//     onFailed: useCallback((error) => {
//       const errMessage = getErrorMessageViaErrCode(
//         error?.response?.data?.errors?.[0]?.code
//       );

//       toastSingleMode({
//         type: "error",
//         message: errMessage.translation.title,
//         descripition: errMessage.translation.detail,
//       });
//     }, []),
//   });

//   const onSubmitLogin = useCallback(
//     (e) => {
//       const site = listRef.current?.value[0] as string;
//       if (!site)
//         return toastSingleMode({
//           type: "warning",
//           message: "Vui lòng chọn site",
//         });
//       employeeLoginExec(data.current.emailOrPhone, data.current.password, site);
//     },
//     [employeeLoginExec]
//   );

//   const handleGetSite = (info: { emailOrPhone: string; password: string }) => {
//     data.current = info;
//     getSiteByUserExe(info.emailOrPhone).then((res) => {
//       if (!res?.data.length) {
//         return toastSingleMode({
//           type: "warning",
//           message: "Không tìm thấy nhân viên",
//         });
//       }
//       setIsOpenSite(true);
//     });
//   };

//   const closeChooseSite = () => {
//     setIsOpenSite(false);
//   };

//   return (
//     <UnAuth>
//       <title>Đăng nhập</title>
//       <Modal
//         isOpen
//         isClosable
//         shouldCloseOnEsc
//         shouldCloseOnOverlayClick
//         style={{
//           content: {
//             maxWidth: 620,
//           },
//         }}
//       >
//         <FormContainer
//           validationSchema={loginValidationSchema}
//           onSubmit={handleGetSite}
//         >
//           <Heading type="h1" centered>
//             ĐĂNG NHẬP ĐỂ QUẢN LÝ HỆ THỐNG
//           </Heading>
//           <Formfield label="Tên đăng nhập" name="emailOrPhone">
//             <TextfieldHookForm name="emailOrPhone" />
//           </Formfield>
//           <Formfield label="Mật khẩu" name="password">
//             <TextfieldHookForm type="password" name="password" />
//           </Formfield>
//           <div className="u-mt-15 u-mb-15">
//             <Button
//               type="submit"
//               fullwidth
//               isLoading={employeeLoginAsyncState.loading}
//             >
//               ĐĂNG NHẬP
//             </Button>
//           </div>
//           <Button
//             href={PAGES.ForgotPasswordPage.path}
//             buttonType="textbutton"
//             modifiers="secondary"
//             fullwidth
//           >
//             Quên mật khẩu
//           </Button>
//           <CheckBoxComponent label="Nhớ mật khẩu" />
//         </FormContainer>
//       </Modal>
//       <Modal
//         isOpen={isOpenSite}
//         onCloseModal={closeChooseSite}
//         isClosable
//         shouldCloseOnEsc
//         shouldCloseOnOverlayClick
//         style={{
//           content: {
//             maxWidth: 560,
//           },
//         }}
//       >
//         <Heading type="h1" centered>
//           CHỌN SITE
//         </Heading>
//         <ListBoxComponent
//           ref={listRef}
//           cssClass="listbox-site"
//           dataSource={listSite as any}
//           fields={{ text: "name", value: "site" }}
//           selectionSettings={{
//             mode: "Single",
//           }}
//           // eslint-disable-next-line no-template-curly-in-string
//           itemTemplate="<div class='e-list-item-template'>${name}<span class='e-icons e-check-small' /></div>"
//         >
//           <Inject services={[CheckBoxSelection]} />
//         </ListBoxComponent>
//         <div className="u-mt-15 u-mb-15">
//           <Button
//             type="submit"
//             fullwidth
//             isLoading={employeeLoginAsyncState.loading}
//             onClick={onSubmitLogin}
//           >
//             Xác nhận
//           </Button>
//         </div>
//       </Modal>
//     </UnAuth>
//   );
// };

// export default IndexPage;
