.m-shippingaddress {
	$root: &;
	padding: rem(16);
	box-shadow: 0px 0px rem(8) rgba(0, 0, 0, 0.1);
	&_title {
		display: flex;
		justify-content: space-between;
		color: $COLOR-DENIM;
		span {
			font-size: rem(14);
			line-height: rem(22);
		}
	}
	&_info {
		& > div {
			display: flex;
			@include spSmall {
				flex-direction: column;
			}
			span {
				margin-top: rem(10);
				&:first-child {
					width: 16%;
					font-size: rem(14);
					line-height: rem(22);
					color: $COLOR-DENIM;
					@include spSmall {
						width: 100%;
					}
				}
				&:last-child {
					width: 84%;
				}
			}
		}
	}
}
