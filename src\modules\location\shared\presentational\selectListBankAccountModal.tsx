import { useCallback, useEffect, useState } from "react";
import { DeleteFilled, SearchOutlined } from "@ant-design/icons";
import { Modal, Input, Row, Col, Table, Tooltip } from "antd";
import { ColumnsType } from "antd/es/table";
import produce from "immer";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { Heading } from "components/atoms/heading";
import { COLOR } from "constants/color";
import useDerivedStateFromProps from "helpers/react-hooks/useDerivedStateFromProps";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { paginationDTO } from "modules/common/pagination";
import { BankDetailDtoType } from "modules/location";
import { BankAccountModel } from "modules/location/entities/bankAccount";
import { getListBankAccount } from "services/crm/system";

interface BankAccountSelectedModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (value: Array<BankDetailDtoType>) => void;
  bankAccountSelected: Array<BankDetailDtoType>;
}

interface State {
  pageSize: number;
  selectedBankAccountModal: Array<BankDetailDtoType>;
}

export const BankAccountSelectedModal = ({
  open,
  onClose,
  onSave,
  bankAccountSelected,
}: BankAccountSelectedModalProps) => {
  const [state, setState] = useState<State>({
    pageSize: 10,
    selectedBankAccountModal: [],
  });
  const [searchText, setSearchText] = useState("");

  const handleSelected = useCallback(
    (item: BankDetailDtoType) => {
      const index = state.selectedBankAccountModal.findIndex((el) => {
        return el.bankAccountId === item.bankAccountId;
      });

      if (index === -1) {
        setState(
          produce((draft) => {
            draft.selectedBankAccountModal.push(item);
          })
        );
      }
    },
    [state.selectedBankAccountModal]
  );

  const handleRemoved = useCallback(
    (item: BankDetailDtoType) => {
      const index = state.selectedBankAccountModal.findIndex((el) => {
        return el.bankAccountId === item.bankAccountId;
      });

      if (index !== -1) {
        setState(
          produce((draft) => {
            draft.selectedBankAccountModal.splice(index, 1);
          })
        );
      }
    },
    [state.selectedBankAccountModal]
  );

  useDerivedStateFromProps((_, nextOpenState) => {
    if (nextOpenState) {
      setState(
        produce((draft) => {
          draft.selectedBankAccountModal = bankAccountSelected || [];
        })
      );
    }
  }, open);

  const [getListAccountExec, getAccountListState] = useAsync(
    useCallback(
      (pageNum: number, pageSize: number, owner?: string) =>
        getListBankAccount({ pageNum, pageSize, owner }).then((res) => ({
          data: BankAccountModel.createMap(res.data.data),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const accountListData = getAccountListState.data?.data;

  const { gotoPage, ...accountListPaginationState } = usePagination({
    pageSize: state.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getListAccountExec(page, pageSize, searchText || undefined),
  });

  const isSelected = (item: BankDetailDtoType) => {
    const isChecked = state.selectedBankAccountModal.find(
      (el) => el.bankAccountId === item.bankAccountId
    );
    return !!isChecked;
  };

  const handleSearch = () => {
    gotoPage(1);
  };

  useEffect(() => {
    if (open) {
      gotoPage(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pageSize, open]);

  // Define table columns for available bank accounts
  const availableBankAccountColumns: ColumnsType<BankDetailDtoType> = [
    {
      title: "Số tài khoản",
      dataIndex: "accountNumber",
      key: "accountNumber",
      align: "center",
      width: 150,
    },
    {
      title: "Chủ tài khoản",
      dataIndex: "owner",
      key: "owner",
      align: "center",
      width: 200,
    },
    {
      title: "Ngân hàng",
      dataIndex: "bankName",
      key: "bankName",
      align: "center",
      width: 200,
    },
    {
      title: "Thao tác",
      key: "action",
      align: "center",
      width: 100,
      render: (_, record) => (
        <BaseButton
          type="primary"
          bgColor={COLOR.BLUE[500]}
          hoverColor={COLOR.BLUE[700]}
          disabled={isSelected(record)}
          onClick={() => handleSelected(record)}
        >
          {isSelected(record) ? "Đã chọn" : "Chọn"}
        </BaseButton>
      ),
    },
  ];

  // Define table columns for selected bank accounts
  const selectedBankAccountColumns: ColumnsType<BankDetailDtoType> = [
    {
      title: "Số tài khoản",
      dataIndex: "accountNumber",
      key: "accountNumber",
      align: "center",
    },
    {
      title: "Chủ tài khoản",
      dataIndex: "owner",
      key: "owner",
      align: "center",
    },
    {
      title: "Ngân hàng",
      dataIndex: "bankName",
      key: "bankName",
      align: "center",
    },
    {
      title: "Thao tác",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Tooltip title="Xóa">
          <BaseButton
            type="primary"
            bgColor={COLOR.RED[500]}
            hoverColor={COLOR.RED[700]}
            icon={<DeleteFilled rev={undefined} />}
            onClick={() => handleRemoved(record)}
          />
        </Tooltip>
      ),
    },
  ];

  return (
    <Modal
      title="CHỌN TÀI KHOẢN NGÂN HÀNG"
      open={open}
      onCancel={onClose}
      footer={null}
      width={1300}
    >
      <div className="space-y-6">
        {/* Search Section */}
        <Row gutter={16}>
          <Col span={18}>
            <Input
              placeholder="Thông tin tài khoản ngân hàng"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              suffix={<SearchOutlined rev={undefined} />}
            />
          </Col>
          <Col span={6}>
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              onClick={handleSearch}
              className="w-full"
            >
              TÌM KIẾM
            </BaseButton>
          </Col>
        </Row>
        {/* Available Bank Accounts Table */}
        <div>
          <Heading>DANH SÁCH TÀI KHOẢN NGÂN HÀNG</Heading>
          <Table
            loading={getAccountListState.loading}
            columns={availableBankAccountColumns}
            dataSource={accountListData || []}
            rowKey="bankAccountId"
            size="small"
            bordered
            scroll={{ x: 800 }}
            pagination={{
              current: accountListPaginationState?.currentPage || 1,
              total:
                (accountListPaginationState?.totalPage || 0) * state.pageSize,
              pageSize: state.pageSize,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} của ${total} mục`,
              pageSizeOptions: ["5", "10", "15", "20"],
              onChange: (page, size) => {
                gotoPage(page);
                if (size !== state.pageSize) {
                  setState(
                    produce((draft) => {
                      draft.pageSize = size;
                    })
                  );
                }
              },
              onShowSizeChange: (_, size) => {
                setState(
                  produce((draft) => {
                    draft.pageSize = size;
                  })
                );
                gotoPage(1);
              },
            }}
          />
        </div>

        {/* Selected Bank Accounts Table */}
        <div>
          <Heading>
            TÀI KHOẢN ĐÃ CHỌN ({state.selectedBankAccountModal.length})
          </Heading>
          <Table
            columns={selectedBankAccountColumns}
            dataSource={state.selectedBankAccountModal}
            rowKey="bankAccountId"
            size="small"
            bordered
            scroll={{ y: 250 }}
            pagination={false}
          />
        </div>

        {/* Footer Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <BaseButton type="default" onClick={onClose}>
            HỦY
          </BaseButton>
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={() => onSave(state.selectedBankAccountModal)}
          >
            LƯU
          </BaseButton>
        </div>
      </div>
    </Modal>
  );
};
