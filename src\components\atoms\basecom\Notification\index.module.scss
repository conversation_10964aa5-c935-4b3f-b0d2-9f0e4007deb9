.content {
	display: flex;
	flex-direction: column;
	position: fixed;
	background-color: white;
	right: 0;
	transform: translate(50px, 0px);
	z-index: 1002;
	max-height: 95vh;
  height: 800px;
	width: 800px;
	overflow-x: hidden;
	overflow-y: auto;
	box-shadow: 0 0 15px 0 grey;
	border-radius: 7px;

	& .navigate {
		display: flex;
		padding: 5px;
		border-bottom: 1px solid #e8e8e8;
		-webkit-user-select: none; /* Safari */
		-ms-user-select: none; /* IE 10 and IE 11 */
		user-select: none; /* Standard syntax */
		& div[role="button"] {
			text-align: center;
			width: 20%;
			&.active {
				font-weight: 500;
				color: blue;
			}
			&:hover:not(.active) {
				color: orange;
			}
		}
	}
	& .notification {
		display: flex;
		align-items: center;
		min-height: 70px;
		// & .avatar {
		// 	flex-shrink: 0;
		// 	background-color: black;
		// 	border-radius: 50%;
		// 	width: 50px;
		// 	max-width: 50px;
		// 	height: 50px;
		// 	max-height: 50px;
		// 	overflow: hidden;
		// 	margin: 10px;
		// }
    .avatar {
      flex-shrink: 0;
      background-color: black;
      border-radius: 50%;
      width: 50px;
      max-width: 50px;
      height: 50px;
      max-height: 50px;
      overflow: hidden;
      margin: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .user-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: white;
        padding: 2px;
        border-radius: 50%;
      }
    }
		& .text {
			margin: 5px 0;
			flex-grow: 1;
			& .note {
				font-size: 0.9em;
				& span.name {
					font-weight: 500;
				}
			}
			& .time {
				font-size: 0.8em;
				font-weight: 300;
			}
		}
		&:hover {
			background-color: #dfdfdf;
		}
	}
}
