import { Model, ModelValue, Number, String } from "libs/domain";

export const GroupEmployeeSchema = {
  employeeGroupId: Number(),
  name: String(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => new Date(raw),
};

export const GroupEmployeeModel = new Model(GroupEmployeeSchema);
export type GroupEmployeeEntityType = ModelValue<typeof GroupEmployeeModel>;
