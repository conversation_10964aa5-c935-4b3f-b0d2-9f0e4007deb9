import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import { GetAllOrderByCustomerIdDto } from "../dto/order.dto";
import orderServices from "../order.service";

export const UseGetOrderByCustomerId = (dto: GetAllOrderByCustomerIdDto) => {
  const [orderByCustomerRefetch, orderByCustomerData] = useAsync(
    useCallback(() => {
      return orderServices.getAllOrderByCustomerId(dto);
    }, [dto])
  );

  return {
    orderByCustomerRefetch,
    orderByCustomerData: orderByCustomerData?.data?.data,
    loading: orderByCustomerData?.loading,
  };
};
