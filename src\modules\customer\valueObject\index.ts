import {
  CustomerProfileEntityType,
  ShippingAddressModelType,
} from "../entities";

export const customerShippingAddressOption = (
  customerShippingAddress: ShippingAddressModelType
) => ({
  label: customerShippingAddress?.address,
  value: customerShippingAddress?.shippingAddressId?.toString(),
});

export const customerOption = (customer: CustomerProfileEntityType) => ({
  label: customer?.name,
  value: customer?.customerId.toString(),
});
