import { useCallback, useMemo } from "react";

import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { ShippingAddressModel } from "modules/customer";
import { customerShippingAddressOption } from "modules/customer/valueObject";
import { getCustomerShippingAddress } from "services/crm/customer";

export interface PulldownShippingAddressFunctionalOptions {
  excludePending: boolean;
}

export const usePulldownShippingAddress = ({
  excludePending = false,
}: PulldownShippingAddressFunctionalOptions) => {
  const [fetchCustomerShippingAddress, fetchCustomerShippingAddressState] =
    useAsync(
      useCallback(
        (customerId: number) =>
          getCustomerShippingAddress(customerId).then((res) =>
            ShippingAddressModel.createMap(res.data.data)
          ),
        []
      ),
      {
        excludePending,
      }
    );

  const shippingAddresses = useMemo(
    () => fetchCustomerShippingAddressState.data || [],
    [fetchCustomerShippingAddressState.data]
  );

  const {
    options: shippingAddressOptions,
    getOptionByValue,
    formatOption: formatShippingAddressOption,
  } = usePulldownHelper({
    dataSource: shippingAddresses,
    optionCreator: customerShippingAddressOption,
    valueTrans: Number,
  });

  return {
    shippingAddressOptions,
    shippingAddresses,
    fetchCustomerShippingAddress,
    getOptionByValue,
    formatShippingAddressOption,
    fetchCustomerShippingAddressState,
    fetchCustomerShippingAddressLoading:
      fetchCustomerShippingAddressState.loading,
  };
};
