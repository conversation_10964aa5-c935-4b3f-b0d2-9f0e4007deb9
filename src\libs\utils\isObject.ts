/**
 * @function isObject
 * @description Checks whether a data is an `object` or `not`
 * @param `obj` - Any data
 * @returnType Boolean
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const isObject = (obj: any) => {
  if (typeof obj === "object" && obj !== null) {
    if (typeof Object.getPrototypeOf === "function") {
      const prototype = Object.getPrototypeOf(obj);
      return prototype === Object.prototype || prototype === null;
    }

    return Object.prototype.toString.call(obj) === "[object Object]";
  }

  return false;
};
