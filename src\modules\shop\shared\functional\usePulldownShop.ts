import { useCallback, useMemo } from "react";

import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { shopOption, ShopModel } from "modules/shop";
import { getShops } from "services/crm/system";

export interface PulldownShopFunctionalOptions {
  excludePending: boolean;
}

export const usePulldownShop = ({
  excludePending = false,
}: PulldownShopFunctionalOptions) => {
  const [fetchShops, fetchShopsState] = useAsync(
    useCallback(
      () => getShops().then((res) => ShopModel.createMap(res.data.data)),
      []
    ),
    {
      excludePending,
    }
  );

  const shops = useMemo(
    () => fetchShopsState.data || [],
    [fetchShopsState.data]
  );

  const { options: shopOptions } = usePulldownHelper({
    dataSource: shops,
    optionCreator: shopOption,
    valueTrans: Number,
  });

  return {
    fetchShops,
    shopOptions,
  };
};
