/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useRef } from "react";
import {
  DeleteFilled,
  EditFilled,
  InfoCircleFilled,
  PlusSquareOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Card,
  Checkbox,
  Col,
  ColorPicker as AntColorPicker,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
  Pagination,
  Row,
  Select,
  Switch,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { Color } from "antd/es/color-picker";
import Table, { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { Link } from "react-router";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelectWithFeatures } from "components/atoms/base/select/BaseSelectWithFeatures";
import { PaginationReference } from "components/molecules/pagination";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import RootPageRouter from "..";
import { OrderStatusListPageVm } from "./vm";

const { Option } = Select;
export default function ListOrderStatusV2() {
  const [form] = Form.useForm();

  const {
    getOrderStatusListLoading,
    gotoPage,
    pageSize,
    orderStatusListData,
    orderStatusListPaginationState,
    handleChangePageSize,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    colorSelected,
    createOrderStatus,
    createOrderStatusState,
    orderStatusOptionPulldown,
    onChangeColor,
    orderStatusLoadMore,
    handleDeleteOrderStatus,

    searchText,
    handleSearchInputChange,
    handleClearSearch,
  } = OrderStatusListPageVm();

  const paginationRef = useRef<PaginationReference>(null);

  const handleOnChangePageSizePulldown = useCallback(
    (value: string) => {
      if (value) {
        handleChangePageSize(Number(value));
        if (paginationRef.current) paginationRef.current.reset();
      }
    },
    [handleChangePageSize]
  );

  const handleConfirmDeleteOrderStatus = (record: any) => {
    const { orderStatusId, name } = record || {};
    Modal.confirm({
      title: "Xác nhận xóa trạng thái đơn hàng",
      content: `Bạn có chắc chắn muốn xóa trạng thái đơn hàng "${name}" không?`,
      okText: "Xóa",
      cancelText: "Hủy",
      centered: true,
      okType: "primary",
      okButtonProps: {
        className: "bg-red-500 hover:!bg-red-700 text-white",
      },
      onOk: () => {
        handleDeleteOrderStatus(orderStatusId);
      },
    });
  };

  const handleChangeColor = (value: Color, hex: string) => {
    const rgbColorValue = value.toRgbString();
    onChangeColor(rgbColorValue);
  };

  const onSubmitFormCreate = useCallback(
    async (values: any) => {
      form.validateFields();
      const prevOrderStatusIds = values.prevOrderStatusIds?.map((item: any) => {
        return item;
      });

      const color =
        typeof values?.color === "string"
          ? values.color
          : (values.color as unknown as Color).toRgbString();
      const payload = {
        ...values,
        isDefault: values.isActiveToggle,
        displayOrder: Number(values.displayOrder),
        color,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        prevOrderStatusIds,
      };
      createOrderStatus(payload);
    },
    [createOrderStatus]
  );

  const onCloseModal = () => {
    form.resetFields();
    handleCloseModal();
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const columns: ColumnsType<any> = [
    {
      title: "STT",
      key: "no.",
      render: (_, __, index) => index + 1,
      fixed: "left",
      align: "center",
    },
    {
      title: "Tên trạng thái",
      sorter: true,
      dataIndex: "name",
      key: "name",
      align: "center",
    },
    {
      title: "Màu sắc",
      dataIndex: "color",
      align: "center",
      key: "color",
      render: (value, record) => {
        return <Tag className="w-12 h-4 rounded-xl" color={value} />;
      },
    },
    {
      title: "Mô tả",
      dataIndex: "description",
      align: "center",
      key: "description",
    },
    {
      title: "Mặc định",
      dataIndex: "isDefault",
      align: "center",
      key: "isDefault",
      render: (value, record) => {
        return (
          <div className="flex justify-center">
            <Checkbox checked={value} />
          </div>
        );
      },
    },
    {
      title: "Tagline",
      dataIndex: "orderStatusTaglines",
      align: "center",
      key: "orderStatusTaglines",
      render: (value, record) => {
        return (
          <Link
            to={RootPageRouter.children.listOrderStatusTagline.generatePath({
              orderStatusId: record?.orderStatusId.toString(),
              orderStatusCode: record?.code.toString(),
            })}
            relative="path"
          >
            <span
              style={{
                color: "#FF1515",
              }}
            >
              {(record.orderStatusTaglines || []).length} Tagline
            </span>
          </Link>
        );
      },
    },

    {
      title: "Thứ tự hiển thị",
      dataIndex: "displayOrder",
      align: "center",
      key: "displayOrder",
    },
    {
      title: "Cập nhật cuối",
      dataIndex: "updatedAt",
      align: "center",
      key: "updatedAt",
      sorter: true,
      render: (value) => {
        return (
          <div className="flex justify-center">
            <span>{dayjs(value).format("DD/MM/YYYY HH:mm")}</span>
          </div>
        );
      },
    },
    {
      title: "Thao tác",
      align: "center",
      key: "action",
      fixed: "right",
      render: (_, record) => {
        const { orderStatusId, code } = record ?? {};
        return (
          <div className="flex justify-center gap-3">
            <Tooltip title="Chi tiết">
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                icon={<InfoCircleFilled rev={undefined} />}
                onClick={() => {
                  RootPageRouter.gotoChild("orderStatusDetail", {
                    params: {
                      orderStatusCode: code?.toString(),
                    },
                  });
                }}
              />
            </Tooltip>
            <Tooltip title="Chỉnh sửa">
              <BaseButton
                type="primary"
                bgColor={COLOR.GREEN[500]}
                hoverColor={COLOR.GREEN[700]}
                icon={<EditFilled rev={undefined} />}
                onClick={() => {
                  RootPageRouter.gotoChild("orderStatusDetail", {
                    params: {
                      orderStatusCode: code?.toString(),
                    },
                    queryString: "?action=edit",
                  });
                }}
              />
            </Tooltip>
            <Tooltip title="Xóa">
              <BaseButton
                type="primary"
                bgColor={COLOR.RED[500]}
                hoverColor={COLOR.RED[700]}
                icon={<DeleteFilled rev={undefined} />}
                onClick={() => {
                  const payload = {
                    id: `${orderStatusId}delete`,
                  };
                  handleConfirmDeleteOrderStatus(record);
                }}
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <General>
        <title>Trạng thái đơn hàng</title>
        {/* <div className="flex flex-col gap-4 p-4">
            <Heading type="h1" modifiers="primary">
              TRẠNG THÁI ĐƠN HÀNG
            </Heading> */}
        <Section>
          <Card
            title="TRẠNG THÁI ĐƠN HÀNG"
            style={{ marginLeft: "2.5%", marginRight: "2.5%" }}
            // className="mx-5"
            extra={
              <div className="grid grid-cols-1 lg:grid-cols-[1fr_max-content] gap-3">
                <Input
                  placeholder="Tìm kiếm"
                  allowClear
                  onChange={(e) => {
                    handleSearchInputChange(e.target.value);
                  }}
                  onClear={handleClearSearch}
                  prefix={
                    <SearchOutlined className="text-xl" rev={undefined} />
                  }
                />
                <BaseButton
                  type="primary"
                  bgColor={COLOR.BLUE[500]}
                  hoverColor={COLOR.BLUE[700]}
                  className="w-fit"
                  icon={<PlusSquareOutlined rev={undefined} />}
                  onClick={() => handleOpenModalByType("createStatus")}
                >
                  Tạo mới
                </BaseButton>
              </div>
            }
            // styles={{
            //   header: {
            //     position: "sticky",
            //     top: 0,
            //     zIndex: 100,
            //     width: "100%",
            //     backgroundColor: "#fff",
            //   },
            // }}
          >
            <div className="backgroundW">
              <Table
                loading={getOrderStatusListLoading}
                scroll={{ x: 1500 }}
                // sticky={{ offsetHeader: 56 }}
                // size="small"
                bordered
                columns={columns}
                dataSource={orderStatusListData}
                pagination={false}
              />
            </div>
            <div className="backgroundW">
              <Row
                justify="space-between"
                align="middle"
                style={{ padding: "16px" }}
              >
                <Col>
                  {/* <Pulldown
                      placeholder="Số lượng hiển thị"
                      value={{ label: `${pageSize}`, value: `${pageSize}` }}
                      options={[5, 10, 15, 25, 30].map((size) => ({
                        label: `${size}`,
                        value: `${size}`,
                      }))}
                      onChange={handleOnChangePageSizePulldown}
                    /> */}
                  <Select
                    placeholder="Số lượng hiển thị"
                    value={`${pageSize}`}
                    onChange={handleOnChangePageSizePulldown}
                    style={{ width: 150 }}
                  >
                    {[5, 10, 15, 25, 30].map((size) => (
                      <Option key={size} value={`${size}`}>
                        {size}
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col>
                  {orderStatusListPaginationState.totalPage && (
                    <Pagination
                      total={orderStatusListPaginationState.totalPage * 10}
                      showSizeChanger={false}
                      defaultCurrent={1}
                      onChange={gotoPage}
                      // ref={paginationRef}
                    />
                  )}
                </Col>
              </Row>
            </div>
          </Card>
        </Section>
        {/* <Row gutter={[16, 16]}>
              <Col xs={{ span: 24, order: 2 }} lg={{ span: 12, order: 1 }}>
                <Input
                  placeholder="Tìm kiếm"
                  size="large"
                  suffix={
                    <SearchOutlined className="text-xl" rev={undefined} />
                  }
                />
              </Col>
              <Col
                xs={{ span: 24, order: 1 }}
                lg={{ span: 3, order: 2, offset: 9 }}
              >
                <BaseButton
                  type="primary"
                  bgColor={COLOR.BLUE[500]}
                  hoverColor={COLOR.BLUE[700]}
                  className="w-full"
                  size="large"
                  onClick={() => handleOpenModalByType("createStatus")}
                >
                  Tạo mới
                </BaseButton>
              </Col>
            </Row> */}

        {/* <div className="">
              <Table
                loading={getOrderStatusListLoading}
                scroll={{ x: 1500 }}
                size="small"
                bordered
                columns={columns}
                dataSource={orderStatusListData}
                pagination={false}
              />

              <PaginationSection
                appearanceOption={
                  <Pulldown
                    placeholder="Số lượng hiển thị"
                    value={{ label: `${pageSize}`, value: `${pageSize}` }}
                    options={[5, 10, 15, 25, 30].map((size) => ({
                      label: `${size}`,
                      value: `${size}`,
                    }))}
                    onChange={handleOnChangePageSizePulldown}
                  />
                }
                paginateOption={
                  orderStatusListPaginationState.totalPage && (
                    <Pagination
                      modifiers="center"
                      total={orderStatusListPaginationState.totalPage}
                      pageCount={5}
                      defaultCurrentPage={1}
                      onPageChange={gotoPage}
                      ref={paginationRef}
                    />
                  )
                }
              />
            </div> */}
        {/* </div> */}
      </General>
      <Modal
        styles={{
          header: {
            textAlign: "center",
          },
          content: { maxWidth: 700 },
        }}
        open={modalTypeIsOpen("createStatus")}
        onCancel={onCloseModal}
        closeIcon={false}
        footer={false}
        centered
        title={
          <Typography.Title level={4}>
            TẠO MỚI TRẠNG THÁI ĐƠN HÀNG
          </Typography.Title>
        }
      >
        {/* <Heading type="h3" centered>
          TẠO MỚI TRẠNG THÁI ĐƠN HÀNG
        </Heading> */}

        <Form
          form={form}
          labelAlign="left"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          layout="vertical"
          onFinish={onSubmitFormCreate}
        >
          <Form.Item
            label="Tên trạng thái"
            name="name"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập tên",
              },
            ]}
          >
            <Input placeholder="Tên trạng thái" />
          </Form.Item>

          <Form.Item label="Mô tả" name="description">
            <Input.TextArea name="description" placeholder="Mô tả" rows={3} />
          </Form.Item>

          {/* <Form.Item label="Màu sắc" name="color">
            <ColorPicker format="rgb" onChange={handleChangeColor} />
          </Form.Item> */}
          <Form.Item label="Màu sắc" name="color" initialValue="#000">
            <AntColorPicker />
          </Form.Item>

          <Form.Item label="Thứ tự hiển thị" name="displayOrder">
            <InputNumber
              className="w-full"
              name="displayOrder"
              placeholder="Thứ tự hiển thị"
            />
          </Form.Item>

          <Form.Item hidden label="Trạng thái trước" name="prevOrderStatusIds">
            <BaseSelectWithFeatures
              placeholder="Trạng thái trước"
              mode="multiple"
              options={orderStatusOptionPulldown}
              triggerLoadMore={async () => orderStatusLoadMore()}
            />
          </Form.Item>

          <Form.Item
            label="Mặc định"
            labelAlign="left"
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            name="isActiveToggle"
          >
            <Switch />
          </Form.Item>

          <Flex align="center" justify="end" gap={16}>
            <BaseButton type="link" onClick={onCloseModal}>
              Hủy
            </BaseButton>
            <BaseButton
              htmlType="submit"
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              disabled={createOrderStatusState.loading}
              loading={createOrderStatusState.loading}
            >
              Lưu
            </BaseButton>
          </Flex>
        </Form>
      </Modal>
    </>
  );
}
