{
  "extends": ["eslint:recommended", "airbnb"],
  "overrides": [
    {
      "files": "*.{ts,tsx,js,jsx}",
      "extends": [
        "react-app",
        "airbnb",
        "plugin:import/typescript",
        "plugin:prettier/recommended"
      ],
      "settings": {
        "import/resolver": {
          "node": {
            "extensions": [".js", ".jsx", ".ts", ".tsx"],
            "moduleDirectory": ["node_modules", "src/"],
            "path": ["src"]
          }
        }
      },
      "rules": {
        "no-lonely-if": 0,
        "react/default-props-match-prop-types": 1,
        "react/no-unused-prop-types": 1,
        "react/destructuring-assignment": 1,
        "react/require-default-props": 0,
        "no-nested-ternary": 0,
        "jsx-a11y/click-events-have-key-events": 0,
        "jsx-a11y/label-has-associated-control": [
          "error",
          {
            "required": {
              "some": ["nesting", "id"]
            }
          }
        ],
        "react/jsx-filename-extension": [
          2,
          {
            "extensions": [".tsx"]
          }
        ],
        "import/extensions": [
          "error",
          "always",
          {
            "js": "never",
            "jsx": "never",
            "ts": "never",
            "tsx": "never"
          }
        ],
        "import/prefer-default-export": 0,
        "import/no-extraneous-dependencies": [
          "error",
          {
            "devDependencies": [
              "src/helpers/*.@(ts|tsx)",
              "src/services/http.ts",
              "src/setupTests.ts",
              "src/**/*.stories.tsx",
              "src/**/*.test.tsx",
              "src/**/*.test.ts"
            ]
          }
        ],
        "import/order": [
          2,
          {
            "groups": [
              "builtin",
              "external",
              "internal",
              "parent",
              "sibling",
              "index",
              "object",
              "type"
            ],
            "pathGroups": [
              {
                "pattern": "react",
                "group": "external",
                "position": "before"
              }
            ],
            "pathGroupsExcludedImportTypes": ["react"],
            "newlines-between": "ignore",
            "alphabetize": {
              "order": "asc",
              "caseInsensitive": true
            }
          }
        ],
        "react/prop-types": 0, // already validate using TS,
        "no-use-before-define": 0,
        "@typescript-eslint/no-use-before-define": "error",
        "react/jsx-props-no-spreading": [
          "error",
          {
            "exceptions": [
              "RouterLink",
              "Link",
              "a",
              "input",
              "textarea",
              "Formfield",
              "ReactTabs",
              "Select",
              "components.Menu",
              "components.DropdownIndicator",
              "SketchPicker",
              "GridComponent"
            ]
          }
        ],
        "react/forbid-elements": [
          2,
          {
            "forbid": [
              { "element": "a", "message": "use components/utils/link instead" }
            ]
          }
        ],
        "no-param-reassign": 0,
        "react/jsx-key": [2, { "checkFragmentShorthand": true }],
        "@typescript-eslint/no-explicit-any": [
          "error",
          { "ignoreRestArgs": true }
        ],
        "react/no-danger": 0,
        "no-unused-vars": 0,
        "import/no-cycle": 0,
        "react/react-in-jsx-scope": 0,
        "no-undef": 0,
        // "no-underscore-dangle": [1, { "allow": ["_id", "itemNo_", "variantNo_", "uoMNo_"] }],
        "no-underscore-dangle": [0],
        "no-shadow": "off",
        "@typescript-eslint/no-shadow": ["error"],
        "react/function-component-definition": [0],
        "react/no-unstable-nested-components": 0
      }
    }
  ],
  "rules": {
    "import/no-extraneous-dependencies": [
      "error",
      {
        "devDependencies": ["scripts/*.js"]
      }
    ],
    "import/order": [
      2,
      {
        "groups": ["builtin", "external", "internal"],
        "pathGroups": [
          {
            "pattern": "react",
            "group": "external",
            "position": "before"
          }
        ],
        "pathGroupsExcludedImportTypes": ["react"],
        "newlines-between": "always",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ]
  }
}
