import { <PERSON>, Meta } from "@storybook/react/types-6-0";

import { <PERSON><PERSON>, <PERSON>psTab<PERSON>, Tab, <PERSON>b<PERSON><PERSON>, Tab<PERSON>anel } from ".";

export default {
  title: "Components|organisms/Tabs",
  component: Tabs,
} as Meta;

const Template: Story<PropsTabs> = ({ type, ...innerProps }) => (
  // eslint-disable-next-line react/jsx-props-no-spreading
  <Tabs type={type} {...innerProps}>
    <TabList>
      <Tab>Tab 01</Tab>
      <Tab>Tab 02</Tab>
      <Tab>Tab 03</Tab>
      <Tab>Tab 04</Tab>
      <Tab>Tab 05</Tab>
      <Tab>Tab 06</Tab>
    </TabList>

    <TabPanel>
      <h2>Any content 1</h2>
    </TabPanel>
    <TabPanel>
      <h2>Any content 2</h2>
    </TabPanel>
    <TabPanel>
      <h2>Any content 3</h2>
    </TabPanel>
    <TabPanel>
      <h2>Any content 4</h2>
    </TabPanel>
    <TabPanel>
      <h2>Any content 5</h2>
    </TabPanel>
    <TabPanel>
      <h2>Any content 6</h2>
    </TabPanel>
  </Tabs>
);

export const Normal = Template.bind({});

Normal.args = {
  type: "main",
};
