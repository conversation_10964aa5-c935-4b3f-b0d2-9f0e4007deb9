import { fromSchema, createMapper } from "libs/adapters/dto";
import { Array, mergeSchema, Mixed } from "libs/domain";

import {
  OrderReturnStatusSchema,
  OrderReturnStatusTaglineSchema,
} from "../entities";

export const orderReturnStatusDetailDto = createMapper(
  fromSchema(
    mergeSchema(OrderReturnStatusSchema, {
      prevReturnOrderStatus: Array(fromSchema(OrderReturnStatusSchema)),
      returnOrderStatusTaglines: Array(Mixed(OrderReturnStatusTaglineSchema)),
    })
  )
);

export type OrderReturnStatusDetailDtoType = ReturnType<
  typeof orderReturnStatusDetailDto
>;
