/* eslint-disable @typescript-eslint/no-explicit-any */
import ReactQuill from "react-quill";
import "./index.module.scss";

type Props = {
  value?: string;
  onChange?: any;
};

/**
 * Wrapped ReactQuill in Syncfusion to use with Ant Design Form
 */
export const RichTextEditor = ({ value, onChange }: Props) => {
  return (
    <ReactQuill
      onChange={onChange}
      value={value}
      theme="snow"
      modules={{
        toolbar: [
          [{ font: [] }],
          [{ header: [1, 2, 3, 4, 5, 6, false] }],
          [{ align: [] }],
          ["bold", "italic", "underline", "strike"], // toggled buttons
          [{ color: [] }, { background: [] }], // dropdown with defaults from theme
          ["blockquote", "code-block"],
          [{ list: "ordered" }, { list: "bullet" }],
          [{ script: "sub" }, { script: "super" }], // superscript/subscript
          [{ indent: "-1" }, { indent: "+1" }], // outdent/indent
          ["link", "image"],
          ["clean"],
        ],
      }}
    />
  );
};
