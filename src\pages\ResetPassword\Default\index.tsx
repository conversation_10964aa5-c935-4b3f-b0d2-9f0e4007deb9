/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useState } from "react";

import { useNavigate, useSearchParams } from "react-router";
import * as Yup from "yup";

import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Text } from "components/atoms/text";
import { TextfieldHookForm } from "components/atoms/textfield";
import { toastSingleMode } from "components/atoms/toastify";
import { Formfield } from "components/molecules/formfield";
import { Modal } from "components/organisms/modal";
import { UnAuth } from "components/pages/unauth";
import { BasePageProps } from "helpers/component";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { FormContainer } from "helpers/form";
import { useAsync } from "hooks/useAsync";
import * as PAGES from "pages/pages";
import {
  employeeResetPassword,
  EmployeeResetPasswordPayload,
} from "services/crm/employee";

interface IResetPasswordForm
  extends Omit<EmployeeResetPasswordPayload, "token"> {}

const validationSchema = Yup.object({
  newPassword: Yup.string()
    .min(8, "Mật khẩu vui lòng sử dụng 8 ký tự trở lên")
    .required("Vui lòng nhập mật khẩu mới"),
  cnewPassword: Yup.string()
    .oneOf([Yup.ref("newPassword"), ""], "Mật khẩu không khớp")
    .required("Nhập lại mật khẩu mới là bắt buộc"),
});

const IndexPage: React.FC<BasePageProps> = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isTokenInvalid, setTokenInvalid] = useState<boolean>(false);

  const [updateEmployeePasswordExec, updateEmployeeState] = useAsync(
    employeeResetPassword,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({
          type: "success",
          message: "Cập nhật mật khẩu thành công",
        });
      }, []),
      onFailed: useCallback((error: any) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const onSubmitEmployeePassword = useCallback(
    (formData: IResetPasswordForm, reset: () => void) => {
      const token = searchParams.get("token");

      if (!token) {
        setTokenInvalid(true);
        return;
      }

      updateEmployeePasswordExec({
        token,
        newPassword: formData.newPassword,
      });
      reset();
    },
    [searchParams, updateEmployeePasswordExec]
  );

  return (
    <UnAuth>
      <title key="title">Làm mới mật khẩu</title>
      <Modal
        isOpen
        isClosable={false}
        shouldCloseOnEsc={false}
        shouldCloseOnOverlayClick={false}
        style={{
          content: {
            maxWidth: 620,
          },
        }}
      >
        {!isTokenInvalid ? (
          <FormContainer
            validationSchema={validationSchema}
            onSubmit={onSubmitEmployeePassword}
          >
            <Heading type="h1" centered>
              LÀM MỚI MẬT KHẨU
            </Heading>
            <Formfield label="Mật khẩu mới" name="newpassword">
              <TextfieldHookForm
                name="newPassword"
                id="newpassword"
                type="password"
              />
            </Formfield>
            <Formfield label="Xác nhận mật khẩu mới" name="confirmpassword">
              <TextfieldHookForm
                name="cnewPassword"
                id="confirmpassword"
                type="password"
              />
            </Formfield>
            <div className="u-mt-15 u-mb-15">
              <Button
                type="submit"
                fullwidth
                isLoading={updateEmployeeState.loading}
              >
                RESET
              </Button>
            </div>
          </FormContainer>
        ) : (
          <>
            <Heading type="h1" centered>
              Token không hợp lệ
            </Heading>
            <Text centered>
              Không tìm thấy token, cần lấy lại token trước khi cập nhật mật
              khẩu mới
            </Text>
            <Button
              fullwidth
              onClick={() => navigate(PAGES.ForgotPasswordPage.path)}
            >
              Xác nhận
            </Button>
          </>
        )}
      </Modal>
    </UnAuth>
  );
};

export default IndexPage;
