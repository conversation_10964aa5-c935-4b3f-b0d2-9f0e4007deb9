import { ReactNode, useRef } from "react";

const UNMOUNTED_VALUE = {};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type IChildren = ReactNode & { props: any };

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function arePropsEqual(prevProps: any, nextProps: any) {
  const nextPropKeys = Object.keys(nextProps);
  const prevPropKeys = Object.keys(prevProps);

  if (nextPropKeys.length !== prevPropKeys.length) {
    return false;
  }

  for (let i = 0; i < nextPropKeys.length && i < prevPropKeys.length; i += 1) {
    if (Object.is(nextProps[nextPropKeys[i]], prevProps[prevPropKeys[i]])) {
      // eslint-disable-next-line no-continue
      continue;
    }
    return false;
  }
  return true;
}

export interface MemorizePresenterProps<MemorizeValue> {
  memorizeValue: MemorizeValue;
  children?: IChildren;
}

export const MemorizePresenter = <MemorizeValue,>({
  memorizeValue,
  children,
}: MemorizePresenterProps<MemorizeValue>) => {
  const prevChildren = useRef<IChildren | undefined>(
    UNMOUNTED_VALUE as IChildren
  );
  const prevMemorizeValue = useRef<MemorizeValue>(
    UNMOUNTED_VALUE as MemorizeValue
  );

  if (
    prevMemorizeValue.current === UNMOUNTED_VALUE ||
    prevChildren.current === UNMOUNTED_VALUE ||
    !Object.is(memorizeValue, prevMemorizeValue.current)
  ) {
    prevChildren.current = children;
  }

  prevMemorizeValue.current = memorizeValue;

  return prevChildren.current;
};

export const SafeMemorizePresenter = <MemorizeValue,>({
  memorizeValue,
  children,
}: MemorizePresenterProps<MemorizeValue>) => {
  const prevChildren = useRef<IChildren | undefined>(
    UNMOUNTED_VALUE as IChildren
  );
  const prevMemorizeValue = useRef<MemorizeValue>(
    UNMOUNTED_VALUE as MemorizeValue
  );

  if (
    prevMemorizeValue.current == null ||
    prevChildren.current == null ||
    !Object.is(memorizeValue, prevMemorizeValue.current) ||
    !arePropsEqual(prevChildren.current.props, children?.props)
  ) {
    Object.assign(prevChildren, {
      current: children,
    });
  }

  Object.assign(prevMemorizeValue, {
    current: memorizeValue,
  });

  return prevChildren.current;
};
