import { Story, Meta } from "@storybook/react/types-6-0";

import { AutoComplete, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|atoms/AutoComplete",
  component: AutoComplete,
} as Meta;

const Template: Story<Props> = ({
  iconSrc,
  options,
  closeListAfterSelected,
  clearInputAfterSelected,
}) => {
  const onSelectedItem = () => {};
  const onInputChange = () => {};
  const onTrigger = async () => {};
  const onRenderItem = (label: string) => {
    return <div>*{label}*</div>;
  };

  return (
    <AutoComplete
      options={options}
      placeholder="Searching..."
      iconSrc={iconSrc}
      closeListAfterSelected={closeListAfterSelected}
      clearInputAfterSelected={clearInputAfterSelected}
      onSelectedItem={onSelectedItem}
      onInputChange={onInputChange}
      triggerOnLoadmore={onTrigger}
      onRenderItem={onRenderItem}
    />
  );
};

export const Normal = Template.bind({});

Normal.args = {
  options: [
    {
      label: "Thanh Thanh",
      value: "1",
    },
    {
      label: "Ngọc Ngân",
      value: "2",
    },
    {
      label: "Trâm Anh",
      value: "3",
    },
    {
      label: "Thanh Anh",
      value: "4",
    },
    {
      label: "Thanh Trần",
      value: "5",
    },
    {
      label: "Ngọc Anh",
      value: "6",
    },
    {
      label: "Ngọc Trinh",
      value: "7",
    },
    {
      label: "Ngọc Thịnh",
      value: "8",
    },
    {
      label: "Thanh Trần",
      value: "9",
    },
    {
      label: "Ngọc Anh",
      value: "10",
    },
    {
      label: "Ngọc Trinh",
      value: "11",
    },
    {
      label: "Ngọc Thịnh",
      value: "12",
    },
  ],
  iconSrc: "search-blue",
};
