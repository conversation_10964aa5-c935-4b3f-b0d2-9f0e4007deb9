import React from "react";

import { useLocation } from "react-router";

import PATH from "constants/paths";
import { mapModifiers } from "helpers/component";

type Modifiers = "overflow-y-hidden";
export interface Props {
  modifiers?: Modifiers | Array<Modifiers>;
  helmet?: React.ReactNode;
  children?: React.ReactNode;
}

export const General: React.FC<Props> = ({ modifiers, helmet, children }) => {
  const location = useLocation();
  const noUseCss = [
    PATH.GENERAL_CONFIG,
    PATH.WORKSPACE,
    PATH.LEAD,
    "InspectionOrder/",
  ];
  return noUseCss.some((v) => location.pathname.includes(v)) ? (
    <div className="custom-general">
      {helmet}
      {children}
    </div>
  ) : (
    <div className={mapModifiers("p-general", modifiers)}>
      {helmet}
      {children}
    </div>
  );
};
