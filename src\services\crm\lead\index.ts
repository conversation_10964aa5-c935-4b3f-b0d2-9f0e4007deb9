import { CreateLeadDTOType } from "modules/lead";

import crmDriverV1 from "../crm-driver-v1";

export const getLeadDetail = (payload: { leadId: string }) =>
  crmDriverV1.get(`/conversations/external/leads/${payload.leadId}`);

export const createLead = (payload: CreateLeadDTOType) =>
  crmDriverV1.post("/conversations/external/leads", payload);

export const updateLeadNote = (payload: { leadId: string; note: string }) =>
  crmDriverV1.put(`/conversations/external/leads/${payload.leadId}`, {
    ...payload,
    leadId: undefined,
  });

export const deleteLead = (leadId: string) =>
  crmDriverV1.delete(`/conversations/external/leads/${leadId}`);
