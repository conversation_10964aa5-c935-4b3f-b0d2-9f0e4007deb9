import dayjs from "dayjs";
import localeData from "dayjs/plugin/localeData";
import weekday from "dayjs/plugin/weekday";
import { createRoot } from "react-dom/client";
import { Provider } from "react-redux";
import { <PERSON><PERSON>erRouter } from "react-router";
import { PersistGate } from "redux-persist/integration/react";

import ErrorBoundary from "components/atoms/error-boundary";
import { Toastify } from "components/atoms/toastify";
import { DialogServiceProvider, UseConfirm, UseAlert } from "contexts/dialog";
import { AppNavigationProvider } from "contexts/navigation";
import { AppInitializer } from "helpers/appinitializer";
import App from "pages/app";

import reportWebVitals from "./reportWebVitals";
import store, { persistor } from "./store";

import "./index.scss";

dayjs.extend(weekday);
dayjs.extend(localeData);

const container = document.getElementById("root");
const root = createRoot(container!);
root.render(
  <Provider store={store}>
    <PersistGate loading={null} persistor={persistor}>
      <BrowserRouter>
        <AppNavigationProvider>
          <DialogServiceProvider>
            <AppInitializer />
            <UseConfirm />
            <UseAlert />
            <ErrorBoundary>
              <App />
            </ErrorBoundary>
          </DialogServiceProvider>
        </AppNavigationProvider>
      </BrowserRouter>
    </PersistGate>
    <Toastify />
  </Provider>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
