/* eslint-disable @typescript-eslint/no-array-constructor */
/* eslint-disable no-array-constructor */
import { Model, String, Number, Boolean, Array, ModelValue } from "libs/domain";

export const OrderStatusSchema = {
  _id: String(),
  name: String(),
  orderStatusId: Number(),
  isDefault: Boolean({ defaultValue: false }),
  displayOrder: Number(),
  prevOrderStatusIds: Array(Number(), { defaultValue: [] }),
  color: String(),
  description: String(),
  updatedAt: String(),
  code: String(),
  orderStatusTaglines: Array(Number(), { defaultValue: [] }),
};

export const OrderStatusModel = new Model(OrderStatusSchema);
export type OrderStatusEntityType = ModelValue<typeof OrderStatusModel>;
