import { lazy } from "react";

import PATHS from "constants/paths";
import { createAppPage } from "libs/react";

import { ChildrenPage } from "./types";

export default createAppPage<{}, ChildrenPage>({
  name: "ConfigureSystem",
  path: PATHS.CONFIGURE_SYSTEM,
  childrenPages: {
    configuringPointsExchange: createAppPage({
      name: "ConfiguringPointsExchange",
      path: "/cau-hinh-diem-thanh-vien",
      page: () => lazy(() => import("./ConfiguringPointsExchange")),
    }),
    manageFacebook: createAppPage({
      name: "ManageFacebook",
      path: "/quan-ly-trang-facebook",
      page: () => lazy(() => import("./ManageFacbook")),
    }),
  },
});
