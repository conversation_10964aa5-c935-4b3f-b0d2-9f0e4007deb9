import { useCallback } from "react";

import useDidMount from "helpers/react-hooks/useDidMount";
import { useAsync } from "hooks/useAsync";
import { orderPaymentDetailDto } from "modules/order";
import { getOrderPaymentById } from "services/crm/order";

interface PaymentTransactionDetailVmProps {
  paymentId: number;
}

export const PaymentTransactionDetailVm = ({
  paymentId,
}: PaymentTransactionDetailVmProps) => {
  const [getOrderPaymentByIdExec, getOrderPaymentByIdState] = useAsync(
    useCallback(
      (payload: { paymentId: number }) =>
        getOrderPaymentById({ ...payload }).then((res) =>
          orderPaymentDetailDto(res.data.data)
        ),
      []
    )
  );

  useDidMount(() => getOrderPaymentByIdExec({ paymentId }));

  return {
    paymentData: getOrderPaymentByIdState.data,
    loading: getOrderPaymentByIdState.loading,
  };
};
