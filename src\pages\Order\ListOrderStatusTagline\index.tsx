import React, { use<PERSON>allback, useRef } from "react";

import { LeftOutlined, SearchOutlined } from "@ant-design/icons";
import { Flex, Input } from "antd";
import Title from "antd/es/typography/Title";
import { useParams } from "react-router";
import { ValueType } from "react-select";

import imgSearchBlue from "assets/images/icons/search-blue.svg";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Pulldown } from "components/atoms/pulldown";
import { Tag } from "components/atoms/tag";
import { Textfield } from "components/atoms/textfield";
import {
  Pagination,
  PaginationReference,
} from "components/molecules/pagination";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Table, Tbody, Td, Th, Thead, Tr } from "components/organisms/table";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import { BasePageProps } from "helpers/component";
import dayjs from "helpers/dayjs";
import { PageParamsType } from "libs/react";
import { CreateOrderStatusTaglineModal } from "modules/order";
import PaginationSection from "pages/Common/paginationSection";
import TableManipulation from "pages/Common/tableManipulation";

import RootPageRouter from "..";
import { ChildrenPage } from "../types";
import {
  inputValidationSchema,
  CreateOrderStatusTaglineFormPayload,
} from "./constant";
import { OrderStatusTaglineListPageVm } from "./vm";

const IndexPage: React.FC<BasePageProps> = () => {
  const { orderStatusId, orderStatusCode } =
    useParams<PageParamsType<ChildrenPage["listOrderStatusTagline"]>>();

  const {
    getOrderStatusTaglineListLoading,
    gotoPage,
    pageSize,
    orderStatusTaglineListData,
    orderStatusTaglineListPaginationState,
    handleChangePageSize,
    toggleSortOrderBy,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    orderStatus,
    createOrderStatusTagline,
    onChangeColor,
    colorSelected,
    handleSearchInputChange,
    handleClearSearch,
  } = OrderStatusTaglineListPageVm({
    orderStatusId: Number(orderStatusId),
    orderStatusCode,
  });

  const paginationRef = useRef<PaginationReference>(null);

  const handleOnChangePageSizePulldown = useCallback(
    (option: ValueType<{ label: string; value: string }, false>) => {
      if (option?.value) {
        handleChangePageSize(Number(option?.value));
        if (paginationRef.current) paginationRef.current.reset();
      }
    },
    [handleChangePageSize]
  );

  const handleSubmitCreateStatusTagline = useCallback(
    async (formData: CreateOrderStatusTaglineFormPayload) => {
      const { orderStatus: _orderStatus, ...rest } = formData;
      await createOrderStatusTagline({
        ...rest,
        color: colorSelected,
        orderStatusId: Number(_orderStatus?.value),
      });
    },
    [colorSelected, createOrderStatusTagline]
  );

  return (
    <General>
      <title key="title">Danh sách Tagline trạng thái đơn hàng</title>
      <Section>
        <Flex className="p-4" align="center" justify="start" gap={16}>
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            icon={<LeftOutlined />}
            onClick={() => {
              RootPageRouter.gotoChild("listOrderStatus");
            }}
          />
          <Title level={4} className="mb-0">
            DANH SÁCH TAGLINE TRẠNG THÁI ĐƠN HÀNG
          </Title>
        </Flex>

        <Section>
          <Row className="d-flex">
            <Col
              className="ml-auto"
              xs={{ span: 12, order: 2 }}
              lg={{ span: 6, order: 1 }}
            >
              <Input
                allowClear
                className="h-10"
                prefix={<SearchOutlined />}
                onChange={(e) => {
                  handleSearchInputChange(e.target.value);
                }}
                onClear={handleClearSearch}
                placeholder="Tìm kiếm"
              />
            </Col>
            <Col
              className="d-flex justify-content-end u-mb-15 u-mb-lg-0"
              xs={{ span: 12, order: 1 }}
              lg={{ span: 6, order: 2 }}
            >
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                className="h-10"
                onClick={() => handleOpenModalByType("createStatusTagline")}
              >
                Tạo mới
              </BaseButton>
            </Col>
          </Row>
        </Section>

        <Section>
          <Table
            modofiers="borderdotted"
            scroll={{ x: 1500 }}
            loading={getOrderStatusTaglineListLoading}
            hasData={orderStatusTaglineListData.length > 0}
          >
            <Thead>
              <Tr>
                <Th colSpan={1} modifiers="center" stickyLeft>
                  STT
                </Th>
                <Th colSpan={3} modifiers="center">
                  Tên trạng thái
                </Th>
                <Th
                  colSpan={4}
                  modifiers="center"
                  isSortable
                  onSort={() => toggleSortOrderBy("name")}
                >
                  Tên tagline
                </Th>
                <Th colSpan={2} modifiers="center">
                  Màu sắc
                </Th>
                <Th colSpan={2} modifiers="center">
                  Mô tả
                </Th>
                <Th colSpan={2} modifiers="center">
                  Thứ tự hiển thị
                </Th>
                <Th
                  colSpan={3}
                  modifiers="center"
                  isSortable
                  onSort={() => toggleSortOrderBy("updatedAt")}
                >
                  Cập nhật cuối
                </Th>
                <Th colSpan={3} modifiers="center" stickyRight>
                  Thao tác
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {orderStatusTaglineListData.map((orderStatusTagline, index) => (
                <Tr key={orderStatusTagline.taglineId}>
                  <Td colSpan={1} modifiers="center" stickyLeft>
                    {index + 1}
                  </Td>
                  <Td colSpan={3} modifiers="center">
                    {orderStatusTagline.orderStatus?.name}
                  </Td>
                  <Td colSpan={4} modifiers="center">
                    {orderStatusTagline.name}
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    <Tag color={orderStatusTagline.color} />
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    {orderStatusTagline.description}
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    {orderStatusTagline.displayOrder}
                  </Td>
                  <Td colSpan={3} modifiers="center">
                    {dayjs(orderStatusTagline.updatedAt).format(
                      "DD/MM/YYYY HH:mm"
                    )}
                  </Td>
                  <Td colSpan={3} modifiers="center" stickyRight>
                    <TableManipulation
                      infoAction={{
                        id: `${orderStatusTagline.taglineId}info`,
                        action: () =>
                          RootPageRouter.gotoChild("orderStatusTaglineDetail", {
                            params: {
                              taglineId:
                                orderStatusTagline.taglineId.toString(),
                              orderStatusId,
                            },
                          }),
                      }}
                      editAction={{
                        id: `${orderStatusTagline.taglineId}edit`,
                        action: () =>
                          RootPageRouter.gotoChild("orderStatusTaglineDetail", {
                            params: {
                              taglineId:
                                orderStatusTagline.taglineId.toString(),
                              orderStatusId,
                            },
                            queryString: "?action=edit",
                          }),
                      }}
                      deleteAction={{
                        id: `${index}delete`,
                      }}
                    />
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Section>
        <Section>
          <PaginationSection
            appearanceOption={
              <Pulldown
                placeholder="Số lượng hiển thị"
                value={{ label: `${pageSize}`, value: `${pageSize}` }}
                options={[5, 10, 15, 25, 30].map((size) => ({
                  label: `${size}`,
                  value: `${size}`,
                }))}
                onChange={handleOnChangePageSizePulldown}
              />
            }
            paginateOption={
              orderStatusTaglineListPaginationState.totalPage && (
                <Pagination
                  modifiers="center"
                  total={orderStatusTaglineListPaginationState.totalPage}
                  pageCount={5}
                  defaultCurrentPage={1}
                  onPageChange={gotoPage}
                  ref={paginationRef}
                />
              )
            }
          />
        </Section>
      </Section>
      <CreateOrderStatusTaglineModal
        open={modalTypeIsOpen("createStatusTagline")}
        onClose={handleCloseModal}
        inputValidationSchema={inputValidationSchema}
        onChangeColor={onChangeColor}
        orderStatus={orderStatus}
        handleSubmitCreateStatusTagline={handleSubmitCreateStatusTagline}
      />
    </General>
  );
};
export default IndexPage;
