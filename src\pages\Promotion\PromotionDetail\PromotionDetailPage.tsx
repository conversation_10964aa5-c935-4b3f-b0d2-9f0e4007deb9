import React from "react";
import {
  Checkbox,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Typography,
} from "antd";
import { useParams, useSearchParams } from "react-router";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";

const { Title } = Typography;

type FormValuesType = {
  promotionCode: string;
  promotionName: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
};

export default function PromotionDetailPage() {
  const { promotionId } = useParams<{ promotionId: string }>();
  const [searchParams] = useSearchParams();
  const pageActionType = searchParams.get("action") || "view";
  const isDisabled = pageActionType === "view";
  const [form] = Form.useForm<FormValuesType>();

  const onFinish = (values: FormValuesType) => {
    console.log(values, "Form Values");
  };

  return (
    <div className="p-6 m-8 bg-white flex flex-col rounded-xl">
      <Title level={4}>Thông tin chương trình khuyến mãi</Title>
      <Form form={form} onFinish={onFinish} layout="vertical">
        <div className="grid grid-cols-1 max-w-full lg:grid-cols-4 mx-auto gap-4 items-center">
          <Form.Item
            label="Mã khuyến mãi"
            name="promotionCode"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập mã khuyến mãi",
              },
            ]}
          >
            <Input placeholder="Nhập mã khuyến mãi" />
          </Form.Item>
          <Form.Item
            label="Tên khuyến mãi"
            name="promotionName"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập tên khuyến mãi",
              },
            ]}
          >
            <Input placeholder="Nhập tên khuyến mãi" />
          </Form.Item>
          <div className="grid grid-cols-1 lg:grid-cols-2 col-span-1 gap-4 items-center">
            <Form.Item
              label="Ngày bắt đầu"
              name="startDate"
              rules={[
                {
                  message: "Vui lòng chọn ngày bắt đầu",
                  required: true,
                },
              ]}
            >
              <DatePicker
                format="DD/MM/YYYY"
                className="w-full"
                placeholder="Chọn ngày bắt đầu"
              />
            </Form.Item>
            <Form.Item
              label="Ngày kết thúc"
              name="endDate"
              rules={[
                {
                  message: "Vui lòng chọn ngày kết thúc",
                  required: true,
                },
              ]}
            >
              <DatePicker
                format="DD/MM/YYYY"
                className="w-full"
                placeholder="Chọn ngày kết thúc"
              />
            </Form.Item>
          </div>

          <div className="col-span-1 grid grid-cols-1 lg:grid-cols-2 gap-4 items-center">
            <Form.Item label="Giá trị khuyến mãi" name="promotionValue">
              <InputNumber
                className="w-full"
                placeholder="Nhập giá trị khuyến mãi"
              />
            </Form.Item>

            <Form.Item label="Loại khuyến mãi" name="promotionType">
              <BaseSelect
                placeholder="Chọn loại khuyến mãi"
                options={[
                  {
                    label: "Theo phần trăm (%)",
                    value: "percent",
                  },
                  {
                    label: "Theo số tiền (VND)",
                    value: "amount",
                  },
                ]}
              />
            </Form.Item>
          </div>

          <Form.Item name="description" label="Mô tả">
            <Input.TextArea rows={3} placeholder="Nhập mô tả" allowClear />
          </Form.Item>
          <Form.Item noStyle name="isActive" valuePropName="checked">
            <Checkbox className="w-fit translate-y-[50%]">Kích hoạt</Checkbox>
          </Form.Item>
        </div>
      </Form>
    </div>
  );
}
