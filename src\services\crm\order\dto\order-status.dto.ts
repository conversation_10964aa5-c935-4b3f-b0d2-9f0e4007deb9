/**
 * @Module ORDER_STATUS
 * @description DTOs for Order Status operations
 */

export interface GetOrderStatusDto {
  searchText: string;
}

export interface GetOrderStatusWithPaginationDto extends GetOrderStatusDto {
  pageNum: number;
  pageSize: number;
}

export interface GetOrderStatusWithoutPaginationResponseDto {
  data: OrderStatusDto[];
}

export interface OrderStatusDto {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  prevOrderStatusIds: number[];
  orderStatusId: number;
  name: string;
  description: string;
  isDefault: boolean;
  displayOrder: number;
  isOriginal?: boolean;
  code: string;
  color: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string | null;
}

/**
 * @Module ORDER_STATUS_TAGLINE
 * @description DTOs for Order Status Tagline operations
 */

export interface GetOrderStatusTaglineDto {
  orderStatusId: number;
}

export interface GetOrderStatusTagLineWithoutPaginationDto
  extends GetOrderStatusTaglineDto {}

export interface GetOrderStatusTagLineWithoutPaginationResponseDto {
  data: OrderStatusTaglineDto[];
}

export interface GetDetailOrderStatusTaglineDto
  extends GetOrderStatusTaglineDto {
  orderStatusTaglineId: number;
}

export interface GetDetailOrderStatusTaglineResponseDto {
  data: OrderStatusTaglineDto;
}

export interface OrderStatusTaglineDto {
  taglineId: number;
  orderStatusId: number;
  name: string;
  description: string;
  displayOrder: number;
  color: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  orderStatus: OrderStatusDto;
}
