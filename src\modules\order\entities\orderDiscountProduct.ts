import { Model, Array, ExtendSchema } from "libs/domain";

import { OrderFreeProductSchema } from "./orderFreeProduct";
import { OrderSamePriceProductSchema } from "./orderSamePriceProduct";

export const OrderDiscountProductSchema = {
  freeProducts: Array(ExtendSchema(OrderFreeProductSchema)),
  samePriceProducts: Array(ExtendSchema(OrderSamePriceProductSchema)),
};

export const OrderDiscountProductModel = new Model(OrderDiscountProductSchema);
