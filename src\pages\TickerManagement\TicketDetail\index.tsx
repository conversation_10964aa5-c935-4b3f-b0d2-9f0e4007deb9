/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useState } from "react";
import { LeftOutlined, SaveFilled } from "@ant-design/icons";
import { Col, DatePicker, Form, Input, Row, Slider, Spin } from "antd";
import TextArea from "antd/es/input/TextArea";
import dayjs from "dayjs";
import { use } from "i18next";
import { debounce } from "lodash";
import { useParams, useSearchParams } from "react-router";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { BaseSelectWithFeatures } from "components/atoms/base/select/BaseSelectWithFeatures";
import { showLoading } from "components/atoms/base/Spinner";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import { goBack } from "helpers/navigation";
import { PageParamsType } from "libs/react";
import { usePulldownAssignEmployee } from "modules/employee";
import { useInfinityMasterDataTicket } from "modules/ticket/hook/useGetListMasterDataTicket";
import { TicketDto } from "../ListTicket/dto/ticket.dto";
import {
  useCreateTicket,
  useGetTicketById,
  useUpdateTicket,
} from "../ListTicket/hooks/ticket.hooks";
import { TicketDataType } from "../ListTicket/ListTicketPageV2";
import { useInfinityStatusTicket } from "../StatusTicket/hooks/useGetStatusTicket";
import { ChildrenPage } from "../types";

interface FormType extends Partial<Omit<TicketDto, "employeeId">> {
  employeeId?: string;
}

export default function TicketDetail() {
  const [form] = Form.useForm<FormType>();
  const [statusTicketInput, setStatusTicketInput] = useState<string>("");
  const [priorityTicketInput, setPriorityTicketInput] = useState<string>("");
  const { ticketId } =
    useParams<PageParamsType<ChildrenPage["ticketDetail"]>>();
  const { updateTicketExe } = useUpdateTicket();
  const { createTicketExe } = useCreateTicket();
  const { fetchData, ticketData, loading } = useGetTicketById();
  const isAddNew = ticketId === "add-new";
  const [searchParams] = useSearchParams();
  const isView = searchParams.get("action") === "view";

  // const isView = searchParams.get("action") === "view";

  const {
    data: priorityOptions,
    hasMore: priorityHasMore,
    loadMore: loadMorePriority,
    loading: priorityLoading,
    reset: resetPriority,
    totalRecords: priorityTotalRecords,
  } = useInfinityMasterDataTicket({
    searchText: priorityTicketInput,
  });

  const {
    data: statusOptions,
    hasMore: statusHasMore,
    loadMore: loadMoreStatus,
    loading: statusLoading,
    reset: resetStatus,
    totalRecords: statusTotalRecords,
  } = useInfinityStatusTicket({
    searchText: statusTicketInput,
  });
  const isDateLike = (value: any) => {
    return value instanceof Date;
  };
  const convertTicketDtoToFormType = (data: TicketDataType) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const result: any = {};
    Object.entries(data).forEach(([key, value]) => {
      if (isDateLike(value)) {
        result[key] = value ? dayjs(value) : undefined;
      } else {
        result[key] = value;
      }
    });
    return result;
  };

  const handleResetPriority = () => {
    resetPriority();
    setPriorityTicketInput("");
  };

  const handleResetStatus = () => {
    resetStatus();
    setStatusTicketInput("");
  };

  const {
    loadMoreEmployee,
    assignEmployeeOptions,
    loadMoreEmployeeState,
    formatAssignEmployeeOption,
  } = usePulldownAssignEmployee();

  const onAssignEmployeePulldownInputChange = useCallback(
    debounce(
      (textSearch: string) => loadMoreEmployee({ name: textSearch }),
      200
    ),
    [loadMoreEmployee]
  );

  const onFinish = (values: any) => {
    // Handle form submission logic here
    const payload = {
      ...values,
      endDate: values.endDate.toISOString(),
      reminderAt: values.reminderAt
        ? values.reminderAt.toISOString()
        : undefined,
    };
    showLoading(true);
    if (values.ticketId) {
      updateTicketExe({ ...payload, ticketId: values.ticketId })
        .then(() => {
          showNotification({
            type: "success",
            message: "Cập nhật ticket thành công",
          });
        })
        .catch((err) => {
          const apiError = err?.response?.data?.errors;
          if (apiError && apiError.length > 0) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            apiError.forEach((e: any) => {
              showNotification({
                type: "error",
                message: e.title,
              });
            });
          }
        })
        .finally(() => {
          showLoading(false);
        });
      return;
    }

    createTicketExe(payload)
      .then(() => {
        showNotification({
          type: "success",
          message: "Tạo ticket thành công",
        });
      })
      .catch((err) => {
        const apiError = err?.response?.data?.errors;
        if (apiError && apiError.length > 0) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          apiError.forEach((e: any) => {
            showNotification({
              type: "error",
              message: e.title,
            });
          });
        }
      })
      .finally(() => {
        showLoading(false);
      });
  };

  useEffect(() => {
    loadMoreEmployee();
  }, []);

  useEffect(() => {
    if (!isAddNew) {
      fetchData(Number(ticketId));
    }
  }, []);

  useEffect(() => {
    if (ticketData) {
      const {
        title,
        employeeId,
        endDate,
        note,
        priority,
        priorityId,
        ticketId: idTicket,
        status,
        statusId,
        progress,
        reminderAt,
      } = ticketData.data ?? {};

      form.setFieldsValue({
        title,
        ticketId: idTicket,
        priorityId,
        statusId,
        employeeId: String(employeeId),
        endDate: endDate ? dayjs(endDate) : null,
        note,
        reminderAt: reminderAt ? dayjs(reminderAt) : null,
        progress: progress ?? 0,
      });
    }
  }, [ticketData, form]);
  return (
    <General>
      <Row justify="start" gutter={[8, 8]} className="p-3 m-0">
        <Col>
          <BaseButton
            type="primary"
            className="w-fit"
            // onClick={navigationHelper.goBack}
            icon={<LeftOutlined />}
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[600]}
            onClick={() => {
              goBack();
            }}
          />
        </Col>
        <Col>
          {!isView && (
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              // disabled={updateShopDetailState.loading}
              // loading={updateShopDetailState.loading}
              icon={<SaveFilled />}
              onClick={() => form.submit()}
            >
              Lưu
            </BaseButton>
          )}
        </Col>
      </Row>
      <Form
        // disabled={isViewRef.current}
        disabled={isView}
        form={form}
        layout="vertical"
        onFinish={onFinish}
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-center px-5">
          <Form.Item name="ticketId" noStyle hidden>
            <Input />
          </Form.Item>

          <div className="lg:col-span-2">
            <Form.Item
              label="Tiêu đề"
              name="title"
              rules={[
                {
                  required: true,
                  message: "Vui lòng nhập tiêu đề!",
                },
              ]}
            >
              <Input placeholder="Nhập tiêu đề" />
            </Form.Item>
          </div>

          <Form.Item
            label="Mức độ ưu tiên"
            name="priorityId"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn mức độ ưu tiên",
              },
            ]}
          >
            <BaseSelect
              key="priorityIds"
              allowClear
              showSearch
              filterOption={false}
              onSearch={(value) => {
                setPriorityTicketInput(value);
              }}
              onClear={() => {
                handleResetPriority();
              }}
              onBlur={() => {
                handleResetPriority();
              }}
              onSelect={(value, option) => {
                setPriorityTicketInput("");
              }}
              fieldNames={{
                label: "name",
                value: "masterDataTicketId",
              }}
              placeholder="Chọn độ ưu tiên"
              onPopupScroll={(event) => {
                const target = event.target as HTMLElement;
                if (
                  target.scrollTop + target.clientHeight ===
                  target.scrollHeight
                ) {
                  if (statusHasMore && !statusLoading) {
                    loadMorePriority();
                  }
                }
              }}
              popupRender={(menu) => (
                <>
                  {menu}
                  <div
                    style={{
                      textAlign: "center",
                      padding: "8px 0",
                    }}
                  >
                    {priorityLoading ? (
                      <Spin size="small" />
                    ) : !priorityHasMore && priorityOptions.length > 0 ? (
                      <div className="w-full">
                        <span style={{ color: "#999" }}>
                          Đã tải hết dữ liệu
                        </span>
                      </div>
                    ) : null}
                  </div>
                </>
              )}
              options={priorityOptions}
            />
          </Form.Item>

          <Form.Item
            label="Trạng thái"
            name="statusId"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn trạng thái",
              },
            ]}
          >
            <BaseSelect
              key="statusIds"
              allowClear
              showSearch
              filterOption={false}
              onSearch={(value) => {
                setStatusTicketInput(value);
              }}
              onClear={() => {
                handleResetStatus();
              }}
              onBlur={() => {
                handleResetStatus();
              }}
              onSelect={(value, option) => {
                setStatusTicketInput("");
              }}
              fieldNames={{
                label: "name",
                value: "statusTicketId",
              }}
              placeholder="Chọn trạng thái"
              onPopupScroll={(event) => {
                const target = event.target as HTMLElement;
                if (
                  target.scrollTop + target.clientHeight ===
                  target.scrollHeight
                ) {
                  if (statusHasMore && !statusLoading) {
                    loadMoreStatus();
                  }
                }
              }}
              popupRender={(menu) => (
                <>
                  {menu}
                  <div
                    style={{
                      textAlign: "center",
                      padding: "8px 0",
                    }}
                  >
                    {statusLoading ? (
                      <Spin size="small" />
                    ) : !statusHasMore && statusOptions.length > 0 ? (
                      <div className="w-full">
                        <span style={{ color: "#999" }}>
                          Đã tải hết dữ liệu
                        </span>
                      </div>
                    ) : null}
                  </div>
                </>
              )}
              options={statusOptions}
            />
          </Form.Item>

          <Form.Item
            label="Phân công"
            name="employeeId"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn nhân viên",
              },
            ]}
          >
            <BaseSelectWithFeatures
              placeholder="Chọn nhân viên"
              onSearch={(value) => {
                onAssignEmployeePulldownInputChange(value);
              }}
              triggerLoadMore={async ({ searchInputValue }) => {
                await loadMoreEmployee({ name: searchInputValue });
              }}
              isLoading={loadMoreEmployeeState.loading}
              options={assignEmployeeOptions}
            />
          </Form.Item>

          <Form.Item
            label="Ngày kết thúc"
            name="endDate"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn ngày kết thúc!",
              },
            ]}
          >
            <DatePicker
              className="w-full"
              placeholder="Chọn ngày kết thúc"
              format="DD/MM/YYYY"
            />
          </Form.Item>
          <Form.Item label="Hẹn lịch ngày nhắc" name="reminderAt">
            <DatePicker
              showTime={{ format: "HH:mm" }}
              format="DD/MM/YYYY HH:mm"
              className="w-full"
            />
          </Form.Item>
          <Col span={23}>
            <Form.Item label="Tiến độ" name="progress">
              <Slider
                min={0}
                max={100}
                tooltip={{
                  formatter(value) {
                    return `${value}%`;
                  },
                }}
                className="w-full"
              />
            </Form.Item>
          </Col>
          <div className="lg:col-span-2">
            <Form.Item label="Nội dung" name="note">
              <TextArea placeholder="Nhập nội dung" rows={4} allowClear />
            </Form.Item>
          </div>
          {/* <Form.Item label="Ngày tạo" name="createdAt">
            <DatePicker
              showTime={{ format: "HH:mm" }}
              format="DD/MM/YYYY HH:mm"
              className="w-full"
            />
          </Form.Item> */}

          {/* <Col>
            <Form.Item label="Trạng thái" name="createdBy">
              <BaseSelect
                className="w-full"
                placeholder="Chọn trạng thái"
                options={[
                  { label: "Mới", value: "new" },
                  { label: "Đang xử lý", value: "in_progress" },
                  { label: "Đã hoàn thành", value: "completed" },
                  { label: "Đã hủy", value: "cancelled" },
                ]}
              />
            </Form.Item>
          </Col> */}
        </div>
      </Form>
    </General>
  );
}
