import {
  <PERSON>rray,
  <PERSON>olean,
  Enum,
  ExtendSchema,
  Model,
  ModelValue,
  Number,
  String,
} from "libs/domain";

import {
  CreateOrderItemEntity,
  CreateOrderItemSchema,
} from "./createOrderItem";
import { OrderDiscountProductSchema } from "./orderDiscountProduct";
import { OrderPaymentSchema } from "./orderPayment";
import { OrderPersonalInfoSchema } from "./orderPersonalInfo";

export const OrderItemSchema = {
  _id: String(),
  code: String(),
  orderId: Number(),

  customerId: Number(),
  name: String(),
  email: String(),
  phoneNumber: String(),
  birthDay: String(),
  gender: String(),
  sex: String(),
  customerHeight: Number(),
  customerWeight: Number(),
  customerWaist: Number(),

  cityId: Number(),
  districtId: Number(),
  wardId: Number(),
  shippingAddressDetail: String(),

  voucherCodes: Array(String),
  voucherId: Number(),
  totalAmount: Number(),
  requestSupport: Boolean({ defaultValue: false }),
  orderNote: String(),
  deliveryNote: String(),
  paymentMethod: Enum({ values: [0, 1, 2, 3] }),
  // personalInfo: OrderPersonalInfoSchema,
  items: Array(ExtendSchema(CreateOrderItemSchema)),
  discountProducts: OrderDiscountProductSchema,
  orderStatusId: Number(),
  orderStatusTaglineId: Number(),
  returnOrderStatusId: Number(),
  returnOrderStatusTaglineId: Number(),
  payment: OrderPaymentSchema,
  weight: Number(),
  // size: Number(),
  apiNote: String(),
  shippingCost: Number({ defaultValue: 0 }),
  applicableFee: Number({ defaultValue: 0 }),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  createdAt: (raw: any) => raw && new Date(raw),
  orderType: Number(),
  channel: String(),
  shipping: Number(),
  cskhNote: String(),
  isShopSent: Boolean(),
  zipcode: String(),
  assignedEmployeeId: Number(),
  orderSourceId: Number(),
  boxCode: String(),
  discount: Number(),
  discountValue: Number(),
  discountType: String(),
  pay: Number(),
  remainingAmount: Number(),
  paid: Number(),
  shopSent: Number(),
  shopSentNote: String(),
  promotionValue: Number(),
  applyreferralcode: String(),
  applyreferralValue: Number(),

  totalApplyPoint: Number(),
  totalApplyPointValue: Number(),

  cancelReason: String(),

  // DANH SÁCH NGÀY //
  // confirmDate: (raw: string) => (raw ? new Date(raw) : null),
  // deliveryDate: (raw: string) => (raw ? new Date(raw) : null),
  // cancelDate: (raw: string) => (raw ? new Date(raw) : null),
  // cancelReason: String(),
  // completeDate: (raw: string) => (raw ? new Date(raw) : null),
  // returnDate: (raw: string) => (raw ? new Date(raw) : null),
};

export const OrderItemModel = new Model(OrderItemSchema);

export type OrderItemEntityType = ModelValue<typeof OrderItemModel>;

// export const calcTotalAmount = (
//   payload: {
//     items: CreateOrderItemEntity[];
//     shippingCost: number;
//     applicableFee: number;
//   } = {
//     items: [],
//     shippingCost: 0,
//     applicableFee: 0,
//   }
// ) =>
//   payload.items.reduce(
//     (amount, { quantity, unitPrice }) => amount + quantity * unitPrice,
//     payload.shippingCost + payload.applicableFee
//   );

// export const calcTotalAmount = (
//   payload: {
//     items: CreateOrderItemEntity[];
//     shippingCost: number;
//     applicableFee: number;
//   } = {
//     items: [],
//     shippingCost: 0,
//     applicableFee: 0,
//   }
// ) =>
//   payload.items.reduce(
//     (amount, { quantity, unitPrice, discountAmount }) =>
//       amount + quantity * unitPrice - (discountAmount * quantity || 0),
//     payload.shippingCost + payload.applicableFee
//   );

export const calcTotalAmount = (
  payload: {
    items: CreateOrderItemEntity[];
  } = {
    items: [],
  }
) =>
  payload.items.reduce(
    (amount, { quantity, unitPrice, discountAmount }) =>
      amount + quantity * unitPrice - (discountAmount * quantity || 0),
    0
  );
