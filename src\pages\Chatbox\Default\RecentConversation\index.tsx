import imgSearchBlue from "assets/images/icons/search-blue.svg";
import { Textfield } from "components/atoms/textfield";
import { RecentConversation as RecentConversationItem } from "components/molecules/recentconversation";
import { RecentConversation } from "components/organisms/recentconversation";

import {
  useChatboxPageContext,
  useRecentConversation,
  useSelectedConversation,
} from "../hook";

const RecentConversationSection = () => {
  const { selectConversationById } = useChatboxPageContext();
  const selectedConversation = useSelectedConversation();
  const { recentConversationStack } = useRecentConversation();

  return (
    <div>
      <Textfield iconSrc={imgSearchBlue} placeholder="Tìm kiếm" />
      <RecentConversation
        conversations={recentConversationStack}
        overscanRowCount={20}
        onConversationRender={({ client, latestMessage, conversationId }) => (
          <RecentConversationItem
            avatar={client?.profilePic}
            name={client.name}
            alt={client.name}
            message={latestMessage}
            onSelect={() => selectConversationById({ id: conversationId })}
            modifiers={
              selectedConversation?.conversationId === conversationId
                ? "selected"
                : undefined
            }
          />
        )}
      />
    </div>
  );
};

export default RecentConversationSection;
