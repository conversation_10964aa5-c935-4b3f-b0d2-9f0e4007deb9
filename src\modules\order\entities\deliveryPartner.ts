import { Model, Number, String, ModelValue } from "libs/domain";

export const DeliveryPartnerSchema = {
  _id: String(),
  deliveryPartnerId: Number(),
  name: String(),
  code: String(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => new Date(raw),
};

export const DeliveryPartnerModel = new Model(DeliveryPartnerSchema);

export type DeliveryPartnerEntityType = ModelValue<typeof DeliveryPartnerModel>;
