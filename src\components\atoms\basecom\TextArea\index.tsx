/* eslint-disable react/destructuring-assignment */
import {
  useRef,
  DetailedHTMLProps,
  TextareaHTMLAttributes,
  useEffect,
} from "react";

import style from "./index.module.scss";

type TextAreaProp = {
  send?: (text: string) => void;
} & DetailedHTMLProps<
  TextareaHTMLAttributes<HTMLTextAreaElement>,
  HTMLTextAreaElement
>;

export const TextArea = (prop: TextAreaProp) => {
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const areaProp = { ...prop };
  delete areaProp.send;

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.altKey === false && e.key === "Enter") {
        e.preventDefault();
        if (prop.send) prop.send((e.target as HTMLTextAreaElement).value);
        (e.target as HTMLTextAreaElement).value = "";
      }
      if (e.altKey === true && e.key === "Enter") {
        e.preventDefault();
        (e.target as HTMLTextAreaElement).value += "\n";
        (e.target as HTMLTextAreaElement).scrollTop = 100;
      }
    };
    if (textAreaRef.current) {
      textAreaRef.current.addEventListener("keydown", handleKeyDown);
    }
    return () => {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      textAreaRef.current?.removeEventListener("keydown", handleKeyDown);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [prop.send]);

  return (
    <textarea
      {...areaProp}
      ref={textAreaRef}
      className={
        prop.className ? `${style.textarea} ${prop.className}` : style.textarea
      }
    />
  );
};
