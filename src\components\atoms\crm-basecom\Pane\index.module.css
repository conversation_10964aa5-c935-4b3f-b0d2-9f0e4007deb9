.pane {
	box-shadow: #9f9f9f 0px 0px 3px;
	background-color: white;
	border-radius: 0.5em 0.5em 0 0;
	padding-bottom: 1em;
	transition: box-shadow 0.1s linear;
}

.pane:hover {
	box-shadow: #9f9f9f 0px 0px 4px 1px;
}

.pane+.pane {
	margin-top: 15px;
}

.header_pane {
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: center;
	padding: 0.5em 0 0.5em 2em;
	background-color: #1c86c8;
	color: white;
	border-radius: 0.5em 0.5em 0 0;
	transition: all 0.1s linear;
}

.header_pane::before {
	content: "";
	position: absolute;
	left: 30%;
	bottom: 0;
	width: 40%;
	transform: translateY(2px);
	border-bottom: 2px solid rgba(155, 155, 155, 0.5);
	transition: all 0.3s linear 0.1s;
}

.pane:hover .header_pane::before {
	left: 0;
	bottom: 1px;
	width: 100%;
	border-bottom: 1px solid rgb(104, 104, 104);
}

.header {
	font-size: 1.5em;
	font-weight: 500;
}

.pane:hover .header {
	font-weight: 600;
}

.describe {
	font-size: 1em;
	font-style: italic;
}
