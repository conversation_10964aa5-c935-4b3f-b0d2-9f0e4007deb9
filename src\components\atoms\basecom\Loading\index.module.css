.overlay {
	position: absolute;
	/* Sit on top of the page content */
	display: flex;
	justify-content: center;
	align-items: center;
	/* Hidden by default */
	width: 100%;
	/* Full width (cover the whole page) */
	height: 100%;
	/* Full height (cover the whole page) */
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: white;
	/* Black background with opacity */
	z-index: 2;
	/* Specify a stack order in case you're using a different order for other elements */
}

.lds_ring {
	display: inline-block;
	position: relative;
	width: 80px;
	height: 80px;
}

.lds_ring div {
	box-sizing: border-box;
	display: block;
	position: absolute;
	width: 64px;
	height: 64px;
	margin: 8px;
	border: 8px solid #fff;
	border-radius: 50%;
	animation: lds_ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
	border-color: #1c86c8 transparent transparent transparent;
}

.lds_ring div:nth-child(1) {
	animation-delay: -0.45s;
}

.lds_ring div:nth-child(2) {
	animation-delay: -0.3s;
}

.lds_ring div:nth-child(3) {
	animation-delay: -0.15s;
}

@keyframes lds_ring {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}