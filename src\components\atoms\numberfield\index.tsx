/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { forwardRef, useCallback } from "react";

import { mapModifiers } from "helpers/component";
import { ConnectForm, UseFormProps } from "helpers/form";

const ExceptionKeysNumberField = [
  "1",
  "2",
  "3",
  "4",
  "5",
  "6",
  "7",
  "8",
  "9",
  "0",
  "Backspace",
  "Enter",
];

export interface Props extends React.InputHTMLAttributes<HTMLInputElement> {
  errorMessage?: string;
}

export const Numberfield = forwardRef<HTMLInputElement, Props>(
  ({ disabled, errorMessage, onKeyDown, ...innerProps }, ref) => {
    const onChangeFactory = useCallback(
      (event) => {
        if (!ExceptionKeysNumberField.includes(event.key)) {
          event.preventDefault();
        }
        if (onKeyDown) {
          onKeyDown(event);
        }
      },
      [onKeyDown]
    );

    return (
      <div
        className={mapModifiers(
          "a-numberfield",
          disabled && "disabled",
          errorMessage && "error"
        )}
      >
        <input
          className="a-numberfield_input"
          {...innerProps}
          onKeyDown={onChangeFactory}
          type="number"
          ref={ref}
          min={0}
        />
        {errorMessage && (
          <div className="a-numberfield_errormessage">{errorMessage}</div>
        )}
      </div>
    );
  }
);

interface NumberfieldHookFormProps extends Props {
  name: string;
}

export const NumberfieldHookForm = (props: NumberfieldHookFormProps) => (
  <ConnectForm>
    {
      (({ register, errors }: UseFormProps) => (
        <Numberfield
          ref={register}
          errorMessage={
            errors[props.name] ? errors[props.name].message : undefined
          }
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...props}
        />
      )) as any
    }
  </ConnectForm>
);
