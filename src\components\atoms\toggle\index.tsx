import React, { forwardRef } from "react";

import { Text } from "components/atoms/text";

export interface Props extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: React.ReactNode;
}

export const Toggle = forwardRef<HTMLInputElement, Props>(
  ({ label, id, ...props }, ref) => (
    <label htmlFor={id} className="a-toggle">
      <input ref={ref} {...props} id={id} type="checkbox" />
      {label && <Text>{label}</Text>}
      <span />
    </label>
  )
);
