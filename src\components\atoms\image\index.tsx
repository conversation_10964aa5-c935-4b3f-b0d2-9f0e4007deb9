import React from "react";

import { mapModifiers } from "helpers/component";

type Modifier = "transparent" | "oversize" | "center" | "circled" | "fullwidth";

type Ratio = "16x9" | "1x1" | "2x1" | "3x2" | "3x4" | "4x3" | "file";

export interface Props {
  modifiers?: Modifier | Modifier[];
  src: string;
  alt: string;
  lazy?: boolean;
  aspectRatio?: Ratio;
}

export const Image: React.FC<Props> = ({
  src,
  alt,
  aspectRatio,
  modifiers,
  lazy,
}) => (
  <div
    className={mapModifiers(
      "a-image",
      aspectRatio,
      aspectRatio ? "fixed" : undefined,
      modifiers
    )}
  >
    <img
      className="a-image_img"
      loading={lazy ? "lazy" : undefined}
      src={src}
      alt={alt}
    />
  </div>
);

Image.defaultProps = {
  lazy: true,
};
