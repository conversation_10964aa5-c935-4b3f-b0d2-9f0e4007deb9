/* eslint-disable @typescript-eslint/no-explicit-any */
import { data } from "./data";
import Widget2 from "./widget2";

export const ChartTrend = () => {
  return (
    <div className="mt-4">
      <p className="text-xl">How are your active users trending over time?</p>
      <div className="flex mt-3">
        {data.map((i: any) => {
          return <Widget2 data={i} key={i.id} />;
        })}
      </div>
    </div>
  );
};
