import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef } from "react";

import {
  Col as ColAntd,
  ColorPicker as ColorPickerAntd,
  Form,
  Input,
  InputNumber,
  Row as RowAntd,
  Switch,
} from "antd";
import { Color } from "antd/es/color-picker";
import { useSearchParams, useParams } from "react-router";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { Heading } from "components/atoms/heading";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import { COLOR } from "constants/color";
import { BasePageProps } from "helpers/component";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";

import { ChildrenPage } from "../types";
import { OrderReturnStatusFormType } from "./constant";
import { ReturnStatusDetailPageVm } from "./vm";

const IndexPage: React.FC<BasePageProps> = () => {
  const defaultOrderStatusRef = useRef<HTMLInputElement | null>(null);
  const colorPickerRef = useRef<string>();
  const [searchUrlPath] = useSearchParams();
  const [form] = Form.useForm();
  const { returnStatusId } =
    useParams<PageParamsType<ChildrenPage["orderReturnStatusDetail"]>>();

  const pageActionType = searchUrlPath.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  const {
    returnOrderStatusDetail,
    loading,
    orderReturnStatusOptions,
    updateOrderReturnStatusState,
    handleUpdateOrderReturnStatus,
  } = ReturnStatusDetailPageVm({
    returnOrderStatusId: Number(returnStatusId),
  });

  const onSubmitOrderReturnStatusUpdate = useCallback(
    (formData: OrderReturnStatusFormType) => {
      form.validateFields();

      const color =
        typeof formData?.color === "string"
          ? formData.color
          : (formData.color as unknown as Color).toRgbString();

      handleUpdateOrderReturnStatus({
        ...formData,
        isDefault: formData.isDefault || false,
        color,
        displayOrder: Number(formData.displayOrder),
        prevReturnOrderStatusIds: formData.prevOrderReturnStatus?.map((item) =>
          Number(item.value)
        ),
      });
    },
    [handleUpdateOrderReturnStatus]
  );

  useEffect(() => {
    if (returnOrderStatusDetail) {
      form.setFieldsValue({
        name: returnOrderStatusDetail.name,
        color: returnOrderStatusDetail.color,
        displayOrder: returnOrderStatusDetail.displayOrder,
        prevOrderReturnStatus: orderReturnStatusOptions,
        description: returnOrderStatusDetail.description,
        isDefault: returnOrderStatusDetail.isDefault,
      });

      // Gán luôn giá trị vào ref nếu cần dùng submit thủ công
      colorPickerRef.current = returnOrderStatusDetail.color;
      if (defaultOrderStatusRef.current) {
        defaultOrderStatusRef.current.checked =
          returnOrderStatusDetail.isDefault ?? false;
      }
    }
  }, [returnOrderStatusDetail]);
  return (
    <SpinnerContainer animating={loading}>
      <General>
        <title>
          {viewMode ? "Chi tiết" : "Chỉnh sửa"} thông tin trạng thái đổi trả
        </title>
        <Section>
          <Heading type="h1" modifiers="primary">
            {viewMode ? "CHI TIẾT" : "CHỈNH SỬA"} THÔNG TIN TRẠNG THÁI ĐỔI TRẢ
          </Heading>
          <Section>
            <div className="flex flex-col ">
              {!loading && (
                // <FormContainer
                //   validationSchema={
                //     editMode ? OrderReturnStatusValidationSchema : undefined
                //   }
                //   onSubmit={onSubmitOrderReturnStatusUpdate}
                // >
                //   <Row>
                //     <Col lg="6" className="u-mb-15">
                //       <Formfield label="Tên trạng thái đổi trả" name="name">
                //         <TextfieldHookForm
                //           name="name"
                //           defaultValue={returnOrderStatusDetail?.name}
                //           readOnly={viewMode}
                //         />
                //       </Formfield>
                //     </Col>
                //     <Col lg="6" className="u-mb-15">
                //       <Formfield label="Màu sắc" name="color">
                //         <ColorPicker
                //           disabled={viewMode}
                //           defaultColor={returnOrderStatusDetail?.color}
                //           onChangeClolor={(colorpicker) => {
                //             colorPickerRef.current = colorpicker;
                //           }}
                //         />
                //       </Formfield>
                //     </Col>
                //     <Col lg="6" className="u-mb-15">
                //       <Formfield label="Thứ tự hiển thị" name="displayOrder">
                //         <NumberfieldHookForm
                //           name="displayOrder"
                //           defaultValue={returnOrderStatusDetail?.displayOrder}
                //           readOnly={viewMode}
                //         />
                //       </Formfield>
                //     </Col>
                //     <Col lg="6" className="u-mb-15">
                //       <Formfield
                //         label="Trạng thái trước"
                //         name="prevOrderReturnStatus"
                //       >
                //         <PulldownHookForm
                //           name="prevOrderReturnStatus"
                //           isMultiSelect
                //           defaultValue={orderReturnStatusOptions}
                //           options={orderStatusOptionPulldown}
                //           isDisabled={viewMode}
                //           triggerLoadMore={handleLoadmoreOrderReturnStatus}
                //         />
                //       </Formfield>
                //     </Col>
                //     <Col lg="6" className="u-mb-15">
                //       <Formfield label="Mô tả" name="description">
                //         <TextareafieldHookForm
                //           name="description"
                //           defaultValue={returnOrderStatusDetail?.description}
                //           readOnly={viewMode}
                //         />
                //       </Formfield>
                //     </Col>
                //     <Col lg="12">
                //       <Toggle
                //         label="Mặc định"
                //         defaultChecked={returnOrderStatusDetail?.isDefault}
                //         ref={defaultOrderStatusRef}
                //         disabled={viewMode}
                //       />
                //     </Col>
                //   </Row>
                //   <div className="d-flex justify-content-end u-mt-20">
                //     <div className="u-mr-12">
                //       <Button
                //         buttonType="outline"
                //         modifiers="secondary"
                //         onClick={navigationHelper.goBack}
                //       >
                //         QUAY LẠI
                //       </Button>
                //     </div>
                //     <Button
                //       type="submit"
                //       isLoading={updateOrderReturnStatusState.loading}
                //       disabled={updateOrderReturnStatusState.loading}
                //     >
                //       Lưu
                //     </Button>
                //   </div>
                // </FormContainer>
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={onSubmitOrderReturnStatusUpdate}
                  initialValues={{
                    name: returnOrderStatusDetail?.name,
                    color: returnOrderStatusDetail?.color,
                    displayOrder: returnOrderStatusDetail?.displayOrder,
                    prevOrderReturnStatus: orderReturnStatusOptions,
                    description: returnOrderStatusDetail?.description,
                    isDefault: returnOrderStatusDetail?.isDefault,
                  }}
                >
                  <RowAntd gutter={[16, 16]}>
                    <ColAntd span={12}>
                      <Form.Item
                        label="Trạng thái đổi trả"
                        name="name"
                        rules={[
                          {
                            required: true,
                            message: "Vui lòng nhập tên trạng thái",
                          },
                        ]}
                      >
                        <Input
                          defaultValue={returnOrderStatusDetail?.name}
                          disabled={viewMode}
                        />
                      </Form.Item>
                    </ColAntd>
                    <ColAntd span={12}>
                      <Form.Item label="Màu sắc" name="color">
                        {/* <Input type="color" disabled={viewMode} /> */}

                        <ColorPickerAntd disabled={viewMode} />
                      </Form.Item>
                    </ColAntd>
                  </RowAntd>
                  <RowAntd gutter={[16, 16]}>
                    <ColAntd span={12}>
                      <Form.Item label="Thứ tự hiển thị" name="displayOrder">
                        <InputNumber className="w-full" disabled={viewMode} />
                      </Form.Item>
                    </ColAntd>
                    <ColAntd span={12}>
                      <Form.Item
                        label="Trạng thái trước"
                        name="prevOrderReturnStatus"
                      >
                        <BaseSelect
                          showSelectAll
                          mode="multiple"
                          allowClear
                          options={orderReturnStatusOptions}
                          disabled={viewMode}
                        />
                      </Form.Item>
                    </ColAntd>
                  </RowAntd>
                  <RowAntd gutter={[16, 16]}>
                    <ColAntd span={12}>
                      <Form.Item label="Mô tả" name="description">
                        <Input.TextArea rows={4} disabled={viewMode} />
                      </Form.Item>
                    </ColAntd>
                    <ColAntd span={12}>
                      <Form.Item
                        label="Mặc định"
                        name="isDefault"
                        valuePropName="checked"
                      >
                        <Switch disabled={viewMode} />
                      </Form.Item>
                    </ColAntd>
                  </RowAntd>
                  <RowAntd gutter={[16, 16]}>
                    <ColAntd span={12} />
                    <ColAntd span={12}>
                      <RowAntd gutter={[8, 8]} className="flex justify-end">
                        <ColAntd>
                          <BaseButton
                            type="primary"
                            bgColor={COLOR.GRAY[400]}
                            hoverColor={COLOR.GRAY[600]}
                            onClick={navigationHelper.goBack}
                          >
                            Quay lại
                          </BaseButton>
                        </ColAntd>
                        {editMode && (
                          <ColAntd>
                            <BaseButton
                              type="primary"
                              bgColor={COLOR.BLUE[400]}
                              hoverColor={COLOR.BLUE[600]}
                              disabled={updateOrderReturnStatusState.loading}
                              onClick={() => {
                                form.submit();
                              }}
                            >
                              Lưu
                            </BaseButton>
                          </ColAntd>
                        )}
                      </RowAntd>
                    </ColAntd>
                  </RowAntd>
                </Form>
              )}
            </div>
          </Section>
        </Section>
      </General>
    </SpinnerContainer>
  );
};
export default IndexPage;
