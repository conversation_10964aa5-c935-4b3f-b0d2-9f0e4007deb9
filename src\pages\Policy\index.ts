import { lazy } from "react";

import PATHS from "constants/paths";
import { createAppPage } from "libs/react";

import { ChildrenPage } from "./types";

export default createAppPage<{}, ChildrenPage>({
  name: "ServicesPolicy",
  path: PATHS.POLICY,
  childrenPages: {
    privacy: createAppPage({
      name: "Privacy",
      path: "/quyen-rieng-tu",
      page: () => lazy(() => import("./Privacy")),
    }),
  },
});
