import { useCallback } from "react";

import { LeftOutlined, PlusOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { COLOR } from "constants/color";
import { OrderForm } from "modules/order";

import RootPageRouter from "..";
import { CreateOrderFormPayload, createOrderValidateSchema } from "./constant";
import { CreateOrderPageVm } from "./vm";

const CreateOrderPage = () => {
  const { createOrder, createOrderState } = CreateOrderPageVm();

  const handleOnSubmitCreateOrder = useCallback(
    (formData: CreateOrderFormPayload) => {
      const { items } = formData ?? {};
      const convertItem = items.map((item) => ({
        ...item,
        skuId: Number(item.skuId),
        productId: Number(item.productId),
        // itemType: item.itemType || "SOLD",
      }));

      // console.log(formData, "formData create order");

      // const payload = {
      //   ...formData,
      //   items: convertItem,
      //   // shippingAddressId: Number(formData.shippingAddressId),
      //   orderStatusId: Number(formData.orderStatusId),
      //   orderStatusTaglineId: Number(formData.orderStatusTaglineId),
      //   returnOrderStatusId: Number(formData.returnStatus),
      //   returnOrderStatusTaglineId: Number(formData.returnStatusTagline),
      //   assignedEmployeeId: Number(formData.assignedEmployeeId),
      //   orderSourceId: Number(formData.orderSourceId),
      // };
      // console.log(payload, "payload");

      createOrder({
        ...formData,
        items: convertItem,
        birthDay: formData.birthDay
          ? dayjs(formData.birthDay).toISOString()
          : null,
        // shippingAddressId: Number(formData.shippingAddressId),
        orderStatusId: Number(formData.orderStatusId),
        orderStatusTaglineId: Number(formData.orderStatusTaglineId),
        returnOrderStatusId: Number(formData.returnStatus),
        returnOrderStatusTaglineId: Number(formData.returnStatusTagline),
        assignedEmployeeId: Number(formData.assignedEmployeeId),
        orderSourceId: Number(formData.orderSourceId),
      });
    },
    [createOrder]
  );

  const goBack = () => {
    RootPageRouter.gotoChild("listOrder");
  };

  return (
    <OrderForm
      validationSchema={createOrderValidateSchema}
      onSubmit={handleOnSubmitCreateOrder}
      mode="create"
      cancelButton={
        <BaseButton
          type="primary"
          className="w-fit"
          onClick={goBack}
          icon={<LeftOutlined rev={undefined} />}
          bgColor={COLOR.BLUE[500]}
          hoverColor={COLOR.BLUE[600]}
        >
          {/* Quay lại */}
        </BaseButton>
      }
      submitButton={
        <BaseButton
          htmlType="submit"
          type="primary"
          bgColor={COLOR.BLUE[500]}
          hoverColor={COLOR.BLUE[600]}
          disabled={createOrderState.loading}
          icon={<PlusOutlined rev={undefined} />}
          className="w-fit"
        >
          Tạo mới
        </BaseButton>
      }
    />
  );
};

export default CreateOrderPage;
