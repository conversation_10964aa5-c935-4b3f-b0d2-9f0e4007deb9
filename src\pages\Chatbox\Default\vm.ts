import { useCallback, useState } from "react";

import produce from "immer";

type ModalType = "filterPage" | "filterTag" | "filterEmployee" | "filterTime";

interface State {
  modalState: {
    open: boolean;
    type?: ModalType;
  };
}

export const ChatPageVm = () => {
  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      type: undefined,
    },
  });

  const handleOpenModalByType = useCallback(
    (type: ModalType) =>
      setState(
        produce((draft) => {
          draft.modalState.open = true;
          draft.modalState.type = type;
        })
      ),
    []
  );

  const handleCloseModal = useCallback(
    () =>
      setState(
        produce((draft) => {
          draft.modalState.open = false;
          draft.modalState.type = undefined;
        })
      ),
    []
  );

  const modalTypeIsOpen = useCallback(
    (type: ModalType) =>
      state.modalState.open && state.modalState.type === type,
    [state.modalState.open, state.modalState.type]
  );

  return {
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
  };
};
