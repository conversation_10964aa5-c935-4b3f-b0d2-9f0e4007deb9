import pick from "object.pick";

import { cleanEmptyString } from "helpers/object";
import { createMapper, force, fromSchema, merge } from "libs/adapters/dto";
import { mergeSchema } from "libs/domain";

import { OrderReturnStatusSchema } from "..";

export const updateReturnOrderStatusDto = createMapper(
  fromSchema(
    mergeSchema(
      pick(OrderReturnStatusSchema, [
        "name",
        "isDefault",
        "displayOrder",
        "description",
        "prevReturnOrderStatusIds",
        "color",
      ])
    )
  ),

  merge((data) => ({
    prevReturnOrderStatusIds: !data.prevReturnOrderStatusIds?.length
      ? null
      : data.prevReturnOrderStatusIds,
    description: data.description || undefined,
  })),

  force((data) => ({
    ...cleanEmptyString(data),
  }))
);

export type UpdateReturnOrderStatusDtoType = ReturnType<
  typeof updateReturnOrderStatusDto
>;
