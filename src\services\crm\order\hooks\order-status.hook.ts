import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import {
  GetDetailOrderStatusTaglineDto,
  GetOrderStatusDto,
  GetOrderStatusTaglineDto,
} from "../dto/order-status.dto";
import orderServices from "../order.service";

/**
 * @Module ORDER_STATUS
 * @description Hook to fetch order status
 * @param dto - GetOrderStatusDto
 * @returns {Array<OrderStatusDto>} - Contains orderStatusRefetch, orderStatusData
 */

export const useGetOrderStatusWithoutPagination = (dto: GetOrderStatusDto) => {
  const [orderStatusRefetch, orderStatusData] = useAsync(
    useCallback(() => {
      return orderServices.getOrderStatusWithoutPagination(dto);
    }, [dto])
  );
  return {
    orderStatusRefetch,
    orderStatusData: orderStatusData?.data?.data,
    loading: orderStatusData.loading,
  };
};

/**
 * @Module ORDER_STATUS_TAGLINE
 * @description Hook to fetch order status taglines
 */

/**
 * @param dto - GetOrderStatusTaglineDto
 * @returns {Array<OrderStatusTaglineDto>} - Contains orderStatusTaglineRefetch, orderStatusTaglineData, loading
 */
export const useGetOrderStatusTaglineWithoutPagination = (
  dto: GetOrderStatusTaglineDto
) => {
  const [orderStatusTaglineRefetch, orderStatusTaglineData] = useAsync(
    useCallback(() => {
      return orderServices.getListTaglineOrderStatusWithoutPagination(dto);
    }, [dto])
  );

  return {
    orderStatusTaglineRefetch,
    orderStatusTaglineData: orderStatusTaglineData?.data?.data,
    loading: orderStatusTaglineData.loading,
  };
};

/**
 * @description Hook to fetch details of a specific order status tagline
 * @param dto - GetDetailOrderStatusTaglineDto
 * @returns {Object<OrderStatusTaglineDto>} - Contains orderStatusTaglineRefetch, orderStatusTaglineData, loading
 */

export const useGetOrderStatusTaglineDetail = () => {
  const [orderStatusTaglineDetailRefetch, orderStatusTaglineDetailData] =
    useAsync(
      useCallback((dto: GetDetailOrderStatusTaglineDto) => {
        return orderServices.getOrderStatusTaglineDetail(dto);
      }, [])
    );

  return {
    orderStatusTaglineDetailRefetch,
    orderStatusTaglineDetailData: orderStatusTaglineDetailData?.data?.data,
    loading: orderStatusTaglineDetailData.loading,
  };
};

/**
 * @description Hook to fetch list of taglines not associated with any order status
 * @returns {Array<OrderStatusTaglineDto>} - Contains listTaglineDontByOrderStatusRefetch, listTaglineDontByOrderStatusData, loading
 */

export const useGetListTaglineDontByOrderStatus = () => {
  const [
    listTaglineDontByOrderStatusRefetch,
    listTaglineDontByOrderStatusData,
  ] = useAsync(
    useCallback(() => {
      return orderServices.getListTagLineDontByOrderStatus();
    }, [])
  );

  return {
    listTaglineDontByOrderStatusRefetch,
    listTaglineDontByOrderStatusData:
      listTaglineDontByOrderStatusData?.data?.data,
    loading: listTaglineDontByOrderStatusData.loading,
  };
};
