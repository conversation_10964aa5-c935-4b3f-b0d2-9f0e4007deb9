import { Model, String, Number, Mixed, ModelValue } from "libs/domain";

export const OrderStatusTaglineSchema = {
  _id: String(),
  taglineId: Number(),
  orderStatusId: Number(),
  name: String(),
  description: String(),
  color: String(),
  displayOrder: Number(),
  updatedAt: String(),
  orderStatus: Mixed({ name: String() }),
};

export const OrderStatusTaglineModel = new Model(OrderStatusTaglineSchema);
export type OrderStatusTaglineEntityType = ModelValue<
  typeof OrderStatusTaglineModel
>;
