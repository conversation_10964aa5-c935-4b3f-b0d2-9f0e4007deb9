import crmDriverV1 from "../crm-driver-v1";

export const getConversationMessage = (payload: {
  conversationId: string;
  beforeTime?: string;
  pageSize: number;
}) =>
  crmDriverV1.get(
    `/conversations/external/me/${payload.conversationId}/messages`,
    {
      params: {
        beforeTime: payload.beforeTime,
        pageSize: payload.pageSize,
      },
    }
  );

export const getAssignConversation = (payload: {
  pageNum: number;
  pageSize: number;
}) =>
  crmDriverV1.get(`/conversations/external/me`, {
    params: {
      pageNum: payload.pageNum,
      pageSize: payload.pageSize,
    },
  });

export const getDetailConversationById = (payload: {
  conversationId: string;
}) => crmDriverV1.get(`/conversations/external/${payload.conversationId}`);

export const getLeads = (payload: { pageNum: number; pageSize: number }) =>
  crmDriverV1.get("/conversations/external/leads", {
    params: {
      pageNum: payload.pageNum,
      pageSize: payload.pageSize,
    },
  });
