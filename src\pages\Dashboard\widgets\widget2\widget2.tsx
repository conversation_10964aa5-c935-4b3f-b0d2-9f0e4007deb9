/* eslint-disable @typescript-eslint/no-explicit-any */
import Card from "@material-ui/core/Card";
import Icon from "@material-ui/core/Icon";
import Typography from "@material-ui/core/Typography";
import _ from "lodash";
import ReactApexChart from "react-apexcharts";

function Widget2({ data }: any) {
  return (
    <div className="p-2 w-full">
      {data ? (
        <Card className="w-full rounded-20 shadow">
          <div className="px-3 pt-3">
            <Typography className="!h3 !font-medium !text-xl">
              {data?.title}
            </Typography>

            <div className="flex flex-row flex-wrap items-center mt-3">
              <Typography className="!text-5xl !font-semibold leading-none tracking-tighter">
                {data?.conversion.value}
              </Typography>

              <div className="flex flex-col mx-8">
                {data?.conversion.ofTarget > 0 && (
                  <Icon className="text-green-600 text-20">trending_up</Icon>
                )}
                {data?.conversion.ofTarget < 0 && (
                  <Icon className="text-red-600 text-20">trending_down</Icon>
                )}
                <div className="flex items-center">
                  <Typography className="!font-semibold" color="textSecondary">
                    {data?.conversion.ofTarget}%
                  </Typography>
                  <Typography
                    className="!whitespace-nowrap mx-1"
                    color="textSecondary"
                  >
                    of target
                  </Typography>
                </div>
              </div>
            </div>
          </div>
          <div className="h-40 w-100-p">
            <ReactApexChart
              options={data?.options}
              series={data?.series}
              type={data?.options.chart.type as any}
              height={data?.options.chart.height}
            />
          </div>
        </Card>
      ) : (
        <div />
      )}
    </div>
  );
}

export default Widget2;
