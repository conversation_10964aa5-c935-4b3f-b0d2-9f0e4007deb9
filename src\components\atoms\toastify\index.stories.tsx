import { Story, Meta } from "@storybook/react/types-6-0";

import { <PERSON><PERSON> } from "components/atoms/button";

import { Toastify, ToastifyMessageProps, toastSingleMode } from ".";

export default {
  title: "Components|atoms/Toastify",
  component: Toastify,
} as Meta;

const Template: Story<ToastifyMessageProps> = ({
  type,
  message,
  descripition,
}) => (
  <section>
    <Toastify />
    <Button
      onClick={() =>
        toastSingleMode({
          type,
          message,
          descripition,
        })
      }
    >
      Info
    </Button>
  </section>
);

export const Normal = Template.bind({});

Normal.args = {
  type: "success",
  message: "Example Toastr",
  descripition: `You don't have any new items in your calendar today!`,
};
