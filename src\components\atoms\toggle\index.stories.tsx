/* eslint-disable */
import React from "react";

import { Story, Meta } from "@storybook/react/types-6-0";

import { Toggle, Props } from ".";

export default {
  title: "Components|atoms/Toggle",
  component: Toggle,
} as Meta;

const Template: Story<Props> = ({ label }) => <Toggle label={label} />;

export const Normal = Template.bind({});
export const Label = Template.bind({});

Normal.args = {};

Label.args = {
  label: "Chỉ hiển thị sự khác biệt",
};
