.a-icon {
  $root: &;
  display: inline-block;
  width: rem(24);
  height: rem(24);
  vertical-align: middle;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  $icons: "angle-left", "angle-right", "browser", "caret-down", "cart", "chart",
    "chat", "check-mark", "circle-checked-green", "circle-error-red",
    "close-blue", "close-modal", "close-white", "close", "dashboard", "dust-bin",
    "emoji", "employee", "file-upload", "filter", "folder", "human",
    "image-upload", "info", "lead", "line-dash", "loading-blue", "loading",
    "menu", "minus", "news", "note", "pdf-file", "pen", "pencil", "person-group",
    "phone", "plus-black", "plus", "recycle", "remote-monitoring",
    "send-message", "setting", "signout", "slidebar", "sort-ascending", "store",
    "system", "triangle-warning-yellow", "upload-file", "upload-white", "upload",
    "plus-blue", "search-blue", "ticket", "calendar-black", "comment",
    "employee-black", "facebook", "group", "has-phone-number", "inbox",
    "not-has-phone-number", "reply", "report", "seen", "settings-black", "tag",
    "unreply", "unseen";

  @each $icon in $icons {
    &-#{$icon} {
      background-image: url("~assets/images/icons/#{$icon}.svg");
    }
  }

  &-clickable {
    cursor: pointer;
    transition: opacity 0.3s ease-in-out;

    &:hover {
      opacity: 0.7;
    }
  }

  &-small {
    width: rem(20);
    height: rem(20);
  }

  &-tiny {
    width: rem(16);
    height: rem(16);
  }
}
