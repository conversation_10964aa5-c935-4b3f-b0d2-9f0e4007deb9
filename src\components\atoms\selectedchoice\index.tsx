import React from "react";

export interface Props {
  title: string;
  color: string;
  onDelete?: () => void;
}

export const Selectedchoice: React.FC<Props> = ({ title, color, onDelete }) => (
  <div className="a-selectedchoice" style={{ backgroundColor: color }}>
    <span className="a-selectedchoice_title">{title}</span>
    <span
      aria-hidden="true"
      className="a-selectedchoice_button"
      onClick={onDelete}
    >
      ×
    </span>
  </div>
);
