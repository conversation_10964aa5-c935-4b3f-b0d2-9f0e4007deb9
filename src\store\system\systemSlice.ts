import { createSlice } from "@reduxjs/toolkit";
import { NotificationArgsProps } from "antd";

import type { PayloadAction } from "@reduxjs/toolkit";

export type NotificationState = {
  type: "error" | "info" | "warning" | "success" | "open";
} & Pick<NotificationArgsProps, "message">;

export interface SystemState {
  loading: boolean;
  notication?: NotificationState;
  reload: {};
  menuCollapsed: boolean;
}

const initialState: SystemState = {
  loading: false,
  reload: {},
  menuCollapsed: true,
};

export const systemSlice = createSlice({
  name: "system",
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setNotification: (state, action: PayloadAction<NotificationState>) => {
      state.notication = action.payload;
    },
    reloadAction: (state) => {
      state.reload = {};
    },
    setMenuCollapsedAction: (state, action: PayloadAction<boolean>) => {
      state.menuCollapsed = action.payload;
    },
    toggleMenuCollapsedAction: (state) => {
      state.menuCollapsed = !state.menuCollapsed;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setLoading,
  setNotification,
  reloadAction,
  setMenuCollapsedAction,
  toggleMenuCollapsedAction,
} = systemSlice.actions;

export default systemSlice.reducer;
