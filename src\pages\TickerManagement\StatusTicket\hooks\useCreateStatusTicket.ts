import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import {
  CreateStatusTicketDto,
  UpdateStatusTicketDto,
} from "../dto/status-ticket.dto";
import statusTicketServices from "../services/status-ticket.service";

export const UseCreateStatusTicket = () => {
  const [createStatusTicketExe] = useAsync(
    useCallback(
      (data: CreateStatusTicketDto) =>
        statusTicketServices.createStatusTicket(data),
      []
    )
  );

  return {
    createStatusTicketExe,
  };
};

export const useUpdateStatusTicket = () => {
  const [updateStatusTicketExe] = useAsync(
    useCallback(
      (data: UpdateStatusTicketDto) =>
        statusTicketServices.updateStatusTicket(data),
      []
    )
  );

  return {
    updateStatusTicketExe,
  };
};

export const useDeleteStatusTicket = () => {
  const [deleteStatusTicketExe] = useAsync(
    useCallback(
      ({ statusTicketId }: { statusTicketId: number }) =>
        statusTicketServices.deleteStatusTicket({ statusTicketId }),
      []
    )
  );
  return {
    deleteStatusTicketExe,
  };
};

export default {
  UseCreateStatusTicket,
  useUpdateStatusTicket,
  useDeleteStatusTicket,
};
