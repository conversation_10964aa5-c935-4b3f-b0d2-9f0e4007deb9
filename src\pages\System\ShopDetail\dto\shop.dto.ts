import { BankDto } from "pages/System/ListBankAccount/dto/bank.dto";

/* eslint-disable @typescript-eslint/no-explicit-any */
export interface GetListShopWithoutPaginationResponseDto {
  data: ShopDto[];
}

export type ShopDto = {
  shopCode: string;
  shopId: number;
  name: string;
  displayOrder: number;
  type: string;
  hotline: string;
  cityId: number;
  districtId: number;
  address: string;
  isFranchisedShop: boolean;
  activeStatus: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  mail: string;
  area: string | null;
  startActiveDate: string;
  endActiveDate: string;
  posId: string | null;
  printEx: string | null;
  isDefaultShop: boolean;
  storage: {
    storageId: number;
    name: string;
    code: string;
    displayOrder: number;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  };
  bankAccounts: BankDto[];
  city: {
    cityId: number;
    name: string;
    countryId: number;
    createdAt: string;
    updatedAt: string;
  };
  district: {
    districtId: number;
    name: string;
    cityId: number;
    createdAt: string;
    updatedAt: string;
  };
};
