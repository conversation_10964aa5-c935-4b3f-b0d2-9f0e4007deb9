import { useMemo, useRef } from "react";

import dayjs from "dayjs";
import { useSearchParams, useParams } from "react-router";

import { But<PERSON> } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Icon } from "components/atoms/icon";
import { Textareafield } from "components/atoms/textareafield";
import { Textfield, TextfieldHookForm } from "components/atoms/textfield";
import { toastSingleMode } from "components/atoms/toastify";
import { Formfield } from "components/molecules/formfield";
import { Accordion } from "components/organisms/accordion";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Link } from "components/utils/link";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import PATHS from "constants/paths";
import { FormContainer } from "helpers/form";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";

import { ChildrenPage } from "../types";
import { AddNote, TAddNote } from "./AddNote";
import { inputValidationSchema } from "./constant";
import { LeadDetailPageVm } from "./vm";

const IndexPage = () => {
  const addRef = useRef<TAddNote>(null);

  const [searchParams] = useSearchParams();

  const { leadId } = useParams<PageParamsType<ChildrenPage["leadDetail"]>>();
  const { leadDetail, groupEmployee, isLoading, insertLeadNote, forceUpdate } =
    LeadDetailPageVm({
      leadId,
    });

  const isEditAction = useMemo(
    () => searchParams.get("action") === "edit",
    [searchParams]
  );

  const {
    conversationId,
    creators,
    createdAt,
    assignedEmployee,
    notes,
    customerName,
    clientInchargeName,
    clientInchargeTitle,
    clientInchargeDOB,
    clientInchargeGender,
    clientInchargePhone,
    clientInchargeEmail,
    clientReviewName,
    clientReviewTitle,
    clientReviewDOB,
    clientReviewGender,
    clientReviewPhone,
    clientReviewEmail,
  } = leadDetail || {};
  return (
    <>
      <AddNote
        ref={addRef}
        onAddNote={(data) => {
          insertLeadNote({
            leadId,
            note: data,
          })
            .then(() => {
              forceUpdate();
            })
            .catch(() => {
              toastSingleMode({
                type: "error",
                message: "Something went wrong! Try again in a few minutes",
              });
            });
        }}
      />
      <SpinnerContainer animating={isLoading}>
        <title>Thông tin Lead</title>
        <Section>
          <Heading type="h1" modifiers="primary">
            THÔNG TIN LEAD: {customerName}
          </Heading>
          <Section>
            <Heading type="h2">
              SỐ ĐIỆN THOẠI:
              <span style={{ color: "rgb(22, 132, 185)" }}>
                &nbsp;{leadDetail?.phone}
              </span>
            </Heading>

            <Heading type="h2">
              NGƯỜI TẠO:
              <span style={{ color: "rgb(22, 132, 185)" }}>
                &nbsp;{creators?.[0].name}
              </span>
            </Heading>

            <Section>
              <Row>
                <Col lg="4" className="u-mb-15">
                  <Formfield label="Mã nhân viên" name="employeeId">
                    <Textfield
                      placeholder="Mã nhân viên"
                      name="employeeId"
                      disabled
                      defaultValue={creators?.[0].employeeId}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15">
                  <Formfield label="Vai trò" name="name">
                    <Textfield
                      placeholder="Vai trò"
                      name="name"
                      disabled={!isEditAction}
                      defaultValue={groupEmployee?.creatorsGroup?.name}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15">
                  <Formfield label="Ngày tạo" name="createdAt">
                    <Textfield
                      placeholder="Ngày tạo"
                      name="createdAt"
                      disabled
                      defaultValue={
                        createdAt && dayjs(createdAt).format("DD/MM/YYYY")
                      }
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield label="Số điện thoại" name="phone">
                    <Textfield
                      placeholder="Số điện thoại"
                      name="phone"
                      disabled
                      defaultValue={creators?.[0].phoneNumber}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield label="Email" name="email">
                    <Textfield
                      placeholder="Email"
                      name="email"
                      disabled
                      defaultValue={creators?.[0].email}
                    />
                  </Formfield>
                </Col>
                <Col lg="4">
                  <Formfield label="Mở cuộc trò chuyện" name="employeeId">
                    <div
                      aria-hidden
                      className="d-flex align-items-center"
                      style={{ height: 48 }}
                    >
                      {/* TODO: Link to room conversation */}
                      <Link to={`${PATHS.CHAT}/${conversationId}`}>
                        {conversationId}
                      </Link>
                    </div>
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield
                    label="Client Incharge Name"
                    name="clientInchargeName"
                  >
                    <Textfield
                      placeholder="Client Incharge Name"
                      name="clientInchargeName"
                      disabled
                      defaultValue={clientInchargeName}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield
                    label="Client Incharge Title"
                    name="clientInchargeTitle"
                  >
                    <Textfield
                      placeholder="Client Incharge Title"
                      name="clientInchargeTitle"
                      disabled
                      defaultValue={clientInchargeTitle}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield
                    label="Client Incharge DOB"
                    name="clientInchargeDOB"
                  >
                    <Textfield
                      placeholder="Client Incharge DOB"
                      name="clientInchargeDOB"
                      disabled
                      defaultValue={clientInchargeDOB}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield
                    label="Client Incharge Gender"
                    name="clientInchargeGender"
                  >
                    <Textfield
                      placeholder="Client Incharge Gender"
                      name="clientInchargeGender"
                      disabled
                      defaultValue={clientInchargeGender}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield
                    label="Client Incharge Phone"
                    name="clientInchargePhone"
                  >
                    <Textfield
                      placeholder="Client Incharge Phone"
                      name="clientInchargePhone"
                      disabled
                      defaultValue={clientInchargePhone}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield
                    label="Client Incharge Email"
                    name="clientInchargeEmail"
                  >
                    <Textfield
                      placeholder="Client Incharge Email"
                      name="clientInchargeEmail"
                      disabled
                      defaultValue={clientInchargeEmail}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield label="Client Review Name" name="clientReviewName">
                    <Textfield
                      placeholder="Client Review Name"
                      name="clientReviewName"
                      disabled
                      defaultValue={clientReviewName}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield
                    label="Client Review Title"
                    name="clientReviewTitle"
                  >
                    <Textfield
                      placeholder="ClientReview Title"
                      name="clientReviewTitle"
                      disabled
                      defaultValue={clientReviewTitle}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield label="Client Review DOB" name="clientReviewDOB">
                    <Textfield
                      placeholder="Client Review DOB"
                      name="clientReviewDOB"
                      disabled
                      defaultValue={clientReviewDOB}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield
                    label="Client Review Gender"
                    name="clientReviewGender"
                  >
                    <Textfield
                      placeholder="Client Review Gender"
                      name="clientReviewGender"
                      disabled
                      defaultValue={clientReviewGender}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield
                    label="Client Review Phone"
                    name="clientReviewPhone"
                  >
                    <Textfield
                      placeholder="Client Review Phone"
                      name="clientReviewPhone"
                      disabled
                      defaultValue={clientReviewPhone}
                    />
                  </Formfield>
                </Col>
                <Col lg="4" className="u-mb-15 u-mb-lg-0">
                  <Formfield
                    label="Client Review Email"
                    name="clientReviewEmail"
                  >
                    <Textfield
                      placeholder="Client Review Email"
                      name="clientReviewEmail"
                      disabled
                      defaultValue={clientReviewEmail}
                    />
                  </Formfield>
                </Col>
              </Row>
            </Section>
          </Section>

          <Section>
            <Heading type="h2">
              NHÂN VIÊN ĐƯỢC PHÂN CÔNG:
              <span style={{ color: "rgb(22, 132, 185)" }}>
                &nbsp;{assignedEmployee?.name}
              </span>
            </Heading>

            <Section>
              <FormContainer validationSchema={inputValidationSchema}>
                <Row>
                  <Col lg="4" className="u-mb-15">
                    <Formfield label="Mã nhân viên" name="employeeId">
                      <TextfieldHookForm
                        placeholder="Mã nhân viên"
                        name="employeeId"
                        disabled={!isEditAction}
                        defaultValue={assignedEmployee?.employeeId}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="4" className="u-mb-15">
                    <Formfield label="Vai trò" name="name">
                      <TextfieldHookForm
                        placeholder="Vai trò"
                        name="name"
                        disabled={!isEditAction}
                        defaultValue={
                          groupEmployee?.assignedEmployeeGroup?.name
                        }
                      />
                    </Formfield>
                  </Col>
                  <Col lg="4" />
                  <Col lg="4" className="u-mb-15 u-mb-lg-0">
                    <Formfield label="Số điện thoại" name="phone">
                      <TextfieldHookForm
                        placeholder="Số điện thoại"
                        name="phone"
                        disabled={!isEditAction}
                        defaultValue={assignedEmployee?.phoneNumber}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="4">
                    <Formfield label="Email" name="email">
                      <TextfieldHookForm
                        placeholder="Email"
                        name="email"
                        disabled={!isEditAction}
                        defaultValue={assignedEmployee?.email}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="4" />
                </Row>
              </FormContainer>
            </Section>
          </Section>
          <Section>
            <Heading type="h2">GHI CHÚ</Heading>

            <Section>
              {notes?.map((note, index) => (
                <div className="u-mb-20" key={note?._id}>
                  <Accordion
                    title={`Ghi chú ${index + 1}`}
                    type="highlight"
                    // deletable={isEditAction}
                  >
                    <div className="d-flex justify-content-between">
                      <Heading type="h2">
                        Người tạo ghi chú:
                        <span style={{ color: "rgb(22, 132, 185)" }}>
                          &nbsp;{note?.employee.name}
                        </span>
                      </Heading>
                      <Heading type="h2">
                        Tạo lúc:
                        <span style={{ fontWeight: "normal" }}>
                          &nbsp;{dayjs(note?.createdAt).format("DD/MM/YYYY")}
                        </span>
                      </Heading>
                    </div>
                    <Section>
                      <Formfield label="Nội dung" name="content">
                        <Textareafield
                          name="content"
                          row={5}
                          readOnly
                          defaultValue={note?.content}
                        />
                      </Formfield>
                    </Section>
                  </Accordion>
                </div>
              ))}

              {isEditAction && (
                <div
                  role="button"
                  tabIndex={0}
                  className="d-flex justify-content-end"
                  style={{ marginTop: 20, cursor: "pointer" }}
                  onClick={() => {
                    addRef.current?.show();
                  }}
                >
                  <span
                    className="d-flex align-items-center"
                    style={{ border: "2px solid #1684B9", padding: 10 }}
                  >
                    <Icon iconName="note" />
                    <span className="u-ml-16" style={{ color: "#1684B9" }}>
                      <strong>THÊM GHI CHÚ MỚI</strong>
                    </span>
                  </span>
                </div>
              )}
            </Section>
          </Section>
          <Section>
            <div className="d-flex justify-content-between">
              <Button
                modifiers="secondary"
                buttonType="outline"
                onClick={navigationHelper.goBack}
              >
                QUAY LẠI
              </Button>
              {isEditAction && <Button>Lưu</Button>}
            </div>
          </Section>
        </Section>
      </SpinnerContainer>
    </>
  );
};

export default IndexPage;
