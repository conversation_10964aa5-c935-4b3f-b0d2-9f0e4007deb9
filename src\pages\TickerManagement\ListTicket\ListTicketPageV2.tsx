import React, { useC<PERSON>back, useEffect, useRef, useState } from "react";
import {
  DeleteFilled,
  EditFilled,
  EyeFilled,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  App,
  Card,
  Checkbox,
  Collapse,
  DatePicker,
  Flex,
  Form,
  Input,
  Modal,
  Spin,
  Table,
  Tabs,
  TabsProps,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { ColumnsType } from "antd/es/table";
import dayjs, { Dayjs } from "dayjs";
import _ from "lodash";
import { data } from "react-router";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { showLoading } from "components/atoms/base/Spinner";
import { COLOR } from "constants/color";
import useDidMount from "helpers/react-hooks/useDidMount";
import { usePulldownAssignEmployee } from "modules/employee";
import { useInfinityMasterDataTicket } from "modules/ticket/hook/useGetListMasterDataTicket";
import RootPageRouter from "..";
import { useInfinityStatusTicket } from "../StatusTicket/hooks/useGetStatusTicket";
import { PriorityDto, StatusDto } from "./dto/ticket.dto";
import {
  useCreateTicket,
  useDeleteTicket,
  useGetTicketList,
  useUpdateTicket,
} from "./hooks/ticket.hooks";
import {
  FromTicketType,
  TicketModal,
  TicketModalRef,
} from "./modal/TicketModal";

const { Title, Text } = Typography;

const { RangePicker } = DatePicker;
type FormFilterType = {
  startDate: Date | undefined;
  endDate: Date | undefined;
  title: string | undefined;
  statusIds: number[] | undefined;
  priorityIds: number[] | undefined;
  employeeId: number | undefined;
  myTicket: boolean;
  expired: boolean;
  type: TabTypeKey;
};

export interface PriorityType
  extends Partial<Omit<PriorityDto, "createdAt" | "updatedAt" | "deletedAt">> {
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

export interface StatusType
  extends Partial<Omit<StatusDto, "createdAt" | "updatedAt" | "deletedAt">> {
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

export interface TicketDataType {
  key: number;
  ordinalNumber: number;
  ticketId: number;
  title: string;
  priorityId: number;
  statusId: number;
  employeeId: number;
  endDate: Date;
  note: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  createdBy: string;
  updatedBy: string;
  priority: PriorityType;
  status: StatusType;
}

export enum TabTypeKey {
  all = "all",
  pending = "pending",
  overdue = "overdue",
}
export default function ListTicketPageV2() {
  const [formFilter] = Form.useForm<FormFilterType>();
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [statusTicketInput, setStatusTicketInput] = useState<string>("");
  const [priorityTicketInput, setPriorityTicketInput] = useState<string>("");
  const [dataSource, setDataSource] = useState<TicketDataType[]>([]);
  const [tabKey, setTabKey] = useState<TabTypeKey>(TabTypeKey.all);
  const [filterValue, setFilterValue] = useState<FormFilterType>({
    startDate: undefined,
    endDate: undefined,
    employeeId: undefined,
    title: "",
    statusIds: [],
    priorityIds: [],
    myTicket: false,
    expired: false,
    type: tabKey,
  });
  const ticketModalRef = useRef<TicketModalRef>(null);
  const handleChangePageAndPageSize = useCallback(
    (offset: number, limit: number) => {
      setPage(offset);
      setPageSize(limit);
    },
    []
  );
  const handleGoToPage = useCallback(
    ({ ticketId, action }: { ticketId: number | string; action?: string }) =>
      RootPageRouter.gotoChild("ticketDetail", {
        params: { ticketId: ticketId.toString() },
        queryString: action ? `?action=${action}` : undefined,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  const { assignEmployeeOptions, loadMoreEmployee } =
    usePulldownAssignEmployee();

  function employeeIdToName(employeeId: string) {
    return assignEmployeeOptions.find((el) => el.value === employeeId)?.label;
  }

  const {
    data: statusOptions,
    hasMore: statusHasMore,
    loadMore: loadMoreStatus,
    loading: statusLoading,
    reset: resetStatus,
    totalRecords: statusTotalRecords,
  } = useInfinityStatusTicket({
    searchText: statusTicketInput,
  });

  const {
    data: priorityOptions,
    hasMore: priorityHasMore,
    loadMore: loadMorePriority,
    loading: priorityLoading,
    reset: resetPriority,
    totalRecords: priorityTotalRecords,
  } = useInfinityMasterDataTicket({
    searchText: priorityTicketInput,
  });

  const handleResetStatus = () => {
    resetStatus();
    setStatusTicketInput("");
  };

  const handleResetPriority = () => {
    resetPriority();
    setPriorityTicketInput("");
  };

  const {
    data: ticketList,
    loading: ticketListLoading,
    refetch: refetchTicketList,
  } = useGetTicketList({
    ...filterValue,
    pageNum: page,
    pageSize,
  });
  const { createTicketExe } = useCreateTicket();
  const { updateTicketExe } = useUpdateTicket();

  useEffect(() => {
    if (_.size(ticketList?.data) > 0) {
      const convertData = _.map(ticketList?.data, (item, index) => {
        const { priority, status } = item ?? {};
        return {
          ...item,
          key: item.ticketId,
          ordinalNumber: (page - 1) * pageSize + index + 1,
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt),
          deletedAt: item.deletedAt ? new Date(item.deletedAt) : null,
          endDate: new Date(item.endDate),
          priority: {
            ...priority,
            createdAt: new Date(priority?.createdAt),
            updatedAt: new Date(priority?.updatedAt),
            deletedAt: priority?.deletedAt
              ? new Date(priority?.deletedAt)
              : null,
          },
          status: {
            ...status,
            createdAt: new Date(status?.createdAt),
            updatedAt: new Date(status?.updatedAt),
            deletedAt: status?.deletedAt ? new Date(status?.deletedAt) : null,
          },
        };
      });
      setDataSource(convertData);
    } else {
      setDataSource([]);
    }
  }, [ticketList]);

  const handleFinishFilter = async (values: FormFilterType) => {
    const { startDate, ...rest } = values;
    const payload = {
      ...rest,
      type: tabKey,
      employeeId: Number(rest.employeeId) || undefined,
      startDate: startDate
        ? startDate[0].startOf("day").toISOString()
        : undefined,
      endDate: startDate ? startDate[1].endOf("day").toISOString() : undefined,
    };
    setFilterValue(payload);
    setPage(1);
  };

  const onCreateOrUpdate = (values: FromTicketType) => {
    const payload = {
      ...values,

      endDate: values.endDate.toISOString(),
    };
    showLoading(true);
    if (values.ticketId) {
      updateTicketExe({ ...payload, ticketId: values.ticketId })
        .then(() => {
          showNotification({
            type: "success",
            message: "Cập nhật ticket thành công",
          });
          ticketModalRef.current?.close();
          refetchTicketList();
        })
        .catch((err) => {
          const apiError = err?.response?.data?.errors;
          if (apiError && apiError.length > 0) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            apiError.forEach((e: any) => {
              showNotification({
                type: "error",
                message: e.title,
              });
            });
          }
        })
        .finally(() => {
          showLoading(false);
        });
      return;
    }

    createTicketExe(payload)
      .then(() => {
        showNotification({
          type: "success",
          message: "Tạo ticket thành công",
        });
        ticketModalRef.current?.close();
        refetchTicketList();
      })
      .catch((err) => {
        const apiError = err?.response?.data?.errors;
        if (apiError && apiError.length > 0) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          apiError.forEach((e: any) => {
            showNotification({
              type: "error",
              message: e.title,
            });
          });
        }
      })
      .finally(() => {
        showLoading(false);
      });
  };

  const { deleteTicketExe } = useDeleteTicket();

  const handleDeleteTicket = (record: TicketDataType) => {
    const { ticketId, title } = record;
    Modal.confirm({
      centered: true,
      title: "Xác nhận xóa",
      content: `Bạn có chắc chắn muốn xóa ticket "${title}" không?`,
      okText: "Xóa",
      okType: "primary",
      okButtonProps: {
        className: "bg-red-500 hover:!bg-red-700",
      },
      onOk: () => {
        showLoading(true);
        deleteTicketExe({
          ticketId,
        })
          .then(() => {
            refetchTicketList();
            showNotification({
              type: "success",
              message: "Xóa ticket thành công",
            });
          })
          .catch((err) => {
            showNotification({
              type: "error",
              message: err.message,
            });
          })
          .finally(() => {
            showLoading(false);
          });
      },
    });
  };

  const columns: ColumnsType<TicketDataType> = [
    {
      title: "STT",
      dataIndex: "ordinalNumber",
      key: "ordinalNumber",
      width: 100,
      align: "center",
    },
    {
      title: "Mã ticket",
      dataIndex: "ticketId",
      key: "ticketId",
      width: 144,
      align: "center",
    },
    {
      title: "Tiêu đề",
      dataIndex: "title",
      key: "title",
      width: 256,
    },

    {
      title: "Độ ưu tiên",
      dataIndex: "priority",
      key: "priority",
      width: 144,
      align: "center",
      render: (__, record) => {
        const { priority } = record ?? {};
        return (
          <Tag className="px-3 text-base" color={priority?.color}>
            {priority?.name}
          </Tag>
        );
      },
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      width: 144,
      align: "center",
      render: (__, record) => {
        const { status } = record ?? {};
        return (
          <Tag className="px-3 text-base" color={status?.color}>
            {status?.name}
          </Tag>
        );
      },
    },
    {
      title: "Người tạo",
      dataIndex: "createdBy",
      key: "createdBy",
      align: "center",
      width: 180,
      render: (__, record) => {
        const createdBy = record?.createdBy;
        return <Text>{createdBy ?? "--"}</Text>;
      },
    },
    {
      title: "Phân công",
      dataIndex: "employeeId",
      key: "employeeId",
      align: "center",
      width: 180,
      render: (value) => {
        return <Text>{employeeIdToName(String(value)) ?? "--"}</Text>;
      },
    },
    {
      title: "Tiến độ",
      dataIndex: "progress",
      key: "progress",
      width: 144,
      align: "center",
      render: (value) => {
        return <Text>{value ? `${value}%` : "--"}</Text>;
      },
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 156,
      render: (value) => dayjs(value).format("DD/MM/YYYY"),
    },

    {
      title: "Ngày kết thúc",
      dataIndex: "endDate",
      key: "endDate",
      width: 144,
      render: (value) => dayjs(value).format("DD/MM/YYYY"),
    },

    {
      title: "Thao tác",
      key: "action",
      align: "center",
      fixed: "right",
      width: 144,
      render: (__, record) => {
        return (
          <Flex align="center" gap={8} justify="center">
            <Tooltip title="Xem chi tiết">
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                icon={<EyeFilled />}
                onClick={() => {
                  // ticketModalRef.current.open(record, true);
                  handleGoToPage({ ticketId: record.ticketId, action: "view" });
                }}
              />
            </Tooltip>
            <Tooltip title="Chỉnh sửa">
              <BaseButton
                type="primary"
                bgColor={COLOR.GREEN[500]}
                hoverColor={COLOR.GREEN[700]}
                icon={<EditFilled />}
                onClick={() => {
                  // ticketModalRef.current.open(record);
                  handleGoToPage({ ticketId: record.ticketId, action: "edit" });
                }}
              />
            </Tooltip>

            <Tooltip title="Xóa">
              <BaseButton
                type="primary"
                bgColor={COLOR.RED[500]}
                hoverColor={COLOR.RED[700]}
                icon={<DeleteFilled />}
                onClick={() => {
                  handleDeleteTicket(record);
                }}
              />
            </Tooltip>
          </Flex>
        );
      },
    },
  ];

  const renderTabContent = (dataSourrce: TicketDataType[]) => {
    return (
      <Table
        loading={ticketListLoading}
        tableLayout="auto"
        scroll={{ x: "max-content" }}
        columns={columns}
        dataSource={dataSource}
        pagination={{
          current: page,
          pageSize,
          onChange: handleChangePageAndPageSize,
          onShowSizeChange: (__, size) => {
            setPage(1);
            setPageSize(size);
          },
          pageSizeOptions: [5, 10, 15, 25, 30],
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Tổng số ${total} bản ghi`,
          total: ticketList?.meta?.totalRecords,
        }}
      />
    );
  };

  const items: TabsProps["items"] = [
    {
      key: TabTypeKey.all,
      label: "Tất cả",
      children: renderTabContent(dataSource),
    },
    {
      key: TabTypeKey.pending,
      label: "Cần xử lý",
      children: renderTabContent(dataSource),
    },
    {
      key: TabTypeKey.overdue,
      label: "Quá hạn",
      children: renderTabContent(dataSource),
    },
  ];

  const handleChangeTab = (key: TabTypeKey) => {
    if (key === TabTypeKey.all) {
      setFilterValue({
        ...filterValue,
        type: TabTypeKey.all,
      });
      setPage(1);
    } else if (key === TabTypeKey.pending) {
      setFilterValue({
        ...filterValue,
        type: TabTypeKey.pending,
      });
      setPage(1);
    } else if (key === TabTypeKey.overdue) {
      setFilterValue({
        ...filterValue,
        type: TabTypeKey.overdue,
      });
      setPage(1);
    }
  };

  useEffect(() => {
    refetchTicketList().catch((error) => {
      const { errors } = error?.response?.data ?? {};
      if (errors && errors.length > 0) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        errors.forEach((err: any) => {
          showNotification({
            type: "error",
            message: err.title || "Lỗi máy chủ",
          });
        });
        return;
      }
      const { title } = error?.response?.data ?? {};
      showNotification({
        type: "error",
        message: title || "Lỗi máy chủ",
      });
    });
  }, [page, pageSize, filterValue]);

  useDidMount(() => {
    loadMoreEmployee();
  });

  return (
    <>
      <Card className="m-4">
        <div className="flex flex-col gap-3">
          <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:justify-between">
            <Title level={4}>Danh sách Ticket</Title>
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              icon={<PlusOutlined rev={undefined} />}
              onClick={() => {
                // ticketModalRef.current.open();
                handleGoToPage({ ticketId: "add-new" });
              }}
            >
              Tạo mới
            </BaseButton>
          </div>
          <Collapse
            bordered
            items={[
              {
                label: "Bộ lọc",
                key: "filter",
                children: (
                  <div className="flex flex-col gap-3">
                    <Form
                      onFinish={handleFinishFilter}
                      form={formFilter}
                      layout="vertical"
                      initialValues={{
                        myTicket: false,
                        expired: false,
                      }}
                    >
                      <div className="grid grid-cols-1 lg:grid-cols-5 gap-3">
                        <Form.Item label="Ngày tạo" name="startDate">
                          {/* <Input allowClear placeholder="Nhập tiêu đề" /> */}
                          <RangePicker />
                        </Form.Item>
                        <Form.Item label="Tiêu đề" name="title">
                          <Input allowClear placeholder="Nhập tiêu đề" />
                        </Form.Item>

                        <Form.Item label="Người phân công" name="employeeId">
                          <BaseSelect
                            allowClear
                            showSearch
                            options={assignEmployeeOptions}
                            placeholder="Chọn người phân công"
                          />
                        </Form.Item>

                        <Form.Item
                          label="Trạng thái công việc"
                          name="statusIds"
                        >
                          <BaseSelect
                            mode="multiple"
                            key="statusIds"
                            allowClear
                            showSearch
                            filterOption={false}
                            onSearch={(value) => {
                              setStatusTicketInput(value);
                            }}
                            onClear={() => {
                              handleResetStatus();
                            }}
                            onBlur={() => {
                              handleResetStatus();
                            }}
                            onSelect={(value, option) => {
                              setStatusTicketInput("");
                            }}
                            fieldNames={{
                              label: "name",
                              value: "statusTicketId",
                            }}
                            placeholder="Chọn trạng thái"
                            onPopupScroll={(event) => {
                              const target = event.target as HTMLElement;
                              if (
                                target.scrollTop + target.clientHeight ===
                                target.scrollHeight
                              ) {
                                if (statusHasMore && !statusLoading) {
                                  loadMoreStatus();
                                }
                              }
                            }}
                            popupRender={(menu) => (
                              <>
                                {menu}
                                <div
                                  style={{
                                    textAlign: "center",
                                    padding: "8px 0",
                                  }}
                                >
                                  {statusLoading ? (
                                    <Spin size="small" />
                                  ) : !statusHasMore &&
                                    statusOptions.length > 0 ? (
                                    <div className="w-full">
                                      <span style={{ color: "#999" }}>
                                        Đã tải hết dữ liệu
                                      </span>
                                    </div>
                                  ) : null}
                                </div>
                              </>
                            )}
                            options={statusOptions}
                          />
                        </Form.Item>

                        <Form.Item label="Độ ưu tiên" name="priorityIds">
                          <BaseSelect
                            mode="multiple"
                            key="priorityIds"
                            allowClear
                            showSearch
                            filterOption={false}
                            onSearch={(value) => {
                              setPriorityTicketInput(value);
                            }}
                            onClear={() => {
                              handleResetPriority();
                            }}
                            onBlur={() => {
                              handleResetPriority();
                            }}
                            onSelect={(value, option) => {
                              setPriorityTicketInput("");
                            }}
                            fieldNames={{
                              label: "name",
                              value: "masterDataTicketId",
                            }}
                            placeholder="Chọn độ ưu tiên"
                            onPopupScroll={(event) => {
                              const target = event.target as HTMLElement;
                              if (
                                target.scrollTop + target.clientHeight ===
                                target.scrollHeight
                              ) {
                                if (statusHasMore && !statusLoading) {
                                  loadMorePriority();
                                }
                              }
                            }}
                            popupRender={(menu) => (
                              <>
                                {menu}
                                <div
                                  style={{
                                    textAlign: "center",
                                    padding: "8px 0",
                                  }}
                                >
                                  {priorityLoading ? (
                                    <Spin size="small" />
                                  ) : !priorityHasMore &&
                                    priorityOptions.length > 0 ? (
                                    <div className="w-full">
                                      <span style={{ color: "#999" }}>
                                        Đã tải hết dữ liệu
                                      </span>
                                    </div>
                                  ) : null}
                                </div>
                              </>
                            )}
                            options={priorityOptions}
                          />
                        </Form.Item>

                        {/* <div className="flex flex-col gap-2 justify-center">
                          <Form.Item
                            noStyle
                            valuePropName="checked"
                            name="myTicket"
                          >
                            <Checkbox className="w-fit">
                              Ticket của tôi
                            </Checkbox>
                          </Form.Item>

                          <Form.Item
                            noStyle
                            valuePropName="checked"
                            name="expired"
                          >
                            <Checkbox className="w-fit">Quá hạn</Checkbox>
                          </Form.Item>
                        </div> */}
                      </div>
                      <div className="mt-2 place-self-end">
                        <BaseButton
                          type="primary"
                          bgColor={COLOR.BLUE[500]}
                          hoverColor={COLOR.BLUE[700]}
                          htmlType="submit"
                          icon={<SearchOutlined rev={undefined} />}
                        >
                          Tìm kiếm
                        </BaseButton>
                      </div>
                    </Form>
                  </div>
                ),
              },
            ]}
          />
          <Tabs
            defaultActiveKey="1"
            items={items}
            onChange={(key) => {
              setTabKey(key as TabTypeKey);
              handleChangeTab(key as TabTypeKey);
            }}
          />
        </div>
      </Card>
      {/* <TicketModal ref={ticketModalRef} onSubmit={onCreateOrUpdate} /> */}
    </>
  );
}
