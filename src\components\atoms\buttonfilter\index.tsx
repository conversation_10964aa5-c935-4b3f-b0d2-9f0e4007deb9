import React, { useCallback, useState } from "react";

import { Icon } from "components/atoms/icon";
import { mapModifiers } from "helpers/component";

/* eslint-disable */
export interface Props {
	children?: React.ReactNode;
	title?: string;
	defaultExpand?: boolean;
	onClick?: (isExpanded: boolean) => void;
}

export const ButtonFilter: React.FC<Props> = ({
	defaultExpand,
	title,
	onClick,
}) => {
	const [isExpanded, setExpanded] = useState(defaultExpand || false);

	const onToggleExpanded = useCallback(() => {
		setExpanded(!isExpanded);

		if (onClick) {
			onClick(!isExpanded);
		}
	}, [isExpanded, onClick]);

	return (
		<button
			type="button"
			onClick={onToggleExpanded}
			className={mapModifiers("a-buttonfilter", isExpanded && "expand")}
		>
			<Icon iconName="filter" />
			<span className="a-buttonfilter_title">{title}</span>
			<Icon iconName="line-dash" />
			<Icon iconName="angle-right" />
		</button>
	);
};

ButtonFilter.defaultProps = {
	title: "LỌC DỮ LIỆU",
};
