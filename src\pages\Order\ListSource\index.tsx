import {
  DeleteFilled,
  EditFilled,
  InfoCircleFilled,
  SearchOutlined,
} from "@ant-design/icons";
import { Col, Input, Row, Modal, Form, Tooltip } from "antd";
import Table, { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { Heading } from "components/atoms/heading";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";

import { deleteOrderSource } from "services/crm/order";
import { FormPayloadOfCreation } from "./constant";
import { ListSourcePageVm } from "./vm";

interface OrderSource {
  orderSourceId: number;
  name: string;
  updatedAt: string | Date;
}

const IndexPage = () => {
  const [form] = Form.useForm();
  const {
    loading,
    gotoPage,
    listOrderSource,
    listOrderSourcePaginationState,
    pageSize,
    handleChangePageSize,
    handleOpenModalByType,
    handleOnCloseModal,
    gotoDetailOrderSource,
    gotoEditOrderSource,
    modalState,
    createOrderSource,
    handleDeleteOrderSource,
    createOrderSourceState,
  } = ListSourcePageVm();

  // Define table columns
  const columns: ColumnsType<OrderSource> = [
    {
      title: "STT",
      key: "no.",
      render: (_, __, index) => index + 1,
      fixed: "left",
      align: "center",
    },
    {
      title: "Nguồn đơn",
      sorter: true,
      dataIndex: "name",
      key: "name",
      align: "center",
    },
    {
      title: "Cập nhật cuối",
      dataIndex: "updatedAt",
      align: "center",
      key: "updatedAt",
      sorter: true,
      render: (value) => {
        return (
          <div className="flex justify-center">
            <span>{dayjs(value).format("DD/MM/YYYY HH:mm")}</span>
          </div>
        );
      },
    },
    {
      title: "Thao tác",
      align: "center",
      key: "action",
      fixed: "right",
      render: (_, record) => {
        const { orderSourceId } = record ?? {};
        return (
          <div className="flex justify-center gap-3">
            <Tooltip title="Xem chi tiết">
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                icon={<InfoCircleFilled rev={undefined} />}
                onClick={() => gotoDetailOrderSource(orderSourceId)}
              />
            </Tooltip>
            <Tooltip title="Chỉnh sửa">
              <BaseButton
                type="primary"
                bgColor={COLOR.GREEN[500]}
                hoverColor={COLOR.GREEN[700]}
                icon={<EditFilled rev={undefined} />}
                onClick={() => gotoEditOrderSource(orderSourceId)}
              />
            </Tooltip>
            <Tooltip title="Xóa">
              <BaseButton
                type="primary"
                bgColor={COLOR.RED[500]}
                hoverColor={COLOR.RED[700]}
                icon={<DeleteFilled rev={undefined} />}
                onClick={() => {
                  handleDeleteOrderSource(orderSourceId);
                  // TODO: Implement delete functionality
                  // Handle delete action for order source
                }}
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const handleClose = () => {
    handleOnCloseModal();
    form.resetFields();
  };

  const onFinish = (values: FormPayloadOfCreation) => {
    form.resetFields();
    createOrderSource(values);
  };

  return (
    <General>
      <title key="title">Danh sách nguồn đơn</title>
      <Section>
        <div className="mb-6">
          <Heading modifiers="primary">DANH SÁCH NGUỒN ĐƠN</Heading>
        </div>

        <div className="mb-6">
          <Row gutter={[16, 16]}>
            <Col xs={{ span: 24, order: 2 }} lg={{ span: 12, order: 1 }}>
              <Input
                placeholder="Tìm kiếm"
                suffix={<SearchOutlined className="text-xl" rev={undefined} />}
              />
            </Col>
            <Col
              xs={{ span: 24, order: 1 }}
              lg={{ span: 3, order: 2, offset: 9 }}
            >
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                className="w-full"
                onClick={() => handleOpenModalByType("sourceCreation")}
              >
                Tạo mới
              </BaseButton>
            </Col>
          </Row>
        </div>

        <div className="">
          <Table
            loading={loading}
            scroll={{ x: 1200 }}
            size="small"
            bordered
            columns={columns}
            dataSource={listOrderSource}
            rowKey="orderSourceId"
            pagination={{
              current: listOrderSourcePaginationState?.currentPage || 1,
              total:
                (listOrderSourcePaginationState?.totalPage || 0) * pageSize,
              pageSize,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} của ${total} mục`,
              pageSizeOptions: ["5", "10", "15", "25", "30", "50", "100"],
              onChange: (page, size) => {
                gotoPage(page);
                if (size !== pageSize) {
                  handleChangePageSize(size);
                }
              },
              onShowSizeChange: (_, size) => {
                handleChangePageSize(size);
                gotoPage(1); // Reset to first page when changing page size
              },
            }}
          />
        </div>
      </Section>

      <Modal
        title="TẠO MỚI NGUỒN ĐƠN"
        open={modalState.open && modalState.type === "sourceCreation"}
        onCancel={handleClose}
        footer={null}
        width={700}
      >
        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Form.Item
            label="Tên nguồn đơn"
            name="name"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập tên nguồn đơn",
              },
            ]}
          >
            <Input placeholder="Nhập tên nguồn đơn" />
          </Form.Item>

          <div className="flex justify-end gap-3 mt-6">
            <BaseButton type="default" onClick={handleClose}>
              Hủy
            </BaseButton>
            <BaseButton
              htmlType="submit"
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              disabled={createOrderSourceState.loading}
              loading={createOrderSourceState.loading}
            >
              Lưu
            </BaseButton>
          </div>
        </Form>
      </Modal>
    </General>
  );
};

export default IndexPage;
