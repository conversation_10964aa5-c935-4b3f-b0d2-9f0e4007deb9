import { Story, Meta } from "@storybook/react/types-6-0";

import { Selectedchoice, Props } from ".";

export default {
  title: "Components|atoms/Selectedchoice",
  component: Selectedchoice,
} as Meta;

const Template: Story<Props> = ({ title, color, onDelete }) => (
  <Selectedchoice title={title} color={color} onDelete={onDelete} />
);

export const Normal = Template.bind({});

Normal.args = {
  title: "Test",
  color: "red",
};
