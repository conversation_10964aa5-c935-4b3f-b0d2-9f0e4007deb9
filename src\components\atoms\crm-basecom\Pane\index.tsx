import { ReactNode } from "react";

import style from "./index.module.css";

type Props = {
  title: string;
  describe?: string;
  children?: ReactNode;
};

export const Pane = ({ title, describe, children }: Props) => {
  return (
    <div className={style.pane}>
      <div className={style.header_pane}>
        <div className={style.header}>{title}</div>
        {describe ? <div className={style.describe}>{describe}</div> : null}
      </div>
      {children}
    </div>
  );
};

Pane.defaultProps = {
  describe: "",
  children: null,
};
