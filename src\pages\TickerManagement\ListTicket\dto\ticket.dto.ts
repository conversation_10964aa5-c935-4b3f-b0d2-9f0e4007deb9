import { PaginationDTOType } from "modules/common/pagination";
import { MetaTotalRecords } from "pages/TickerManagement/StatusTicket/dto/get-status-ticket.dto";

export interface GetListTicketDto {
  pageNum: number;
  pageSize: number;
  title: string;
  statusIds: number[] | null;
  priorityIds: number[] | null;
  myTicket: boolean;
  expired: boolean;
}

export interface GetListTicketResponseDto extends MetaTotalRecords {
  data: TicketDto[];
  links: PaginationDTOType;
}

export interface TicketDto {
  ticketId: number;
  title: string;
  priorityId: number;
  statusId: number;
  employeeId: number;
  endDate?: Date;
  note: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  createdBy: string;
  updatedBy: string;
  reminderAt?: Date;
  progress?: number;
  priority: PriorityDto;
  status: StatusDto;
}

export interface TicketResponse {
  data: TicketDto;
}

export interface PriorityDto {
  masterDataTicketId: number;
  name: string;
  color: string;
  displayOrder: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface StatusDto {
  statusTicketId: number;
  name: string;
  color: string;
  displayOrder: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface CreateTicketDto {
  title: string;
  priorityId: number;
  statusId: number;
  employeeId: number;
  endDate: string;
  note?: string;
}

export interface UpdateTicketDto extends CreateTicketDto {
  ticketId: number;
}
