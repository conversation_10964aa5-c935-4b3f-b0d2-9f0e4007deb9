import React, { useRef, useEffect, useCallback, useLayoutEffect } from "react";

import { EditableElement, Props } from "./types";
import { blobFromFiles, shouldDisplayPlaceholder } from "./utils";

interface ElementRefs {
  editable: EditableElement | null;
  placeholder: HTMLSpanElement | null;
}

export const EditableInput: React.FC<Props> = ({
  placeholder,
  defaultValue,
  onPasteFile,
  onPasteFileError,
  placeholderStyle,
  onChange,
  onKeyPress,
  onKeyDown,
  onKeyUp,
  onEnter,
  width,
  height,
  register,
}) => {
  const elementRefs = useRef<ElementRefs>({
    editable: null,
    placeholder: null,
  });

  const displayPlaceholder = useCallback(() => {
    const { editable: editableElement, placeholder: placeholderElement } =
      elementRefs.current;

    if (placeholder) {
      const shouldDisplay = shouldDisplayPlaceholder(editableElement);
      if (shouldDisplay && placeholderElement) {
        Object.assign(placeholderElement.style, {
          display: "initial",
        });
        Object.assign(placeholderElement, {
          textContent: placeholder,
        });
      }
    }
  }, [placeholder]);

  const onPlaceholderFocus = () => {
    const { editable: editableElement } = elementRefs.current;
    if (editableElement) editableElement.focus();
  };

  const handleOnInput = (event: React.FormEvent<HTMLDivElement>) => {
    displayPlaceholder();
    const { textContent } = event.currentTarget;
    if (onChange) onChange(textContent || "");
  };

  const handleOnPaste = async (event: React.ClipboardEvent<HTMLDivElement>) => {
    event.preventDefault();
    const dataType = event.clipboardData.files.length > 0 ? "file" : "text";
    switch (dataType) {
      case "file": {
        const blobFiles = await blobFromFiles(
          Array.from(event.clipboardData.files),
          onPasteFileError
        );
        if (onPasteFile) {
          onPasteFile(blobFiles);
        }
        break;
      }
      case "text": {
        document.execCommand(
          "inserttext",
          false,
          event.clipboardData.getData("text/plain")
        );
        break;
      }
      default:
        break;
    }
  };

  const onEditableElementFocus = () => {
    const { placeholder: placeholderElement } = elementRefs.current;
    if (placeholderElement) {
      Object.assign(placeholderElement.style, {
        display: "none",
      });
    }
  };

  const onEditableElementBlur = () => {
    displayPlaceholder();
  };

  const handleOnKeyPress = useCallback(
    (event: React.KeyboardEvent<HTMLDivElement>) => {
      const { key, shiftKey } = event;
      if (onKeyPress) onKeyPress(event);

      if (key === "Enter" && !shiftKey) {
        event.preventDefault();
        if (onEnter) onEnter(elementRefs.current.editable?.textContent || "");
      }

      if (key === "Enter" && shiftKey) {
        event.preventDefault();
        document.execCommand("insertHTML", false, "\n ");
        document.execCommand("delete");
      }
    },
    [onKeyPress, onEnter]
  );

  const handleOnKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (onKeyDown) onKeyDown(event);
    },
    [onKeyDown]
  );

  const handleOnKeyUp = useCallback(
    (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (onKeyUp) onKeyUp(event);
    },
    [onKeyUp]
  );

  const fireChangeEvent = useCallback((value: string) => {
    const editableElement = elementRefs.current.editable;

    if (editableElement) {
      editableElement.textContent = value;
      if (window.Event) {
        const event = new Event("input", {
          bubbles: true,
          cancelable: true,
        });
        editableElement.dispatchEvent(event);
      } else {
        const event = document.createEvent("HTMLEvents");
        event.initEvent("input", true, true);
        editableElement.dispatchEvent(event);
      }
    }
  }, []);

  useLayoutEffect(() => {
    if (elementRefs.current.editable && defaultValue) {
      fireChangeEvent(defaultValue);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    displayPlaceholder();
  }, [placeholder, displayPlaceholder]);

  useEffect(() => {
    if (register) {
      register({
        getter: () => ({
          value: elementRefs.current.editable?.textContent || "",
        }),
        setter: () => ({
          setValue: (value: string) => {
            if (elementRefs.current.editable) {
              fireChangeEvent(value);
            }
          },
        }),
      });
    }
  }, [register, fireChangeEvent]);

  return (
    <div className="a-editableinput">
      <div
        aria-hidden
        ref={(ref) => {
          elementRefs.current.editable = ref;
        }}
        onBlur={onEditableElementBlur}
        onFocus={onEditableElementFocus}
        className="a-editableinput_content"
        contentEditable
        onInput={handleOnInput}
        onPaste={handleOnPaste}
        style={{ width, height }}
        onKeyDown={handleOnKeyDown}
        onKeyPress={handleOnKeyPress}
        onKeyUp={handleOnKeyUp}
      />
      <span
        ref={(ref) => {
          elementRefs.current.placeholder = ref;
        }}
        style={placeholderStyle}
        tabIndex={-1}
        onFocus={onPlaceholderFocus}
        className="a-editableinput_placeholder"
      />
    </div>
  );
};
