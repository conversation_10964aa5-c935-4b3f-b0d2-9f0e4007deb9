import { useCallback, useEffect, useState } from "react";

import produce from "immer";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { paginationDTO } from "modules/common/pagination";
import {
  GroupEmployeeModel,
  createEmployeeGroupDto,
  CreateEmployeeGroupDtoType,
} from "modules/employee";
import {
  createEmployeeGroup as createEmployeeGroupService,
  getEmployeeGroups,
} from "services/crm/employee";

import RootPageRouter from "../index";

type ModalType = "employeeGroup";

interface State {
  modalState: {
    open: boolean;
    modalType?: ModalType;
  };
  pagination: {
    pageSize: number;
  };
}

export const EmployeeGroupPageVm = () => {
  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      modalType: undefined,
    },
    pagination: {
      pageSize: 10,
    },
  });

  const [getEmployeeGroupsExec, getEmployeeGroupsState] = useAsync(
    useCallback(
      (pageNum: number, pageSize: number) =>
        getEmployeeGroups({ pageNum, pageSize }).then((res) => ({
          employeeGroups: GroupEmployeeModel.createMap(res.data.data),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const { gotoPage, ...employeeGroupPaginationState } = usePagination({
    pageSize: state.pagination.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getEmployeeGroupsExec(page, pageSize),
  });

  const {
    sortedData: employeeGroups,
    toggleSortState: toggleEmployeeGroupsBy,
  } = useSortable({
    data: getEmployeeGroupsState.data?.employeeGroups,
    sortBy: {
      name: (employeeGroup) => employeeGroup.name,
    },
  });

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pagination.pageSize = pageSize;
      })
    );
  }, []);

  const gotoDetailEmployeeGroup = useCallback(
    (groupId: number) =>
      RootPageRouter.gotoChild("groupEmployeeDetail", {
        params: { groupId: groupId?.toString() },
      }),
    []
  );

  const gotoEditEmployeeGroup = useCallback(
    (groupId: number) =>
      RootPageRouter.gotoChild("groupEmployeeDetail", {
        params: { groupId: groupId?.toString() },
        queryString: "?action=edit",
      }),
    []
  );

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize]);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) => {
      return state.modalState.open && state.modalState.modalType === type;
    },
    [state.modalState]
  );

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.modalType = type;
      })
    );
  }, []);

  const handleCloseModal = useCallback(
    () =>
      setState(
        produce((draft) => {
          draft.modalState.open = false;
          draft.modalState.modalType = undefined;
        })
      ),
    []
  );

  const [createEmployeeGroupExec, createEmployeeGroupState] = useAsync(
    createEmployeeGroupService,
    {
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
      onSuccess: useCallback(() => {
        handleCloseModal();
        toastSingleMode({
          type: "success",
          message: "Tạo mới thành công",
        });
        gotoPage(1);
      }, [gotoPage, handleCloseModal]),
    }
  );

  const createEmployeeGroup = useCallback(
    (rawPayload: Partial<CreateEmployeeGroupDtoType>): Promise<unknown> => {
      const employeeGroupPayload = createEmployeeGroupDto({
        ...rawPayload,
      });
      return createEmployeeGroupExec(employeeGroupPayload);
    },
    [createEmployeeGroupExec]
  );

  return {
    loading: getEmployeeGroupsState.loading,
    gotoPage,
    employeeGroupPaginationState,
    employeeGroups: employeeGroups || [],
    toggleEmployeeGroupsBy,
    pageSize: state.pagination.pageSize,
    handleChangePageSize,
    gotoDetailEmployeeGroup,
    gotoEditEmployeeGroup,
    createEmployeeGroup,
    createEmployeeGroupState,
    handleOpenModalByType,
    modalTypeIsOpen,
    handleCloseModal,
  };
};
