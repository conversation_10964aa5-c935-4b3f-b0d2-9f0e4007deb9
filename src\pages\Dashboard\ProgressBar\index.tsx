import { ProgressBarComponent } from "@syncfusion/ej2-react-progressbar";

/* eslint-disable */
export default function ProgressBar() {
	return (
		<div
			style={{
				height: "100%",
				width: "100%",
				display: "flex",
				flexDirection: "column",
			}}
		>
			<div
				className="main"
				style={{ flex: 1, display: "flex", alignItems: "center" }}
			>
				<div
					className="progress-wrapper"
					style={{ display: "flex", alignItems: "center", flex: "1" }}
				>
					<div className="process-bar">
						<ProgressBarComponent
							id="sale-progress"
							type="Circular"
							height="100px"
							value={70}
							showProgressValue
							labelStyle={{ size: "12px" }}
							animation={{
								enable: true,
								duration: 2000,
								delay: 0,
							}}
							theme="Bootstrap"
							trackThickness={12}
							progressThickness={12}
							progressColor="#3498db"
						/>
					</div>
					<div className="title">
						<p style={{ fontSize: "18px", textShadow: "3px 3px 3px #bbb", color: "#3498db", fontWeight: "bolder" }}>
							Sale Target
						</p>
						<ul style={{ color: "#555", fontSize: "12px", listStyle: "none" }}>
							<li>Target: 1000</li>
							<li>Current: 700</li>
						</ul>
					</div>
				</div>
				<div
					className="progress-wrapper"
					style={{ display: "flex", alignItems: "center", flex: "1" }}
				>
					<div className="process-bar">
						<ProgressBarComponent
							id="user-progress"
							type="Circular"
							height="100px"
							value={80}
							showProgressValue
							labelStyle={{ size: "12px" }}
							animation={{
								enable: true,
								duration: 2000,
								delay: 0,
							}}
							theme="Bootstrap"
							trackThickness={12}
							progressThickness={12}
							progressColor="#ff7675"
						/>
					</div>
					<div className="title">
						<p style={{ fontSize: "18px", textShadow: "3px 3px 3px #bbb", color: "#ff7675", fontWeight: "bolder"}}>
							User Target
						</p>
						<ul style={{ color: "#555", fontSize: "12px", listStyle: "none" }}>
							<li>Target: 1000</li>
							<li>Current: 800</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	);
}
