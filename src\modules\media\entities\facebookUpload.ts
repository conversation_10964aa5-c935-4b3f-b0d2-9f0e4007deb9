import { Model, ModelValue, Number, String } from "libs/domain";

export const FacebookUploadMediaSchema = {
  createdAt: String(),
  updatedAt: String(),
  name: String(),
  originalName: String(),
  mimeType: String(),
  size: Number(),
  type: String(),
  mediaId: String(),
};

export const FacebookUploadMediaModel = new Model(FacebookUploadMediaSchema);
export type FacebookUploadMediaEntityType = ModelValue<
  typeof FacebookUploadMediaModel
>;
