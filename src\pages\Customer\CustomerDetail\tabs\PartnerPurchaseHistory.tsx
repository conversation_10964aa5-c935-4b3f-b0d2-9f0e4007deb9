/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { SearchOutlined } from "@ant-design/icons";
import { Flex, Input, Tag, Typography } from "antd";
import Table, { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import _ from "lodash";
import { fromDefaltOptionToData } from "helpers/convertDataToDefaultOption.helper";
import { useGetListDeliveryPartnerWithoutPagination } from "pages/Order/ListDeliveryPartner/hooks/useGetListDeliveryPartner";
import { useGetListEmployeeWithoutPagination } from "services/crm/employee";
import { UseGetOrderByCustomerId } from "services/crm/order/hooks/order-by-customer";
import { useGetOrderStatusWithoutPagination } from "services/crm/order/hooks/order-status.hook";
import { PartnerTabKeys } from "../enums/partnerTabs.enum";

interface PartnerPurchaseHistoryProps {
  activeKey: PartnerTabKeys;
  customerId: string;
}
interface PartnerPurchaseHistoryDataTypes {
  createdDate: Date;
  MaDonNoiBo: string;
  MaDonVanChuyen: string;
  status: number;
  shippingUnit: string;
  employeeName: string;
  employeeId: number;
  partnerName: string;
  partnerId: number;
  partnerPhone: string;
  partnerAdress: string;
  note: string;
  productList: string;
  totalAmount: number;
  totalQuantity: number;
}
const { Text } = Typography;

export default function PartnerPurchaseHistory(
  props: PartnerPurchaseHistoryProps
) {
  const { activeKey, customerId } = props;
  const [searchText, setSearchText] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(5);
  const { orderByCustomerData, loading, orderByCustomerRefetch } =
    UseGetOrderByCustomerId({
      customerId: +customerId,
      pageNum: page,
      pageSize,
    });

  const { orderStatusData, orderStatusRefetch } =
    useGetOrderStatusWithoutPagination({
      searchText: "",
    });
  const { deliveryData, deliveryRefetch } =
    useGetListDeliveryPartnerWithoutPagination();

  const { employeeRefetch, employeeData } =
    useGetListEmployeeWithoutPagination();

  const debouncedSearch = _.debounce((value: string) => {
    setSearchText(value);
  }, 300);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value);
  };

  const columns: ColumnsType<any> = useMemo(() => {
    return [
      {
        title: "Ngày tạo",
        dataIndex: "createdDate",
        key: "createdDate",
        width: 144,
        render: (value) => dayjs(value).format("DD/MM/YYYY hh:mm A"),
      },
      {
        title: "Đơn hàng",
        dataIndex: "code",
        key: "code",
        width: 144,
        align: "center",
        render: (__, record) => {
          const { code, MaDonVanChuyen } = record ?? {};
          return (
            <Flex align="center" justify="center" vertical gap={4}>
              <Tag className="text-center" color="orange">
                {code}
              </Tag>
              <Text className="text-blue-400">{MaDonVanChuyen}</Text>
            </Flex>
          );
        },
      },
      {
        title: "Giao",
        dataIndex: "orderStatusId",
        key: "orderStatusId",
        width: 144,
        align: "center",
        render: (__, record) => {
          const { orderStatusId, shipping } = record ?? {};

          let orderStatus = null;
          let shippingDetail = null;
          if (orderStatusId && _.size(orderStatusData?.data) > 0) {
            orderStatus = fromDefaltOptionToData({
              data: orderStatusData?.data,
              value: orderStatusId,
              valueKey: "orderStatusId",
            });
          }

          if (shipping && _.size(deliveryData?.data) > 0) {
            shippingDetail = fromDefaltOptionToData({
              data: deliveryData?.data,
              value: shipping,
              valueKey: "deliveryPartnerId",
            });
          }
          return (
            <Flex align="center" justify="center" vertical gap={4}>
              {orderStatus && (
                <Tag color={orderStatus?.color}>{orderStatus?.name}</Tag>
              )}
              <Text>{shippingDetail?.name}</Text>
            </Flex>
          );
        },
      },
      {
        title: "Nhân viên",
        dataIndex: "assignedEmployeeId",
        key: "assignedEmployeeId",
        width: 144,
        align: "center",
        render: (value) => {
          let employee = null;
          if (_.size(employeeData?.data) > 0) {
            employee = fromDefaltOptionToData({
              data: employeeData?.data,
              value,
              valueKey: "employeeId",
            });
          }
          return <Text>{employee?.name}</Text>;
        },
      },
      {
        title: "Khách hàng",
        dataIndex: "name",
        key: "name",
        width: 188,
        render: (__, record) => {
          return (
            <Flex vertical gap={4}>
              <Text>{record.name}</Text>
              <Text>{record.phoneNumber}</Text>
            </Flex>
          );
        },
      },
      {
        title: "Địa chỉ",
        dataIndex: "shippingAddressDetail",
        key: "shippingAddressDetail",
        width: 196,
      },
      {
        title: "Ghi chú",
        dataIndex: "note",
        key: "note",
        width: 196,
      },
      {
        title: "Sản phẩm",
        dataIndex: "items",
        key: "items",
        width: 188,

        render: (items) => {
          return (
            <div>
              {items?.map(
                (item: {
                  sku: { code: any };
                  quantity: number;
                  orderItemId: number;
                }) => (
                  <div key={item?.orderItemId}>
                    {`${item?.quantity} ${item?.sku?.code}`}
                  </div>
                )
              )}
            </div>
          );
        },
      },
      {
        title: "Thành tiền (Số lượng)",
        dataIndex: "totalAmount",
        key: "totalAmount",
        width: 144,
        align: "center",
        render: (__, record) => {
          const { totalAmount, itemsQuantity } = record ?? {};
          return (
            <Typography>
              <Text>{totalAmount.toLocaleString("vi-VN")}</Text> (
              <Text>{itemsQuantity}</Text>)
            </Typography>
          );
        },
      },
    ];
  }, [orderStatusData]);

  useEffect(() => {
    orderByCustomerRefetch();
  }, [page, pageSize, searchText]);

  useEffect(() => {
    if (activeKey.includes(PartnerTabKeys.PURCHASE_HISTORY)) {
      orderStatusRefetch();
      deliveryRefetch();
      employeeRefetch();
    }
    // Fetch data based on activeKey
  }, [activeKey]);

  return (
    <div className="py-3 flex flex-col gap-3">
      <Input
        className="w-full md:w-96"
        placeholder="Tìm kiếm"
        allowClear
        onChange={handleInputChange}
        onClear={() => setSearchText("")}
        prefix={<SearchOutlined rev={undefined} />}
      />
      <Table
        loading={loading}
        rowKey={(record) => `${record.orderId}-${record.customerId}`}
        columns={columns}
        dataSource={orderByCustomerData?.data}
        scroll={{
          x: "max-content",
        }}
        pagination={{
          current: page,
          pageSize,
          onChange(offset, limit) {
            setPage(offset);
            setPageSize(limit);
          },
          total: orderByCustomerData?.meta?.totalRecords || 0,
          showSizeChanger: true,
          pageSizeOptions: [5, 10, 20],
          onShowSizeChange(current, size) {
            setPage(1);
            setPageSize(size);
          },
          showTotal(total, range) {
            return `Total: ${range[0]}-${range[1]} of ${total} items`;
          },
          responsive: true,
        }}
      />
    </div>
  );
}
