import { forwardRef, useCallback, useImperativeHandle, useState } from "react";

import { DialogComponent } from "@syncfusion/ej2-react-popups";

import { Button } from "components/atoms/button";
import { PhonefieldHookForm } from "components/atoms/phonefield";
import { PulldownHookForm } from "components/atoms/pulldown";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { Formfield } from "components/molecules/formfield";
import { FormContainer } from "helpers/form";
import useDidMount from "helpers/react-hooks/useDidMount";
import { usePulldownAssignEmployee } from "modules/employee";
import { CreateLeadChatboxPage } from "pages/Chatbox/Default/ConversationDetail/vm";

import { CreateLeadFormPayload, inputValidationSchema } from "./constanst";

export type TNewLeadModal = {
  show(): void;
  hide(): void;
};

type Prop = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onCreated?: (data: any) => void;
};

export const NewLeadModal = forwardRef<TNewLeadModal, Prop>(
  ({ onCreated }, ref) => {
    const [show, setShow] = useState(false);

    useImperativeHandle(ref, () => ({
      show: handleShow,
      hide: handleHide,
    }));

    const { loadMoreEmployee, assignEmployeeOptions, loadMoreEmployeeState } =
      usePulldownAssignEmployee();

    useDidMount(() => {
      loadMoreEmployee();
    });

    const {
      createLead,
      createLeadState,
      registerFormContainer,
      resetFieldsForm,
    } = CreateLeadChatboxPage();

    const handleOnSubmitCreateLead = useCallback(
      (formData: CreateLeadFormPayload) => {
        const { assignedEmployee, ...rest } = formData;
        console.log(formData);
        createLead({
          ...rest,
          assignedEmployeeId:
            assignedEmployee && Number(assignedEmployee.value),
        }).then((res) => {
          if (onCreated) onCreated({ data: [res.data.data] });
        });
      },
      // eslint-disable-next-line
			[createLead]
    );

    const handleShow = () => {
      setShow(true);
    };
    const handleHide = () => {
      setShow(false);
      resetFieldsForm();
    };

    return (
      <DialogComponent
        visible={show}
        showCloseIcon
        close={handleHide}
        height="80vh"
        width="70vw"
        header="Tạo mới lead"
        isModal
        content={() => (
          <FormContainer
            validationSchema={inputValidationSchema}
            onSubmit={handleOnSubmitCreateLead}
            register={registerFormContainer}
          >
            <div className="u-mb-20">
              <Formfield label="Full Name" name="customerName">
                <TextareafieldHookForm
                  placeholder="Tên khách hàng"
                  name="customerName"
                />
              </Formfield>
              <Formfield label="Phone" name="phone">
                <PhonefieldHookForm placeholder="Số điện thoại" name="phone" />
              </Formfield>
              <Formfield
                label="Client Incharge's Name"
                name="clientInchargeName"
              >
                <TextareafieldHookForm
                  placeholder="Client Incharge's Name"
                  name="clientInchargeName"
                />
              </Formfield>
              <Formfield
                label="	Client Incharge's Title"
                name="clientInchargeTitle"
              >
                <TextareafieldHookForm
                  placeholder="Ghi chú"
                  name="clientInchargeTitle"
                />
              </Formfield>
              <Formfield label="Client Incharge's DOB" name="clientInchargeDOB">
                <TextareafieldHookForm
                  placeholder="Client Incharge's DOB"
                  name="clientInchargeDOB"
                />
              </Formfield>
              <Formfield
                label="Client Incharge's Gender"
                name="clientInchargeGender"
              >
                <TextareafieldHookForm
                  placeholder="Client Incharge's Gender"
                  name="clientInchargeGender"
                />
              </Formfield>
              <Formfield
                label="Client Incharge's Phone Number"
                name="clientInchargePhone"
              >
                <TextareafieldHookForm
                  placeholder="Client Incharge's Phone Number"
                  name="clientInchargePhone"
                />
              </Formfield>
              <Formfield
                label="Client Incharge's Email"
                name="clientInchargeEmail"
              >
                <TextareafieldHookForm
                  placeholder="Client Incharge's Email"
                  name="clientInchargeEmail"
                />
              </Formfield>
              <Formfield label="Client Review's Name" name="clientReviewName">
                <TextareafieldHookForm
                  placeholder="Client Review's Name"
                  name="clientReviewName"
                />
              </Formfield>
              <Formfield label="Client Review's Title" name="clientReviewTitle">
                <TextareafieldHookForm
                  placeholder="Client Review's Title"
                  name="clientReviewTitle"
                />
              </Formfield>
              <Formfield label="Client Review's DOB" name="clientReviewDOB">
                <TextareafieldHookForm
                  placeholder="Client Review's DOB"
                  name="clientReviewDOB"
                />
              </Formfield>
              <Formfield
                label="Client Review's Gender"
                name="clientReviewGender"
              >
                <TextareafieldHookForm
                  placeholder="Client Review's Gender"
                  name="clientReviewGender"
                />
              </Formfield>
              <Formfield
                label="Client Review's Phone Number"
                name="clientReviewPhone"
              >
                <TextareafieldHookForm
                  placeholder="Client Review's Phone Number"
                  name="clientReviewPhone"
                />
              </Formfield>
              <Formfield label="Client Review's Email" name="clientReviewEmail">
                <TextareafieldHookForm
                  placeholder="Client Review's Email"
                  name="clientReviewEmail"
                />
              </Formfield>

              <Formfield label="Assigned Employee" name="assignedEmployee">
                <PulldownHookForm
                  name="assignedEmployee"
                  isClearable
                  isSearchable
                  placeholder="Mã nhân viên"
                  isLoading={loadMoreEmployeeState.loading}
                  options={assignEmployeeOptions}
                />
              </Formfield>
            </div>
            <div className="d-flex justify-content-center">
              <Button modifiers="secondary" onClick={handleHide}>
                Hủy
              </Button>
              <div className="u-ml-16">
                <Button
                  type="submit"
                  disabled={createLeadState.loading}
                  isLoading={createLeadState.loading}
                >
                  Lưu
                </Button>
              </div>
            </div>
          </FormContainer>
        )}
      />
    );
  }
);
