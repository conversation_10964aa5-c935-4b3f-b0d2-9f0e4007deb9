import {
  Array,
  ExtendSchema,
  Model,
  ModelValue,
  Number,
  String,
} from "libs/domain";

export const ItemStockLocationSchema = {
  id: Number(),
  itemCode: String(),
  skuCode: String(),
  whCode: String(),
  storeCode: String(),
  onhandQty: Number(),
  bookingQty: Number(),
  orderQty: Number(),
  stockQty: Number(),
  deletedAt: String(),
  skuId: Number(),
  productId: Number(),
  storeName: String(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  createdAt: (raw: any) => raw && new Date(raw),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => raw && new Date(raw),
};

export const ItemStockLocationModel = new Model(ItemStockLocationSchema);
export type ItemStockLocationEntityType = ModelValue<
  typeof ItemStockLocationModel
>;
