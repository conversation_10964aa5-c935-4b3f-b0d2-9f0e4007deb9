/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/jsx-props-no-spreading */
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  ExportOutlined,
  PlusOutlined,
  SaveOutlined,
  SearchOutlined,
  SettingFilled,
} from "@ant-design/icons";
import {
  Checkbox,
  Col,
  Collapse,
  CollapseProps,
  DatePicker,
  Dropdown,
  Form,
  Input,
  InputNumber,
  Row,
  Space,
  Typography,
} from "antd";
import { DefaultOptionType } from "antd/es/select";
import { Dayjs } from "dayjs";
import _ from "lodash";
import { COLOR } from "constants/color";
import { orderTypeOptions } from "modules/order";
import { paymentMethodOptions } from "modules/order/shared/presentational/PaymentOrderModal";
import { BaseButton } from "../button/BaseButton";
import { BaseSelect } from "../select/BaseSelect";
import BaseSelectGroupCustomer from "../select/shared/BaseGroupCustomer";
import BaseSelectCity from "../select/shared/BaseSelectCity.share";
import BaseSelectDistrict from "../select/shared/BaseSelectDistrict.share";

export type FilterType = {
  orderType: number[]; // LOẠI ĐƠN HÀNG
  orderStatusId?: number[] | string[]; // TRẠNG THÁI ĐƠN HÀNG
  orderStatusTaglineId?: number[] | string[]; // TAGLINE TRẠNG THÁI ĐƠN HÀNG
  dateChangeStatus?: {
    statusId?: number | string; // Checkbox for date change status
    rangeDateChange?: Dayjs[]; // Range date for change status
  }; // NGÀY CHUYỂN TRẠNG THÁI
  dateChangeShop?: Dayjs[]; // NGÀY CHUYỂN SHOP
  orderSourceId?: number[] | string[]; // NGUỒN ĐƠN
  changeOrderDate?: Dayjs[]; // NGÀY ĐỔI HÀNG
  orderReturnStatusId?: number[] | string[]; // TRẠNG THÁI ĐỔI TRẢ
  gender?: number[] | string[]; // GIỚI TÍNH
  successDate?: {
    checked?: boolean;
    rangeDateSuccess?: Dayjs[];
  }; // NGÀY THÀNH CÔNG
  groupCustomer?: number[] | string[]; // NHÓM KHÁCH HÀNG
  birthday?: Dayjs; // SINH NHẬT
  dateOfBirth?: Dayjs; // NGÀY SINH
  MonthOfBirth: Dayjs; // THÁNG SINH
  city?: number; // TỈNH/THÀNH PHỐ
  district?: number; // QUẬN/HUYỆN
  ward?: number; // PHƯỜNG/XÃ
  inputSearch?: string; // input_search_fields
  customerType?: number; // LOẠI KHÁCH HÀNG
  purchaseNumberRange?: {
    from: number;
    to: number;
  }; // SỐ LƯỢNG MUA HÀNG
  totalPurchaseAmount?: {
    from: string;
    to: string;
  }; // SỐ TIỀN ĐÃ MUA
  createdAt?: Dayjs[]; // NGÀY TẠO
  paymentMethod?: string; // PHƯƠNG THỨC THANH TOÁN
  deliveryMethod?: string[] | number[]; // PHƯƠNG THỨC GIAO HÀNG
  employeeId?: number[] | string[]; // NHÂN VIÊN
  franchiseStore?: number[] | string[]; // SHOP NHƯỢNG QUYỀN
  orderReturnStatusTaglineId?: number[] | string[]; // TAGLINE ĐỔI TRẢ
  rangeDateObject?: {
    from?: Dayjs; // NGÀY BẮT ĐẦU
    to?: Dayjs; // NGÀY KẾT THÚC
  }; // ĐỐI TƯỢNG NGÀY
  rangeDate?: Dayjs[]; // KHOẢNG NGÀY
};

export type ButtonKey = "add" | "update" | "export" | "search";
type ButtonAction = (() => void) | (() => void)[];
interface FilterPickerCollapseProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onFinish: (values: any) => void;
  customLabels?: Partial<Record<keyof FilterType, string>>;
  isRequireFields?: (keyof FilterType)[];
  storageKey?: string; // Corrected typo from "storeageKey" to "storage
  buttonActions?: Partial<Record<ButtonKey, ButtonAction>>;
  defaultSelectedKeys?: (keyof FilterType)[]; // Default selected keys for fields
  listFieldsUsed?: (keyof FilterType)[]; // All available fields for selection
  listOptions?: Partial<Record<keyof FilterType, DefaultOptionType[]>>;
  onChange?: Partial<Record<keyof FilterType, (value?: any) => void>>;
}

export const DataGender = [
  {
    label: "Nam",
    value: 1,
  },
  {
    label: "Nữ",
    value: 0,
  },
];

export interface FilterPickerCollapseRef {
  open: () => void;
  close: () => void;
}

interface FilterField {
  key: keyof FilterType;
  label?: string;
  noName?: boolean;

  // rules?: Array<{
  //   required?: boolean;
  //   message?: string;
  // }>;
  noFormItem?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  rules?: any[];
  component: React.ReactNode;
  valuePropName?: string;
}

export const FilterPickerCollapse = forwardRef<
  FilterPickerCollapseRef,
  FilterPickerCollapseProps
>(
  (
    {
      onFinish,
      customLabels,
      isRequireFields,
      storageKey = "filter-picker-settings",
      buttonActions,
      defaultSelectedKeys,
      listFieldsUsed,
      listOptions,
      onChange,
    },
    ref
  ) => {
    const [formFilter] = Form.useForm();
    const [activeKey, setActiveKey] = useState<string[]>(["filter"]);
    const isFilterOpen = _.includes(activeKey, "filter");
    const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedFieldsOrder, setSelectedFieldsOrder] = useState<string[]>(
      []
    );

    const cityIdValue = Form.useWatch("cityId", formFilter);

    useEffect(() => {
      let initialKeys: string[] = [];
      let initialOrder: string[] = [];

      const savedSettings = localStorage.getItem(storageKey);
      if (savedSettings) {
        try {
          const {
            selectedKeys: savedSelectedKeys,
            selectedFieldsOrder: savedSelectedFieldsOrder,
          } = JSON.parse(savedSettings);

          const filteredSavedKeys =
            savedSelectedKeys?.filter((key: keyof FilterType) =>
              listFieldsUsed ? listFieldsUsed.includes(key) : false
            ) || [];

          const filteredSavedOrder =
            savedSelectedFieldsOrder?.filter((key: keyof FilterType) =>
              listFieldsUsed ? listFieldsUsed.includes(key) : false
            ) || [];
          // const filteredSavedKeys = savedSelectedKeys || [];
          // const filteredSavedOrder = savedSelectedFieldsOrder || [];
          if (filteredSavedKeys && filteredSavedKeys.length > 0) {
            initialKeys = filteredSavedKeys;
            initialOrder = filteredSavedOrder || filteredSavedKeys;
          }
        } catch (error) {
          console.error("Error loading filter settings:", error);
        }
      }

      if (!initialKeys.length && defaultSelectedKeys) {
        const validDefaultKeys = defaultSelectedKeys.filter((key) =>
          listFieldsUsed ? listFieldsUsed.includes(key) : true
        );
        initialKeys = validDefaultKeys;
        initialOrder = validDefaultKeys;
      }

      setSelectedKeys(initialKeys);
      setSelectedFieldsOrder(initialOrder);
    }, [storageKey]);

    // XỬ LÝ THAY ĐỔI THÁNG SINH -- NGÀY SINH //
    // START
    // const handleMonthChange = (value: Dayjs | null) => {
    //   if (!value) return;
    //   const firstDayOfMonth = value.startOf("month");
    //   const lastDayOfMonth = value.endOf("month");

    //   // Lấy giá trị ngày sinh hiện tại
    //   const currentDob = formFilter.getFieldValue("dateOfBirth");

    //   let newDob = null;

    //   // Nếu ngày sinh hiện tại nằm ngoài tháng đã chọn → reset về đầu tháng
    //   if (
    //     !currentDob ||
    //     currentDob.isBefore(firstDayOfMonth) ||
    //     currentDob.isAfter(lastDayOfMonth)
    //   ) {
    //     newDob = firstDayOfMonth;
    //   }

    //   // Set lại giá trị dateOfBirth
    //   formFilter.setFieldValue("dateOfBirth", newDob);
    // };

    // const disabledDateByMonth = (current: Dayjs) => {
    //   const selectedMonth = formFilter.getFieldValue("MonthOfBirth");

    //   if (!selectedMonth) return false;

    //   const startOfMonth = selectedMonth.startOf("month");
    //   const endOfMonth = selectedMonth.endOf("month");

    //   return (
    //     current.isBefore(startOfMonth, "day") ||
    //     current.isAfter(endOfMonth, "day")
    //   );
    // };
    // END
    const runActions = (action?: ButtonAction) => {
      if (!action) return;
      if (Array.isArray(action)) {
        action.forEach((fn) => fn());
      } else {
        action();
      }
    };

    useEffect(() => {
      const settings = {
        selectedKeys: selectedKeys.map(String),
        selectedFieldsOrder: selectedFieldsOrder.map(String),
      };

      // console.log("Saving to localStorage:", settings);
      localStorage.setItem(storageKey, JSON.stringify(settings));
    }, [selectedKeys, selectedFieldsOrder, storageKey]);

    useImperativeHandle(ref, () => ({
      open: () => {
        setActiveKey(["filter"]);
      },
      close: () => {
        setActiveKey([]);
      },
    }));

    const isFieldRequired = useCallback(
      (field: keyof FilterType): boolean => {
        return isRequireFields?.includes(field) ?? false;
      },
      [isRequireFields]
    );

    const filterFields = useMemo<FilterField[]>(
      () => [
        {
          key: "orderType",
          label: customLabels?.orderType || "Loại đơn hàng",
          rules: isFieldRequired("orderType")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn loại đơn hàng",
                },
              ]
            : [],
          component: (
            <BaseSelect
              mode="multiple"
              showSelectAll
              placeholder="Chọn loại đơn hàng"
              options={listOptions?.orderType || orderTypeOptions}
            />
          ),
        },
        {
          key: "orderStatusId",
          label: customLabels?.orderStatusId || "Trạng thái đơn hàng",
          rules: isFieldRequired("orderStatusId")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn trạng thái đơn hàng",
                },
              ]
            : [],
          component: (
            <BaseSelect
              mode="multiple"
              showSelectAll
              placeholder="Chọn trạng thái đơn hàng"
              options={listOptions?.orderStatusId || []}
              onChange={(value) => {
                onChange?.orderStatusId?.(value);
              }}
            />
          ),
        },
        {
          key: "orderStatusTaglineId",
          label:
            customLabels?.orderStatusTaglineId || "Tagline trạng thái đơn hàng",
          rules: isFieldRequired("orderStatusTaglineId")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn tagline trạng thái đơn hàng",
                },
              ]
            : [],
          component: (
            <BaseSelect
              mode="multiple"
              showSelectAll
              placeholder="Chọn tagline trạng thái đơn hàng"
              options={listOptions?.orderStatusTaglineId || []}
            />
          ),
        },
        {
          key: "inputSearch",
          label: customLabels?.inputSearch || "Mã đơn hàng",
          rules: isFieldRequired("inputSearch")
            ? [
                {
                  required: true,
                  message: `Vui lòng nhập ${
                    customLabels?.inputSearch || "mã đơn hàng"
                  }`,
                },
              ]
            : [],
          component: (
            <Input
              placeholder={`Nhập ${customLabels?.inputSearch || "mã đơn hàng"}`}
            />
          ),
        },
        {
          key: "dateChangeStatus",
          label: customLabels?.dateChangeStatus || "Ngày chuyển trạng thái",
          rules: isFieldRequired("dateChangeStatus")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn ngày chuyển trạng thái",
                },
              ]
            : [],
          noName: true,
          component: (
            <Row gutter={[16, 0]}>
              <Col span={10}>
                <Form.Item
                  className="mb-0"
                  name={["dateChangeStatus", "statusId"]}
                  rules={
                    isFieldRequired("dateChangeStatus")
                      ? [
                          {
                            required: true,
                            message: "Vui lòng chọn trạng thái",
                          },
                        ]
                      : []
                  }
                >
                  <BaseSelect
                    allowClear
                    placeholder="Chọn trạng thái"
                    options={listOptions?.dateChangeStatus || []}
                  />
                </Form.Item>
              </Col>
              <Col span={14}>
                <Form.Item
                  className="mb-0"
                  name={["dateChangeStatus", "rangeDateChange"]}
                  rules={
                    isFieldRequired("dateChangeStatus")
                      ? [
                          {
                            required: true,
                            message: "Vui lòng chọn khoảng thời gian",
                          },
                        ]
                      : []
                  }
                >
                  <DatePicker.RangePicker
                    style={{ width: "100%" }}
                    format={["DD/MM/YYYY", "DD/MM/YYYY"]}
                    placeholder={["Từ ngày", "Đến ngày"]}
                  />
                </Form.Item>
              </Col>
            </Row>
          ),
        },
        {
          key: "dateChangeShop",
          label: customLabels?.dateChangeShop || "Ngày chuyển shop",
          rules: isFieldRequired("dateChangeShop")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn ngày chuyển shop",
                },
              ]
            : [],
          component: (
            <DatePicker.RangePicker
              style={{ width: "100%" }}
              format={["DD/MM/YYYY", "DD/MM/YYYY"]}
              placeholder={["Từ ngày", "Đến ngày"]}
            />
          ),
        },
        {
          key: "orderSourceId",
          label: customLabels?.orderSourceId || "Nguồn đơn",
          rules: isFieldRequired("orderSourceId")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn nguồn đơn",
                },
              ]
            : [],
          component: (
            <BaseSelect
              placeholder="Chọn nguồn đơn"
              options={listOptions?.orderSourceId || []}
              mode="multiple"
              showSelectAll
            />
          ),
        },
        {
          key: "changeOrderDate",
          label: customLabels?.changeOrderDate || "Ngày đổi hàng",
          rules: isFieldRequired("changeOrderDate")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn ngày đổi hàng",
                },
              ]
            : [],
          component: (
            <DatePicker.RangePicker
              style={{ width: "100%" }}
              format={["DD/MM/YYYY", "DD/MM/YYYY"]}
              placeholder={["Từ ngày", "Đến ngày"]}
            />
          ),
        },
        {
          key: "orderReturnStatusId",
          label: customLabels?.orderReturnStatusId || "Trạng thái đổi trả",
          rules: isFieldRequired("orderReturnStatusId")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn trạng thái đổi trả",
                },
              ]
            : [],
          component: (
            <BaseSelect
              mode="multiple"
              showSelectAll
              placeholder="Chọn trạng thái đổi trả"
              options={listOptions?.orderReturnStatusId || []}
            />
          ),
        },
        {
          key: "orderReturnStatusTaglineId",
          label: customLabels?.orderReturnStatusTaglineId || "Tagline đổi trả",
          rules: isFieldRequired("orderReturnStatusTaglineId")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn tagline đổi trả",
                },
              ]
            : [],
          component: (
            <BaseSelect
              mode="multiple"
              showSelectAll
              placeholder="Chọn tagline đổi trả"
              options={listOptions?.orderReturnStatusTaglineId || []}
            />
          ),
        },

        {
          key: "gender",
          label: customLabels?.gender || "Giới tính",
          rules: isFieldRequired("gender")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn giới tính",
                },
              ]
            : [],
          component: (
            <BaseSelect
              placeholder="Chọn giới tính"
              options={DataGender}
              fieldNames={{ label: "label", value: "value" }}
            />
          ),
        },
        {
          key: "successDate",
          label: customLabels?.successDate || "Ngày thành công",
          noName: true,
          component: (
            <div className="grid grid-cols-[max-content_1fr] items-center gap-4">
              <Form.Item
                className="mb-0"
                name={["successDate", "checked"]}
                valuePropName="checked"
                noStyle
              >
                <Checkbox />
              </Form.Item>

              <Form.Item
                className="mb-0"
                name={["successDate", "rangeDateSuccess"]}
              >
                <DatePicker.RangePicker
                  format={["DD/MM/YYYY", "DD/MM/YYYY"]}
                  placeholder={["Từ ngày", "Đến ngày"]}
                />
              </Form.Item>
            </div>
          ),
        },
        {
          key: "groupCustomer",
          label: customLabels?.groupCustomer || "Nhóm khách hàng",
          rules: isFieldRequired("groupCustomer")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn nhóm khách hàng",
                },
              ]
            : [],
          component: (
            // <BaseSelect
            //   placeholder="Chọn nhóm khách hàng"
            //   options={listOptions?.groupCustomer || []}
            // />
            <BaseSelectGroupCustomer placeholder="Chọn nhóm khách hàng" />
          ),
        },
        {
          key: "customerType",
          label: customLabels?.customerType || "Loại khách hàng",
          rules: isFieldRequired("customerType")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn loại khách hàng",
                },
              ]
            : [],
          component: (
            <BaseSelect
              // mode="multiple"
              // showSelectAll
              placeholder="Chọn loại khách hàng"
              options={listOptions?.customerType || []}
            />
          ),
        },
        {
          key: "birthday",
          label: customLabels?.birthday || "Sinh nhật",
          rules: isFieldRequired("birthday")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn ngày sinh",
                },
              ]
            : [],
          component: (
            <DatePicker
              style={{ width: "100%" }}
              format="DD/MM/YYYY"
              placeholder={customLabels?.birthday || "Chọn ngày sinh"}
            />
          ),
        },
        {
          key: "city",
          label: customLabels?.city || "Tỉnh/TP",
          rules: isFieldRequired("city")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn tỉnh/thành phố",
                },
              ]
            : [],
          component: (
            <BaseSelect
              placeholder={customLabels?.city || "Chọn tỉnh/TP"}
              options={listOptions?.city || []}
              onChange={(value) => {
                onChange?.city?.(value);
              }}
            />
          ),
        },
        {
          key: "district",
          label: customLabels?.district || "Phường/Xã",
          rules: isFieldRequired("district")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn phường/xã",
                },
              ]
            : [],
          component: (
            <BaseSelect
              placeholder={customLabels?.district || "Chọn phường/xã"}
              options={listOptions?.district || []}
            />
          ),
        },
        {
          key: "ward",
          label: customLabels?.ward || "Phường/Xã",
          rules: isFieldRequired("ward")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn phường/xã",
                },
              ]
            : [],
          component: (
            <BaseSelect
              placeholder={customLabels?.ward || "Chọn phường/xã"}
              options={listOptions?.ward || []}
            />
          ),
        },
        {
          key: "purchaseNumberRange",
          label: customLabels?.purchaseNumberRange || "Số lần mua",
          rules: isFieldRequired("purchaseNumberRange")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn số lần mua hàng",
                },
              ]
            : [],
          noName: true,
          component: (
            <Space
              direction="horizontal"
              styles={{
                item: {
                  width: "100%",
                },
              }}
              size={16}
              style={{ width: "100%" }}
            >
              <Form.Item
                className="mb-0"
                name={["purchaseNumberRange", "from"]}
                rules={
                  isFieldRequired("purchaseNumberRange")
                    ? [
                        {
                          required: true,
                          message: "Không được để trống",
                        },
                      ]
                    : []
                }
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="Số lần mua từ"
                  min={0}
                />
              </Form.Item>
              <Form.Item
                className="mb-0"
                name={["purchaseNumberRange", "to"]}
                rules={
                  isFieldRequired("purchaseNumberRange")
                    ? [
                        {
                          required: true,
                          message: "Không được để trống",
                        },
                      ]
                    : []
                }
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="Số lần mua đến"
                  min={0}
                />
              </Form.Item>
            </Space>
          ),
        },
        {
          key: "totalPurchaseAmount",
          label: customLabels?.totalPurchaseAmount || "Tổng số tiền đã mua",
          noName: true,
          component: (
            <Space
              styles={{
                item: {
                  width: "100%",
                },
              }}
              direction="horizontal"
              size={16}
              style={{ width: "100%" }}
            >
              <Form.Item
                className="mb-0"
                name={["totalPurchaseAmount", "from"]}
                rules={
                  isFieldRequired("purchaseNumberRange")
                    ? [
                        {
                          required: true,
                          message: "Không được để trống",
                        },
                      ]
                    : []
                }
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="Số tiền từ"
                  min={0}
                  formatter={(value) =>
                    value !== undefined && value !== null
                      ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                      : ""
                  }
                />
              </Form.Item>
              <Form.Item
                className="mb-0"
                name={["totalPurchaseAmount", "to"]}
                rules={
                  isFieldRequired("totalPurchaseAmount")
                    ? [
                        {
                          required: true,
                          message: "Không được để trống",
                        },
                      ]
                    : []
                }
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="Số tiền đến"
                  min={0}
                  formatter={(value) =>
                    value !== undefined && value !== null
                      ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                      : ""
                  }
                />
              </Form.Item>
            </Space>
          ),
        },
        {
          key: "dateOfBirth",
          label: customLabels?.dateOfBirth || "Ngày sinh",
          noFormItem: true,
          component: (
            <Form.Item
              name="dateOfBirth"
              label={customLabels?.dateOfBirth || "Ngày sinh"}
              rules={
                isFieldRequired("dateOfBirth")
                  ? [
                      {
                        required: true,
                        message: "Vui lòng chọn ngày sinh",
                      },
                    ]
                  : []
              }
            >
              <BaseSelect
                options={listOptions?.dateOfBirth || []}
                placeholder="Chọn ngày sinh"
                style={{ width: "100%" }}
              />
              {/* <DatePicker
                style={{ width: "100%" }}
                placeholder="Chọn ngày sinh"
                format="DD"
                showNow={false}
                picker="date"
                // disabledDate={disabledDateByMonth}
              /> */}
            </Form.Item>
          ),
        },
        {
          key: "MonthOfBirth",
          label: customLabels?.MonthOfBirth || "Tháng sinh",
          noFormItem: true,
          component: (
            <Form.Item
              name="MonthOfBirth"
              label={customLabels?.MonthOfBirth || "Tháng sinh"}
              rules={
                isFieldRequired("MonthOfBirth")
                  ? [
                      {
                        required: true,
                        message: "Vui lòng chọn tháng sinh",
                      },
                    ]
                  : []
              }
            >
              <BaseSelect
                options={listOptions?.MonthOfBirth || []}
                placeholder="Chọn tháng sinh"
                style={{ width: "100%" }}
                onChange={(value) => {
                  onChange?.MonthOfBirth?.(value);
                }}
              />
              {/* <DatePicker
                style={{ width: "100%" }}
                placeholder="Chọn tháng sinh"
                picker="month"
                format="MM"
                onChange={(value) => handleMonthChange(value)}
              /> */}
            </Form.Item>
          ),
        },
        {
          key: "createdAt",
          label: customLabels?.createdAt || "Ngày tạo",
          rules: isFieldRequired("createdAt")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn ngày tạo",
                },
              ]
            : [],
          // component: <DatePicker.RangePicker style={{ width: "100%" }} />,
          component: (
            <DatePicker.RangePicker
              format={["DD/MM/YYYY", "DD/MM/YYYY"]}
              placeholder={["Chọn ngày bắt đầu", "Chọn ngày kết thúc"]}
              style={{ width: "100%" }}
            />
          ),
        },
        {
          key: "paymentMethod",
          label: customLabels?.paymentMethod || "Phương thức thanh toán",
          rules: isFieldRequired("paymentMethod")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn phương thức thanh toán",
                },
              ]
            : [],
          component: (
            <BaseSelect
              placeholder="Chọn phương thức thanh toán"
              options={paymentMethodOptions}
              // fieldNames={{
              //   label: "payment_method_name",
              //   value: "payment_method_id",
              // }}
            />
          ),
        },
        {
          key: "deliveryMethod",
          label: customLabels?.deliveryMethod || "Hình thức giao hàng",
          rules: isFieldRequired("deliveryMethod")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn hình thức giao hàng",
                },
              ]
            : [],
          component: (
            <BaseSelect
              placeholder="Chọn hình thức giao hàng"
              options={listOptions?.deliveryMethod || []}
              mode="multiple"
              showSelectAll
              // fieldNames={{
              //   label: "deliveryMethod_name",
              //   value: "deliveryMethod_id",
              // }}
            />
          ),
        },
        {
          key: "employeeId",
          label: customLabels?.employeeId || "Nhân viên",
          rules: isFieldRequired("employeeId")
            ? [
                {
                  required: true,
                  message: `Vui lòng chọn ${
                    customLabels?.employeeId || "nhân viên"
                  }`,
                },
              ]
            : [],
          component: (
            <BaseSelect
              mode="multiple"
              showSelectAll
              placeholder="Chọn nhân viên"
              options={listOptions?.employeeId || []}
            />
          ),
        },
        {
          key: "franchiseStore",
          label: customLabels?.franchiseStore || "Cửa hàng nhượng quyền",
          rules: isFieldRequired("franchiseStore")
            ? [
                {
                  required: true,
                  message: `Vui lòng chọn ${
                    customLabels?.inputSearch || "cửa hàng nhượng quyền"
                  }`,
                },
              ]
            : [],
          component: (
            <BaseSelect
              placeholder="Chọn cửa hàng nhượng quyền"
              options={listOptions?.franchiseStore || []}
              mode="multiple"
              showSelectAll
            />
          ),
        },
        {
          key: "rangeDateObject",
          label: customLabels?.rangeDateObject || "Từ ngày - đến ngày",
          noName: true,
          rules: isFieldRequired("rangeDateObject")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn khoảng ngày",
                },
              ]
            : [],
          component: (
            <Space
              direction="horizontal"
              styles={{
                item: {
                  width: "100%",
                },
              }}
              size={16}
              style={{ width: "100%" }}
            >
              <Form.Item
                className="mb-0"
                name={["rangeDateObject", "from"]}
                rules={
                  isFieldRequired("rangeDateObject")
                    ? [
                        {
                          required: true,
                          message: "Không được để trống",
                        },
                      ]
                    : []
                }
              >
                <DatePicker
                  className="w-full"
                  format="DD/MM/YYYY"
                  placeholder="Từ ngày"
                />
              </Form.Item>

              <Form.Item
                className="mb-0"
                name={["rangeDateObject", "to"]}
                rules={
                  isFieldRequired("rangeDateObject")
                    ? [
                        {
                          required: true,
                          message: "Không được để trống",
                        },
                      ]
                    : []
                }
              >
                <DatePicker
                  className="w-full"
                  format="DD/MM/YYYY"
                  placeholder="Đến ngày"
                />
              </Form.Item>
            </Space>
          ),
        },
        {
          key: "rangeDate",
          label: customLabels?.rangeDate || "Khoảng ngày",
          rules: isFieldRequired("rangeDate")
            ? [
                {
                  required: true,
                  message: "Vui lòng chọn khoảng ngày",
                },
              ]
            : [],
          component: (
            <DatePicker.RangePicker
              style={{ width: "100%" }}
              placeholder={["Chọn ngày bắt đầu", "Chọn ngày kết thúc"]}
              format={["DD/MM/YYYY", "DD/MM/YYYY"]}
            />
          ),
        },
      ],
      [customLabels, isFieldRequired]
    );

    const availableFields = useMemo(() => {
      return listFieldsUsed
        ? filterFields.filter((field) => listFieldsUsed.includes(field.key))
        : filterFields;
    }, [filterFields, listFieldsUsed]);

    const handleCheckboxChange = useCallback(
      (fieldKey: string, checked: boolean) => {
        // console.log("Checkbox changed:", fieldKey, checked);

        if (checked) {
          setSelectedKeys((prev) => [...prev, fieldKey]);

          setSelectedFieldsOrder((prev) =>
            prev.includes(fieldKey) ? prev : [...prev, fieldKey]
          );
        } else {
          setSelectedKeys((prev) => prev.filter((k) => k !== fieldKey));

          setSelectedFieldsOrder((prev) => prev.filter((k) => k !== fieldKey));
        }
      },
      []
    );

    const moveFieldUp = useCallback(
      (fieldKey: string) => {
        // console.log("Move up:", fieldKey);
        const currentIndex = selectedFieldsOrder.indexOf(fieldKey);

        if (currentIndex > 0) {
          const newOrder = [...selectedFieldsOrder];
          // Swap with previous item
          [newOrder[currentIndex - 1], newOrder[currentIndex]] = [
            newOrder[currentIndex],
            newOrder[currentIndex - 1],
          ];
          setSelectedFieldsOrder(newOrder);
        }
      },
      [selectedFieldsOrder]
    );

    const moveFieldDown = useCallback(
      (fieldKey: string) => {
        // console.log("Move down:", fieldKey);
        const currentIndex = selectedFieldsOrder.indexOf(fieldKey);

        if (currentIndex < selectedFieldsOrder.length - 1) {
          const newOrder = [...selectedFieldsOrder];
          // Swap with next item
          [newOrder[currentIndex], newOrder[currentIndex + 1]] = [
            newOrder[currentIndex + 1],
            newOrder[currentIndex],
          ];
          setSelectedFieldsOrder(newOrder);
        }
      },
      [selectedFieldsOrder]
    );

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const onFinishCallBack = (values: any) => {
      onFinish(values);
    };

    const visibleFilterFields = useMemo(() => {
      // console.log("Computing visible fields:", {
      //   selectedKeys,
      //   selectedFieldsOrder,
      // });

      return selectedFieldsOrder
        .filter((key) => selectedKeys.includes(key))
        .map((key) => filterFields.find((f) => f.key === key))
        .filter(Boolean);
    }, [selectedKeys, selectedFieldsOrder, filterFields]);

    const orderedSelectedFields = useMemo(() => {
      return selectedFieldsOrder
        .filter((key) => selectedKeys.includes(key))
        .map((key, index) => {
          const field = filterFields.find((f) => f.key === key);
          return { ...field, displayIndex: index };
        });
    }, [selectedFieldsOrder, selectedKeys, filterFields]);

    const itemsCollapse: CollapseProps["items"] = [
      {
        key: "filter",
        label: <Typography>Tìm kiếm</Typography>,
        extra: (
          <Dropdown
            trigger={["click"]}
            onOpenChange={(visible) => setIsDropdownOpen(visible)}
            open={isDropdownOpen}
            popupRender={() => (
              <div
                style={{
                  padding: 0,
                  background: "#fff",
                  borderRadius: 4,
                  boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                  minWidth: 400,
                  // maxHeight: "70vh",
                  // overflowY: "auto",
                }}
              >
                <div className="p-3 sticky top-0 bg-white z-10 border-b border-gray-200">
                  <Typography.Text className="font-semibold">
                    Tùy chọn tìm kiếm
                  </Typography.Text>
                </div>
                <div className="grid grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto">
                  {/* Available Fields Section */}
                  <div className="p-3">
                    <Typography.Text className="text-sm font-medium mb-2 block">
                      Chọn trường hiển thị:
                    </Typography.Text>
                    {availableFields.map((field) => (
                      <div
                        key={field.key}
                        style={{ marginBottom: 8 }}
                        className="flex items-center justify-between"
                      >
                        <Checkbox
                          checked={selectedKeys.includes(field.key)}
                          onChange={(e) =>
                            handleCheckboxChange(field.key, e.target.checked)
                          }
                        >
                          {field.label}
                        </Checkbox>
                        {selectedKeys.includes(field.key) && (
                          <Typography.Text className="text-xs text-gray-500">
                            #{selectedFieldsOrder.indexOf(field.key) + 1}
                          </Typography.Text>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Selected Fields Order Section */}
                  {selectedKeys.length > 0 && (
                    <div className="border-l border-gray-200">
                      <div className="p-3">
                        <Typography.Text className="text-sm font-medium mb-2 block">
                          Thứ tự hiển thị:
                        </Typography.Text>
                        {orderedSelectedFields.map((field, index) => (
                          <div
                            key={field?.key}
                            className="flex items-center justify-between mb-2 p-2 bg-gray-50 rounded"
                          >
                            <Space>
                              <span className="font-mono text-sm text-gray-500 w-6">
                                {index + 1}.
                              </span>
                              <span className="text-sm">{field?.label}</span>
                            </Space>
                            <Space>
                              <BaseButton
                                type="text"
                                size="small"
                                icon={<ArrowUpOutlined rev={undefined} />}
                                disabled={index === 0}
                                onClick={() => moveFieldUp(field?.key || "")}
                                className="hover:bg-blue-100"
                              />
                              <BaseButton
                                type="text"
                                size="small"
                                icon={<ArrowDownOutlined rev={undefined} />}
                                disabled={
                                  index === orderedSelectedFields.length - 1
                                }
                                onClick={() => moveFieldDown(field?.key || "")}
                                className="hover:bg-blue-100"
                              />
                            </Space>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          >
            <BaseButton
              className={`
                border-none text-base
                transition-opacity duration-300 ease-in-out
                hover:opacity-70
                ${
                  isFilterOpen ? "opacity-100" : "opacity-0 pointer-events-none"
                }
              `}
              icon={
                <SettingFilled
                  style={{
                    fontSize: "16px",
                    color: "#000000",
                  }}
                  rev={undefined}
                />
              }
            />
          </Dropdown>
        ),
        children: (
          <Form
            layout="vertical"
            className="[&_.ant-form-item]:!mb-0"
            form={formFilter}
            onFinish={onFinishCallBack}
          >
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 items-end [&>div>label]:mb-2">
              {visibleFilterFields.map((field, index) => (
                <div key={field?.key}>
                  {field?.valuePropName ? (
                    <Form.Item
                      className="mb-0"
                      name={field?.key}
                      noStyle
                      valuePropName={field?.valuePropName}
                    >
                      {field?.component}
                    </Form.Item>
                  ) : field?.noName ? (
                    <Form.Item
                      className="mb-0"
                      label={field.label}
                      rules={field?.rules}
                    >
                      {field?.component}
                    </Form.Item>
                  ) : field.noFormItem ? (
                    field?.component
                  ) : (
                    <Form.Item
                      className="mb-0"
                      label={field?.label}
                      name={field?.key}
                      rules={field?.rules}
                    >
                      {field?.component}
                    </Form.Item>
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-end mt-4 gap-2">
              {buttonActions?.add && (
                <BaseButton
                  type="primary"
                  className="w-fit"
                  bgColor={COLOR.RED[500]}
                  hoverColor={COLOR.RED[700]}
                  icon={<PlusOutlined rev={undefined} />}
                  onClick={() => {
                    runActions(buttonActions?.add);
                  }}
                >
                  Thêm mới
                </BaseButton>
              )}
              {buttonActions?.update && (
                <BaseButton
                  type="primary"
                  className="w-fit"
                  bgColor={COLOR.YELLOW[500]}
                  hoverColor={COLOR.YELLOW[700]}
                  icon={<SaveOutlined rev={undefined} />}
                  onClick={() => runActions(buttonActions?.update)}
                >
                  Cập nhật
                </BaseButton>
              )}
              {buttonActions?.export && (
                <BaseButton
                  type="primary"
                  className="w-fit"
                  bgColor={COLOR.BLUE[500]}
                  hoverColor={COLOR.BLUE[700]}
                  icon={<ExportOutlined rev={undefined} />}
                  onClick={() => runActions(buttonActions?.export)}
                >
                  Excel
                </BaseButton>
              )}
              <BaseButton
                type="primary"
                htmlType="submit"
                bgColor={COLOR.GREEN[500]}
                hoverColor={COLOR.GREEN[700]}
                icon={<SearchOutlined rev={undefined} />}
                onClick={() => runActions(buttonActions?.search)}
              >
                Tìm kiếm
              </BaseButton>
            </div>
          </Form>
        ),
      },
    ];

    return (
      <Collapse
        collapsible="header"
        activeKey={activeKey}
        onChange={(keys) => {
          setActiveKey(keys as string[]);
          if (keys.length === 0) {
            setIsDropdownOpen(false);
          }
        }}
        items={itemsCollapse}
        className="[&_.ant-collapse-header]:!items-center"
      />
    );
  }
);
