import { Button } from "components/atoms/button";
import { Divider } from "components/atoms/divider";
import { Heading } from "components/atoms/heading";
import { Radio } from "components/atoms/radio";
import { Textfield } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Col } from "components/organisms/grid";
import { Modal } from "components/organisms/modal";

export interface FilterEmployeeModalProps {
  open: boolean;
  onClose?: () => void;
  listEmployee: Array<{ name: string; code: string }>;
}

export const FilterEmployeeModal: React.FC<FilterEmployeeModalProps> = ({
  open,
  onClose,
  listEmployee,
}: FilterEmployeeModalProps) => {
  return (
    <Modal
      style={{ content: { maxWidth: 800 } }}
      onCloseModal={onClose}
      isClosable
      isOpen={open}
    >
      <Heading type="h1">LỌC THEO NHÂN VIÊN</Heading>
      <div className="d-flex align-items-center">
        <div className="u-pr-13">
          <Radio name="isWorking" value={1} defaultChecked>
            Đ<PERSON> được phân công
          </Radio>
        </div>
        <div>
          <Radio name="isWorking" value={0} defaultChecked={false}>
            Chưa được phân công
          </Radio>
        </div>
      </div>
      <div className="d-flex align-items-end u-mt-15 u-mb-15 justify-content-between">
        <div className="flex-grow-1">
          <Formfield label="Nhập tên nhân viên" name="name">
            <Textfield name="name" placeholder="Nhập tên nhân viên" />
          </Formfield>
        </div>
        <Col xs={4}>
          <div style={{ width: "fit-content" }} className="ml-auto">
            <Button type="submit">Tìm kiếm</Button>
          </div>
        </Col>
      </div>
      <div style={{ height: "30vh", overflowY: "auto" }} className="u-pr-15">
        {listEmployee &&
          listEmployee.map((item, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <div key={index}>
              <div className="d-flex align-items-end u-mt-10 u-mb-10 justify-content-between">
                <div className="flex-grow-1">{`${item.name} (${item.code})`}</div>
                <Button buttonType="textbutton" type="button">
                  Chọn
                </Button>
              </div>
              <Divider type="dash" />
            </div>
          ))}
      </div>
      <div className="d-flex justify-content-end u-mt-20">
        <div className="u-mr-15">
          <Button buttonType="outline" modifiers="secondary" onClick={onClose}>
            HUỶ
          </Button>
        </div>
      </div>
    </Modal>
  );
};
