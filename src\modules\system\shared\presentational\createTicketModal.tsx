import { <PERSON><PERSON>, <PERSON> } from "antd";
// import { Card } from "react-bootstrap";
import * as Yup from "yup";

import { But<PERSON> } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Icon } from "components/atoms/icon";
import { PulldownHookForm } from "components/atoms/pulldown";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { TextfieldHookForm } from "components/atoms/textfield";
import useUploadFile from "components/hooks/useUploadFile";
import { Calendar } from "components/molecules/calendar";
import { Formfield } from "components/molecules/formfield";
import { Col, Row } from "components/organisms/grid";
// import { Modal } from "components/organisms/modal";

import { Section } from "components/organisms/section";
import { FormContainer } from "helpers/form";

interface CreateTicketModalProps {
  open: boolean;
  onClose: () => void;
  CreateTikcetValidation: Yup.ObjectSchema<Yup.AnyObject>;
}

export const CreateTicketModal = ({
  open,
  onClose,
  CreateTikcetValidation,
}: CreateTicketModalProps) => {
  const { refInput, fileUploadMulti, onChangeFileUpload, handleRemoveFile } =
    useUploadFile({
      extensions: ["jpg", "jpeg", "png"],
      fileSize: 1,
      keepPrevData: true,
    });

  return (
    <Modal
      title="Tạo mới Ticket"
      open={open}
      // onOk={handleOk}
      onCancel={onClose}
    >
      <Card type="inner" title="Chi tiết TICKET">
        <Section>
          <div style={{ backgroundColor: "white" }}>
            <FormContainer validationSchema={CreateTikcetValidation}>
              <Row>
                <Col lg="12" className="u-mb-15">
                  <Formfield label="Tiêu đề" name="title">
                    <TextfieldHookForm name="title" />
                  </Formfield>
                </Col>
                <Col lg="6" className="u-mb-15">
                  <Formfield label="Mức độ ưu tiên" name="displayOrder">
                    <PulldownHookForm name="displayOrder" />
                  </Formfield>
                </Col>
                <Col lg="6" className="u-mb-15">
                  <Formfield label="Trạng thái" name="status">
                    <PulldownHookForm name="status" />
                  </Formfield>
                </Col>
                <Col lg="6" className="u-mb-15">
                  <Formfield label="Hình thức phân công" name="type">
                    <PulldownHookForm name="type" />
                  </Formfield>
                </Col>
                <Col lg="6" className="u-mb-15">
                  <Formfield label="Phân công" name="assginment">
                    <PulldownHookForm name="assginment" />
                  </Formfield>
                </Col>
                <Col lg="12" className="u-mb-15">
                  <Formfield label="Nội dung" name="content">
                    <TextareafieldHookForm name="content" />
                  </Formfield>
                </Col>
                <Col lg="12" className="u-mb-15">
                  <Formfield label="Ngày kết thúc" name="endDate">
                    <Calendar />
                  </Formfield>
                </Col>
                <Col lg="12" className="u-mb-15">
                  <Formfield label="File đính kèm" name="file">
                    <div
                      style={{
                        border: "1px solid #8C9196",
                        borderStyle: "dotted",
                        borderRadius: 10,
                        padding: 10,
                      }}
                      className="d-flex flex-column align-items-center"
                    >
                      {fileUploadMulti?.map((file, index) => (
                        <div
                          key={Math.random()}
                          style={{
                            border: "1px dashed #ACACAC",
                            borderStyle: "dashed",
                            width: "100%",
                            textAlign: "center",
                            position: "relative",
                          }}
                          className="u-pt-12 u-pb-12 u-mb-15"
                        >
                          <span
                            style={{ color: "#920D10", fontWeight: "bold" }}
                          >
                            {file.name}
                          </span>
                          <Icon
                            iconName="close"
                            style={{
                              backgroundSize: 12,
                              position: "absolute",
                              right: 0,
                              top: 0,
                              cursor: "pointer",
                            }}
                            onClick={() => handleRemoveFile(index)}
                          />
                        </div>
                      ))}

                      <label
                        className="m-filewrapper_button"
                        htmlFor="file-upload"
                      >
                        <input
                          id="file-upload"
                          type="file"
                          ref={refInput}
                          hidden
                          onChange={onChangeFileUpload}
                          multiple
                        />
                        <Icon iconName="upload" />
                        <span className="m-filewrapper_label">Upload</span>
                      </label>
                    </div>
                  </Formfield>
                </Col>
              </Row>
            </FormContainer>
          </div>
        </Section>
      </Card>
    </Modal>
    // <Modal
    //   style={{ content: { maxWidth: 1000 } }}
    //   isOpen={open}
    //   onCloseModal={onClose}
    // >
    //   <Section>
    //     <Heading type="h1" centered>
    //       TẠO MỚI TICKET
    //     </Heading>
    //     <Section>
    //       <FormContainer validationSchema={CreateTikcetValidation}>
    //         <Row>
    //           <Col lg="12" className="u-mb-15">
    //             <Formfield label="Tiêu đề" name="title">
    //               <TextfieldHookForm name="title" />
    //             </Formfield>
    //           </Col>
    //           <Col lg="6" className="u-mb-15">
    //             <Formfield label="Mức độ ưu tiên" name="displayOrder">
    //               <PulldownHookForm name="displayOrder" />
    //             </Formfield>
    //           </Col>
    //           <Col lg="6" className="u-mb-15">
    //             <Formfield label="Trạng thái" name="status">
    //               <PulldownHookForm name="status" />
    //             </Formfield>
    //           </Col>
    //           <Col lg="6" className="u-mb-15">
    //             <Formfield label="Hình thức phân công" name="type">
    //               <PulldownHookForm name="type" />
    //             </Formfield>
    //           </Col>
    //           <Col lg="6" className="u-mb-15">
    //             <Formfield label="Phân công" name="assginment">
    //               <PulldownHookForm name="assginment" />
    //             </Formfield>
    //           </Col>
    //           <Col lg="12" className="u-mb-15">
    //             <Formfield label="Nội dung" name="content">
    //               <TextareafieldHookForm name="content" />
    //             </Formfield>
    //           </Col>
    //           <Col lg="12" className="u-mb-15">
    //             <Formfield label="Ngày kết thúc" name="endDate">
    //               <Calendar />
    //             </Formfield>
    //           </Col>
    //           <Col lg="12" className="u-mb-15">
    //             <Formfield label="File đính kèm" name="file">
    //               <div
    //                 style={{
    //                   border: "1px solid #8C9196",
    //                   borderStyle: "dotted",
    //                   borderRadius: 10,
    //                   padding: 10,
    //                 }}
    //                 className="d-flex flex-column align-items-center"
    //               >
    //                 {fileUploadMulti?.map((file, index) => (
    //                   <div
    //                     key={Math.random()}
    //                     style={{
    //                       border: "1px dashed #ACACAC",
    //                       borderStyle: "dashed",
    //                       width: "100%",
    //                       textAlign: "center",
    //                       position: "relative",
    //                     }}
    //                     className="u-pt-12 u-pb-12 u-mb-15"
    //                   >
    //                     <span style={{ color: "#920D10", fontWeight: "bold" }}>
    //                       {file.name}
    //                     </span>
    //                     <Icon
    //                       iconName="close"
    //                       style={{
    //                         backgroundSize: 12,
    //                         position: "absolute",
    //                         right: 0,
    //                         top: 0,
    //                         cursor: "pointer",
    //                       }}
    //                       onClick={() => handleRemoveFile(index)}
    //                     />
    //                   </div>
    //                 ))}

    //                 <label
    //                   className="m-filewrapper_button"
    //                   htmlFor="file-upload"
    //                 >
    //                   <input
    //                     id="file-upload"
    //                     type="file"
    //                     ref={refInput}
    //                     hidden
    //                     onChange={onChangeFileUpload}
    //                     multiple
    //                   />
    //                   <Icon iconName="upload" />
    //                   <span className="m-filewrapper_label">Upload</span>
    //                 </label>
    //               </div>
    //             </Formfield>
    //           </Col>
    //           <Col lg="12" className="d-flex justify-content-end u-mt-20">
    //             <div className="u-mr-15">
    //               <Button
    //                 buttonType="outline"
    //                 modifiers="secondary"
    //                 onClick={onClose}
    //               >
    //                 Hủy
    //               </Button>
    //             </div>
    //             <Button type="submit">Lưu</Button>
    //           </Col>
    //         </Row>
    //       </FormContainer>
    //     </Section>
    //   </Section>
    // </Modal>
  );
};
