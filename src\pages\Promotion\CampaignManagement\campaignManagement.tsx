/* eslint-disable @typescript-eslint/no-shadow */
import { useCallback, useEffect, useState } from "react";
import {
  DeleteFilled,
  DeleteOutlined,
  EditFilled,
  EditOutlined,
  EyeFilled,
  EyeOutlined,
  PlusOutlined,
  SearchOutlined,
  UserOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Card,
  Col,
  Collapse,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Space,
  Tag,
  Tooltip,
} from "antd";
import Table, { ColumnsType } from "antd/es/table";
import { id } from "date-fns/locale";
import dayjs from "dayjs";
import { data, useNavigate } from "react-router";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { Heading } from "components/atoms/heading";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import RootPageRouter from "..";
import {
  UseDeleteCampaign,
  UseGetCampaignPage,
} from "../hook/useCreateCampaign";

interface Campaign {
  campaignId: number;
  campaignCode: string;
  campaignName: string;
  createdDate: string;
  assignedUser: number;
  groupCustomer: number;
  activeStatus: number;
}

interface SearchValues {
  name?: string;
  creator?: string;
  status?: string;
  category?: string;
}

const { Option } = Select;

// Mock data - replace with real API data

const statusOptions = [
  { label: "Tất cả", value: "" },
  { label: "Chờ duyệt", value: "0" },
  { label: "Đang hoạt động", value: "1" },
  { label: "Hoàn thành", value: "2" },
  { label: "Tạm dừng", value: "3" },
  { label: "Hủy bỏ", value: "4" },
];

const dataGroupCustomer = [
  { id: 1, name: "Shop A" },
  { id: 2, name: "Shop B" },
  { id: 3, name: "Shop C" },
];

// Mock data for users
const dataAssignedUsers = [
  { id: 1, name: "Nguyễn Văn A" },
  { id: 2, name: "Trần Thị B" },
  { id: 3, name: "Lê Văn C" },
];

const getStatusColor = (status: number) => {
  switch (status) {
    case 0:
      return "#faad14"; // yellow
    case 1:
      return "#52c41a"; // green
    case 2:
      return "#1890ff"; // blue
    case 3:
      return "#fa8c16"; // orange
    case 4:
      return "#f5222d"; // red
    default:
      return "#d9d9d9"; // gray
  }
};

const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return "Chờ duyệt";
    case 1:
      return "Đang hoạt động";
    case 2:
      return "Chưa hoạt động";
    case 3:
      return "Tạm dừng";
    case 4:
      return "Hủy bỏ";
    default:
      return "Không xác định";
  }
};

export default function CampaignManagement() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { getCampaignPageExec, getCampaignPageState } = UseGetCampaignPage();
  const { deleteCampaignExec } = UseDeleteCampaign();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: getCampaignPageState?.data?.length,
  });

  const handleSearch = useCallback((values: SearchValues) => {
    // TODO: Implement search logic
    console.log("Search values:", values); // eslint-disable-line no-console
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  const handleReset = useCallback(() => {
    form.resetFields();
  }, [form]);

  const handleDelete = (id: number) => {
    Modal.confirm({
      title: "Xác nhận xóa",
      content: "Bạn có chắc chắn muốn xóa chiến dịch này?",
      okText: "Xóa",
      cancelText: "Hủy",
      onOk: () => {
        deleteCampaignExec(id)
          .then((res) => {
            if (res.status === 200) {
              showNotification({
                type: "success",
                message: "Xóa chiến dịch thành công",
              });
              getCampaignPageExec({
                pageNum: pagination.current,
                pageSize: pagination.pageSize,
              });
            }
          })
          .catch((error) => {
            console.error("Error deleting campaign:", error);
            showNotification({
              type: "error",
              message:
                error?.response?.data?.errors?.[0]?.message ||
                "Xóa chiến dịch thất bại",
            });
          })
          .finally(() => {
            setLoading(false);
          });
      },
    });
  };

  const gotoCampaignPage = useCallback(
    ({
      campaignId,
      action,
    }: {
      campaignId: number | string;
      action?: string;
    }) => {
      if (action) {
        RootPageRouter.gotoChild("campaignDetailV2", {
          params: { campaignId: String(campaignId) },
          queryString: action ? `?action=${action}` : undefined,
        });
      } else {
        RootPageRouter.gotoChild("campaignDetailV2", {
          params: { campaignId: "isAddNew" },
        });
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const columns: ColumnsType<Campaign> = [
    {
      title: "#",
      key: "index",
      width: 60,
      align: "center",
      render: (_, __, index) =>
        (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: "Tên chiến dịch",
      key: "assignedUser",
      width: 300,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500, color: "#1890ff", marginBottom: 4 }}>
            {record.campaignCode} - {record.campaignName}
          </div>
        </div>
      ),
    },
    {
      title: "Ngày tạo",
      dataIndex: "created_at",
      key: "created_at",
      width: 240,
      align: "center",
      render: (text) => {
        return <div>{dayjs(text).format("DD/MM/YYYY")}</div>;
      },
    },
    {
      title: "Người phụ trách",
      dataIndex: "assignedUser",
      key: "assignedUser",
      width: 250,
      align: "center",
      render: (value) => {
        console.log(value, "value assignedUser");

        const valueName =
          dataAssignedUsers.find((user) => user.id === value)?.name ||
          "Chưa có";
        return valueName;
      },
    },
    {
      title: "Nhóm khách hàng",
      dataIndex: "groupCustomer",
      key: "groupCustomer",
      width: 250,
      align: "center",
      render: (value) => {
        console.log(value, "value groupCustomer");

        const valueName =
          dataGroupCustomer.find((group) => group.id === value)?.name ||
          "Chưa có";
        return valueName;
      },
    },
    {
      title: "Trạng thái",
      key: "activeStatus",
      width: 140,
      align: "center",
      render: (_, record) => (
        <Tag color={getStatusColor(record.activeStatus)}>
          {getStatusText(record.activeStatus)}
        </Tag>
      ),
    },
    {
      title: "Thao tác",
      key: "actions",
      width: 150,
      align: "center",
      render: (_, record) => (
        <Space size={8}>
          <Tooltip title="Xem chi tiết">
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              icon={<EyeFilled />}
              onClick={() =>
                gotoCampaignPage({
                  campaignId: record.campaignId,
                  action: "view",
                })
              }
              style={{ padding: "4px 8px", fontSize: 12 }}
            />
          </Tooltip>
          <Tooltip title="Chỉnh sửa">
            <BaseButton
              type="primary"
              bgColor={COLOR.GREEN[500]}
              hoverColor={COLOR.GREEN[700]}
              icon={<EditFilled />}
              onClick={() =>
                gotoCampaignPage({
                  campaignId: record.campaignId,
                  action: "edit",
                })
              }
              style={{ padding: "4px 8px", fontSize: 12 }}
            />
          </Tooltip>
          <Tooltip title="Xóa">
            <BaseButton
              type="primary"
              bgColor={COLOR.RED[500]}
              hoverColor={COLOR.RED[700]}
              icon={<DeleteFilled />}
              onClick={() => handleDelete(record.campaignId)}
              style={{ padding: "4px 8px", fontSize: 12 }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    getCampaignPageExec({
      pageNum: 1,
      pageSize: 10,
    });
  }, []);

  return (
    <General>
      <title key="title">Quản lý chiến dịch</title>
      <Section>
        <div className="p-4">
          {/* <Heading modifiers="primary">Quản lý chiến dịch</Heading> */}
          <Card
            styles={{
              body: {
                padding: 0,
              },
            }}
            title="Quản lý chiến dịch"
            style={{ marginLeft: "1.5%", marginRight: "1.5%" }}
          >
            <div className="flex flex-col gap-4">
              <div className="p-6">
                <div className="mb-6">
                  <Collapse>
                    <Collapse.Panel header="Tìm kiếm" key="1">
                      <Form
                        form={form}
                        onFinish={handleSearch}
                        layout="vertical"
                      >
                        <Row gutter={[16, 16]}>
                          <Col xs={24} sm={12} md={6}>
                            <Form.Item
                              name="campaignName"
                              label="Tên chiến dịch"
                            >
                              <Input
                                placeholder="Tên chiến dịch"
                                suffix={<SearchOutlined />}
                              />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12} md={6}>
                            <Form.Item
                              name="assignedUser"
                              label="Người tạo chiến dịch"
                            >
                              <BaseSelect />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12} md={6}>
                            <Form.Item name="status" label="Đang sử dụng">
                              <Select placeholder="Đang sử dụng" allowClear>
                                {statusOptions.map((option) => (
                                  <Option
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12} md={6}>
                            <Form.Item name="category" label="Tất cả">
                              <Select placeholder="Tất cả" allowClear>
                                <Option value="">Tất cả</Option>
                                <Option value="email">Email Marketing</Option>
                                <Option value="sms">SMS Marketing</Option>
                                <Option value="social">Social Media</Option>
                              </Select>
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row gutter={[16, 16]} justify="end">
                          <Col>
                            <Space>
                              <BaseButton onClick={handleReset}>
                                Đặt lại
                              </BaseButton>
                              <BaseButton
                                type="primary"
                                bgColor={COLOR.BLUE[500]}
                                hoverColor={COLOR.BLUE[700]}
                                htmlType="submit"
                                loading={loading}
                              >
                                Tìm kiếm
                              </BaseButton>
                              <BaseButton
                                type="primary"
                                bgColor={COLOR.GREEN[500]}
                                hoverColor={COLOR.GREEN[700]}
                                icon={<PlusOutlined />}
                                onClick={() =>
                                  gotoCampaignPage({
                                    campaignId: "isAddnew",
                                  })
                                }
                              >
                                Thêm mới
                              </BaseButton>
                            </Space>
                          </Col>
                        </Row>
                      </Form>
                    </Collapse.Panel>
                  </Collapse>
                </div>
                <Table
                  columns={columns}
                  dataSource={getCampaignPageState?.data || []}
                  rowKey="id"
                  loading={loading}
                  // pagination={{
                  //   current: pagination.current,
                  //   pageSize: pagination.pageSize,
                  //   total: pagination.total,
                  //   showSizeChanger: true,
                  //   showQuickJumper: true,
                  //   showTotal: (total, range) =>
                  //     `Hiển thị ${range[0]} kết quả/trang`,
                  //   pageSizeOptions: ["10", "20", "50", "100"],
                  //   onChange: (page, pageSize) => {
                  //     setPagination({
                  //       ...pagination,
                  //       current: page,
                  //       pageSize: pageSize || pagination.pageSize,
                  //     });
                  //   },
                  // }}
                  scroll={{ x: 1200 }}
                  size="middle"
                />
                <Row justify="space-between" align="middle" className="p-6">
                  <Col>
                    <Select
                      placeholder="Chọn số lượng hiển thị"
                      // value={`${pageSize}`}
                      // onChange={handleOnChangePageSizePulldown}
                      style={{ width: 150 }}
                    >
                      {[5, 10, 15, 25, 30].map((size) => (
                        <Option key={size} value={`${size}`}>
                          {size}/pages
                        </Option>
                      ))}
                    </Select>
                  </Col>
                  <Col>
                    {/* {shopPaginationState?.totalPage > 0 && (
                      <Pagination
                        total={shopPaginationState?.totalPage * 10}
                        showSizeChanger={false}
                        defaultCurrent={1}
                        onChange={gotoPage}
                      />
                    )} */}
                  </Col>
                </Row>
              </div>
            </div>
          </Card>
        </div>

        {/* Action Button */}
        {/* <div className="mb-4">
          <Row justify="space-between" align="middle">
            <Col>
              <span style={{ fontSize: 14, color: "#666" }}>
                Sắp xếp theo cập nhật mới
              </span>
            </Col>
            <Col>
              <BaseButton
                type="primary"
                bgColor={COLOR.GREEN[500]}
                hoverColor={COLOR.GREEN[700]}
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                Thêm mới
              </BaseButton>
            </Col>
          </Row>
        </div> */}

        {/* Table */}
      </Section>
    </General>
  );
}
