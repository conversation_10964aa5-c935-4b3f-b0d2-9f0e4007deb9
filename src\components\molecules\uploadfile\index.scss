.m-uploadfile {
	$root: &;

	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	background-repeat: no-repeat;
	background-position: center;
	background-size: contain;
	border: 2px dashed $COLOR-PLATINUM-3;
	border-radius: rem(2);
	transition: border 0.2s ease-in-out;
	@include aspectRatio(706, 359);

	&:hover {
		border-color: $COLOR-DENIM;
	}

	&_close {
		position: absolute;
		top: 10px;
		right: 10px;
		display: flex;

		.a-icon {
			width: rem(10);
			height: rem(10);
		}
	}

	&_wrap {
		padding-top: rem(40);
		padding-bottom: rem(40);
		text-align: center;
	}

	&_wrapicon {
		height: rem(85.56);
		text-align: center;

		.a-icon {
			width: 100%;
			height: 100%;
		}
	}

	&_title,
	&_filename {
		font-family: $FONTFAMILY-ARIAL;
		font-size: rem(18);
		line-height: rem(21);
		color: $COLOR-SPANISH-GRAY;
		@include u-fw-regular;
	}

	&_fileicon {
		height: rem(85.56);
		margin-bottom: rem(10);

		.a-icon {
			width: 100%;
			height: 100%;
		}
	}

	&-uploaded {
		align-items: center;
	}

	&_input {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 100%;
		cursor: pointer;
		opacity: 0;
	}

	.a-button {
		padding: rem(14) rem(33);
		margin-top: rem(56.11);
		margin-bottom: rem(10);
		font-family: inherit;
	}
}
