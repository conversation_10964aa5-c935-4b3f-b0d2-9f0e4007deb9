.a-spinner {
	$root: &;
	position: relative;

	@mixin absolute-center {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	&-wrap {
		height: 100%;
		& > .a-icon {
			@include absolute-center;
		}
	}

	&-absolute {
		position: absolute;
		width: 100%;
		#{$root}-wrap {
			@include absolute-center;
		}
	}

	&-full {
		width: 100vw;
		height: 100vh;
		& > :first-child {
			@include absolute-center;
		}
	}

	&-fullcontainer {
		width: 100%;
		text-align: center;
	}

	& > .a-icon {
		width: rem(40);
		height: rem(40);
	}
}
