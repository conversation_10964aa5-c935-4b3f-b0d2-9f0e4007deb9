import {
  Model,
  String,
  Number,
  ModelValue,
  Array,
  Mixed,
  ExtendSchema,
} from "libs/domain";
import { EmployeeSchema } from "modules/employee/entities";

export const LeadItemSchema = {
  _id: String(),
  leadId: String(),
  customerName: String(),
  phone: String(),
  assignedEmployeeId: Number(),
  conversationId: String(),
  creators: Array(ExtendSchema(EmployeeSchema)),
  assignedEmployee: ExtendSchema(EmployeeSchema),

  clientInchargeName: String(),
  clientInchargeTitle: String(),
  clientInchargeDOB: String(),
  clientInchargeGender: String(),
  clientInchargePhone: String(),
  clientInchargeEmail: String(),
  clientReviewName: String(),
  clientReviewTitle: String(),
  clientReviewDOB: String(),
  clientReviewGender: String(),
  clientReviewPhone: String(),
  clientReviewEmail: String(),

  notes: Array(
    Mixed({
      _id: String(),
      employeeId: Number(),
      content: String(),
      employee: ExtendSchema(EmployeeSchema),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      createdAt: (raw: any) => new Date(raw),
    })
  ),
  status: String(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  createdAt: (raw: any) => new Date(raw),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => new Date(raw),
};

export const LeadItemSchemaModal = new Model(LeadItemSchema);
export type LeadItemSchemaType = ModelValue<typeof LeadItemSchemaModal>;
