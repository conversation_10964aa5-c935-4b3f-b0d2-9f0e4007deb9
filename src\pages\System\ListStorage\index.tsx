/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useState } from "react";
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Input,
  Row,
  Col,
  Table,
  Tooltip,
  Modal,
  Form,
  InputNumber,
} from "antd";
import { ColumnsType } from "antd/es/table";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { Heading } from "components/atoms/heading";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import { BasePageProps } from "helpers/component";
import dayjs from "helpers/dayjs";

import RootPageRouter from "..";
import { FormDataType } from "./constant";
import { ListStoragePageVm } from "./vm";

const IndexPage: React.FC<BasePageProps> = () => {
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState("");

  const {
    loading,
    gotoPage,
    listStorage,
    listStoragePaginationState,
    toggleListStorageBy,
    pageSize,
    handleChangePageSize,
    handleOpenModalByType,
    handleCloseModal,
    modalState,
    createStorageExec,
    createStorageState,
  } = ListStoragePageVm();

  const onSubmitStorageCreation = useCallback(
    (formData: FormDataType) => {
      const { name, displayOrder, code } = formData;
      createStorageExec({
        name,
        displayOrder,
        code,
      }).then(() => {
        form.resetFields();
        gotoPage(1); // Refresh data
      });
    },
    [createStorageExec, form, gotoPage]
  );

  // Define table columns
  const columns: ColumnsType<any> = [
    {
      title: "STT",
      key: "index",
      align: "center",
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: "Mã kho",
      dataIndex: "code",
      key: "code",
      align: "center",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleListStorageBy("code"),
      }),
    },
    {
      title: "Tên kho",
      dataIndex: "name",
      key: "name",
      align: "center",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleListStorageBy("name"),
      }),
    },
    {
      title: "Thứ tự hiển thị",
      dataIndex: "displayOrder",
      key: "displayOrder",
      align: "center",
    },
    {
      title: "Cập nhật cuối",
      dataIndex: "updatedAt",
      key: "updatedAt",
      align: "center",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleListStorageBy("updatedAt"),
      }),
      render: (updatedAt: string) =>
        dayjs(updatedAt).format("DD/MM/YYYY HH:mm"),
    },
    {
      title: "Thao tác",
      key: "action",
      align: "center",
      width: 120,
      render: (_, record) => (
        <div className="flex gap-2 justify-center">
          <Tooltip title="Xem chi tiết">
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              icon={<EyeOutlined rev={undefined} />}
              onClick={() =>
                RootPageRouter.gotoChild("storageDetail", {
                  params: { storageId: record.storageId.toString() },
                })
              }
            />
          </Tooltip>
          <Tooltip title="Chỉnh sửa">
            <BaseButton
              type="primary"
              bgColor={COLOR.ORANGE[500]}
              hoverColor={COLOR.ORANGE[700]}
              icon={<EditOutlined rev={undefined} />}
              onClick={() =>
                RootPageRouter.gotoChild("storageDetail", {
                  params: { storageId: record.storageId.toString() },
                  queryString: "?action=edit",
                })
              }
            />
          </Tooltip>
          <Tooltip title="Xóa">
            <BaseButton
              type="primary"
              bgColor={COLOR.RED[500]}
              hoverColor={COLOR.RED[700]}
              icon={<DeleteOutlined rev={undefined} />}
              onClick={() => {
                // TODO: Implement delete functionality
                console.log("Delete storage:", record.storageId);
              }}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <General>
      <title key="title">Danh sách kho</title>
      <Section>
        <Heading>DANH SÁCH KHO</Heading>

        {/* Search and Create Button */}
        <div className="mb-6">
          <Row gutter={16}>
            <Col xs={24} lg={12}>
              <Input
                placeholder="Tìm kiếm"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                suffix={<SearchOutlined rev={undefined} />}
                className="w-full"
              />
            </Col>
            <Col xs={24} lg={12} className="flex justify-end">
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                icon={<PlusOutlined rev={undefined} />}
                onClick={() => handleOpenModalByType("storageCreation")}
              >
                Tạo mới
              </BaseButton>
            </Col>
          </Row>
        </div>
        {/* Storage Table */}
        <Table
          loading={loading}
          columns={columns}
          dataSource={listStorage}
          rowKey="storageId"
          size="small"
          bordered
          scroll={{ x: 800 }}
          pagination={{
            current: listStoragePaginationState?.currentPage || 1,
            total: (listStoragePaginationState?.totalPage || 0) * pageSize,
            pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} mục`,
            pageSizeOptions: ["5", "10", "15", "20"],
            onChange: (page, size) => {
              gotoPage(page);
              if (size !== pageSize) {
                handleChangePageSize(size);
              }
            },
            onShowSizeChange: (_, size) => {
              handleChangePageSize(size);
              gotoPage(1);
            },
          }}
        />
      </Section>
      {/* Create Storage Modal */}
      <Modal
        title="TẠO MỚI KHO"
        open={modalState.type === "storageCreation" && modalState.open}
        onCancel={handleCloseModal}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onSubmitStorageCreation}
          className="space-y-4"
        >
          <Form.Item
            label="Mã kho"
            name="code"
            rules={[{ required: true, message: "Vui lòng nhập mã kho" }]}
          >
            <Input placeholder="Nhập mã kho" />
          </Form.Item>

          <Form.Item
            label="Tên kho"
            name="name"
            rules={[{ required: true, message: "Vui lòng nhập tên kho" }]}
          >
            <Input placeholder="Nhập tên kho" />
          </Form.Item>

          <Form.Item
            label="Thứ tự hiển thị"
            name="displayOrder"
            rules={[
              { required: true, message: "Vui lòng nhập thứ tự hiển thị" },
            ]}
          >
            <InputNumber
              placeholder="Nhập thứ tự hiển thị"
              className="w-full"
              min={0}
            />
          </Form.Item>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <BaseButton type="default" onClick={handleCloseModal}>
              HỦY
            </BaseButton>
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              htmlType="submit"
              loading={createStorageState.loading}
            >
              LƯU
            </BaseButton>
          </div>
        </Form>
      </Modal>
    </General>
  );
};
export default IndexPage;
