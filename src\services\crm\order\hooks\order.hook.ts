import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import {
  CreateExchangeOrderDto,
  GetDetailOrderDto,
  UpdateOrderExchangeDto,
} from "../dto/order.dto";
import orderServices from "../order.service";

/**
 * Hook to create an exchange order
 * @returns { createOrderExchangeRefetch: function to create exchange order }
 */

export const useCreateOrderExchange = () => {
  const [createOrderExchangeRefetch] = useAsync(
    useCallback((dto: CreateExchangeOrderDto) => {
      return orderServices.createOrderExchange(dto);
    }, [])
  );
  return {
    createOrderExchangeRefetch,
  };
};

export const useUpdateOrderExchange = () => {
  const [updateOrderExchangeRefetch] = useAsync(
    useCallback((dto: UpdateOrderExchangeDto) => {
      return orderServices.updateOrderChange(dto);
    }, [])
  );
  return {
    updateOrderExchangeRefetch,
  };
};

export const useGetOrderDetail = (dto: GetDetailOrderDto) => {
  const [orderDetailRefetch, orderDetailData] = useAsync(
    useCallback(() => {
      return orderServices.getOrderDetail(dto);
    }, [dto])
  );
  return {
    orderDetailRefetch,
    orderDetailData: orderDetailData?.data?.data,
    loading: orderDetailData?.loading,
  };
};
