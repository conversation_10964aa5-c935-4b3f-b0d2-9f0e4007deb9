/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useMemo, useState } from "react";
import { ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Typography,
} from "antd";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { formatCurrencyWithSuffix } from "helpers/currency.helper";
import { InventoryItem } from "./dto/inventory.dto";
import { UseGetInventory } from "./hook/useGetInventory";
import { UseGetStorage } from "./hook/useGetStorage";
import type { ColumnsType } from "antd/es/table";

const { Title } = Typography;
const { Option } = Select;

// Mock data

// Mock warehouse data

interface SearchForm {
  searchText?: string;
  storeCode?: string;
}

export default function Inventory() {
  const [form] = Form.useForm();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filterValue, setFilterValue] = useState<SearchForm>({
    searchText: "",
    storeCode: "",
  });
  const { fetchStorageExe, stateStorages } = UseGetStorage();
  const {
    fetchData,
    inventoryData,
    loading: itemStockLocationLoading,
  } = UseGetInventory();
  const storageOptions = useMemo(() => {
    return stateStorages?.data?.map((storage) => ({
      label: storage.name,
      value: storage.code,
    }));
  }, [stateStorages?.data?.length]);

  const handleSearch = useCallback(async (values: SearchForm) => {
    if (values.searchText) {
      values.searchText = values.searchText.trim();
    }
    if (values.storeCode) {
      values.storeCode = values.storeCode.trim();
    }
    setPage(1);
    fetchData({
      searchText: values.searchText,
      storeCode: values.storeCode,
      pageNum: 1,
      pageSize,
    });
    setFilterValue({
      ...values,
    });
  }, []);

  const handleReset = useCallback(() => {
    form.resetFields();
  }, [form]);

  const columns: ColumnsType<InventoryItem> = [
    {
      title: "Kho",
      dataIndex: "storeName",
      key: "storeName",
      width: 180,
      ellipsis: true,
    },
    {
      title: "Sản phẩm",
      dataIndex: "name",
      key: "name",
      width: 200,
      ellipsis: true,
      render: (text, record) => {
        const name = record.product;
        return <span>{name?.name}</span>;
      },
    },
    {
      title: "SKU",
      dataIndex: "skuCode",
      key: "skuCode",
      width: 150,
    },

    {
      title: "Tồn",
      dataIndex: "onhandQty",
      key: "onhandQty",
      width: 80,
      align: "center",
      render: (quantity: number) => (
        <Tag color={quantity > 0 ? "blue" : "default"}>{quantity}</Tag>
      ),
    },
    {
      title: "GIÁ",
      dataIndex: "sku",
      key: "sku",
      width: 120,
      align: "right",
      render: (sku) => formatCurrencyWithSuffix(sku.price),
    },
  ];

  useEffect(() => {
    fetchStorageExe();
  }, []);

  useEffect(() => {
    if (filterValue.storeCode) {
      fetchData({
        searchText: filterValue.searchText,
        storeCode: filterValue.storeCode,
        pageNum: 1,
        pageSize,
      });
    }
  }, [page, pageSize, filterValue]);

  return (
    <div style={{ padding: "24px" }}>
      <Title level={2}>Quản lý tồn kho</Title>

      {/* Search Form */}
      <Card style={{ marginBottom: 16 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={16}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="Tìm kiếm sản phẩm" name="searchText">
                <Input placeholder="Nhập tên sản phẩm hoặc SKU..." allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="Kho" name="storeCode">
                <BaseSelect
                  placeholder="Chọn kho"
                  allowClear
                  showSearch
                  options={storageOptions}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={8} lg={12}>
              <Form.Item label=" " style={{ marginBottom: 0 }}>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SearchOutlined />}
                    loading={itemStockLocationLoading}
                  >
                    Tìm kiếm
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleReset}>
                    Làm mới
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* Inventory Table */}
      <Card>
        <Table
          columns={columns}
          rowKey={(record) => record.id}
          dataSource={inventoryData?.data}
          loading={itemStockLocationLoading}
          scroll={{ x: 1200 }}
          pagination={{
            current: page,
            pageSize,
            total: inventoryData?.meta.totalRecords,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} sản phẩm`,
            defaultPageSize: 10,
            pageSizeOptions: ["10", "20", "50", "100"],
            onChange: (offset, limit) => {
              setPage(offset);
              setPageSize(limit);
            },
            onShowSizeChange: (current, size) => {
              setPage(1);
              setPageSize(size);
            },
          }}
        />
      </Card>
    </div>
  );
}
