import { useState, useEffect } from "react";

import { Story, Meta } from "@storybook/react/types-6-0";
import { unstable_batchedUpdates as batchedUpdates } from "react-dom";

import { BoxTextMessage } from "components/molecules/boxmessage";

import { Chatbox, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|organisms/Chatbox",
  component: Chatbox,
} as Meta;

const Template: Story<
  Props<{
    src: string;
    left: boolean;
    content: string;
  }>
> = ({ threshold }) => {
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [state, setState] = useState(
    Array(30)
      .fill(0)
      .map((_, index) => ({
        src: "https://avatars.githubusercontent.com/u/18642063?s=64&v=4",
        left: index % 2 === 0,
        content: `<PERSON><PERSON><PERSON> là demo nè ${index}`,
      }))
  );

  useEffect(() => {
    setInterval(() => {
      setState((prevState) => [
        ...prevState,
        {
          src: "https://avatars.githubusercontent.com/u/18642063?s=64&v=4",
          left: prevState.length % 2 === 0,
          content: `demo nè ${prevState.length}`,
        },
      ]);
    }, 3000);
  }, []);

  const onLoadMore = () => {
    setLoading(true);
    setTimeout(() => {
      batchedUpdates(() => {
        setLoading(false);
        setHasMore(false);
        setState((prevState) => [
          ...Array(10)
            .fill(0)
            .map((_, index) => ({
              src: "https://avatars.githubusercontent.com/u/18642063?s=64&v=4",
              left: index % 2 === 0,
              content: `Đây là demo loadmore nè ${index}`,
            })),
          ...prevState,
        ]);
      });
    }, 5000);
  };

  return (
    <div style={{ height: 700 }}>
      <Chatbox
        loadMorePending={loading}
        messages={state}
        hasMore={hasMore}
        threshold={threshold}
        onMessageRender={(message) => (
          <BoxTextMessage src={message.src} contentLeft={message.left}>
            {message.content}
          </BoxTextMessage>
        )}
        onFloatCommingMessageRender={(message) => (
          <span>{message.content}</span>
        )}
        onLoadMore={onLoadMore}
      />
    </div>
  );
};

export const Normal = Template.bind({});

Normal.args = {
  hasMore: true,
  messages: [],
  threshold: 1,
};
