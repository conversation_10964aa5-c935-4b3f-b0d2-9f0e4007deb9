/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { DatePicker, Form, Input, Modal, Spin } from "antd";
import TextArea from "antd/es/input/TextArea";
import dayjs, { Dayjs } from "dayjs";
import { debounce } from "lodash";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { BaseSelectWithFeatures } from "components/atoms/base/select/BaseSelectWithFeatures";
import { COLOR } from "constants/color";
import useDidMount from "helpers/react-hooks/useDidMount";
import { usePulldownAssignEmployee } from "modules/employee";
import { useInfinityMasterDataTicket } from "modules/ticket/hook/useGetListMasterDataTicket";
import { useInfinityStatusTicket } from "pages/TickerManagement/StatusTicket/hooks/useGetStatusTicket";
import { TicketDataType } from "../ListTicketPageV2";

export interface TicketModalRef {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  open: (data?: any, isView?: boolean) => void;
  close: () => void;
}

export type FromTicketType = {
  ticketId?: number;
  title: string;
  statusId: number;
  priorityId: number;
  employeeId: number;
  endDate: Dayjs;
  note?: string;
};

interface TicketModalProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onSubmit: (values: FromTicketType) => void;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const isDateLike = (value: any) => {
  return value instanceof Date;
};

const convertTicketDtoToFormType = (data: TicketDataType) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const result: any = {};
  Object.entries(data).forEach(([key, value]) => {
    if (isDateLike(value)) {
      result[key] = value ? dayjs(value) : undefined;
    } else {
      result[key] = value;
    }
  });
  return result;
};

export const TicketModal = forwardRef<TicketModalRef, TicketModalProps>(
  (props, ref) => {
    const { onSubmit } = props;
    const [form] = Form.useForm<FromTicketType>();
    const [open, setOpen] = useState<boolean>(false);
    const isEdit = useRef(false);
    const [statusTicketInput, setStatusTicketInput] = useState<string>("");
    const [priorityTicketInput, setPriorityTicketInput] = useState<string>("");

    const isViewRef = useRef(false);

    useImperativeHandle(ref, () => ({
      open: (data, isView) => {
        if (data) {
          isEdit.current = true;
          const { status, priority, ...restData } = data ?? {};
          const convertData = convertTicketDtoToFormType({
            ...restData,
          });

          const employeeValue = {
            label: data?.employee?.name ?? "--",
            value: String(data?.employee?.employeeId),
          };

          const statusvalue = {
            label: status.name,
            value: status.statusTicketId,
          };

          const priorityValue = {
            label: priority.name,
            value: priority.masterDataTicketId,
          };

          form.setFieldsValue({
            ...convertData,
            statusId: statusvalue,
            priorityId: priorityValue,
            employeeId: employeeValue,
          });

          if (isView) {
            isViewRef.current = true;
          } else {
            isViewRef.current = false;
          }
        }
        setOpen(true);
      },
      close: handleClose,
    }));

    const {
      data: statusOptions,
      hasMore: statusHasMore,
      loadMore: loadMoreStatus,
      loading: statusLoading,
      reset: resetStatus,
      totalRecords: statusTotalRecords,
    } = useInfinityStatusTicket({
      searchText: statusTicketInput,
    });

    const {
      data: priorityOptions,
      hasMore: priorityHasMore,
      loadMore: loadMorePriority,
      loading: priorityLoading,
      reset: resetPriority,
      totalRecords: priorityTotalRecords,
    } = useInfinityMasterDataTicket({
      searchText: priorityTicketInput,
    });

    const {
      loadMoreEmployee,
      assignEmployeeOptions,
      loadMoreEmployeeState,
      formatAssignEmployeeOption,
    } = usePulldownAssignEmployee();

    const onAssignEmployeePulldownInputChange = useCallback(
      debounce(
        (textSearch: string) => loadMoreEmployee({ name: textSearch }),
        200
      ),
      [loadMoreEmployee]
    );

    const handleResetStatus = () => {
      resetStatus();
      setStatusTicketInput("");
    };

    const handleResetPriority = () => {
      resetPriority();
      setPriorityTicketInput("");
    };

    const handleClose = () => {
      form.resetFields();
      setOpen(false);
      isEdit.current = false;
    };

    useDidMount(() => {
      loadMoreEmployee();
    });

    const convertObjectToValue = (values: any) => {
      const result: any = {};

      if (values && typeof values === "object") {
        Object.entries(values).forEach(([key, value]) => {
          if (value !== null && typeof value === "object" && "value" in value) {
            result[key] = value.value;
          } else {
            result[key] = value;
          }
        });
      }

      return result;
    };

    const onFinish = (values: any) => {
      const convertedValues = convertObjectToValue(values);
      onSubmit?.(convertedValues);
    };

    return (
      <Modal
        title={`${isEdit.current ? "Chỉnh sửa" : "Tạo mới"} Ticket`}
        open={open}
        onCancel={handleClose}
        centered
        width={780}
        footer={
          <div className="flex items-center gap-3 justify-end">
            <BaseButton type="text" className="" onClick={handleClose}>
              Hủy
            </BaseButton>
            <BaseButton
              type="primary"
              bgColor={COLOR.GREEN[500]}
              hoverColor={COLOR.GREEN[700]}
              onClick={() => {
                form.submit();
              }}
            >
              Lưu
            </BaseButton>
          </div>
        }
        maskClosable={false}
        destroyOnHidden
      >
        <Form
          disabled={isViewRef.current}
          form={form}
          layout="vertical"
          onFinish={onFinish}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-center">
            <Form.Item name="ticketId" noStyle hidden>
              <Input />
            </Form.Item>

            <div className="lg:col-span-2">
              <Form.Item
                label="Tiêu đề"
                name="title"
                rules={[
                  {
                    required: true,
                    message: "Vui lòng nhập tiêu đề!",
                  },
                ]}
              >
                <Input placeholder="Nhập tiêu đề" />
              </Form.Item>
            </div>

            <Form.Item
              label="Mức độ ưu tiên"
              name="priorityId"
              rules={[
                {
                  required: true,
                  message: "Vui lòng chọn mức độ ưu tiên",
                },
              ]}
            >
              <BaseSelect
                key="priorityIds"
                allowClear
                showSearch
                filterOption={false}
                onSearch={(value) => {
                  setPriorityTicketInput(value);
                }}
                onClear={() => {
                  handleResetPriority();
                }}
                onBlur={() => {
                  handleResetPriority();
                }}
                onSelect={(value, option) => {
                  setPriorityTicketInput("");
                }}
                fieldNames={{
                  label: "name",
                  value: "masterDataTicketId",
                }}
                placeholder="Chọn độ ưu tiên"
                onPopupScroll={(event) => {
                  const target = event.target as HTMLElement;
                  if (
                    target.scrollTop + target.clientHeight ===
                    target.scrollHeight
                  ) {
                    if (statusHasMore && !statusLoading) {
                      loadMorePriority();
                    }
                  }
                }}
                popupRender={(menu) => (
                  <>
                    {menu}
                    <div
                      style={{
                        textAlign: "center",
                        padding: "8px 0",
                      }}
                    >
                      {priorityLoading ? (
                        <Spin size="small" />
                      ) : !priorityHasMore && priorityOptions.length > 0 ? (
                        <div className="w-full">
                          <span style={{ color: "#999" }}>
                            Đã tải hết dữ liệu
                          </span>
                        </div>
                      ) : null}
                    </div>
                  </>
                )}
                options={priorityOptions}
              />
            </Form.Item>

            <Form.Item
              label="Trạng thái"
              name="statusId"
              rules={[
                {
                  required: true,
                  message: "Vui lòng chọn trạng thái",
                },
              ]}
            >
              <BaseSelect
                key="statusIds"
                allowClear
                showSearch
                filterOption={false}
                onSearch={(value) => {
                  setStatusTicketInput(value);
                }}
                onClear={() => {
                  handleResetStatus();
                }}
                onBlur={() => {
                  handleResetStatus();
                }}
                onSelect={(value, option) => {
                  setStatusTicketInput("");
                }}
                fieldNames={{
                  label: "name",
                  value: "statusTicketId",
                }}
                placeholder="Chọn trạng thái"
                onPopupScroll={(event) => {
                  const target = event.target as HTMLElement;
                  if (
                    target.scrollTop + target.clientHeight ===
                    target.scrollHeight
                  ) {
                    if (statusHasMore && !statusLoading) {
                      loadMoreStatus();
                    }
                  }
                }}
                popupRender={(menu) => (
                  <>
                    {menu}
                    <div
                      style={{
                        textAlign: "center",
                        padding: "8px 0",
                      }}
                    >
                      {statusLoading ? (
                        <Spin size="small" />
                      ) : !statusHasMore && statusOptions.length > 0 ? (
                        <div className="w-full">
                          <span style={{ color: "#999" }}>
                            Đã tải hết dữ liệu
                          </span>
                        </div>
                      ) : null}
                    </div>
                  </>
                )}
                options={statusOptions}
              />
            </Form.Item>

            <Form.Item
              label="Phân công"
              name="employeeId"
              rules={[
                {
                  required: true,
                  message: "Vui lòng chọn nhân viên",
                },
              ]}
            >
              <BaseSelectWithFeatures
                placeholder="Chọn nhân viên"
                onSearch={(value) => {
                  onAssignEmployeePulldownInputChange(value);
                }}
                triggerLoadMore={async ({ searchInputValue }) => {
                  await loadMoreEmployee({ name: searchInputValue });
                }}
                isLoading={loadMoreEmployeeState.loading}
                options={assignEmployeeOptions}
              />
            </Form.Item>

            <Form.Item
              label="Ngày kết thúc"
              name="endDate"
              rules={[
                {
                  required: true,
                  message: "Vui lòng chọn ngày kết thúc!",
                },
              ]}
            >
              <DatePicker
                className="w-full"
                placeholder="Chọn ngày kết thúc"
                format="DD/MM/YYYY"
              />
            </Form.Item>
            <div className="lg:col-span-2">
              <Form.Item label="Nội dung" name="note">
                <TextArea placeholder="Nhập nội dung" rows={4} allowClear />
              </Form.Item>
            </div>
          </div>
        </Form>
      </Modal>
    );
  }
);
