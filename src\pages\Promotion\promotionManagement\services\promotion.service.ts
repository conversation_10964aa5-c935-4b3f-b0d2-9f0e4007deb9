import crmDriverV1 from "services/crm/crm-driver-v1";
import {
  CreatePromotionDto,
  DeletePromotionDto,
  GetListActivePromotionDto,
  GetListPromotionDto,
  GetListPromotionResponseDto,
  GetListPromotionWithoutPaginationResponseDto,
  PromotionDto,
  UpdatePromotionDto,
} from "../dtos/promotion.dto";

const PROMOTION_BASE_URL = `/promotions/external/promotions`;

const promotionServices = {
  getListPromotions: (
    dto: GetListPromotionDto
  ): Promise<{ data: GetListPromotionResponseDto }> => {
    const url = `${PROMOTION_BASE_URL}/list`;
    return crmDriverV1.post(url, dto);
  },

  getListPromotionsWithoutPagination: (): Promise<{
    data: GetListPromotionWithoutPaginationResponseDto;
  }> => {
    const url = `${PROMOTION_BASE_URL}/list`;
    return crmDriverV1.post(url);
  },

  getListActivePrmomotionWithoutPagination: (): Promise<{
    data: GetListPromotionResponseDto;
  }> => {
    const url = `${PROMOTION_BASE_URL}/list-active`;
    return crmDriverV1.get(url);
  },

  createPromotion: (dto: CreatePromotionDto) => {
    const url = `${PROMOTION_BASE_URL}/create`;
    return crmDriverV1.post(url, dto);
  },

  updatePromotion: (dto: UpdatePromotionDto) => {
    const { promotionId, ...restProps } = dto;
    const url = `${PROMOTION_BASE_URL}/update/${promotionId}`;
    return crmDriverV1.put(url, restProps);
  },

  deletePromotion: (dto: DeletePromotionDto) => {
    const url = `${PROMOTION_BASE_URL}/delete/${dto.promotionId}`;
    return crmDriverV1.delete(url);
  },

  getActivePromotion: (
    dto: GetListActivePromotionDto
  ): Promise<{ data: GetListPromotionResponseDto }> => {
    const { pageNum, pageSize, inputSearch } = dto;
    const url = `${PROMOTION_BASE_URL}/list-active?pageNum=${pageNum}&pageSize=${pageSize}&inputSearch=${inputSearch}`;
    return crmDriverV1.get(url);
  },
};

export default promotionServices;
