.o-table {
	$root: &;

	@mixin shadow-sticky($translate: 100%) {
		position: absolute;
		top: 0;
		bottom: -1px;
		width: 30px;
		pointer-events: none;
		content: "";
		transition: box-shadow 0.3s;
		transform: translateX($translate);
	}

	&_container {
		position: relative;
		display: block;
		width: 100%;
		margin: auto;
		overflow: auto;
		background: $COLOR-WHITE;
	}

	&_table {
		width: 100%;
		table-layout: fixed;
	}

	&_tr {
		#{$root}_tbody & {
			border: solid 1px $COLOR-PLATINUM-3;

			#{$root}-borderdotted & {
				border: dashed 1px $COLOR-PLATINUM-3;
			}
		}
	}

	&_th,
	&_td {
		position: relative;
		padding: rem(12);
		font-family: $FONTFAMILY-ROBOTO;
		font-size: rem(14);
		line-height: rem(16);
		color: $COLOR-QUARTZ;
		text-align: left;
		border: solid 1px $COLOR-PLATINUM-3;

		&-center {
			text-align: center;
		}
	}

	&_th {
		background-color: $COLOR-ISABELLINE;
		@include u-fw-bold;

		#{$root}-fixedheader & {
			position: sticky;
			top: 0;
			z-index: z("table", "fixed");
		}

		&-stickyleft,
		&-stickyright {
			background-color: $COLOR-ISABELLINE;

			#{$root}-fixedheader & {
				z-index: z("table", "fixedsticky");
			}
		}

		&-sortable &_content {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		&_wrapsort {
			flex: 0 0 auto;
			margin-left: rem(10);

			#{$root}_th-center & {
				position: absolute;
				top: 50%;
				right: 0;
				width: rem(24);
				transform: translateY(-50%);
			}
		}

		&-center {
			&#{$root}_th-sortable {
				#{$root}_th_wrapcontent {
					width: 100%;
					padding: 0 rem(34);
				}
			}
		}
	}

	&_td {
		#{$root}-borderdotted & {
			border: dashed 1px $COLOR-PLATINUM-3;
		}

		&-verticalaligntop {
			vertical-align: top;
		}

		&-stickyleft,
		&-stickyright {
			background-color: $COLOR-WHITE;
		}
	}

	&_th,
	&_td {
		&-stickyleft,
		&-stickyright {
			position: sticky;
			z-index: z("table", "sticky");
			border-bottom: solid 1px $COLOR-PLATINUM-3;
		}

		&-stickyleft {
			left: 0;
			#{$root}_container-hasstickyleft & {
				&::after {
					box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
				}
			}
			&::after {
				@include shadow-sticky(100%);
				right: 0;
			}
		}

		&-stickyright {
			right: 0;
			#{$root}_container-hasstickyright & {
				&::after {
					box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
				}
			}
			&::after {
				@include shadow-sticky(-100%);
				left: 0;
			}
		}
	}

	&_wraploading {
		position: absolute;
		top: calc(50% - 12px);
		left: calc(50% - 12px);
		z-index: z("table", "loading");
		width: 100%;
		height: 100%;
	}

	&-loading {
		position: relative;
		pointer-events: none;

		&::after {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: z("table", "afterloading");
			width: 100%;
			height: 100%;
			content: " ";
			background-color: $COLOR-WHITE;
			opacity: 0.5;
		}
	}

	&_empty {
		position: sticky;
		left: 0;
		width: 100%;
		max-width: 100%;
		margin: rem(30) 0;
	}

	&_emptycontent {
		text-align: center;
	}

	&_image {
		width: 100%;
		max-width: rem(100);
		margin: 0 auto;
	}

	&_description {
		font-size: rem(16);
	}
}
