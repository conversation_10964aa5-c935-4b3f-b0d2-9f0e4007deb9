import React from "react";

import { mapModifiers } from "helpers/component";
import { MessageEntityType, messageTypeByContent } from "modules/chat";

import { ImageMessage } from "./imagemessage";
import { OtherFile } from "./otherfile";
import { TextMessage } from "./textmessage";
import { VideoMessage } from "./videomessage";

export interface Props {
  message: MessageEntityType;
}

export const Message: React.FC<Props> = ({ message }) => {
  const messageType = messageTypeByContent(message.content);
  return (
    <div className={mapModifiers("m-message")}>
      {messageType === "text" && <TextMessage text={message.content.text} />}
      {messageType === "image" && (
        <ImageMessage
          src={message.content?.attachments?.[0]?.payload?.url}
          width={message.content?.metadata?.media?.width}
          height={message.content?.metadata?.media?.height}
        />
      )}
      {messageType === "video" && (
        <VideoMessage src={message.content?.attachments?.[0]?.payload?.url} />
      )}
      {messageType === "file" && (
        <OtherFile
          src={message.content?.attachments?.[0]?.payload?.url}
          fileName={message.content?.metadata?.media?.originalName}
        />
      )}
    </div>
  );
};
