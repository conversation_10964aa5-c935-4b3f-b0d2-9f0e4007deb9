/* eslint-disable no-unsafe-optional-chaining */
import { createEvent, createSource } from "libs/adapters/stores/core";

import { AssignConversationDtoType } from "../dtos";
import { ConversationEntityType, MessageEntityType } from "../entities";

type ConversationType = ConversationEntityType | AssignConversationDtoType;

export interface ConversationSourceState<Conversation> {
  conversation: {
    [id: string]: {
      detail: Conversation;
    };
  };
  recentStack: Conversation[];
  selectedConversation?: Conversation;
}

export const conversationSourceCreator = <
  Conversation extends ConversationType
>() => {
  const conversationSource = createSource<
    ConversationSourceState<Conversation>
  >({
    conversation: {},
    recentStack: [],
    selectedConversation: undefined,
  });

  const conversationHasNewMessage =
    createEvent<{
      conversationId: string;
      message: MessageEntityType;
    }>();
  const appendListConversation =
    createEvent<{
      conversations: Conversation[];
    }>();
  const insertConversation = createEvent<{ conversation: Conversation }>();
  const selectConversationById = createEvent<{ id: string }>();

  conversationSource.on(
    conversationHasNewMessage,
    (state, { conversationId, message }) => {
      const conversation = state.conversation[conversationId];
      if (conversation) {
        conversation.detail.latestMessage = message;
        const conversationIndexInStack = state.recentStack.findIndex(
          (item) => item.conversationId === conversation.detail.conversationId
        );
        if (conversationIndexInStack !== -1) {
          state.recentStack.splice(conversationIndexInStack, 1);
        }
        state.recentStack = [conversation.detail, ...state.recentStack];
      }
    }
  );

  conversationSource.on(appendListConversation, (state, { conversations }) => {
    conversations.forEach((conversation) => {
      state.conversation[conversation.conversationId] = {
        detail: conversation,
      };
    });
    state.recentStack = [...state.recentStack, ...conversations];
  });

  conversationSource.on(selectConversationById, (state, { id }) => {
    const selectedConversation = state.conversation[id];
    if (selectedConversation)
      state.selectedConversation = selectedConversation.detail;
  });

  conversationSource.on(insertConversation, (state, { conversation }) => {
    const { conversationId, latestMessage } = conversation;
    if (state.conversation[conversationId]) return;

    state.conversation[conversationId] = {
      detail: conversation,
    };
    const { recentStack } = state;
    if (recentStack.length === 0) state.recentStack = [conversation];
    else {
      let insertStackPositionIndex = recentStack.findIndex(
        (item) =>
          latestMessage?.createdAt?.getTime() -
            item.latestMessage?.createdAt?.getTime() >=
          0
      );

      if (insertStackPositionIndex === -1)
        insertStackPositionIndex = recentStack.length - 1;

      state.recentStack = [
        ...recentStack.slice(0, insertStackPositionIndex),
        conversation,
        ...recentStack.slice(insertStackPositionIndex),
      ];
    }
  });

  return {
    conversationSource,
    conversationHasNewMessage,
    appendListConversation,
    selectConversationById,
    insertConversation,
  };
};
