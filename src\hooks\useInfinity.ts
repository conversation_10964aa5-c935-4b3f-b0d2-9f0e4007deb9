import { useCallback, useMemo } from "react";

import { showNotification } from "components/atoms/base/Notification";
import { paginationDTO, PaginationDTOType } from "modules/common/pagination";

import { useAsync } from "./useAsync";
import { usePagination } from "./usePagination";

interface AsyncFuncProps {
  pageSize: number;
  pageNum: number;
}

export type ResponseData<R> = {
  data: {
    data: R;
    links: PaginationDTOType;
  };
};

interface OptionProps {
  pageSize?: number;
}

type AsyncFunction<R> = ({
  pageNum,
  pageSize,
}: AsyncFuncProps) => Promise<ResponseData<R>>;

const defaultData = {
  data: [],
  loading: false,
  pagination: {
    currentPage: 0,
    pageSize: 0,
    totalPage: 0,
  },
};

const useInfinity = <R>(
  asyncFunction: AsyncFunction<R>,
  optionsMore: OptionProps
) => {
  const { pageSize: _pageSize = 10 } = optionsMore || {};

  const [getDataExec, getDataState, reset] = useAsync(
    useCallback(
      (options: { pageNum: number; pageSize: number }) =>
        asyncFunction({ ...options }).then((res) => ({
          data: res?.data.data,
          pagination: paginationDTO(res?.data.links),
        })),
      [asyncFunction]
    ),
    {
      keepPrevData: true,
    }
  );

  const {
    gotoPage,
    currentPage = 0,
    pageSize = 0,
    totalPage = 0,
  } = usePagination({
    pageSize: _pageSize,
    actionOnPageChange: (params) =>
      getDataExec({ pageSize: params.pageSize, pageNum: params.page }).catch(
        (error) => {
          const apiError = error?.response?.data;
          if ("errors" in apiError && Array.isArray(apiError.errors)) {
            showNotification({
              type: "error",
              message: apiError.errors[0].detail || apiError.errors[0].title,
            });
          }

          // ✅ Return fallback data để đảm bảo đúng kiểu
          return {
            pagination: {
              self: { pageNum: params.page, pageSize: params.pageSize },
              first: { pageNum: 1, pageSize: params.pageSize },
              prev: params.page - 1,
              next: { pageNum: params.page + 1, pageSize: params.pageSize },
              last: { pageNum: 1, pageSize: params.pageSize }, // fallback
            },
          };
        }
      ),
  });

  const infinityState = useMemo(
    () => ({
      ...defaultData,
      data: getDataState.data?.data,
      loading: getDataState.loading,
      pagination: {
        currentPage,
        pageSize,
        totalPage,
      },
    }),
    [
      currentPage,
      getDataState.data?.data,
      getDataState.loading,
      pageSize,
      totalPage,
    ]
  );

  const gotoFirstPage = useCallback(() => {
    reset();
    gotoPage(1);
  }, [gotoPage, reset]);

  const goNextPage = useCallback(() => {
    if (
      infinityState.pagination.currentPage ===
      infinityState.pagination.totalPage
    )
      return;

    gotoPage(infinityState.pagination.currentPage + 1);
  }, [
    gotoPage,
    infinityState.pagination.currentPage,
    infinityState.pagination.totalPage,
  ]);

  return {
    state: infinityState,
    goNextPage,
    gotoFirstPage,
    reset,
  };
};

export default useInfinity;
