import { useCallback, useRef } from "react";

import { ValueType } from "react-select";

import imgSearchBlue from "assets/images/icons/search-blue.svg";
import { But<PERSON> } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Pulldown } from "components/atoms/pulldown";
import { TextfieldHookForm, Textfield } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import {
  Pagination,
  PaginationReference,
} from "components/molecules/pagination";
import { Col, Row } from "components/organisms/grid";
import { Modal } from "components/organisms/modal";
import { Section } from "components/organisms/section";
import { Table, Thead, Tr, Th, Tbody, Td } from "components/organisms/table";
import { General } from "components/pages/general";
import dayjs from "helpers/dayjs";
import { FormContainer } from "helpers/form";
import { useAsync } from "hooks/useAsync";
import PaginationSection from "pages/Common/paginationSection";
import TableManipulation from "pages/Common/tableManipulation";

import { inputValidationSchema } from "./constant";
import { EmployeeGroupPageVm } from "./vm";

const IndexPage = () => {
  const {
    loading,
    gotoPage,
    employeeGroupPaginationState,
    employeeGroups,
    toggleEmployeeGroupsBy,
    pageSize,
    handleChangePageSize,
    gotoDetailEmployeeGroup,
    gotoEditEmployeeGroup,
    handleOpenModalByType,
    createEmployeeGroup,
    createEmployeeGroupState,
    modalTypeIsOpen,
    handleCloseModal,
  } = EmployeeGroupPageVm();

  const paginationRef = useRef<PaginationReference | null>(null);

  const [SubmitExec] = useAsync(createEmployeeGroup, {
    onSuccess: useCallback(() => {
      paginationRef.current?.setPage(1);
    }, []),
  });

  const handleChangePulldownPageSize = useCallback(
    (option: ValueType<{ label: string; value: string }, false>) => {
      if (option?.value) {
        handleChangePageSize(Number(option.value));
        if (paginationRef.current) paginationRef.current.reset();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return (
    <General>
      <title>Nhóm nhân viên</title>
      <Section>
        <Heading type="h1" modifiers="primary">
          VAI TRÒ TRUY CẬP HỆ THỐNG
        </Heading>

        <Section>
          <Row className="d-flex">
            <Col
              className="ml-auto"
              xs={{ span: 12, order: 2 }}
              lg={{ span: 6, order: 1 }}
            >
              <Textfield iconSrc={imgSearchBlue} placeholder="Tìm kiếm" />
            </Col>
            <Col
              className="d-flex justify-content-end u-mb-15 u-mb-lg-0"
              xs={{ span: 12, order: 1 }}
              lg={{ span: 6, order: 2 }}
            >
              <Button onClick={() => handleOpenModalByType("employeeGroup")}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Section>

        <Section>
          <Table loading={loading} hasData={employeeGroups.length > 0}>
            <Thead>
              <Tr>
                <Th modifiers="center" stickyLeft colSpan={1}>
                  STT
                </Th>
                <Th
                  isSortable
                  onSort={() => toggleEmployeeGroupsBy("name")}
                  colSpan={3}
                >
                  Tên vai trò
                </Th>
                <Th isSortable colSpan={3}>
                  Áp dụng cho
                </Th>
                <Th colSpan={3}>Cập nhật cuối</Th>
                <Th modifiers="center" stickyRight colSpan={3}>
                  Thao tác
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {employeeGroups.map(
                ({ employeeGroupId, name, updatedAt }, index) => (
                  <Tr key={employeeGroupId}>
                    <Td modifiers="center" stickyLeft colSpan={1}>
                      {index + 1}
                    </Td>
                    <Td colSpan={3}>{name}</Td>

                    {/* TODO: Missing api */}
                    <Td colSpan={3}>Thành viên truy cập</Td>

                    <Td colSpan={3}>
                      {dayjs(updatedAt).format("DD/MM/YYYY HH:mm")}
                    </Td>
                    <Td modifiers="center" stickyRight colSpan={3}>
                      <TableManipulation
                        infoAction={{
                          id: `${employeeGroupId}info`,
                          action: () =>
                            gotoDetailEmployeeGroup(employeeGroupId),
                        }}
                        editAction={{
                          id: `${employeeGroupId}edit`,
                          action: () => gotoEditEmployeeGroup(employeeGroupId),
                        }}
                        deleteAction={{
                          id: `${employeeGroupId}delete`,
                        }}
                      />
                    </Td>
                  </Tr>
                )
              )}
            </Tbody>
          </Table>
        </Section>

        <Section>
          <PaginationSection
            appearanceOption={
              <Pulldown
                placeholder="Số lượng hiển thị"
                options={[
                  { label: "5", value: "5" },
                  { label: "10", value: "10" },
                  { label: "15", value: "15" },
                  { label: "20", value: "20" },
                ]}
                value={{
                  label: pageSize.toString(),
                  value: pageSize.toString(),
                }}
                onChange={handleChangePulldownPageSize}
              />
            }
            paginateOption={
              employeeGroupPaginationState.totalPage && (
                <Pagination
                  modifiers="center"
                  total={employeeGroupPaginationState.totalPage}
                  pageCount={5}
                  defaultCurrentPage={1}
                  onPageChange={gotoPage}
                  ref={paginationRef}
                />
              )
            }
          />
        </Section>
      </Section>

      <Modal
        style={{ content: { maxWidth: 620 } }}
        isOpen={modalTypeIsOpen("employeeGroup")}
        isClosable={false}
      >
        <FormContainer
          validationSchema={inputValidationSchema}
          onSubmit={SubmitExec}
        >
          <Heading type="h1" centered>
            TẠO MỚI VAI TRÒ TRUY CẬP HỆ THỐNG
          </Heading>
          <Section>
            <Formfield name="name" label="Tên vai trò">
              <TextfieldHookForm name="name" placeholder="Nhập tên vai trò" />
            </Formfield>
            <div className="d-flex justify-content-end u-mt-20">
              <Button
                buttonType="outline"
                modifiers="secondary"
                onClick={handleCloseModal}
              >
                HUỶ
              </Button>
              <div className="u-ml-15">
                <Button
                  isLoading={createEmployeeGroupState.loading}
                  disabled={createEmployeeGroupState.loading}
                  type="submit"
                >
                  LƯU
                </Button>
              </div>
            </div>
          </Section>
        </FormContainer>
      </Modal>
    </General>
  );
};
export default IndexPage;
