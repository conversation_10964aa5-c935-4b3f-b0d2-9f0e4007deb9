import crmDriverV1 from "../crm-driver-v1";
import { GetProductByStoreCodeDto } from "./dto/product.dto";

const PRODUCT_BASE_URL = `/products/external`;

const productServices = {
  getProductByStoreCode: (dto: GetProductByStoreCodeDto) => {
    return crmDriverV1.get(
      `${PRODUCT_BASE_URL}/get-by-store-code/${dto.storeCode}`,
      {
        params: {
          pageNum: dto.pageNum,
          pageSize: dto.pageSize,
          searchText: dto.searchText,
        },
      }
    );
  },
};

export default productServices;
