import pick from "object.pick";

import { createMapper, fromSchema, merge } from "libs/adapters/dto";

import { DeliveryPartnerSchema } from "../entities/deliveryPartner";

export const createDeliveryPartnerDto = createMapper(
  fromSchema(pick(DeliveryPartnerSchema, ["name", "code"])),
  merge((data) => ({
    name: data.name.trim(),
    code: data.code.trim(),
  }))
);

export type CreateDeliveryPartnerDtoType = ReturnType<
  typeof createDeliveryPartnerDto
>;
