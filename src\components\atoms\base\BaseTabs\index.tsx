/* eslint-disable react/jsx-props-no-spreading */
import { Tabs, TabsProps } from "antd";

export type BaseTabsProps = TabsProps;

export const BaseTabs = (props: BaseTabsProps) => {
  const { className = "", ...rest } = props;
  return (
    <Tabs
      className={`${className} overflow-hidden [&_.ant-tabs-content.ant-tabs-content-top]:h-full [&_.ant-tabs-content.ant-tabs-content-top_.ant-tabs-tabpane]:h-full`}
      {...rest}
    />
  );
};
