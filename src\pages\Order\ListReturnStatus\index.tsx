/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useRef } from "react";

import {
  DeleteOutlined,
  EditOutlined,
  InfoOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  ColorPicker as AntColorPicker,
  Modal as AntModal,
  Table as AntTable,
  Card,
  Col,
  Flex,
  Form,
  Input,
  InputNumber,
  Pagination,
  Row,
  Select,
  Space,
  Switch,
  Tooltip,
  Typography,
} from "antd";
import { Color } from "antd/es/color-picker";
import TextArea from "antd/es/input/TextArea";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { Checkbox } from "components/atoms/checkbox";
import { Tag } from "components/atoms/tag";
import {
  Pagination as PagniationComponent,
  PaginationReference,
} from "components/molecules/pagination";
import { Section } from "components/organisms/section";
import { Link } from "components/utils/link";
import { COLOR } from "constants/color";
import { BasePageProps } from "helpers/component";
import dayjs from "helpers/dayjs";

import RootPageRouter from "..";
import { CreateReturnOrderStatusFormPayload } from "./constant";
import { ExchangeStatusPageVm } from "./vm";

const { Option } = Select;
interface ReturnStatusColumn {
  name: string;
  description: string;
  displayOrder: number;
  color: string;
  isDefault: boolean;
  updatedAt: Date;
  tagline: string;
  returnOrderStatusId: number;
  returnOrderStatusTaglines: any;
  prevReturnOrderStatus: [];
  prevReturnOrderStatusIds: [];
}
const IndexPage: React.FC<BasePageProps> = () => {
  const paginationRef = useRef<PaginationReference | null>(null);
  const defaultReturnOrderRef = useRef<HTMLInputElement | null>(null);
  const [form] = Form.useForm();
  const [formModall] = Form.useForm();
  const {
    pageSize,
    loading,
    gotoPage,
    orderReturnStatusPaginationState,
    orderReturnStatuses,
    toggleOrderReturnStatusesBy,
    handleChangePageSize,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    modalCreator,
    handleDeleteReturnStatus,
  } = ExchangeStatusPageVm();

  const handleChangePulldownPageSize = useCallback(
    (value: string) => {
      if (value) {
        handleChangePageSize(Number(value));
        if (paginationRef.current) paginationRef.current.reset();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const handleSubmitCreateReturnOrderStatus = useCallback(
    async (formData: CreateReturnOrderStatusFormPayload) => {
      formModall.validateFields();
      const color =
        typeof formData?.color === "string"
          ? formData.color
          : (formData.color as unknown as Color).toRgbString();
      await modalCreator.createReturnOrderStatus({
        ...formData,
        isDefault: formData.isDefault || false,
        displayOrder: Number(formData.displayOrder),
        color,
        prevReturnOrderStatusIds: formData.prevReturnOrderStatusIds?.map(
          (item) => Number(item)
        ),
      });
    },
    [modalCreator]
  );

  const columns: any[] = [
    {
      title: "STT",
      key: "index",
      width: 60,
      fixed: "left",
      render: (_: any, __: any, index: number) => index + 1,
      align: "center" as const,
    },
    {
      title: "Tên trạng thái đổi trả",
      key: "name",
      dataIndex: "name",
      onHeaderCell: () => ({
        onClick: () => toggleOrderReturnStatusesBy("name"),
      }),
      align: "center" as const,
      width: 100,
    },
    {
      title: "Màu sắc",
      key: "color",
      dataIndex: "color",
      render: (color: string) => <Tag color={color} />,
      align: "center" as const,
      width: 80,
    },
    {
      title: "Mô tả",
      key: "description",
      dataIndex: "description",
      align: "center" as const,
      width: 80,
    },
    {
      title: "Mặc định",
      key: "isDefault",
      dataIndex: "isDefault",
      align: "center" as const,
      render: (isDefault: boolean) => <Checkbox checked={isDefault} disabled />,
      width: 80,
    },
    {
      title: "Tagline đổi trả",
      key: "returnOrderStatusTaglines",
      dataIndex: "returnOrderStatusTaglines",
      render: (taglines: any[], record: any) => (
        <Link
          to={RootPageRouter.children.listOrderReturnStatusTagline.generatePath(
            {
              returnStatusId: record.returnOrderStatusId.toString(),
            }
          )}
        >
          <span style={{ color: "#FF1515" }}>
            {(taglines || []).length} Tagline
          </span>
        </Link>
      ),
      width: 80,
    },
    {
      title: "Thứ tự hiển thị",
      key: "displayOrder",
      dataIndex: "displayOrder",
      align: "center" as const,
      width: 80,
    },
    {
      title: "Cập nhật cuối",
      key: "updatedAt",
      dataIndex: "updatedAt",
      render: (updatedAt: string) => dayjs(updatedAt).format("DD/MM/YYYY"),
      align: "center" as const,
      width: 90,
    },
    {
      title: "Thao tác",
      key: "action",
      align: "center" as const,
      fixed: "right",
      width: 80,
      render: (_: any, record: any) => {
        return (
          <Space>
            <Tooltip title="Chi tiết" trigger="hover">
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                icon={<InfoOutlined rev={undefined} />}
                onClick={() =>
                  RootPageRouter.gotoChild("orderReturnStatusDetail", {
                    params: {
                      returnStatusId: record.returnOrderStatusId.toString(),
                    },
                    // queryString: "?action=edit",
                  })
                }
              />
            </Tooltip>
            <Tooltip title="Sửa" trigger="hover">
              <BaseButton
                type="primary"
                bgColor={COLOR.GREEN[500]}
                hoverColor={COLOR.GREEN[700]}
                icon={<EditOutlined rev={undefined} />}
                onClick={() =>
                  RootPageRouter.gotoChild("orderReturnStatusDetail", {
                    params: {
                      returnStatusId: record?.returnOrderStatusId.toString(),
                    },
                    queryString: "?action=edit",
                  })
                }
              />
            </Tooltip>

            <Tooltip title="Xoá" trigger="hover">
              <BaseButton
                type="primary"
                bgColor={COLOR.RED[500]}
                hoverColor={COLOR.RED[700]}
                icon={<DeleteOutlined rev={undefined} />}
                onClick={() => {
                  handleDeleteReturnStatus(record.returnOrderStatusId);
                }}
              />
            </Tooltip>
          </Space>
        );
      },
    },
  ];

  const handleClose = () => {
    formModall.resetFields();
    handleCloseModal();
  };

  return (
    <>
      <title>Trạng thái đổi trả</title>
      <Section>
        <div className="p-4">
          <Card
            title="TRẠNG THÁI ĐỔI TRẢ"
            style={{ marginLeft: "2.5%", marginRight: "2.5%" }}
            extra={
              <div className="grid grid-cols-1 lg:grid-cols-[1fr_max-content] gap-3">
                <Input
                  placeholder="Tìm kiếm"
                  // size="large"
                  allowClear
                  prefix={
                    <SearchOutlined className="text-xl" rev={undefined} />
                  }
                />
                <BaseButton
                  type="primary"
                  className="w-fit"
                  bgColor={COLOR.BLUE[500]}
                  hoverColor={COLOR.BLUE[700]}
                  icon={<PlusOutlined rev={undefined} />}
                  onClick={() =>
                    handleOpenModalByType("createOrderReturnStatus")
                  }
                >
                  Tạo mới
                </BaseButton>
              </div>
            }
          >
            <div className="backgroundW">
              <AntTable
                columns={columns as any}
                dataSource={orderReturnStatuses}
                loading={loading}
                tableLayout="auto"
                // scroll={{ x: 1000 }}
                pagination={false}
                rowKey="_id"
              />
            </div>
            <div className="backgroundW">
              <Row
                justify="space-between"
                align="middle"
                style={{ padding: "16px" }}
              >
                <Col>
                  <Select
                    placeholder="Số lượng hiển thị"
                    value={`${pageSize}`}
                    onChange={handleChangePulldownPageSize}
                    style={{ width: 150 }}
                  >
                    {[5, 10, 15, 25, 30].map((size) => (
                      <Option key={size} value={`${size}`}>
                        {size} / pages
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col>
                  {orderReturnStatusPaginationState.totalPage && (
                    <Pagination
                      total={orderReturnStatusPaginationState.totalPage * 10}
                      showSizeChanger={false}
                      defaultCurrent={1}
                      onChange={gotoPage}
                    />
                  )}
                </Col>
              </Row>
            </div>
          </Card>
        </div>
      </Section>
      {/* <Typography.Title className="p-3 " level={4}>
        TRẠNG THÁI ĐỔI TRẢ
      </Typography.Title>
      <Form form={form} layout="vertical" className="gap-4 px-3">
        <div className="flex flex-col lg:flex-row items-end lg:justify-between gap-4">
          <Form.Item className="w-[40%]">
            <Input placeholder="Tìm kiếm" className="w-full" />
          </Form.Item>
          <Form.Item>
            <BaseButton
              type="primary"
              className="w-fit"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              icon={<PlusOutlined rev={undefined} />}
              onClick={() => handleOpenModalByType("createOrderReturnStatus")}
            >
              Tạo mới
            </BaseButton>
          </Form.Item>
        </div>
      </Form> */}

      {/* <Section>
        <Table
          modofiers="borderdotted"
          scroll={{ x: 1500 }}
          loading={loading}
          hasData={orderReturnStatuses.length > 0}
        >
          <Thead>
            <Tr>
              <Th modifiers="center" stickyLeft colSpan={1}>
                STT
              </Th>
              <Th
                modifiers="center"
                isSortable
                colSpan={3}
                onSort={() => toggleOrderReturnStatusesBy("name")}
              >
                Tên trạng thái đổi trả
              </Th>
              <Th modifiers="center" colSpan={2}>
                Màu sắc
              </Th>
              <Th modifiers="center" colSpan={2}>
                Mô tả
              </Th>
              <Th modifiers="center" colSpan={2}>
                Mặc định
              </Th>
              <Th modifiers="center" colSpan={2}>
                Tagline đổi trả
              </Th>
              <Th modifiers="center" colSpan={2}>
                Thứ tự hiển thị
              </Th>
              <Th
                modifiers="center"
                isSortable
                colSpan={3}
                onSort={() => toggleOrderReturnStatusesBy("updatedAt")}
              >
                Cập nhật cuối
              </Th>
              <Th modifiers="center" stickyRight colSpan={3}>
                Thao tác
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {orderReturnStatuses.map(
              (
                {
                  color,
                  description,
                  displayOrder,
                  isDefault,
                  name,
                  updatedAt,
                  returnOrderStatusId,
                  returnOrderStatusTaglines,
                },
                index
              ) => (
                <Tr key={returnOrderStatusId}>
                  <Td modifiers="center" stickyLeft colSpan={1}>
                    {index + 1}
                  </Td>
                  <Td modifiers="center" colSpan={3}>
                    {name}
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    <Tag color={color} />
                  </Td>
                  <Td modifiers="center" colSpan={2}>
                    {description}
                  </Td>
                  <Td modifiers="center" colSpan={2}>
                    <Checkbox defaultChecked={isDefault} disabled />
                  </Td>
                  <Td modifiers="center" colSpan={2}>
                    <Link
                      to={RootPageRouter.children.listOrderReturnStatusTagline.generatePath(
                        { returnStatusId: returnOrderStatusId?.toString() }
                      )}
                    >
                      <span
                        style={{
                          color: "#FF1515",
                        }}
                      >
                        {(returnOrderStatusTaglines || []).length} Tagline
                      </span>
                    </Link>
                  </Td>
                  <Td modifiers="center" colSpan={2}>
                    {displayOrder}
                  </Td>
                  <Td modifiers="center" colSpan={3}>
                    {dayjs(updatedAt).format("DD/MM/YYYY HH:mm")}
                  </Td>
                  <Td modifiers="center" stickyRight colSpan={3}>
                    <TableManipulation
                      infoAction={{
                        id: `${returnOrderStatusId}info`,
                        action: () =>
                          RootPageRouter.gotoChild("orderReturnStatusDetail", {
                            params: {
                              returnStatusId: returnOrderStatusId?.toString(),
                            },
                          }),
                      }}
                      editAction={{
                        id: `${returnOrderStatusId}edit`,
                        action: () =>
                          RootPageRouter.gotoChild("orderReturnStatusDetail", {
                            params: {
                              returnStatusId: returnOrderStatusId?.toString(),
                            },
                            queryString: "?action=edit",
                          }),
                      }}
                      deleteAction={{
                        id: `${returnOrderStatusId}delete`,
                      }}
                    />
                  </Td>
                </Tr>
              )
            )}
          </Tbody>
        </Table>
      </Section> */}
      {/* <Section>
        <PaginationSection
          appearanceOption={
            <Pulldown
              placeholder="Số lượng hiển thị"
              options={[
                { label: "5", value: "5" },
                { label: "10", value: "10" },
                { label: "15", value: "15" },
                { label: "20", value: "20" },
              ]}
              value={{
                label: pageSize.toString(),
                value: pageSize.toString(),
              }}
              onChange={handleChangePulldownPageSize}
            />
          }
          paginateOption={
            orderReturnStatusPaginationState.totalPage && (
              <Pagination
                modifiers="center"
                total={orderReturnStatusPaginationState.totalPage}
                pageCount={5}
                defaultCurrentPage={1}
                onPageChange={gotoPage}
                ref={paginationRef}
              />
            )
          }
        />
      </Section> */}
      <AntModal
        width={650}
        styles={{
          header: {
            textAlign: "center",
          },
        }}
        open={modalTypeIsOpen("createOrderReturnStatus")}
        onCancel={handleClose}
        destroyOnClose
        closeIcon={null}
        centered
        title={
          <Typography.Title level={4}>
            TẠO MỚI TRẠNG THÁI ĐỔI TRẢ
          </Typography.Title>
        }
        footer={
          <Flex justify="end" align="center" gap={5}>
            <BaseButton
              type="primary"
              bgColor={COLOR.GRAY[500]}
              hoverColor={COLOR.GRAY[600]}
              onClick={handleClose}
            >
              Hủy
            </BaseButton>
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              onClick={() => {
                formModall.submit();
              }}
              disabled={modalCreator.createOrderReturnStatusState.loading}
            >
              Lưu
            </BaseButton>
          </Flex>
        }
      >
        {/* <FormContainer
          validationSchema={inputValidationSchema}
          onSubmit={handleSubmitCreateReturnOrderStatus}
        >
          <Formfield label="Tên trạng thái đổi trả" name="name">
            <TextfieldHookForm name="name" />
          </Formfield>
          <Formfield label="Mô tả" name="description">
            <TextareafieldHookForm name="description" />
          </Formfield>
          <Formfield label="Màu sắc" name="color">
            <ColorPicker onChangeClolor={modalCreator.onChangeColor} />
          </Formfield>
          <Formfield label="Thứ tự hiển thị" name="displayOrder">
            <NumberfieldHookForm name="displayOrder" />
          </Formfield>
          <Formfield label="Trạng thái trước" name="prevReturnOrderStatusIds">
            <PulldownHookForm
              name="prevReturnOrderStatusIds"
              placeholder="Trạng thái trước"
              isMultiSelect
              options={modalCreator.orderStatusOptionPulldown}
              triggerLoadMore={modalCreator.handleLoadmoreReturnStatus}
            />
          </Formfield>
          <div className="u-mt-15">
            <Toggle label="Mặc định" ref={defaultReturnOrderRef} />
          </div>
        </FormContainer> */}
        <Form
          form={formModall}
          layout="vertical"
          onFinish={handleSubmitCreateReturnOrderStatus}
          initialValues={{ isDefault: false }}
        >
          <Form.Item
            label="Tên trạng thái đổi trả"
            name="name"
            rules={[
              { required: true, message: "Vui lòng nhập tên trạng thái" },
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item label="Mô tả" name="description">
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item label="Màu sắc" name="color" initialValue="#000">
            <AntColorPicker />
          </Form.Item>

          <Form.Item
            label="Thứ tự hiển thị"
            name="displayOrder"
            initialValue={0}
          >
            <InputNumber min={0} style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item label="Trạng thái trước" name="prevReturnOrderStatusIds">
            <BaseSelect
              mode="multiple"
              placeholder="Trạng thái trước"
              options={modalCreator.orderStatusOptionPulldown}
              fieldNames={{
                label: "label",
                value: "value",
              }}
              allowClear
              showSearch
              optionFilterProp="label"
            />
          </Form.Item>

          <Form.Item label="Mặc định" name="isDefault" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Form>
      </AntModal>
    </>
  );
};

export default IndexPage;
