import { useCallback } from "react";

import { createAxiosInstance } from "@dpt/react-pack";
import { AxiosRequestConfig } from "axios";

import { toastSingleMode } from "components/atoms/toastify";
import { useAsync } from "hooks/useAsync";
import { refreshToken } from "services/crm/employee";
import createHTTP from "services/http";
import {
  getEmployeeSessionInfo,
  initialEmployeeSession,
} from "services/session";
import store from "store";
import authSlice from "store/auth/reducer";

const crmDriverV1 = createAxiosInstance({
  baseURL: process.env.REACT_APP_API_BASE_URL_CRM,
});

type FailedQueueTypes = {
  reject: (error: Error) => void;
  resolve: (value: unknown) => void;
};

// for multiple requests
let isTokenRefreshing = false;
let failedQueueByInvaldiatedToken: Array<FailedQueueTypes> = [];

const processQueue = (error: Error | null, token = null) => {
  failedQueueByInvaldiatedToken.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueueByInvaldiatedToken = [];
};

crmDriverV1.interceptors.request.use((config) => {
  if (!config.headers?.authorization) {
    const employeeSession = getEmployeeSessionInfo();
    if (employeeSession) {
      config.headers!.authorization = `Bearer ${employeeSession.accessToken}`;
    }
  }
  return config;
});

crmDriverV1.interceptors.response.use(
  async (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config as AxiosRequestConfig;
    originalRequest.headers!.authorization = "";

    if (error.response?.status === 401) {
      // NOTE: when token is updating, return API connection queue
      if (isTokenRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueueByInvaldiatedToken.push({
            resolve,
            reject,
          });
        })
          .then(() => {
            return crmDriverV1.request(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      isTokenRefreshing = true;

      return new Promise((resolve, reject) => {
        const employeeSession = getEmployeeSessionInfo();

        if (employeeSession && employeeSession.refreshToken) {
          refreshToken({ refreshToken: employeeSession.refreshToken })
            .then((res) => {
              initialEmployeeSession({
                accessToken: res.data.data?.accessToken,
                refreshToken: res.data.data?.refreshToken,
              });
              processQueue(null, res.data.accessToken);
              resolve(crmDriverV1.request(originalRequest));
            })
            .catch((err) => {
              processQueue(err, null);
              store.dispatch(authSlice.actions.employeeLogout());
              toastSingleMode({
                type: "warning",
                message: "Phiên đăng nhập",
                descripition:
                  "Phiên đăng nhập của bạn đã hết. Vui lòng đăng nhập lại để tiếp tục.",
              });
              reject(err);
            })
            .finally(() => {
              isTokenRefreshing = false;
            });
        }
      });
    }
    return Promise.reject(error);
  }
);

export const useCrmApi = (...arg: Parameters<typeof useAsync>) => {
  const [funcExec, ...rest] = arg;

  const wrapper = useCallback(
    async (...argFunc: Parameters<typeof funcExec>) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const res: any = await funcExec(...argFunc);
      return res?.data.data;
    },
    [funcExec]
  );

  return useAsync(wrapper, ...rest);
};

export default crmDriverV1;
