import { Model, Number, ModelValue, String } from "libs/domain";

export const OrderPaymentSchema = {
  paymentId: Number(),
  method: Number(),
  amount: Number(),
  orderId: Number(),
  orderCode: String(),
  status: Number(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  createdAt: (raw: any) => raw && new Date(raw),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => raw && new Date(raw),
};

export const OrderPaymentModel = new Model(OrderPaymentSchema);
export type OrderPaymentType = ModelValue<typeof OrderPaymentModel>;

export const paymentDisplayMethod: { [key: number]: string } = {
  0: "COD",
  1: "VnPay",
  2: "ZaloPay",
  3: "MoMo",
};

export const paymentDisplayStatus: { [key: number]: string } = {
  0: "Chờ thanh toán",
  1: "Đã thanh toán",
};
