import pick from "object.pick";

import { createMapper, fromSchema, merge } from "libs/adapters/dto";

import { OrderReturnStatusSchema } from "..";

export const createOrderReturnStatusDto = createMapper(
  fromSchema(
    pick(OrderReturnStatusSchema, [
      "name",
      "isDefault",
      "displayOrder",
      "description",
      "prevReturnOrderStatusIds",
      "color",
    ])
  ),
  merge((data) => ({
    name: data.name.trim(),
    description: data.description || undefined,
    prevReturnOrderStatusIds: !data.prevReturnOrderStatusIds?.length
      ? null
      : data.prevReturnOrderStatusIds,
  }))
);
export type CreateOrderReturnStatusDtoType = ReturnType<
  typeof createOrderReturnStatusDto
>;
