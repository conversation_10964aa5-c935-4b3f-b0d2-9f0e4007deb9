## CODE CŨ

```tsx
// /* eslint-disable @typescript-eslint/no-explicit-any */
// /* eslint-disable */
import {
  ChangeEvent,
  FocusEvent,
  ReactNode,
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";

import produce from "immer";
import { ValueType } from "react-select";

// import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Numberfield, NumberfieldHookForm } from "components/atoms/numberfield";
import { Phonefield } from "components/atoms/phonefield";
import {
  Pulldown,
  PulldownHookForm,
  ExternalRegister,
} from "components/atoms/pulldown";

import "./button.css";

import { Radio, useRadioProvider } from "components/atoms/radio";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { Textfield, TextfieldHookForm } from "components/atoms/textfield";
import { toastSingleMode } from "components/atoms/toastify";
import { Calendar } from "components/molecules/calendar";
import { Formfield } from "components/molecules/formfield";
import { Shippingaddress } from "components/molecules/shippingaddress";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Table, Tbody, Td, Th, Thead, Tr } from "components/organisms/table";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import debounce from "helpers/debounce";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { FormContainer } from "helpers/form";
import useDerivedStateFromProps from "helpers/react-hooks/useDerivedStateFromProps";
import useDidMount from "helpers/react-hooks/useDidMount";
import { useAsync } from "hooks/useAsync";
import {
  CustomerPickerByPhone,
  CustomerShippingAddressModal,
  CustomerProfileEntityType,
  primaryEmail,
  primaryPhone,
  usePulldownShippingAddress,
  shippingAddressDto,
  displayFullAddress,
  ShippingAddressDtoType,
  createCustomerShippingAddressDto,
  updateCustomerShippingAddressDto,
  SubmitShippingAddressType,
} from "modules/customer";
import { usePulldownAssignEmployee } from "modules/employee";
import {
  calcTotalAmount,
  OrderDetailDtoType,
  orderItemDetailDto,
  usePulldownOrderReturnStatus,
  usePulldownOrderStatus,
  usePulldownOrderStatusTagline,
  usePulldownReturnStatusTagline,
  usePulldownOrderSource,
} from "modules/order";
import { ProductSkuPickerModal, SubmitItem } from "modules/product";
import TableManipulation from "pages/Common/tableManipulation";
import {
  createCustomerShippingAddress,
  getSingleCustomerShippingAddress,
  updateCustomerShippingAddress,
} from "services/crm/customer";
import {
  Card,
  Collapse,
  CollapseProps,
  Button,
  ThemeConfig,
  ConfigProvider,
} from "antd";
import { PlusSquareFilled } from "@ant-design/icons";

interface UncontrolledProperties {
  shippingCost: number;
  applicableFee: number;
}

type OrderItem = OrderDetailDtoType;
type ShippingAddress = ShippingAddressDtoType;

type CustomerItem = CustomerProfileEntityType;

type ModalType =
  | "productSkuPicker"
  | "customerPickerByPhone"
  | "customerCreateShippingModal"
  | "customerUpdateShippingModal";

interface State {
  order?: OrderItem;
  modalState: {
    open: boolean;
    type?: ModalType;
  };
  controlledProperties: {
    selectedOrderStatusId?: number;
    selectedOrderStatusTaglineId?: number;
    selectedOrderReturnStatusId?: number;
    selectedOrderReturnStatusTaglineId?: number;
    items: OrderItem["items"];
    totalAmount: number;
    customer?: CustomerItem;
  };
  customerShippingAddress?: ShippingAddress;
}

export interface OrderFormProps<DataForm> {
  validationSchema: unknown;
  onSubmit: (formData: DataForm & { totalAmount: number }) => void;
  mode?: "view-only" | "editable" | "create";
  initialOrderDetail?: OrderItem;
  cancelButton?: ReactNode;
  submitButton?: ReactNode;
}

export const OrderForm = <DataForm>({
  validationSchema,
  onSubmit,
  mode = "view-only",
  initialOrderDetail,
  cancelButton,
  submitButton,
}: OrderFormProps<DataForm>) => {
  const firstRenderRef = useRef<boolean>(true);
  const firstSelectedRef = useRef<boolean>(true);
  const editMode = mode === "editable";
  const createMode = mode === "create";
  const viewMode = mode === "view-only";

  const uncontrolledPropertiesRef = useRef<UncontrolledProperties>({
    shippingCost: 0,
    applicableFee: 0,
  });

  const shippingAddressSelectRef = useRef<ExternalRegister>();

  const [state, setState] = useState<State>({
    order: undefined,
    modalState: {
      open: false,
      type: undefined,
    },
    controlledProperties: {
      selectedOrderStatusId: undefined,
      selectedOrderStatusTaglineId: undefined,
      selectedOrderReturnStatusId: undefined,
      selectedOrderReturnStatusTaglineId: undefined,
      items: [],
      totalAmount: 0,
      customer: initialOrderDetail?.customer,
    },
    customerShippingAddress: initialOrderDetail?.shippingAddress,
  });

  const [
    getSingleCustomerShippingAddressExec,
    getSingleCustomerShippingAddressState,
  ] = useAsync(
    useCallback(
      (payload: { customerId: number; shippingAddressId: number }) =>
        getSingleCustomerShippingAddress({ ...payload }).then((res) =>
          shippingAddressDto(res.data.data)
        ),
      []
    ),
    {
      onSuccess: useCallback((res) => {
        setState(
          produce((draft) => {
            draft.customerShippingAddress = res;
          })
        );
      }, []),
    }
  );
  const handleChangeShippingAddress = useCallback(
    (option: ValueType<{ label: string; value: string }, false>) => {
      if (firstRenderRef.current && !createMode) {
        firstRenderRef.current = false;
        return;
      }
      // eslint-disable-next-line no-console
      console.log("running");

      if (firstSelectedRef.current) {
        firstSelectedRef.current = false;
      }

      getSingleCustomerShippingAddressExec({
        customerId: Number(state.controlledProperties.customer?.customerId),
        shippingAddressId: Number(option?.value),
      });
    },
    [
      createMode,
      getSingleCustomerShippingAddressExec,
      state.controlledProperties.customer?.customerId,
    ]
  );

  const handleOnCloseModal = useCallback(() => {
    if (!state.modalState.open) return;
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.type = undefined;
      })
    );
  }, [state.modalState]);

  const {
    fetchCustomerShippingAddress,
    shippingAddressOptions: customerShippingAddressOptions,
    formatShippingAddressOption,
    fetchCustomerShippingAddressLoading,
  } = usePulldownShippingAddress({
    excludePending: true,
  });

  const {
    orderStatusesOptions,
    fetchOrderStatus,
    getOptionByValue: getOrderStatusOptionById,
  } = usePulldownOrderStatus({
    excludePending: true,
  });

  const {
    fetchOrderStatusTagline,
    orderStatusTaglineOptions,
    orderStatusTaglines,
    getOptionByValue: getTaglineOptionById,
  } = usePulldownOrderStatusTagline({
    excludePending: true,
    orderStatusId: state.controlledProperties.selectedOrderStatusId,
  });

  const {
    fetchOrderReturnStatus,
    orderReturnStatusOptions,
    getOptionByValue: getReturnStatusOptionById,
  } = usePulldownOrderReturnStatus({
    excludePending: true,
  });

  const {
    fetchReturnOrderStatusTagline,
    orderReturnStatusTaglineOptions,
    orderReturnStatusTaglines,
    getOptionByValue: getReturnStatusTaglineOptionById,
  } = usePulldownReturnStatusTagline({
    excludePending: true,
    orderReturnStatusId: state.controlledProperties.selectedOrderReturnStatusId,
  });

  const {
    loadMoreEmployee,
    assignEmployeeOptions,
    loadMoreEmployeeState,
    formatAssignEmployeeOption,
  } = usePulldownAssignEmployee();

  const selectedStatusTagline =
    getTaglineOptionById(
      state.controlledProperties.selectedOrderStatusTaglineId
    ) || null;

  const selectedOrderStatus =
    getOrderStatusOptionById(
      state.controlledProperties.selectedOrderStatusId
    ) || null;

  const selectedReturnStatus =
    getReturnStatusOptionById(
      state.controlledProperties.selectedOrderReturnStatusId
    ) || null;

  const selectedReturnStatusTagline =
    getReturnStatusTaglineOptionById(
      state.controlledProperties.selectedOrderReturnStatusTaglineId
    ) || null;

  const requestReCalcTotalAmount = useCallback(
    (payload?: {
      shippingCost?: number;
      items?: [];
      applicableFee?: number;
    }) => {
      const shippingCost =
        payload?.shippingCost ?? uncontrolledPropertiesRef.current.shippingCost;
      const applicableFee =
        payload?.applicableFee ??
        uncontrolledPropertiesRef.current.applicableFee;

      uncontrolledPropertiesRef.current = {
        ...uncontrolledPropertiesRef.current,
        shippingCost,
        applicableFee,
      };
      const items = payload?.items || state.controlledProperties.items || [];
      const nextTotalAmount = calcTotalAmount({
        shippingCost,
        items,
        applicableFee,
      });
      setState((prevState) => ({
        ...prevState,
        controlledProperties: {
          ...prevState.controlledProperties,
          totalAmount: nextTotalAmount,
          items: payload?.items ?? prevState.controlledProperties.items,
        },
      }));
    },
    [state.controlledProperties.items]
  );

  const initialOrderState = useCallback((order: OrderItem) => {
    uncontrolledPropertiesRef.current = {
      shippingCost: order.shippingCost ?? 0,
      applicableFee: order.applicableFee ?? 0,
    };
    setState(
      produce((draft) => {
        draft.controlledProperties = {
          selectedOrderStatusId: order.orderStatusId,
          selectedOrderStatusTaglineId: order.orderStatusTaglineId,
          selectedOrderReturnStatusId: order.returnOrderStatusId,
          selectedOrderReturnStatusTaglineId: order.returnOrderStatusTaglineId,
          items: order.items ?? [],
          totalAmount: calcTotalAmount({
            items: order.items ?? [],
            shippingCost: order.shippingCost,
            applicableFee: order.applicableFee,
          }),
          customer: order.customer,
        };
      })
    );
  }, []);

  const setOrderProperties = useCallback(
    (properties: Partial<State["controlledProperties"]>) => {
      setState(
        produce((draft) => {
          draft.controlledProperties = Object.assign(
            draft.controlledProperties,
            properties
          );
        })
      );
    },
    []
  );

  const resetShippingAddress = useCallback(() => {
    setState(
      produce((draft) => {
        draft.customerShippingAddress = undefined;
      })
    );
  }, []);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onAssignEmployeePulldownInputChange = useCallback(
    debounce(
      (textSearch: string) => loadMoreEmployee({ name: textSearch }),
      200
    ),
    [loadMoreEmployee]
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleOnShippingCostInputChange = useCallback(
    debounce(
      (event: ChangeEvent<HTMLInputElement>) =>
        requestReCalcTotalAmount({
          shippingCost: Number(event.target.value) || 0,
        }),
      200
    ),
    [requestReCalcTotalAmount]
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleOnApplicableFeeInputChange = useCallback(
    debounce(
      (event: ChangeEvent<HTMLInputElement>) =>
        requestReCalcTotalAmount({
          applicableFee: Number(event.target.value) || 0,
        }),
      200
    ),
    [requestReCalcTotalAmount]
  );

  const modalTypeIsOpen = useCallback(
    (type: ModalType) =>
      state.modalState.open && state.modalState.type === type,
    [state.modalState]
  );

  const handleOnSubmitProductSkuPickerForm = useCallback(
    (skuItems: SubmitItem[]) => {
      setState(
        produce((draft) => {
          if (!draft.controlledProperties.items) {
            draft.controlledProperties.items = [];
          }
          skuItems.forEach((skuItemFromPicker) => {
            const skuItemExisted = draft.controlledProperties.items!.find(
              (skuItem: { skuId: string }) =>
                skuItem.skuId === skuItemFromPicker.sku.skuId
            );
            if (skuItemExisted) {
              skuItemExisted.quantity += skuItemFromPicker.quantity;
              skuItemExisted.totalAmount += skuItemFromPicker.totalAmount;
            } else {
              draft.controlledProperties.items?.push(
                orderItemDetailDto({
                  ...skuItemFromPicker.sku,
                  sku: skuItemFromPicker.sku,
                  quantity: skuItemFromPicker.quantity,
                  unitPrice: skuItemFromPicker.unitPrice,
                  totalAmount: skuItemFromPicker.totalAmount,
                })
              );
            }
          });
        })
      );
    },
    []
  );

  const handleRemoveItemByIndex = useCallback((index: number) => {
    setState(
      produce((draft) => {
        const nextItems = [...(draft.controlledProperties.items || [])];
        nextItems.splice(index, 1);
        draft.controlledProperties.items = nextItems;
      })
    );
  }, []);

  const handleOnChangeItemQuantityByIndex = useCallback(
    (index: number, quantity: number) => {
      if (quantity < 1) return;
      setState(
        produce((draft) => {
          const item = draft.controlledProperties.items?.[index];
          if (item) {
            item.quantity = quantity;
          }
        })
      );
    },
    []
  );

  const handleOnSubmit = useCallback(
    (formData: DataForm) => {
      // if (!state.controlledProperties.items?.length) {
      //   toastSingleMode({
      //     type: "error",
      //     message: "Vui lòng chọn ít nhất một sản phẩm",
      //   });

      //   return;
      // }
      onSubmit({
        ...formData,
        customer: state.controlledProperties.customer,
        customerId: state.controlledProperties.customer?.customerId,
        totalAmount: state.controlledProperties.totalAmount,
        items: state.controlledProperties.items,
      });
    },
    [
      onSubmit,
      state.controlledProperties.totalAmount,
      state.controlledProperties.customer,
      state.controlledProperties.items,
    ]
  );

  const handleOnFocusCustomerPhoneInput = useCallback(
    (event: FocusEvent<HTMLInputElement>) => {
      setState(
        produce((draft) => {
          draft.modalState.type = "customerPickerByPhone";
          draft.modalState.open = true;
        })
      );
      event.currentTarget.blur();
    },
    []
  );

  const handleOpenModalByType = (type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.type = type;
      })
    );
  };

  const antdToken: ThemeConfig = {
    components: {
      Button: {
        colorPrimary: "#1684b9",
        colorPrimaryHover: "#ffffff",
        colorPrimaryTextHover: "#1684b9",
      },
    },
  };

  const {
    fetchOrderSources,
    formatOrderSourceOption,
    orderSourceOptions,
  } = usePulldownOrderSource({
    excludePending: true,
  });

  useDerivedStateFromProps((_, nextOrderItem) => {
    if (nextOrderItem) {
      initialOrderState(nextOrderItem);
    }
  }, initialOrderDetail);

  useDerivedStateFromProps((_, nextOrderStatusTagline) => {
    if (nextOrderStatusTagline) {
      const shouldShowDefault = nextOrderStatusTagline.find(
        ({ taglineId }) =>
          taglineId === initialOrderDetail?.orderStatusTaglineId
      );
      setOrderProperties({
        selectedOrderStatusTaglineId: shouldShowDefault?.taglineId,
      });
    }
  }, orderStatusTaglines);

  useDerivedStateFromProps((_, nextOrderReturnStatusTaglines) => {
    if (nextOrderReturnStatusTaglines) {
      const shouldShowDefault = nextOrderReturnStatusTaglines.find(
        ({ returnOrderStatusTaglineId }) =>
          returnOrderStatusTaglineId ===
          initialOrderDetail?.returnOrderStatusTaglineId
      );
      setOrderProperties({
        selectedOrderReturnStatusTaglineId:
          shouldShowDefault?.returnOrderStatusTaglineId,
      });
    }
  }, orderReturnStatusTaglines);

  useDerivedStateFromProps(() => {
    requestReCalcTotalAmount();
  }, state.controlledProperties.items);

  useDerivedStateFromProps((prevCustomer) => {
    if (!prevCustomer) return;
    if (shippingAddressSelectRef.current) {
      shippingAddressSelectRef.current.reset();
    }
  }, state.controlledProperties.customer);

  useLayoutEffect(() => {
    if (state.controlledProperties.customer) {
      fetchCustomerShippingAddress(
        state.controlledProperties.customer.customerId
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.controlledProperties.customer]);

  useLayoutEffect(() => {
    fetchOrderStatusTagline();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.controlledProperties.selectedOrderStatusId]);

  useLayoutEffect(() => {
    fetchReturnOrderStatusTagline();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.controlledProperties.selectedOrderReturnStatusId]);

  useEffect(() => {
    const customerId = initialOrderDetail?.customer?.customerId;
    if (customerId) {
      fetchCustomerShippingAddress(customerId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialOrderDetail?.customer?.customerId]);

  useDidMount(() => {
    fetchOrderStatus();
    fetchOrderReturnStatus();
    fetchOrderSources();
    loadMoreEmployee();
  });

  const { bindSilent } = useRadioProvider<{
    gender: "GENDER_MALE" | "GENDER_FEMALE";
  }>({
    defaultValue: {
      gender: "GENDER_MALE",
    },
  });

  const handleSubmitShippingAddressByMode = useCallback(
    async (formData: SubmitShippingAddressType) => {
      if (!state.controlledProperties.customer) return;
      const { customerId } = state.controlledProperties.customer;
      if (modalTypeIsOpen("customerCreateShippingModal")) {
        await createCustomerShippingAddress(
          customerId,
          createCustomerShippingAddressDto(formData)
        );
      }

      if (
        modalTypeIsOpen("customerUpdateShippingModal") &&
        state.customerShippingAddress
      ) {
        await updateCustomerShippingAddress(
          customerId,
          state.customerShippingAddress.shippingAddressId,
          updateCustomerShippingAddressDto(formData)
        );
      }
    },
    [
      modalTypeIsOpen,
      state.controlledProperties.customer,
      state.customerShippingAddress,
    ]
  );

  const [submitCustomerShippingAddressExec] = useAsync(
    handleSubmitShippingAddressByMode,
    {
      excludePending: true,
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
      onSuccess: useCallback(() => {
        if (!state.controlledProperties.customer) return;
        const create = modalTypeIsOpen("customerCreateShippingModal");
        toastSingleMode({
          type: "success",
          message: `${create ? "Tạo" : "Cập nhật"} địa chỉ mới thành công`,
        });

        const { customerId } = state.controlledProperties.customer;

        handleOnCloseModal();
        fetchCustomerShippingAddress(customerId);
        if (!create && state.customerShippingAddress) {
          getSingleCustomerShippingAddressExec({
            customerId,
            shippingAddressId: state.customerShippingAddress.shippingAddressId,
          });
        }
      }, [
        fetchCustomerShippingAddress,
        getSingleCustomerShippingAddressExec,
        handleOnCloseModal,
        modalTypeIsOpen,
        state.controlledProperties.customer,
        state.customerShippingAddress,
      ]),
    }
  );

  const itemKhachHang: CollapseProps["items"] = [
    {
      key: "1",
      label: (
        <>
          Khách hàng{" "}
          <span style={{ color: "rgb(22, 132, 185)" }}>
            {state.controlledProperties.customer?.name}
          </span>
        </>
      ),
      children: (
        <Section>
          <div style={{ backgroundColor: "white" }}>
            {" "}
            <Row>
              <Col lg="6" className="u-mb-15">
                <Formfield label="Điện thoại" name="phone">
                  <Phonefield
                    name="phone"
                    disabled={viewMode}
                    value={
                      primaryPhone(state.controlledProperties.customer) || ""
                    }
                    onFocus={handleOnFocusCustomerPhoneInput}
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Email" name="email">
                  <Textfield
                    name="email"
                    disabled
                    value={
                      primaryEmail(state.controlledProperties.customer) || ""
                    }
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Họ Tên" name="fullname">
                  <Textfield
                    name="fullname"
                    disabled
                    value={state.controlledProperties.customer?.name}
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Ngày sinh" name="birthday">
                  <Calendar
                    disabled
                    value={
                      state.controlledProperties.customer?.birthDay &&
                      new Date(state.controlledProperties.customer?.birthDay)
                    }
                  />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Giới tính" name="sex">
                  <div className="d-flex align-items-center">
                    <div className="u-pr-13">
                      <Radio
                        onChange={bindSilent("gender")}
                        name="gender"
                        value="GENDER_MALE"
                        readOnly
                        disabled
                        checked={
                          state.controlledProperties.customer?.gender === 1
                        }
                      >
                        Nam
                      </Radio>
                    </div>
                    <Radio
                      onChange={bindSilent("gender")}
                      name="gender"
                      value="GENDER_FEMALE"
                      disabled
                      readOnly
                      checked={
                        state.controlledProperties.customer?.gender === 0
                      }
                    >
                      Nữ
                    </Radio>
                  </div>
                </Formfield>
              </Col>

              <Col xs="12" className="u-mb-15">
                <Formfield label="Địa chỉ" name="address">
                  <PulldownHookForm
                    name="shippingAddress"
                    register={shippingAddressSelectRef}
                    isSearchable
                    options={customerShippingAddressOptions}
                    isDisabled={viewMode}
                    isLoading={fetchCustomerShippingAddressLoading}
                    value={
                      state.customerShippingAddress &&
                      formatShippingAddressOption(state.customerShippingAddress)
                    }
                    defaultValue={
                      state.customerShippingAddress &&
                      formatShippingAddressOption(state.customerShippingAddress)
                    }
                    onChange={handleChangeShippingAddress}
                    placeholder="Địa chỉ"
                  />
                </Formfield>
              </Col>
              {state.customerShippingAddress && (
                <Col lg="12" className="u-mb-15">
                  <SpinnerContainer
                    animating={getSingleCustomerShippingAddressState.loading}
                  >
                    <Shippingaddress
                      name={state.customerShippingAddress.name}
                      address={displayFullAddress(
                        state.customerShippingAddress
                      )}
                      phone={state.customerShippingAddress.phoneNumber}
                      editable={
                        (editMode || createMode) && !firstSelectedRef.current
                      }
                      onEdit={() =>
                        handleOpenModalByType("customerUpdateShippingModal")
                      }
                    />
                  </SpinnerContainer>
                </Col>
              )}

              {(editMode || createMode) && state.controlledProperties.customer && (
                <Col lg="12" className="d-flex justify-content-center u-mt-20">
                  {/* <Button
                    buttonType="textbutton"
                    onClick={() =>
                      handleOpenModalByType("customerCreateShippingModal")
                    }
                  >
                    Thêm địa chỉ mới
                  </Button> */}
                  <Button
                    type="primary"
                    size={"large"}
                    onClick={() =>
                      handleOpenModalByType("customerCreateShippingModal")
                    }
                  >
                    Thêm địa chỉ mới
                  </Button>
                </Col>
              )}
            </Row>
          </div>
        </Section>
      ),
    },
  ];

  const itemDonHang: CollapseProps["items"] = [
    {
      key: "1",
      label: <>Đơn hàng</>,
      children: (
        <Section>
          <div style={{ backgroundColor: "white" }}>
            {" "}
            <Row>
              <Col lg="6" className="u-mb-15">
                <Formfield label="Danh mục" name="category">
                  <Textfield name="category" disabled={viewMode} />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Shop" name="shop">
                  <Pulldown
                    isSearchable
                    options={[]}
                    isDisabled={viewMode}
                    placeholder="Shop"
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Nguồn" name="orderSource">
                  <PulldownHookForm
                    name="orderSource"
                    placeholder="Nguồn"
                    isDisabled={viewMode}
                    options={orderSourceOptions}
                    defaultValue={
                      initialOrderDetail?.orderSource &&
                      formatOrderSourceOption(initialOrderDetail.orderSource)
                    }
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Ngày tạo" name="createdAt">
                  <Calendar
                    disabled={viewMode}
                    defaultValue={
                      initialOrderDetail?.createdAt &&
                      new Date(initialOrderDetail.createdAt)
                    }
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Vận chuyển" name="shipping">
                  <Pulldown
                    isSearchable
                    options={[]}
                    isDisabled={viewMode}
                    placeholder="Vận chuyển"
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Mã bưu kiện" name="zipcode">
                  <Textfield name="zipcode" disabled={viewMode} />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Trạng thái" name="status">
                  <PulldownHookForm
                    name="orderStatus"
                    isSearchable={false}
                    isDisabled={viewMode}
                    options={orderStatusesOptions}
                    defaultValue={selectedOrderStatus}
                    value={selectedOrderStatus}
                    placeholder="Trạng thái"
                    onChange={(option) =>
                      option?.value &&
                      setOrderProperties({
                        selectedOrderStatusId: Number(option.value),
                      })
                    }
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Tagline" name="Tagline">
                  <PulldownHookForm
                    name="orderStatusTagline"
                    isSearchable={false}
                    isDisabled={viewMode}
                    options={orderStatusTaglineOptions}
                    value={selectedStatusTagline}
                    defaultValue={selectedStatusTagline}
                    onChange={(option) =>
                      option?.value &&
                      setOrderProperties({
                        selectedOrderStatusTaglineId: Number(option.value),
                      })
                    }
                    placeholder="Tagline"
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Trạng thái đổi hàng" name="barterstatus">
                  <PulldownHookForm
                    name="returnStatus"
                    isSearchable={false}
                    options={orderReturnStatusOptions}
                    value={selectedReturnStatus}
                    defaultValue={selectedReturnStatus}
                    onChange={(option) =>
                      option?.value &&
                      setOrderProperties({
                        selectedOrderReturnStatusId: Number(option.value),
                      })
                    }
                    isDisabled={viewMode}
                    placeholder="Trạng thái đổi hàng"
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Tagline đổi trả" name="Tagline">
                  <PulldownHookForm
                    name="returnStatusTagline"
                    isSearchable={false}
                    options={orderReturnStatusTaglineOptions}
                    value={selectedReturnStatusTagline}
                    defaultValue={selectedReturnStatusTagline}
                    onChange={(option) =>
                      option?.value &&
                      setOrderProperties({
                        selectedOrderReturnStatusTaglineId: Number(
                          option.value
                        ),
                      })
                    }
                    isDisabled={viewMode}
                    placeholder="Tagline đổi trả"
                  />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Phân công" name="assignment">
                  <PulldownHookForm
                    name="assignedEmployee"
                    isClearable
                    isSearchable
                    isDisabled={viewMode}
                    triggerLoadMore={() => loadMoreEmployee()}
                    defaultValue={
                      initialOrderDetail?.assignedEmployee &&
                      formatAssignEmployeeOption(
                        initialOrderDetail.assignedEmployee
                      )
                    }
                    isLoading={loadMoreEmployeeState.loading}
                    onInputChange={onAssignEmployeePulldownInputChange}
                    options={assignEmployeeOptions}
                    placeholder="Phân công"
                  />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Ghi chú giao hàng" name="deliveryNote">
                  <TextareafieldHookForm
                    name="deliveryNote"
                    defaultValue={initialOrderDetail?.deliveryNote}
                    disabled={viewMode}
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Tổng khối lượng (kg)" name="weight">
                  <NumberfieldHookForm
                    name="weight"
                    disabled={viewMode}
                    defaultValue={initialOrderDetail?.weight}
                  />
                </Formfield>
              </Col>

              <Col lg="6" className="u-mb-15">
                <Formfield label="Kích thước đóng gói (cm)" name="size">
                  <TextfieldHookForm
                    defaultValue={initialOrderDetail?.size}
                    disabled={viewMode}
                    name="size"
                  />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Ghi chú đơn hàng" name="orderNote">
                  <TextareafieldHookForm
                    name="orderNote"
                    disabled={viewMode}
                    defaultValue={initialOrderDetail?.orderNote}
                  />
                </Formfield>
              </Col>

              <Col lg="4" className="u-mb-15">
                <Formfield label="Chiều cao (cm)" name="customerHeight">
                  <NumberfieldHookForm
                    name="customerHeight"
                    disabled={viewMode}
                  />
                </Formfield>
              </Col>

              <Col lg="4" className="u-mb-15">
                <Formfield label="Cân nặng (kg)" name="customerWeight">
                  <NumberfieldHookForm
                    disabled={viewMode}
                    name="customerWeight"
                  />
                </Formfield>
              </Col>

              <Col lg="4" className="u-mb-15">
                <Formfield label="Vòng eo (cm)" name="customerWaist">
                  <NumberfieldHookForm
                    disabled={viewMode}
                    name="customerWaist"
                  />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Ghi chú API" name="apiNote">
                  <TextareafieldHookForm
                    name="apiNote"
                    disabled={viewMode}
                    defaultValue={initialOrderDetail?.apiNote}
                  />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Chuyển đến shop" name="shopSent">
                  <PulldownHookForm
                    name="shopSent"
                    placeholder="Chọn shop"
                    isDisabled={viewMode}
                    isSearchable
                    options={[
                      { label: "Shop1", value: "1" },
                      { label: "Shop2", value: "2" },
                    ]}
                  />
                </Formfield>
              </Col>
            </Row>
          </div>
        </Section>
      ),
    },
  ];

  const itemThanhToan: CollapseProps["items"] = [
    {
      key: "1",
      label: <>Thanh toán</>,
      children: (
        <Section>
          <div style={{ backgroundColor: "white" }}>
            <Row>
              <Col lg="12" className="u-mb-15">
                <Formfield label="Tổng" name="total">
                  <Textfield
                    name="total"
                    disabled
                    value={state.controlledProperties.totalAmount}
                  />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Giảm giá" name="discount">
                  <Textfield name="discount" disabled />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Phí ship" name="shippingCost">
                  <NumberfieldHookForm
                    name="shippingCost"
                    onChange={handleOnShippingCostInputChange}
                    defaultValue={initialOrderDetail?.shippingCost}
                    disabled={viewMode}
                  />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Phụ thu" name="applicableFee">
                  <NumberfieldHookForm
                    name="applicableFee"
                    defaultValue={initialOrderDetail?.applicableFee}
                    disabled={viewMode}
                    onChange={handleOnApplicableFeeInputChange}
                  />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Sử dụng khuyến mãi" name="promotioncode">
                  <PulldownHookForm
                    name="promotioncode"
                    id="promotioncode"
                    placeholder="Mã khuyến mãi"
                    isMultiSelect
                    isSearchable
                    isDisabled={viewMode}
                    options={[
                      { label: "test1", value: "1" },
                      { label: "test2", value: "2" },
                      { label: "test3", value: "3" },
                    ]}
                    defaultValue={[
                      { label: "test1", value: "1" },
                      { label: "test2", value: "2" },
                    ]}
                  />

                  {/* <Row>
													<Col lg="12" className="u-mb-15">
														<Selectedchoice
														color="#F2AF4A"
														onDelete={() => {}}
														title="VOUCHER12"
													/>
													</Col>
												</Row> */}
                </Formfield>
              </Col>
              <Col lg="12" className="u-mb-15">
                <Textfield disabled />
              </Col>

              <Col lg="12">
                <Formfield
                  label="Sử dụng điểm tích lũy"
                  name="useaccumulatedpoints"
                >
                  <Row>
                    <Col lg="6" className="u-mb-15">
                      <Textfield
                        name="useaccumulatedpoints"
                        disabled={viewMode}
                      />
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Textfield name="useaccumulatedpoints" disabled />
                    </Col>
                  </Row>
                </Formfield>
              </Col>

              <Col lg="12">
                <Formfield
                  label="Áp dụng mã giới thiệu"
                  name="applyreferralcode"
                >
                  <Row>
                    <Col lg="6" className="u-mb-15">
                      <Textfield name="applyreferralcode" disabled={viewMode} />
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Textfield name="applyreferralcode" disabled />
                    </Col>
                  </Row>
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Thanh toán" name="pay">
                  <Textfield name="pay" disabled />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Đã thanh toán" name="paid">
                  <Textfield name="paid" disabled />
                </Formfield>
              </Col>

              <Col lg="12" className="u-mb-15">
                <Formfield label="Còn lại" name="rest">
                  <Textfield name="rest" disabled />
                </Formfield>
              </Col>
            </Row>
          </div>
        </Section>
      ),
    },
  ];

  return (
    <>
      <FormContainer
        validationSchema={validationSchema}
        onSubmit={handleOnSubmit}
      >
        <Section>
          <Card
            title={
              <Heading type="h1" modifiers="primary">
                THÔNG TIN ĐƠN HÀNG: {initialOrderDetail?.orderId}
              </Heading>
            }
            style={{ marginLeft: "2.5%", marginRight: "2.5%" }}
          >
            <Card type="inner" title="Chi tiết đơn hàng">
              <Section>
                <div style={{ backgroundColor: "white" }}>
                  <Row>
                    <Col xl="4" className="u-mb-20">
                      <Collapse
                        items={itemKhachHang}
                        defaultActiveKey={["1"]}
                      />
                    </Col>

                    <Col xl="4" className="u-mb-20">
                      <Collapse items={itemDonHang} defaultActiveKey={["1"]} />
                    </Col>

                    <Col xl="4" className="u-mb-20">
                      <Collapse
                        items={itemThanhToan}
                        defaultActiveKey={["1"]}
                      />
                    </Col>
                  </Row>
                </div>
              </Section>
            </Card>
            <Card
              type="inner"
              title="Sản phẩm"
              style={{ marginTop: 16 }}
              extra={
                (editMode || createMode) && (
                  <div className="flex u-justify-end u-mt-5">
                    {/* <Button
                      buttonType="outline"
                      type="button"
                      onClick={() => handleOpenModalByType("productSkuPicker")}
                      iconName="plus-blue"
                    >
                      Thêm sản phẩm mới
                    </Button> */}
                    <ConfigProvider theme={antdToken}>
                      <Button
                        type="primary"
                        onClick={() =>
                          handleOpenModalByType("productSkuPicker")
                        }
                        size={"large"}
                        className="buttonADD"
                        icon={
                          <div>
                            <PlusSquareFilled rev={undefined} />
                            Thêm sản phẩm mới
                          </div>
                        }
                      >
                        <div style={{ width: "50xp" }}></div>
                      </Button>
                    </ConfigProvider>
                  </div>
                )
              }
            >
              <Table>
                <Thead>
                  <Tr>
                    <Th colSpan={2}>STT</Th>
                    <Th colSpan={6}>Tên sản phẩm</Th>
                    <Th colSpan={2} modifiers="center">
                      Màu sắc
                    </Th>
                    <Th colSpan={2} modifiers="center">
                      Size
                    </Th>
                    <Th colSpan={2} modifiers="center">
                      Giá NY
                    </Th>
                    <Th colSpan={2} modifiers="center">
                      Giảm giá
                    </Th>

                    <Th colSpan={2} modifiers="center">
                      Giá bán
                    </Th>

                    <Th colSpan={2} modifiers="center">
                      Số lượng
                    </Th>

                    <Th
                      colSpan={3}
                      modifiers="center"
                      stickyRight={viewMode ? true : undefined}
                    >
                      Thành tiền
                    </Th>

                    {editMode && (
                      <Th colSpan={3} modifiers="center" stickyRight />
                    )}
                  </Tr>
                </Thead>
                <Tbody>
                  {state.controlledProperties?.items?.map(
                    (
                      orderItem: {
                        _id: Key | null | undefined;
                        sku: {
                          name:
                            | boolean
                            | ReactChild
                            | ReactFragment
                            | ReactPortal
                            | null
                            | undefined;
                          color: {
                            name:
                              | boolean
                              | ReactChild
                              | ReactFragment
                              | ReactPortal
                              | null
                              | undefined;
                          };
                          size: {
                            name:
                              | boolean
                              | ReactChild
                              | ReactFragment
                              | ReactPortal
                              | null
                              | undefined;
                          };
                          price:
                            | string
                            | number
                            | boolean
                            | {}
                            | ReactElement<
                                any,
                                string | JSXElementConstructor<any>
                              >
                            | ReactNodeArray
                            | ReactPortal
                            | null
                            | undefined;
                          salePrice:
                            | string
                            | number
                            | boolean
                            | {}
                            | ReactElement<
                                any,
                                string | JSXElementConstructor<any>
                              >
                            | ReactNodeArray
                            | ReactPortal
                            | null
                            | undefined;
                        };
                        quantity:
                          | string
                          | number
                          | readonly string[]
                          | undefined;
                        totalAmount:
                          | string
                          | number
                          | boolean
                          | {}
                          | ReactElement<
                              any,
                              string | JSXElementConstructor<any>
                            >
                          | ReactNodeArray
                          | ReactPortal
                          | null
                          | undefined;
                      },
                      index: number
                    ) => (
                      <Tr key={orderItem._id}>
                        <Td colSpan={2} modifiers="center">
                          {index + 1}
                        </Td>
                        <Td colSpan={6} modifiers="center">
                          {orderItem.sku.name}
                        </Td>
                        <Td colSpan={2} modifiers="center">
                          {orderItem.sku.color?.name}
                        </Td>
                        <Td colSpan={2} modifiers="center">
                          {orderItem.sku.size?.name}
                        </Td>
                        <Td colSpan={2} modifiers="center">
                          {orderItem?.sku?.price} đ
                        </Td>
                        <Td colSpan={2} modifiers="center" />
                        <Td colSpan={2} modifiers="center">
                          {orderItem?.sku?.salePrice} đ
                        </Td>
                        <Td colSpan={2} modifiers="center">
                          <Numberfield
                            disabled={viewMode}
                            style={{
                              width: 55,
                              height: 30,
                              padding: 0,
                              textAlign: "center",
                            }}
                            onChange={(event) =>
                              handleOnChangeItemQuantityByIndex(
                                index,
                                Number(event.target.value)
                              )
                            }
                            value={orderItem.quantity}
                          />
                        </Td>
                        <Td
                          colSpan={3}
                          modifiers="center"
                          stickyRight={viewMode ? true : undefined}
                        >
                          {orderItem.totalAmount}
                        </Td>

                        {editMode && (
                          <Td colSpan={3} modifiers="center" stickyRight>
                            <TableManipulation
                              deleteAction={{
                                id: `${orderItem._id}delete`,
                                action: () => handleRemoveItemByIndex(index),
                              }}
                            />
                          </Td>
                        )}
                      </Tr>
                    )
                  )}
                </Tbody>
              </Table>
            </Card>
            <div
              className="d-flex justify-content-between"
              style={{ marginTop: 16 }}
            >
              {cancelButton}
              {submitButton}
            </div>
          </Card>
        </Section>

        {/* <Section></Section>
        <Section>
          <div className="d-flex justify-content-between">
            {cancelButton}
            {submitButton}
          </div>
        </Section> */}
      </FormContainer>
      <CustomerPickerByPhone
        open={state.modalState.type === "customerPickerByPhone"}
        onClose={handleOnCloseModal}
        onChooseCustomer={useCallback(
          (customer) => {
            setOrderProperties({ customer });
            resetShippingAddress();
          },
          [resetShippingAddress, setOrderProperties]
        )}
      />
      <ProductSkuPickerModal
        open={state.modalState.type === "productSkuPicker"}
        onClose={handleOnCloseModal}
        onSubmit={handleOnSubmitProductSkuPickerForm}
      />
      {state.controlledProperties.customer && (
        <CustomerShippingAddressModal
          onClose={handleOnCloseModal}
          open={
            modalTypeIsOpen("customerCreateShippingModal") ||
            modalTypeIsOpen("customerUpdateShippingModal")
          }
          customerShippingAddress={
            modalTypeIsOpen("customerUpdateShippingModal")
              ? state.customerShippingAddress
              : undefined
          }
          onSubmit={submitCustomerShippingAddressExec}
        />
      )}
    </>
  );
};
```
