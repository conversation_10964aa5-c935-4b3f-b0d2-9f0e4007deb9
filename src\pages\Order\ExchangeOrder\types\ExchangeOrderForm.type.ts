import { Dayjs } from "dayjs";

export type ExChangeOrderFormType = {
  orderIdRef: number;
  codeRef: string;
  orderType: number;
  channel: string;
  orderSourceId: number;
  createdAt?: Dayjs | null;
  shipping?: number;
  zipcode?: string | null;
  orderStatusId: number;
  orderStatusTaglineId?: number | null;
  cancelReason?: string | null;
  assignedEmployeeId: number;
  deliveryNote?: string | null;
  weight?: string | null;
  boxCode?: string | null;
  orderNote?: string | null;
  apiNote?: string | null;
  isShopSent: boolean;
  phoneNumber: string | null;
  email?: string | null;
  name: string | null;
  birthDay: Dayjs | null;
  sex: string;
  customerHeight?: string;
  customerWeight?: string;
  customerWaist?: string;
  cityId?: number;
  districtId?: number;
  shippingAddressDetail: string;
  shippingAddressId?: number | null;
  cskhNote?: string | null;
  totalAmount: number;
  discount?: number;
  discountValue?: number;
  shippingCost?: number;
  applicableFee?: number;
  voucherId?: number | null;
  promotionValue?: number | null;
  applyreferralcode: number | null;
  applyreferralValue: number | null;
  pay: number;
  paid: number;
  remainingAmount: number;
  shopSent?: number;
  shopSentNote?: string;
  personalInfo?: PersonalInfo;
  discountType: string;
  customerId: number;
  customer: Customer;
  items: Item[];
};

export interface Customer {
  customerId: number;
}

export interface PersonalInfo {
  height: number;
  weight: number;
  waist: number;
}

export interface Item {
  _id: string;
  skuId: number;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  productId: number;
  discountAmount: number;
  discountPercent: number;
  listedPrice: number;
  orderId: number;
  sku: Sku;
  updatedAt: string;
  createdAt: string;
  isExchange: boolean;
  maxQuantity: number;
  minQuantity: number;
  orderItemIdRef: number;
}

export interface Sku {
  isOriginalDeleted: boolean;
  _id: string;
  skuId: number;
  code: string;
  name: string;
  price: number;
  salePrice: number;
  createdAt: string;
  updatedAt: string;
  color: Color;
  size: Size;
}

export interface Color {
  _id: string;
  colorId: number;
  code: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface Size {
  _id: string;
  sizeId: number;
  code: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}
