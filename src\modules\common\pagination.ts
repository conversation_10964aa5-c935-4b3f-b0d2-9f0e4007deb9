import { createMapper, fromSchema } from "libs/adapters/dto";
import { Number } from "libs/domain";

export const PaginationSchema = {
  self: {
    pageNum: Number(),
    pageSize: Number(),
  },
  first: {
    pageNum: Number(),
    pageSize: Number(),
  },
  prev: Number({ defaultValue: undefined }),
  next: {
    pageNum: Number(),
    pageSize: Number(),
  },
  last: {
    pageNum: Number(),
    pageSize: Number(),
  },
};

export const paginationDTO = createMapper(fromSchema(PaginationSchema));
export type PaginationDTOType = ReturnType<typeof paginationDTO>;
