import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import { CreatePromotionDto } from "../dtos/promotion.dto";
import promotionServices from "../services/promotion.service";

export const useCreatePromotion = () => {
  const [createPromotionExe] = useAsync(
    useCallback(
      (data: CreatePromotionDto) => promotionServices.createPromotion(data),
      []
    )
  );

  return { createPromotionExe };
};
