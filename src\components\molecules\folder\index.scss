.m-folder {
	padding-top: rem(67);
	padding-bottom: rem(10);
	transition: all 0.2s ease-out;

	&:hover {
		background-color: $COLOR-ANTIQUE-WHITE;
	}

	&_iconfolder {
		.a-icon {
			display: block;
			width: rem(90);
			height: rem(60);
			margin: 0 auto rem(56);
		}
	}

	&_wrapname {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 rem(24);
	}

	&_name {
		padding-right: rem(2);
		padding-left: rem(2);
		margin: 0;
		font-family: $FONTFAMILY-ROBOTO;
		line-height: rem(24);
		outline: none;
		@include u-fw-bold();

		&[contentEditable="true"] {
			background-color: $COLOR-PLATINUM-5;
		}
	}

	&_editicon {
		margin-left: rem(10);

		.a-icon {
			width: rem(14);
			height: rem(14);
			cursor: pointer;
		}
	}

	&_totalfile {
		.a-text {
			font-size: rem(12);
			line-height: rem(13.8);
			color: $COLOR-SPANISH-GRAY;
		}
	}
}
