import pick from "object.pick";

import { createMapper, fromSchema, merge } from "libs/adapters/dto";

import { OrderReturnStatusTaglineSchema } from "..";

export const updateReturnOrderStatusTaglineDto = createMapper(
  fromSchema(
    pick(OrderReturnStatusTaglineSchema, [
      "description",
      "name",
      "displayOrder",
      "color",
    ])
  ),
  merge((data) => ({
    description: data.description || undefined,
  }))
);

export type UpdateReturnOrderStatusTaglineDtoType = ReturnType<
  typeof updateReturnOrderStatusTaglineDto
>;
