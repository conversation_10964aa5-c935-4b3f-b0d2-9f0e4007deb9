import { useCallback } from "react";
import { Modal, List, Button, Typography } from "antd";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { Heading } from "components/atoms/heading";
import { COLOR } from "constants/color";
import { ItemStockLocationDto } from "services/crm/location/dtos/item-location-stock.dto";

const dummyStorage = [
  {
    id: 1,
    name: "KHO SƯ VẠN HẠNH - QUẬN 10",
    stock: 500,
  },
  {
    id: 2,
    name: "KHO 2 - QUẬN 2",
    stock: 600,
  },
  {
    id: 3,
    name: "KHO 3 - QUẬN 1",
    stock: 700,
  },
  {
    id: 4,
    name: "KHO 2 - QUẬN 11",
    stock: 800,
  },
];

export interface StoragePickerModalProps {
  loading: boolean;
  itemStockLocationData: ItemStockLocationDto[];
  open: boolean;
  onClose?: () => void;
  onSelected?: (storage: ItemStockLocationDto) => void;
}

export const StoragePickerModal = ({
  loading,
  itemStockLocationData,
  open,
  onClose,
  onSelected,
}: StoragePickerModalProps) => {
  const handleOnModalClose = useCallback(() => {
    if (onClose) {
      onClose();
    }
  }, [onClose]);

  const handleOnModalSelected = useCallback(
    (storage: ItemStockLocationDto) => {
      if (onSelected) {
        onSelected(storage);
      }
    },
    [onSelected]
  );

  return (
    <Modal
      title="CHỌN KHO"
      open={open}
      onCancel={handleOnModalClose}
      footer={null}
      maskClosable={false}
      destroyOnHidden
      centered
      closable
      width={480}
      zIndex={9999}
    >
      <List
        loading={loading}
        dataSource={itemStockLocationData}
        renderItem={(item) => (
          <List.Item
            actions={[
              <BaseButton
                key="select"
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                onClick={() => handleOnModalSelected(item)}
              >
                Chọn
              </BaseButton>,
            ]}
          >
            <List.Item.Meta
              title={
                <Typography.Text strong style={{ color: "#4A4A4A" }}>
                  {item?.storage?.name || "Kho không xác định"}
                </Typography.Text>
              }
              description={
                <Typography.Text italic>
                  Tồn kho: {item.stockQty}
                </Typography.Text>
              }
            />
          </List.Item>
        )}
        className="max-h-96 overflow-y-auto"
      />
    </Modal>
  );
};
