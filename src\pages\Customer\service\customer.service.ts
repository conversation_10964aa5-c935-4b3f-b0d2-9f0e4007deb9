/* eslint-disable @typescript-eslint/no-shadow */
import { rest } from "lodash";
import crmDriverV1 from "services/crm/crm-driver-v1";
import {
  GetPointCustomerDto,
  GetPointCustomerResponseDto,
  pagination,
} from "../Dto/customer.dto";

const customerService = {
  getAllCustomers: (params: pagination) => {
    const { pageNum, pageSize } = params ?? {};
    let url = `customers/external/profile/getAllCustomers`;
    if (pageNum || pageSize) {
      url += `?pageNum=${pageNum}&pageSize=${pageSize}`;
    }
    return crmDriverV1.get(url);
  },
  getPointCustomer: (
    dto: GetPointCustomerDto
  ): Promise<{ data: GetPointCustomerResponseDto }> => {
    const { customerId, ...rest } = dto ?? {};
    const url = `customers/external/point/getCustomerPoints/${customerId}`;
    return crmDriverV1.post(url, rest);
  },
};

export default customerService;
