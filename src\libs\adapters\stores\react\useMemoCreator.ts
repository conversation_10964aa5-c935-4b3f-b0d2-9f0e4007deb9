import { useEffect, useMemo, useRef } from "react";

import { isObject } from "libs/utils/isObject";

import { Event, isEvent, isSource, Source } from "../core";

const cleanup = <T>(target: T) => {
  if (Array.isArray(target)) {
    target.forEach(cleanup);
    return null;
  }
  if (isObject(target)) {
    // eslint-disable-next-line no-restricted-syntax
    for (const field of Object.keys(target)) {
      cleanup((target as never)[field]);
    }
    return null;
  }
  if (isSource(target)) (target as unknown as Source<unknown>).offAll();
  if (isEvent(target)) (target as unknown as Event<unknown>).unsubscribeAll();

  return null;
};

export const useMemoCreator = <U>(
  creator: () => U,
  // eslint-disable-next-line default-param-last
  deps: unknown[] = [],
  cleanupProps?: (prevValue: U) => void
) => {
  const prevValueRef = useRef<U>();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const valueCreated = useMemo(creator, deps);
  useEffect(() => {
    let prevValue = prevValueRef.current;
    if (prevValue) {
      cleanup(prevValue);
    }
    prevValueRef.current = valueCreated;
    prevValue = valueCreated;

    return () => {
      if (cleanupProps && prevValue) cleanupProps(prevValue);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [valueCreated]);

  return valueCreated;
};
