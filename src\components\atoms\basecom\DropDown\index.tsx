import { forwardRef, MutableRefObject } from "react";

import { Query } from "@syncfusion/ej2-data";
import { DefaultHtmlAttributes } from "@syncfusion/ej2-react-base";
import {
  DropDownListComponent,
  DropDownListModel,
  DropDownListTypecast,
  FilteringEventArgs,
} from "@syncfusion/ej2-react-dropdowns";

export type TDropDown = {} & DropDownListComponent;

type Props = DropDownListModel &
  DefaultHtmlAttributes &
  DropDownListTypecast & {
    originRef?: MutableRefObject<DropDownListComponent | null>;
  };

export const DropDown = forwardRef<TDropDown, Props>((prop, ref) => {
  const handleFiltering = (e: FilteringEventArgs) => {
    if (prop.fields && prop.fields.text && prop.dataSource) {
      if (e.text === "") {
        e.updateData(prop.dataSource);
      } else {
        let query = new Query();
        // change the type of filtering
        query =
          e.text !== ""
            ? query.where(prop.fields.text, "contains", e.text, true)
            : query;
        console.log(prop.dataSource);
        e.updateData(prop.dataSource, query);
      }
    }
  };
  return (
    <DropDownListComponent
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...prop}
      ref={prop.originRef}
      // filtering={handleFiltering}
    />
  );
});
