import { useCallback, useRef } from "react";

import {
  DeleteFilled,
  EditFilled,
  InfoCircleFilled,
  SearchOutlined,
} from "@ant-design/icons";
import { Input, Table as TableAntd, Tooltip } from "antd";
import { ColumnsType } from "antd/es/table";
import { ValueType } from "react-select";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Pulldown } from "components/atoms/pulldown";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import {
  Pagination,
  PaginationReference,
} from "components/molecules/pagination";
import { Col, Row } from "components/organisms/grid";
import { Modal } from "components/organisms/modal";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import dayjs from "helpers/dayjs";
import { Form<PERSON>ontainer } from "helpers/form";
import PaginationSection from "pages/Common/paginationSection";

import RootPageRouter from "..";
import {
  CreateBankAccountFormPayload,
  validationSchemaOfCreation,
} from "./constant";
import { ListAccountPageVm } from "./vm";

interface BankAccount {
  bankAccountId: number;
  accountNumber: string;
  owner: string;
  bankName: string;
  branchName?: string;
  updatedAt: string | Date;
}
const ListBankAccountPage = () => {
  const {
    gotoPage,
    handleChangePageSize,
    toggleSortOrderBy,
    accountListData,
    getAccountListLoading,
    pageSize,
    accountListPaginationState,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    createBankAccount,
    createBankAccountState,
    handleDeleteBankAccount,
  } = ListAccountPageVm();

  const paginationRef = useRef<PaginationReference>(null);

  const handleOnChangePageSizePulldown = useCallback(
    (option: ValueType<{ label: string; value: string }, false>) => {
      if (option?.value) {
        handleChangePageSize(Number(option?.value));
        if (paginationRef.current) paginationRef.current.reset();
      }
    },
    [handleChangePageSize]
  );

  const handleSubmitCreateBankAccount = useCallback(
    (formData: CreateBankAccountFormPayload) => {
      createBankAccount(formData);
    },
    [createBankAccount]
  );

  const columns: ColumnsType<BankAccount> = [
    {
      title: "STT",
      key: "no.",
      render: (_, __, index) => index + 1,
      fixed: "left",
      align: "center",
      width: 60,
    },
    {
      title: "Số tài khoản",
      dataIndex: "accountNumber",
      key: "accountNumber",
      align: "center",
      width: 200,
    },
    {
      title: "Chủ tài khoản",
      dataIndex: "owner",
      key: "owner",
      align: "center",
      width: 200,
    },
    {
      title: "Tên ngân hàng",
      dataIndex: "bankName",
      key: "bankName",
      align: "center",
      width: 200,
    },
    {
      title: "Chi nhánh",
      dataIndex: "branchName",
      key: "branchName",
      align: "center",
      width: 200,
      render: (value) => value || "-",
    },
    {
      title: "Cập nhật cuối",
      dataIndex: "updatedAt",
      key: "updatedAt",
      align: "center",
      width: 150,
      sorter: true,
      render: (value) => (
        <div className="flex justify-center">
          <span>{dayjs(value).format("DD/MM/YYYY HH:mm")}</span>
        </div>
      ),
    },
    {
      title: "Thao tác",
      key: "action",
      fixed: "right",
      align: "center",
      width: 120,
      render: (_, record) => {
        const { bankAccountId } = record;
        return (
          <div className="flex justify-center gap-3">
            <Tooltip title="Xem chi tiết">
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                icon={<InfoCircleFilled rev={undefined} />}
                onClick={() =>
                  RootPageRouter.gotoChild("bankAccountDetail", {
                    params: {
                      bankAccountId: bankAccountId?.toString(),
                    },
                  })
                }
              />
            </Tooltip>
            <Tooltip title="Chỉnh sửa">
              <BaseButton
                type="primary"
                bgColor={COLOR.GREEN[500]}
                hoverColor={COLOR.GREEN[700]}
                icon={<EditFilled rev={undefined} />}
                onClick={() =>
                  RootPageRouter.gotoChild("bankAccountDetail", {
                    params: {
                      bankAccountId: bankAccountId?.toString(),
                    },
                    queryString: "?action=edit",
                  })
                }
              />
            </Tooltip>
            <Tooltip title="Xóa">
              <BaseButton
                type="primary"
                bgColor={COLOR.RED[500]}
                hoverColor={COLOR.RED[700]}
                icon={<DeleteFilled rev={undefined} />}
                onClick={() => {
                  handleDeleteBankAccount(bankAccountId);
                }}
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return (
    <General>
      <title key="title">Danh sách tài khoản ngân hàng</title>
      <Section>
        <div className="mb-6">
          <Heading type="h1" modifiers="primary">
            DANH SÁCH TÀI KHOẢN NGÂN HÀNG
          </Heading>
        </div>

        {/* <Section>
          <Row className="d-flex">
            <Col
              className="ml-auto"
              xs={{ span: 12, order: 2 }}
              lg={{ span: 6, order: 1 }}
            >
              <Textfield iconSrc={imgSearchBlue} placeholder="Tìm kiếm" />
            </Col>
            <Col
              className="d-flex justify-content-end u-mb-15 u-mb-lg-0"
              xs={{ span: 12, order: 1 }}
              lg={{ span: 6, order: 2 }}
            >
              <Button onClick={() => handleOpenModalByType("createAccount")}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Section> */}
        {/* ✅ Header với search và button tạo mới */}
        <div className="mb-6">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Input
                placeholder="Tìm kiếm"
                suffix={<SearchOutlined className="text-xl" rev={undefined} />}
              />
            </Col>
            <Col xs={{ span: 6 }} lg={{ span: 6 }}>
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                onClick={() => handleOpenModalByType("createAccount")}
                className="w-full"
              >
                Tạo mới
              </BaseButton>
            </Col>
          </Row>
        </div>

        {/* <Section>
          <Table
            scroll={{ x: 1500 }}
            loading={getAccountListLoading}
            hasData={accountListData.length > 0}
          >
            <Thead>
              <Tr>
                <Th modifiers="center" stickyLeft colSpan={1}>
                  STT
                </Th>
                <Th modifiers="center" colSpan={3}>
                  Số tài khoản
                </Th>
                <Th modifiers="center" colSpan={3}>
                  Chủ tài khoản
                </Th>
                <Th modifiers="center" colSpan={3}>
                  Tên ngân hàng
                </Th>
                <Th
                  modifiers="center"
                  isSortable
                  colSpan={3}
                  onSort={() => toggleSortOrderBy("updatedAt")}
                >
                  Cập nhật cuối
                </Th>
                <Th modifiers="center" stickyRight colSpan={3}>
                  Thao tác
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {accountListData.map((account, index) => (
                <Tr key={account?.bankAccountId}>
                  <Td modifiers="center" stickyLeft colSpan={1}>
                    {index + 1}
                  </Td>
                  <Td modifiers="center" colSpan={3}>
                    {account?.accountNumber}
                  </Td>
                  <Td modifiers="center" colSpan={3}>
                    {account?.owner}
                  </Td>
                  <Td modifiers="center" colSpan={3}>
                    {account?.bankName}
                  </Td>
                  <Td modifiers="center" colSpan={3}>
                    {dayjs(account?.updatedAt).format("DD/MM/YYYY HH:mm")}
                  </Td>
                  <Td modifiers="center" stickyRight colSpan={3}>
                    <TableManipulation
                      infoAction={{
                        id: `${index}info`,
                        action: () =>
                          RootPageRouter.gotoChild("bankAccountDetail", {
                            params: {
                              bankAccountId: account?.bankAccountId?.toString(),
                            },
                          }),
                      }}
                      editAction={{
                        id: `${index}edit`,
                        action: () =>
                          RootPageRouter.gotoChild("bankAccountDetail", {
                            params: {
                              bankAccountId: account?.bankAccountId?.toString(),
                            },
                            queryString: "?action=edit",
                          }),
                      }}
                      deleteAction={{
                        id: `${index}delete`,
                      }}
                    />
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Section> */}

        <TableAntd
          loading={getAccountListLoading}
          scroll={{ x: 1200 }}
          bordered
          columns={columns}
          dataSource={accountListData}
          rowKey="bankAccountId"
          pagination={{
            current: accountListPaginationState?.currentPage || 1,
            total: (accountListPaginationState?.totalPage || 0) * pageSize,
            pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} mục`,
            pageSizeOptions: ["5", "10", "15", "25", "30", "50", "100"],
            onChange: (page, size) => {
              gotoPage(page);
              if (size !== pageSize) {
                handleChangePageSize(size);
              }
            },
            onShowSizeChange: (_, size) => {
              handleChangePageSize(size);
              gotoPage(1); // Reset to first page when changing page size
            },
          }}
        />

        {/* <Section>
          <PaginationSection
            appearanceOption={
              <Pulldown
                placeholder="Số lượng hiển thị"
                value={{ label: `${pageSize}`, value: `${pageSize}` }}
                options={[5, 10, 15, 25, 30].map((size) => ({
                  label: `${size}`,
                  value: `${size}`,
                }))}
                onChange={handleOnChangePageSizePulldown}
              />
            }
            paginateOption={
              accountListPaginationState?.totalPage && (
                <Pagination
                  modifiers="center"
                  total={accountListPaginationState.totalPage}
                  pageCount={5}
                  defaultCurrentPage={1}
                  onPageChange={gotoPage}
                  ref={paginationRef}
                />
              )
            }
          />
        </Section> */}
      </Section>

      <Modal
        style={{ content: { maxWidth: 700 } }}
        isOpen={modalTypeIsOpen("createAccount")}
        onCloseModal={handleCloseModal}
        isClosable={false}
      >
        <FormContainer
          validationSchema={validationSchemaOfCreation}
          onSubmit={handleSubmitCreateBankAccount}
        >
          <Heading centered type="h1">
            TẠO MỚI TÀI KHOẢN NGÂN HÀNG
          </Heading>
          <Formfield label="Tên ngân hàng" name="bankName">
            <TextfieldHookForm
              name="bankName"
              placeholder="Nhập tên ngân hàng"
            />
          </Formfield>
          <Formfield label="Số tài khoản" name="accountNumber">
            <TextfieldHookForm
              name="accountNumber"
              placeholder="Nhập số tài khoản"
            />
          </Formfield>
          <Formfield label="Chủ tài khoản" name="owner">
            <TextfieldHookForm name="owner" placeholder="Nhập chủ tài khoản" />
          </Formfield>
          <Formfield label="Chi nhánh" name="branchName">
            <TextfieldHookForm name="branchName" placeholder="Nhập chi nhánh" />
          </Formfield>

          <div className="d-flex justify-content-end u-mt-20">
            <div className="u-mr-15">
              <Button
                buttonType="outline"
                modifiers="secondary"
                onClick={handleCloseModal}
              >
                HỦY
              </Button>
            </div>
            <Button
              type="submit"
              isLoading={createBankAccountState.loading}
              disabled={createBankAccountState.loading}
            >
              LƯU
            </Button>
          </div>
        </FormContainer>
      </Modal>
    </General>
  );
};

export default ListBankAccountPage;
