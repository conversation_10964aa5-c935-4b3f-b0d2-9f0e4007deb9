import React, { useCallback, useRef } from "react";

import { useParams } from "react-router";
import { ValueType } from "react-select";

import imgSearchBlue from "assets/images/icons/search-blue.svg";
import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Pulldown } from "components/atoms/pulldown";
import { Tag } from "components/atoms/tag";
import { Textfield } from "components/atoms/textfield";
import {
  Pagination,
  PaginationReference,
} from "components/molecules/pagination";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Table, Tbody, Td, Th, Thead, Tr } from "components/organisms/table";
import { General } from "components/pages/general";
import { BasePageProps } from "helpers/component";
import dayjs from "helpers/dayjs";
import { PageParamsType } from "libs/react";
import { CreateReturnOrderStatusTaglineModal } from "modules/order";
import PaginationSection from "pages/Common/paginationSection";
import TableManipulation from "pages/Common/tableManipulation";

import RootPageRouter from "..";
import { ChildrenPage } from "../types";
import {
  CreateReturnOrderStatusTaglineFormPayload,
  CreateOrderExchangeValidationSchema,
} from "./constant";
import { OrderReturnStatusTaglineListPageVm } from "./vm";

const IndexPage: React.FC<BasePageProps> = () => {
  const { returnStatusId } =
    useParams<PageParamsType<ChildrenPage["listOrderReturnStatusTagline"]>>();

  const {
    gotoPage,
    handleChangePageSize,
    pageSize,
    orderReturnStatusTaglineListPaginationState,
    orderReturnStatusTaglineListData,
    orderReturnStatusTaglineListDataLoading,
    toggleSortOrderBy,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    modalCreator,
    returnOrderStatus,
  } = OrderReturnStatusTaglineListPageVm({
    returnOrderStatusId: Number(returnStatusId),
  });

  const paginationRef = useRef<PaginationReference | null>(null);

  const handleChangePulldownPageSize = useCallback(
    (option: ValueType<{ label: string; value: string }, false>) => {
      if (option?.value) {
        handleChangePageSize(Number(option.value));
        if (paginationRef.current) paginationRef.current.reset();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const handleSubmitCreateReturnOrderStatusTagline = useCallback(
    async (formData: CreateReturnOrderStatusTaglineFormPayload) => {
      const { returnOrderStatus: _returnOrderStatus, ...rest } = formData;
      await modalCreator.createReturnOrderStatusTagline({
        ...rest,
        color: modalCreator.colorSelected,
        returnOrderStatusId: Number(_returnOrderStatus.value),
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [modalCreator.colorSelected, modalCreator.createReturnOrderStatusTagline]
  );

  return (
    <General>
      <title key="title">Danh sách Tagline đổi trả</title>
      <Section>
        <Heading type="h1" modifiers="primary">
          DANH SÁCH TAGLINE ĐỔI TRẢ
        </Heading>

        <Section>
          <Row className="d-flex">
            <Col
              className="ml-auto"
              xs={{ span: 12, order: 2 }}
              lg={{ span: 6, order: 1 }}
            >
              <Textfield iconSrc={imgSearchBlue} placeholder="Tìm kiếm" />
            </Col>
            <Col
              className="d-flex justify-content-end u-mb-15 u-mb-lg-0"
              xs={{ span: 12, order: 1 }}
              lg={{ span: 6, order: 2 }}
            >
              <Button
                onClick={() =>
                  handleOpenModalByType("createOrderReturnStatusTagline")
                }
              >
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Section>

        <Section>
          <Table
            modofiers="borderdotted"
            scroll={{ x: 1500 }}
            loading={orderReturnStatusTaglineListDataLoading}
            hasData={orderReturnStatusTaglineListData.length > 0}
          >
            <Thead>
              <Tr>
                <Th colSpan={1} modifiers="center" stickyLeft>
                  STT
                </Th>
                <Th
                  colSpan={3}
                  modifiers="center"
                  isSortable
                  onSort={() => toggleSortOrderBy("name")}
                >
                  Tên trạng thái
                </Th>
                <Th colSpan={4} modifiers="center">
                  Tên tagline
                </Th>
                <Th colSpan={2} modifiers="center">
                  Màu sắc
                </Th>
                <Th colSpan={3} modifiers="center">
                  Mô tả
                </Th>
                <Th colSpan={2} modifiers="center">
                  Thứ tự hiển thị
                </Th>
                <Th
                  colSpan={3}
                  modifiers="center"
                  isSortable
                  onSort={() => toggleSortOrderBy("updatedAt")}
                >
                  Cập nhật cuối
                </Th>
                <Th colSpan={3} modifiers="center" stickyRight>
                  Thao tác
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {orderReturnStatusTaglineListData.map(
                (orderReturnStatusTaglines, index) => (
                  <Tr
                    key={orderReturnStatusTaglines.returnOrderStatusTaglineId}
                  >
                    <Td colSpan={1} modifiers="center" stickyLeft>
                      {index + 1}
                    </Td>
                    <Td colSpan={3} modifiers="center">
                      {orderReturnStatusTaglines.returnOrderStatus?.name}
                    </Td>
                    <Td colSpan={4} modifiers="center">
                      {orderReturnStatusTaglines.name}
                    </Td>
                    <Td colSpan={2} modifiers="center">
                      <Tag color={orderReturnStatusTaglines.color} />
                    </Td>
                    <Td colSpan={3} modifiers="center">
                      {orderReturnStatusTaglines.description}
                    </Td>
                    <Td colSpan={2} modifiers="center">
                      {orderReturnStatusTaglines.displayOrder}
                    </Td>
                    <Td colSpan={3} modifiers="center">
                      {dayjs(orderReturnStatusTaglines.updatedAt).format(
                        "DD/MM/YYYY HH:mm"
                      )}
                    </Td>
                    <Td colSpan={3} modifiers="center" stickyRight>
                      <TableManipulation
                        infoAction={{
                          id: `${index}info`,
                          action: () =>
                            RootPageRouter.gotoChild(
                              "orderReturnStatusTaglineDetail",
                              {
                                params: {
                                  returnStatusId,
                                  taglineId:
                                    orderReturnStatusTaglines.returnOrderStatusTaglineId.toString(),
                                },
                              }
                            ),
                        }}
                        editAction={{
                          id: `${index}edit`,
                          action: () =>
                            RootPageRouter.gotoChild(
                              "orderReturnStatusTaglineDetail",
                              {
                                params: {
                                  returnStatusId,
                                  taglineId:
                                    orderReturnStatusTaglines.returnOrderStatusTaglineId.toString(),
                                },
                                queryString: "?action=edit",
                              }
                            ),
                        }}
                        deleteAction={{
                          id: `${index}delete`,
                        }}
                      />
                    </Td>
                  </Tr>
                )
              )}
            </Tbody>
          </Table>
        </Section>
        <Section>
          <PaginationSection
            appearanceOption={
              <Pulldown
                placeholder="Số lượng hiển thị"
                value={{ label: `${pageSize}`, value: `${pageSize}` }}
                options={[5, 10, 15, 25, 30].map((size) => ({
                  label: `${size}`,
                  value: `${size}`,
                }))}
                onChange={handleChangePulldownPageSize}
              />
            }
            paginateOption={
              orderReturnStatusTaglineListPaginationState.totalPage && (
                <Pagination
                  modifiers="center"
                  total={orderReturnStatusTaglineListPaginationState.totalPage}
                  pageCount={5}
                  defaultCurrentPage={1}
                  onPageChange={gotoPage}
                  ref={paginationRef}
                />
              )
            }
          />
        </Section>
      </Section>
      <CreateReturnOrderStatusTaglineModal
        open={modalTypeIsOpen("createOrderReturnStatusTagline")}
        onClose={handleCloseModal}
        submitValidationSchema={CreateOrderExchangeValidationSchema}
        onChangeColor={modalCreator.onChangeColor}
        returnOrderStatus={returnOrderStatus}
        handleSubmitCreateReturnOrderStatusTagline={
          handleSubmitCreateReturnOrderStatusTagline
        }
      />
    </General>
  );
};
export default IndexPage;
