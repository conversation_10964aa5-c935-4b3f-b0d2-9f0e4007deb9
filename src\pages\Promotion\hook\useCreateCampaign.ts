import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import {
  CampaignDto,
  CampaignGetPage,
} from "../CampaignDetail/dto/campaign.dto";
import CampaignService from "../service";

export const UseCreateCampaign = () => {
  const [createCampaignExec] = useAsync(
    useCallback((body: CampaignDto) => CampaignService.createCampaign(body), [])
  );
  return {
    createCampaignExec,
  };
};

export const UseUpdateCampaign = () => {
  const [updateCampaignExec] = useAsync(
    useCallback(
      (id: string, body: CampaignDto) =>
        CampaignService.updateCampaign(id, body),
      []
    )
  );
  return {
    updateCampaignExec,
  };
};

export const UseGetCampaignById = () => {
  const [getCampaignByIdExec, getCampaignByIdState] = useAsync(
    useCallback((id: string) => CampaignService.getCampaignById(id), [])
  );
  return {
    getCampaignByIdExec,
    getCampaignByIdState: getCampaignByIdState?.data?.data || null,
  };
};

export const UseDeleteCampaign = () => {
  const [deleteCampaignExec] = useAsync(
    useCallback((id: number) => CampaignService.deleteCampaign(id), [])
  );
  return {
    deleteCampaignExec,
  };
};
export const UseGetCampaigns = () => {
  const [getCampaignsExec, getCampaignsState] = useAsync(
    useCallback((body: CampaignDto) => CampaignService.getCampaigns(body), [])
  );
  return {
    getCampaignsExec,
    getCampaignsState: getCampaignsState?.data?.data || [],
  };
};

export const UseGetCampaignPage = () => {
  const [getCampaignPageExec, getCampaignPageState] = useAsync(
    useCallback(
      (params: CampaignGetPage) => CampaignService.getCampaignPage(params),
      []
    )
  );
  return {
    getCampaignPageExec,
    getCampaignPageState: getCampaignPageState?.data?.data || [],
  };
};
