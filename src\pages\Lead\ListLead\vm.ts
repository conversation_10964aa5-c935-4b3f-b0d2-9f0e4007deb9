import { useCallback, useState } from "react";

import produce from "immer";

import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import { leadDTO } from "modules/lead";
import { getLeads } from "services/crm/chat";

interface State {
  pageSize: number;
}

export const LeadPageVm = () => {
  const [state, setState] = useState<State>({
    pageSize: 10,
  });

  const [getDatatableLeadExec, getDatatableLeadState, , , insertDatatableLead] =
    useAsync(
      useCallback(
        (options: { pageNum: number; pageSize: number }) =>
          getLeads({ ...options }).then((res) => ({
            data: mapFrom(res.data.data, leadDTO),
            pagination: paginationDTO(res.data.links),
          })),
        []
      )
    );

  const { gotoPage, ...leadPaginationState } = usePagination({
    pageSize: state.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getDatatableLeadExec({ pageSize, pageNum: page }),
  });

  const { sortedData: listLead, toggleSortState: toggleSortLeadBy } =
    useSortable({
      data: getDatatableLeadState.data?.data,
      sortBy: {
        updateAt: (lead) => lead.updatedAt,
      },
    });

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pageSize = pageSize;
      })
    );
  }, []);

  return {
    leadPaginationState,
    gotoPage,
    leadData: listLead || [],
    loading: getDatatableLeadState.loading,
    toggleSortLeadBy,
    handleChangePageSize,
    pageSize: state.pageSize,
    insertDatatableLead,
  };
};
