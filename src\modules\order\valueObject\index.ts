import { emptyIfNull } from "helpers/string";

import {
  OrderReturnStatusEntityType,
  OrderReturnStatusTaglineEntityType,
  OrderStatusEntityType,
  OrderStatusTaglineEntityType,
} from "../entities";
import { DeliveryPartnerEntityType } from "../entities/deliveryPartner";
import { OrderSourceEntityType } from "../entities/orderSource";

export const orderStatusOption = (
  orderStatus?: Partial<OrderStatusEntityType>
) => ({
  label: orderStatus?.name?.toString() || "",
  value: orderStatus?.orderStatusId?.toString() || "",
});

export const orderStatusTaglineOption = (
  orderTagline: OrderStatusTaglineEntityType
) => ({
  label: orderTagline?.name,
  value: orderTagline?.taglineId?.toString(),
});

export const orderReturnStatusOption = (
  orderReturnStatus?: OrderReturnStatusEntityType
) => ({
  label: orderReturnStatus?.name?.toString() || "",
  value: orderReturnStatus?.returnOrderStatusId?.toString() || "",
});

export const orderReturnStatusTaglineOption = (
  orderReturnStatusTagline: OrderReturnStatusTaglineEntityType
) => ({
  label: orderReturnStatusTagline?.name,
  value: orderReturnStatusTagline?.returnOrderStatusTaglineId?.toString(),
});

export const orderSourceOption = (orderSource?: OrderSourceEntityType) => ({
  label: emptyIfNull(orderSource?.name),
  value: emptyIfNull(orderSource?.orderSourceId?.toString()),
});

export const deliveryPartnerOption = (
  deliveryPartner?: DeliveryPartnerEntityType
) => ({
  label: emptyIfNull(deliveryPartner?.name),
  value: emptyIfNull(deliveryPartner?.deliveryPartnerId?.toString()),
});
