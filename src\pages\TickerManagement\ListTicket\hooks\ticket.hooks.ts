import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import {
  CreateTicketDto,
  GetListTicketDto,
  UpdateTicketDto,
} from "../dto/ticket.dto";
import ticketServices from "../services/ticket.service";

export const useGetTicketList = (dto: GetListTicketDto) => {
  const [refetch, data] = useAsync(
    useCallback(() => ticketServices.getListTicket(dto), [dto])
  );

  return {
    data: data?.data?.data,
    refetch,
    loading: data?.loading,
  };
};

export const useCreateTicket = () => {
  const [createTicketExe] = useAsync(
    useCallback(
      (data: CreateTicketDto) => ticketServices.postCreateTicket(data),
      []
    )
  );

  return {
    createTicketExe,
  };
};

export const useUpdateTicket = () => {
  const [updateTicketExe] = useAsync(
    useCallback(
      (params: UpdateTicketDto) => ticketServices.putUpdateTicket(params),
      []
    )
  );

  return {
    updateTicketExe,
  };
};

export const useDeleteTicket = () => {
  const [deleteTicketExe] = useAsync(
    useCallback(
      ({ ticketId }: { ticketId: number }) =>
        ticketServices.deleteTicket({ ticketId }),
      []
    )
  );

  return {
    deleteTicketExe,
  };
};

export const useGetTicketById = () => {
  const [fetchData, TicketState] = useAsync(
    useCallback(
      (ticketId: number) => ticketServices.getTicketById(ticketId),
      []
    )
  );

  return {
    fetchData,
    ticketData: TicketState.data?.data,
    loading: TicketState.loading,
  };
};
