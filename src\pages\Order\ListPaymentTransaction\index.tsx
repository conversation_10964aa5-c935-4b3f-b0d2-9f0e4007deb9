/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useRef } from "react";
import { DownOutlined } from "@ant-design/icons";
import { Col, DatePicker, Form, Input, Row, Table } from "antd";
import { SorterResult } from "antd/es/table/interface";
import dayjs from "dayjs";
import { ValueType } from "react-select";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { Heading } from "components/atoms/heading";
import { Pulldown } from "components/atoms/pulldown";
import {
  ExternalCalendarRegister,
  useCalendarProvider,
} from "components/molecules/calendar";
import {
  Pagination as PaginationCustom,
  PaginationReference,
} from "components/molecules/pagination";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import { paymentDisplayMethod, paymentDisplayStatus } from "modules/order";
import PaginationSection from "pages/Common/paginationSection";
import TableManipulation from "pages/Common/tableManipulation";
import { FilterFormPayload } from "./constant";
import { ListPaymentTransactionPageVm } from "./vm";

interface DateType {
  paymentId: number;
  method: number;
  amount: number;
  orderId: number;
  orderCode: string;
  status: number;
  createdAt: any;
  updatedAt: any;
}

const IndexPage = () => {
  const [form] = Form.useForm();
  const paginationRef = useRef<PaginationReference>(null);
  const calendarRef = useRef<ExternalCalendarRegister>();
  const { getDate, setDate } =
    useCalendarProvider<{
      rangeDate?: [Date, Date];
    }>();

  const {
    filterFormOpen,
    orderPaymentData,
    toggleFilterForm,
    handleChangePageSize,
    toggleSortOrderPaymentBy,
    getListOrderPaymentLoading,
    gotoDetailPaymentTransaction,
    listOrderPaymentPaginationState,
    pageSize,
    gotoPage,
    handleChangeFilter,
  } = ListPaymentTransactionPageVm();

  const handleChangePulldownPageSize = useCallback(
    (option: ValueType<{ label: string; value: string }, false>) => {
      if (option?.value) {
        handleChangePageSize(Number(option.value));
        if (paginationRef.current) paginationRef.current.reset();
      }
    },
    [handleChangePageSize]
  );

  const onFinish = (formData: FilterFormPayload) => {
    const { filterMethod, filterStatus, orderCode } = formData;

    const dateStart = getDate("rangeDate")?.[0];
    const dateEnd = getDate("rangeDate")?.[1];

    handleChangeFilter({
      orderCode,
      filterStatus: filterStatus && Number(filterStatus),
      filterMethod: filterMethod && Number(filterMethod),
      filterCreatedAtFrom: dateStart && dayjs(dateStart).format("YYYY-MM-DD"),
      filterCreatedAtTo: dateEnd && dayjs(dateEnd).format("YYYY-MM-DD"),
    });
  };

  const onReset = () => {
    // onHandleResetFilterState();
    form.resetFields();
    form.setFieldValue("filterStatus", null);
    form.setFieldValue("filterMethod", null);
    handleChangeFilter({
      orderCode: undefined,
      filterStatus: undefined,
      filterMethod: undefined,
      filterCreatedAtFrom: undefined,
      filterCreatedAtTo: undefined,
    });

    paginationRef.current?.reset();
    calendarRef.current?.reset();
  };

  const handleTableChange = (
    pagination: any,
    filters: any,
    sorter: SorterResult<DateType> | SorterResult<DateType>[]
  ) => {
    if (!Array.isArray(sorter) && sorter?.columnKey === "createdAt") {
      toggleSortOrderPaymentBy("createdAt");
    }
  };

  return (
    <General>
      <title key="title">Giao dịch thanh toán</title>
      <Section>
        <Heading type="h1" modifiers="primary">
          GIAO DỊCH THANH TOÁN
        </Heading>
        <Section>
          <div className="d-flex justify-content-end px-4">
            <BaseButton
              size="large"
              className="text-blue-500 hover:text-blue-700 font-medium !border-blue-500"
              onClick={toggleFilterForm}
              icon={
                <DownOutlined
                  className={`${filterFormOpen ? "rotate-180" : ""}
                  transition-all duration-300 ease-in-out
                  `}
                  rev={undefined}
                />
              }
            >
              Lọc dữ liệu
            </BaseButton>
          </div>
        </Section>
        <Section>
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            onReset={onReset}
            initialValues={{
              filterMethod: "",
              filterStatus: "",
              orderCode: "",
            }}
          >
            {filterFormOpen && (
              <Row gutter={16} className="px-4">
                <Col lg={6} className="u-mb-15 u-mb-lg-0">
                  <Form.Item label="Thông tin giao dịch" name="orderCode">
                    <Input placeholder="Nhập thông tin giao dịch" />
                  </Form.Item>
                </Col>
                <Col lg={6} className="u-mb-15 u-mb-lg-0">
                  <Form.Item label="Trạng thái" name="filterStatus">
                    <BaseSelect
                      placeholder="Chọn trạng thái"
                      options={[0, 1].map((item) => ({
                        label: `${paymentDisplayStatus[item]}`,
                        value: `${item}`,
                      }))}
                      fieldNames={{ label: "label", value: "value" }}
                    />
                  </Form.Item>
                </Col>
                <Col lg={6} className="u-mb-15 u-mb-lg-0">
                  <Form.Item label="Phương thức thanh toán" name="filterMethod">
                    <BaseSelect
                      placeholder="Chọn phương thức thanh toán"
                      options={[0, 1, 2, 3, 4].map((item) => ({
                        label: `${paymentDisplayMethod[item]}`,
                        value: `${item}`,
                      }))}
                      fieldNames={{ label: "label", value: "value" }}
                    />
                  </Form.Item>
                </Col>
                <Col lg={6}>
                  <Form.Item name="filterCreatedAt" label="Thời gian giao dịch">
                    <DatePicker.RangePicker
                      className="w-full"
                      onChange={(dates) => {
                        if (dates) {
                          // eslint-disable-next-line @typescript-eslint/no-explicit-any
                          setDate("rangeDate", dates as any);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col lg={24}>
                  <div className="d-flex justify-content-center u-mt-20">
                    <BaseButton
                      size="large"
                      type="default"
                      onClick={toggleFilterForm}
                    >
                      Đóng
                    </BaseButton>
                    <div className="u-ml-20">
                      <BaseButton
                        size="large"
                        type="primary"
                        bgColor={COLOR.BLUE[500]}
                        hoverColor={COLOR.BLUE[700]}
                        htmlType="submit"
                      >
                        Tìm kiếm
                      </BaseButton>
                    </div>
                    <div className="u-ml-20">
                      <BaseButton
                        style={{
                          borderColor: COLOR.BLUE[500],
                          color: COLOR.BLUE[500],
                          height: "40px",
                          minWidth: "200px",
                          fontWeight: 500,
                          backgroundColor: "white",
                        }}
                        className="hover:!bg-blue-700 hover:!text-white"
                        size="large"
                        type="default"
                        onClick={onReset}
                      >
                        Đặt lại
                      </BaseButton>
                    </div>
                  </div>
                </Col>
              </Row>
            )}
          </Form>
        </Section>

        <Section>
          <Table
            scroll={{ x: 1500 }}
            loading={getListOrderPaymentLoading}
            onChange={handleTableChange}
            columns={[
              {
                title: "STT",
                dataIndex: "index",
                key: "index",
                align: "center",
                render: (_, __, index) => index + 1,
              },
              {
                title: "Mã giao dịch",
                dataIndex: "paymentId",
                key: "paymentId",
                align: "center",
              },
              {
                title: "Mã đơn hàng",
                dataIndex: "orderCode",
                key: "orderCode",
                align: "center",
              },
              {
                title: "Phương thức thanh toán",
                dataIndex: "method",
                key: "method",
                align: "center",
                render: (method) => paymentDisplayMethod[method],
              },
              {
                title: "Trạng thái",
                dataIndex: "status",
                key: "status",
                align: "center",
                render: (status) => paymentDisplayStatus[status],
              },
              {
                title: "Tổng thanh toán",
                dataIndex: "amount",
                key: "amount",
                align: "center",
              },
              {
                title: "Thời gian",
                dataIndex: "createdAt",
                key: "createdAt",
                align: "center",
                sorter: true, // Kích hoạt icon sort
                // sortOrder: toggleSortOrderPaymentBy("createdAt"), // <- Gọi hàm để lấy sortOrder hiện tại
                render: (createdAt) => dayjs(createdAt).format("DD/MM/YYYY"),
              },
              {
                title: "Thao tác",
                dataIndex: "action",
                key: "action",
                align: "center",
                render: (text, record) => (
                  <TableManipulation
                    infoAction={{
                      id: `${record.paymentId}info`,
                      action: () =>
                        gotoDetailPaymentTransaction(record.paymentId),
                    }}
                  />
                ),
              },
            ]}
            dataSource={orderPaymentData}
            pagination={false}
          />
        </Section>

        <Section>
          <PaginationSection
            appearanceOption={
              <Pulldown
                placeholder="Số lượng hiển thị"
                value={{
                  label: pageSize.toString(),
                  value: pageSize.toString(),
                }}
                options={[5, 10, 15, 25, 30].map((size) => ({
                  label: `${size}`,
                  value: `${size}`,
                }))}
                onChange={handleChangePulldownPageSize}
              />
            }
            paginateOption={
              listOrderPaymentPaginationState.totalPage && (
                <PaginationCustom
                  total={listOrderPaymentPaginationState.totalPage}
                  pageCount={5}
                  modifiers="center"
                  defaultCurrentPage={1}
                  onPageChange={gotoPage}
                  ref={paginationRef}
                />
              )
            }
          />
        </Section>
      </Section>
    </General>
  );
};

export default IndexPage;
