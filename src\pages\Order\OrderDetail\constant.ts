import * as Yup from "yup";

import { customerShippingAddressOption } from "modules/customer";
import { assignEmployeeOption } from "modules/employee";
import {
  orderReturnStatusOption,
  orderReturnStatusTaglineOption,
  orderSourceOption,
  orderStatusOption,
  orderStatusTaglineOption,
} from "modules/order";

import { OrderItem } from "./vm";

export const updateOrderValidateSchema = Yup.object({});

export type OrderDetailFormType = OrderItem & {
  totalAmount: number;
  shippingAddress?: ReturnType<typeof customerShippingAddressOption>;
  orderStatus?: ReturnType<typeof orderStatusOption>;
  orderStatusTagline?: ReturnType<typeof orderStatusTaglineOption>;
  returnStatus?: ReturnType<typeof orderReturnStatusOption>;
  returnStatusTagline?: ReturnType<typeof orderReturnStatusTaglineOption>;
  assignedEmployeeId?: ReturnType<typeof assignEmployeeOption>;
  orderSourceId?: ReturnType<typeof orderSourceOption>;
};
