@tailwind base;
@tailwind components;
@tailwind utilities;

#main-treeview > .e-list-parent > .e-list-item:nth-child(even) {
  background-color: #f1f5ff;
}

.e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: #cae1ff !important;
  border-color: #ffffff !important;
  border-radius: 7px !important;
}
.e-list-item {
  margin: 5px 0 !important;
  background-color: #ffffff !important;
}

.e-list-text {
  color: #111827b3 !important;
  font-weight: 420 !important;
  font-family: <PERSON><PERSON><PERSON>, Robot<PERSON>, "Helvetica", Arial, sans-serif !important;
}
.main-menu {
  background-color: #ffffff !important;
}

.e-headercell {
  background-color: #fff !important;
  color: #5b5b5b;
}

.e-filtermenudiv {
  color: #333 !important;
}

.e-gridheader {
  border-top: 1px solid #dee2e6 !important;
  border-bottom: 1px solid #dee2e6 !important;
}

.main-layout {
  height: calc(100vh - 54px);
  overflow: hidden;
}
