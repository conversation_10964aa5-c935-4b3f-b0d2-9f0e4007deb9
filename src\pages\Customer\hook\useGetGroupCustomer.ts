import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import {
  GroupCustomerDto,
  GroupCustomerGetPage,
} from "../GroupCustomerDetail/dto/groupCustomer.dto";
import groupCustomerService from "../service/groupCustomer.service";

export const UseCreateGroupCustomer = () => {
  const [createGroupCustomerExec] = useAsync(
    useCallback(
      (body: GroupCustomerDto) =>
        groupCustomerService.createGroupCustomer(body),
      []
    )
  );
  return {
    createGroupCustomerExec,
  };
};

export const UseUpdateGroupCustomer = () => {
  const [updateGroupCustomerExec] = useAsync(
    useCallback(
      (id: string, body: GroupCustomerDto) =>
        groupCustomerService.updateGroupCustomer(id, body),
      []
    )
  );
  return {
    updateGroupCustomerExec,
  };
};

export const UseGetGroupCustomerById = () => {
  const [getGroupCustomerByIdExec, getGroupCustomerByIdState] = useAsync(
    useCallback(
      (id: string) => groupCustomerService.getGroupCustomerById(id),
      []
    )
  );
  return {
    getGroupCustomerByIdExec,
    getGroupCustomerByIdState: getGroupCustomerByIdState?.data?.data || null,
  };
};

export const UseDeleteGroupCustomer = () => {
  const [deleteGroupCustomerExec] = useAsync(
    useCallback(
      (id: number) => groupCustomerService.deleteGroupCustomer(id),
      []
    )
  );
  return {
    deleteGroupCustomerExec,
  };
};

export const UseGetGroupCustomers = () => {
  const [getGroupCustomersExec, getGroupCustomersState] = useAsync(
    useCallback(
      (body: GroupCustomerGetPage) =>
        groupCustomerService.getGroupCustomers(body),
      []
    )
  );
  return {
    getGroupCustomersExec,
    getGroupCustomersState: getGroupCustomersState?.data?.data || [],
    loading: getGroupCustomersState.loading,
  };
};
