import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { NumberfieldHookForm } from "components/atoms/numberfield";
import { Formfield } from "components/molecules/formfield";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { FormContainer } from "helpers/form";

import { createPointExchangeFormSchema } from "./constant";
import { ConfiguringPointExchangePageVm } from "./vm";

const ConfiguringPointsExchangePage = () => {
  const { createPointExchange, createPointExchangeState } =
    ConfiguringPointExchangePageVm();

  return (
    <General>
      <title>C<PERSON>u hình điểm thành viên</title>
      <Section>
        <Heading type="h1" modifiers="primary">
          CẤU HÌNH ĐIỂM THÀNH VIÊN
        </Heading>
        <Section>
          <FormContainer
            validationSchema={createPointExchangeFormSchema}
            onSubmit={createPointExchange}
          >
            <Formfield label="Số tiền cần tiêu để tích được 1 điểm" name="">
              <NumberfieldHookForm
                name="conversionRateFromMoneyToPoint"
                placeholder="Nhập số tiền"
              />
            </Formfield>
            <Formfield label="Số tiền đổi được bằng 1 điểm" name="">
              <NumberfieldHookForm
                name="conversionRateFromPointToMoney"
                placeholder="Nhập số tiền"
              />
            </Formfield>

            <div className="d-flex justify-content-end u-mt-20">
              <Button modifiers="secondary" buttonType="outline">
                HỦY
              </Button>

              <div className="u-ml-15">
                <Button
                  isLoading={createPointExchangeState.loading}
                  disabled={createPointExchangeState.loading}
                  type="submit"
                >
                  LƯU
                </Button>
              </div>
            </div>
          </FormContainer>
        </Section>
      </Section>
    </General>
  );
};

export default ConfiguringPointsExchangePage;
