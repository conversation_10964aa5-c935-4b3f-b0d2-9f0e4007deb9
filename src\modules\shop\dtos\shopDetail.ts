import { createMapper, fromSchema } from "libs/adapters/dto";
import { mergeSchema, Array } from "libs/domain";
import { employeeDetailDto } from "modules/employee";

import { ShopSchema } from "../entities";

export const shopDetailDto = createMapper(
  fromSchema(
    mergeSchema(ShopSchema, {
      employees: Array(employeeDetailDto),
    })
  )
);
export type ShopDetailDtoType = ReturnType<typeof shopDetailDto>;
