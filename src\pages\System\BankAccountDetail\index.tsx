import React, { useCallback, useMemo } from "react";

import { useSearchParams, useParams } from "react-router";

import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Row, Col } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import { FormContainer } from "helpers/form";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";

import { ChildrenPage } from "../types";
import { BankAccountDetailFormType, inputValidationSchema } from "./constant";
import { BankDetailPageVm } from "./vm";

const IndexPage = () => {
  const [searchUrlPath] = useSearchParams();

  const { bankAccountId } =
    useParams<PageParamsType<ChildrenPage["bankAccountDetail"]>>();

  const {
    loading,
    bankAccountData,
    updateBankAccountDetailState,
    handleUpdateBankAccountDetail,
  } = BankDetailPageVm({
    bankAccountId: Number(bankAccountId),
  });

  const updateBankAccountDetail = useCallback(
    (formData: BankAccountDetailFormType) =>
      handleUpdateBankAccountDetail({
        ...formData,
      }),
    [handleUpdateBankAccountDetail]
  );

  const pageActionType = searchUrlPath.get("action") || "";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  return (
    <SpinnerContainer animating={loading}>
      <General>
        <title key="title">Chi tiết tài khoản ngân hàng</title>
        <Section>
          <Heading type="h1" modifiers="primary">
            THÔNG TIN TÀI KHOẢN NGÂN HÀNG
          </Heading>

          <Section>
            <FormContainer
              validationSchema={inputValidationSchema}
              onSubmit={updateBankAccountDetail}
            >
              <Row>
                <Col xs="12" className="u-mb-15">
                  <Formfield label="Tên ngân hàng" name="bankName">
                    <TextfieldHookForm
                      name="bankName"
                      placeholder="Nhập tên ngân hàng"
                      defaultValue={bankAccountData?.bankName}
                      disabled={viewMode}
                    />
                  </Formfield>
                </Col>

                <Col xs="12" className="u-mb-15">
                  <Formfield label="Số tài khoản" name="accountNumber">
                    <TextfieldHookForm
                      name="accountNumber"
                      placeholder="Nhập số tài khoản"
                      defaultValue={bankAccountData?.accountNumber}
                      disabled={viewMode}
                    />
                  </Formfield>
                </Col>

                <Col xs="12" className="u-mb-15">
                  <Formfield label="Chủ tài khoản" name="owner">
                    <TextfieldHookForm
                      name="owner"
                      placeholder="Nhập chủ tài khoản"
                      defaultValue={bankAccountData?.owner}
                      disabled={viewMode}
                    />
                  </Formfield>
                </Col>

                <Col xs="12">
                  <Formfield label="Chi nhánh" name="branchName">
                    <TextfieldHookForm
                      name="branchName"
                      placeholder="Nhập chi nhánh"
                      defaultValue={bankAccountData?.branchName}
                      disabled={viewMode}
                    />
                  </Formfield>
                </Col>
              </Row>

              <div className="d-flex justify-content-end u-mt-20">
                <Button
                  buttonType="outline"
                  modifiers="secondary"
                  onClick={navigationHelper.goBack}
                >
                  QUAY LẠI
                </Button>
                {editMode && (
                  <div className="u-ml-15">
                    <Button
                      type="submit"
                      disabled={updateBankAccountDetailState.loading}
                      isLoading={updateBankAccountDetailState.loading}
                    >
                      LƯU
                    </Button>
                  </div>
                )}
              </div>
            </FormContainer>
          </Section>
        </Section>
      </General>
    </SpinnerContainer>
  );
};

export default IndexPage;
