import { createEvent, createSource } from "libs/adapters/stores/core";

import { AssignConversationDtoType } from "../dtos";
import {
  ConversationEntityType,
  messageCreatorAvatar,
  MessageCreatorType,
  messageCreatorType,
  MessageEntityType,
} from "../entities";

export type MessageSourceItem = MessageEntityType;

type ConversationType = ConversationEntityType | AssignConversationDtoType;

export type ConversationMessageSourceState = {
  messages: (MessageSourceItem & {
    creatorType: MessageCreatorType;
    creatorAvatar: string;
  })[];
  hasMore: boolean;
  conversation?: ConversationType;
};

export interface ConversationMessageSourceProps {
  loadmoreSlotSize: number;
  conversation?: ConversationType;

  infasInfo: {
    socketId: string;
  };
}

export const implementCreatorTypeMessage = <
  ConversationInputType extends ConversationType
>(
  conversation: ConversationInputType,
  message: MessageSourceItem
) => ({
  ...message,
  creatorType: messageCreatorType(conversation, message.sender),
  creatorAvatar: messageCreatorAvatar(conversation, message.sender) || "",
});

export const conversationMessageSourceCreator = ({
  loadmoreSlotSize,
  conversation,
  infasInfo: { socketId },
}: ConversationMessageSourceProps) => {
  const messageState = createSource<ConversationMessageSourceState>({
    messages: [],
    hasMore: true,
    conversation,
  });

  const changeConversation =
    createEvent<{
      conversation: ConversationType;
    }>();

  const unShiftConversationListMessages =
    createEvent<{
      messages: MessageSourceItem[];
    }>();
  const appendConversationMessage =
    createEvent<{
      message: MessageSourceItem;
    }>();
  const appendMultiConversationMessage =
    createEvent<{
      messages: MessageSourceItem[];
      conversationId: string;
    }>();
  const clearConversationListMessages = createEvent();

  messageState.on(unShiftConversationListMessages, (state, { messages }) => {
    if (!state.conversation) return;

    if (messages.length !== loadmoreSlotSize) {
      state.hasMore = false;
    }

    state.messages = [
      ...messages.map((message) =>
        implementCreatorTypeMessage(state.conversation!, message)
      ),
      ...state.messages,
    ];
  });

  messageState.on(clearConversationListMessages, (state) => {
    state.messages = [];
    state.hasMore = true;
  });

  messageState.on(
    changeConversation,
    (state, { conversation: conversationPayload }) => {
      state.messages = [];
      state.conversation = conversationPayload;
      state.hasMore = true;
    }
  );

  messageState.on(appendConversationMessage, (state, { message }) => {
    if (!state.conversation) return;
    if (message.conversationId !== state.conversation.conversationId) return;
    if (message?.content?.metadata?.fromSocketId !== socketId) {
      state.messages = [
        ...state.messages,
        implementCreatorTypeMessage(state.conversation, message),
      ];
    } else {
      const tempMessage = [...state.messages]
        .reverse()
        .find(({ _id }) => _id === message?.content?.metadata?.tempId);
      if (!tempMessage) return;
      Object.assign(
        tempMessage,
        implementCreatorTypeMessage(state.conversation, message)
      );
    }
  });

  messageState.on(
    appendMultiConversationMessage,
    (state, { messages, conversationId }) => {
      if (!state.conversation) return;
      if (conversationId !== state.conversation.conversationId) return;
      const currentMessages = [...state.messages];
      messages.forEach((message) => {
        if (message?.content?.metadata?.fromSocketId !== socketId) {
          currentMessages.push(
            implementCreatorTypeMessage(state.conversation!, message)
          );
        } else {
          const tempMessage = currentMessages
            .reverse()
            .find(({ _id }) => _id === message?.content?.metadata?.tempId);
          if (!tempMessage) return;
          Object.assign(
            tempMessage,
            implementCreatorTypeMessage(state.conversation!, message)
          );
        }
      });
      state.messages = [...currentMessages];
    }
  );

  return {
    conversationMessageSource: messageState,
    unShiftConversationListMessages,
    clearConversationListMessages,
    appendConversationMessage,
    appendMultiConversationMessage,
    changeConversation,
  };
};
