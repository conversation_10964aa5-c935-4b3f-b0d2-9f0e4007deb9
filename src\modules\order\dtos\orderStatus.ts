import { createMapper, fromSchema } from "libs/adapters/dto";
import { Array, mergeSchema } from "libs/domain";

import { OrderStatusSchema } from "../entities/orderStatus";

export const orderStatusItemListDto = createMapper(
  fromSchema(
    mergeSchema(OrderStatusSchema, {
      prevOrderStatus: Array(fromSchema(OrderStatusSchema)),
    })
  )
);

export type OrderStatusItemListDtoType = ReturnType<
  typeof orderStatusItemListDto
>;
