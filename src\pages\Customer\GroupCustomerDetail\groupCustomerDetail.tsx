import React, { useCallback, useEffect, useMemo } from "react";
import { ArrowLeftOutlined, SaveOutlined } from "@ant-design/icons";
import { Card, Col, Form, Input, Row } from "antd";
import { useLocation, useNavigate, useParams } from "react-router";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { showLoading } from "components/atoms/base/Spinner";
import { Heading } from "components/atoms/heading";
import { COLOR } from "constants/color";
import {
  UseCreateGroupCustomer,
  UseGetGroupCustomerById,
  UseUpdateGroupCustomer,
} from "../hook/useGetGroupCustomer";
import { GroupCustomerDto } from "./dto/groupCustomer.dto";

export default function GroupCustomerDetail() {
  const [form] = Form.useForm();
  const { groupCustomerId } = useParams<{ groupCustomerId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const { createGroupCustomerExec } = UseCreateGroupCustomer();
  const { updateGroupCustomerExec } = UseUpdateGroupCustomer();
  const { getGroupCustomerByIdExec, getGroupCustomerByIdState } =
    UseGetGroupCustomerById();
  const isAddNew = useMemo(() => {
    return groupCustomerId === "isAddNew";
  }, [location.pathname, groupCustomerId]);

  console.log(isAddNew, "isAddNew");

  const isView = searchParams.get("action") === "view";

  const goBack = useCallback(() => {
    navigate(-1);
    // eslint-disable-next-line
  }, [navigate]);

  const handleSave = useCallback(
    async (value: GroupCustomerDto) => {
      showLoading(true);
      if (isAddNew) {
        createGroupCustomerExec(value)
          .then((res) => {
            if (res.status === 200) {
              showNotification({
                type: "success",
                message: "Tạo nhóm khách hàng thành công",
              });
            }
            goBack();
          })
          .catch((error) => {
            console.error("Error creating campaign:", error);
            showNotification({
              type: "error",
              message:
                error?.response?.data?.errors?.[0]?.message ||
                "Tạo nhóm khách hàng thất bại",
            });
          })
          .finally(() => {
            showLoading(false);
          });
        return;
      }
      updateGroupCustomerExec(groupCustomerId, value)
        .then((res) => {
          if (res.status === 200) {
            showNotification({
              type: "success",
              message: "Cập nhật nhóm khách hàng thành công",
            });
          }
        })
        .catch((error) => {
          console.error("Error updating group customer:", error);
          showNotification({
            type: "error",
            message:
              error?.response?.data?.errors?.[0]?.message ||
              "Cập nhật nhóm khách hàng thất bại",
          });
        })
        .finally(() => {
          showLoading(false);
        });
    },
    [groupCustomerId, updateGroupCustomerExec]
  );

  useEffect(() => {
    if (!isAddNew) {
      getGroupCustomerByIdExec(groupCustomerId)
        .then((res) => {
          if (res.status === 200) {
            form.setFieldsValue(res.data.data);
          }
        })
        .catch((error) => {
          console.error("Error fetching group customer by ID:", error);
          showNotification({
            type: "error",
            message:
              error?.response?.data?.errors?.[0]?.message ||
              "Lấy thông tin nhóm khách hàng thất bại",
          });
        });
    }
  }, []);
  return (
    <>
      <title>Chi tiết nhóm khách hàng</title>
      <div className="p-4 flex flex-col gap-4">
        <div className="flex items-center justify-start gap-3">
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={goBack}
            icon={<ArrowLeftOutlined rev={undefined} />}
          />
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={() => {
              form.submit();
            }}
            disabled={isView && !isAddNew}
            icon={<SaveOutlined rev={undefined} />}
          >
            Lưu
          </BaseButton>
        </div>
        <Card
          title="Thông tin nhóm khách hàng"
          className="shadow-md rounded-lg"
        >
          <Form
            form={form}
            layout="vertical"
            disabled={isView && !isAddNew}
            onFinish={handleSave}
            initialValues={{
              status: "active",
            }}
          >
            <div style={{ marginBottom: 24 }}>
              <Heading type="h4">Thông tin nhóm khách hàng</Heading>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="Mã nhóm khách hàng"
                    name="groupCustomerCode"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng nhập mã nhóm khách hàng",
                      },
                    ]}
                  >
                    <Input placeholder="Nhập mã nhóm khách hàng" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Tên nhóm khách hàng"
                    name="groupCustomerName"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng nhập tên nhóm khách hàng",
                      },
                    ]}
                  >
                    <Input placeholder="Nhập tên nhóm khách hàng" />
                  </Form.Item>
                </Col>
              </Row>
            </div>
          </Form>
        </Card>
      </div>
    </>
  );
}
