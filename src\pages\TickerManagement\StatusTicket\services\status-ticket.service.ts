import crmDriverV1 from "services/crm/crm-driver-v1";
import {
  GetListStatusTicketDto,
  GetListStatusTicketResponseDto,
} from "../dto/get-status-ticket.dto";
import {
  CreateStatusTicketDto,
  UpdateStatusTicketDto,
} from "../dto/status-ticket.dto";

const statusTicketServices = {
  getListStatusTicket: (
    dto: GetListStatusTicketDto
  ): Promise<{ data: GetListStatusTicketResponseDto }> => {
    const url = `/media/external/status-ticket/list?pageNum=${dto.pageNum}&pageSize=${dto.pageSize}&searchText=${dto.searchText}`;
    return crmDriverV1.get(url);
  },

  createStatusTicket: (dto: CreateStatusTicketDto) => {
    const url = `/media/external/status-ticket`;
    return crmDriverV1.post(url, dto);
  },

  updateStatusTicket: (dto: UpdateStatusTicketDto) => {
    const { statusTicketId, ...restDto } = dto;
    const url = `/media/external/status-ticket/${statusTicketId}`;
    return crmDriverV1.put(url, restDto);
  },

  deleteStatusTicket: ({ statusTicketId }: { statusTicketId: number }) => {
    const url = `/media/external/status-ticket/${statusTicketId}`;
    return crmDriverV1.delete(url);
  },
};

export default statusTicketServices;
