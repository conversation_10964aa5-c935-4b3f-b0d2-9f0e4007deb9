// Test navigation paths for Campaign Management
// This file documents the expected navigation behavior

/*
Navigation Test Cases:

1. From Campaign Management (/QuanLyChienDich):
   - Click "Thêm mới" → Navigate to: /QuanLyChienDich/new?mode=add
   - Click "View" on a record → Navigate to: /QuanLyChienDich/{id}?mode=view
   - Click "Edit" on a record → Navigate to: /QuanLyChienDich/{id}?mode=edit

2. From Campaign Detail:
   - Click "Quay lại" → Navigate to: /QuanLyChienDich
   - Click "Lưu" (Save) → Navigate to: /QuanLyChienDich (after save)
   - Click "Hủy" (Cancel) → Stay on page but change mode to view

3. URL Parameters:
   - campaignId: "new" for add mode, actual ID for view/edit
   - mode: "add", "view", or "edit" (defaults based on campaignId)

4. Form Behavior:
   - View mode: Form is disabled, only "Chỉnh sửa" button visible
   - Edit mode: Form is enabled, "Hủy" and "Lưu" buttons visible
   - Add mode: Form is enabled, "Hủy" and "Thêm mới" buttons visible

5. Data Loading:
   - New campaign: Form reset with default values
   - Existing campaign: Load data via API call using campaignId
*/

export const CAMPAIGN_ROUTES = {
  MANAGEMENT: "/QuanLyChienDich",
  DETAIL: "/QuanLyChienDich/:campaignId",
  ADD_NEW: "/QuanLyChienDich/new?mode=add",
  VIEW: (id: string) => `/QuanLyChienDich/${id}?mode=view`,
  EDIT: (id: string) => `/QuanLyChienDich/${id}?mode=edit`,
};
