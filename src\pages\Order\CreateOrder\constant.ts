import * as Yup from "yup";

import { customerShippingAddressOption } from "modules/customer";
import { assignEmployeeOption } from "modules/employee";
import {
  OrderItemEntityType,
  orderReturnStatusOption,
  orderReturnStatusTaglineOption,
  orderSourceOption,
  orderStatusOption,
  orderStatusTaglineOption,
} from "modules/order";

export type CreateOrderFormPayload = OrderItemEntityType & {
  totalAmount: number;
  shippingAddress?: ReturnType<typeof customerShippingAddressOption>;
  orderStatus?: ReturnType<typeof orderStatusOption>;
  orderStatusTagline?: ReturnType<typeof orderStatusTaglineOption>;
  returnStatus?: ReturnType<typeof orderReturnStatusOption>;
  returnStatusTagline?: ReturnType<typeof orderReturnStatusTaglineOption>;
  assignedEmployeeId?: ReturnType<typeof assignEmployeeOption>;
  orderSourceId?: ReturnType<typeof orderSourceOption>;
};
export const createOrderValidateSchema = Yup.object({
  shippingAddress: Yup.object().required("Địa chỉ giao hàng hàng bắt buộc"),
  assignedEmployeeId: Yup.object().required(
    "Vui lòng phân công cho một nhân viên"
  ),
  orderSource: Yup.object().required("Nguồn đơn là bắt buộc"),
});
