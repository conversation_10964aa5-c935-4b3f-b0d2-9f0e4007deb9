import { useCallback, useEffect, useRef, useState } from "react";

import produce from "immer";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import useInfinity from "hooks/useInfinity";
import { usePagination } from "hooks/usePagination";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import {
  orderReturnStatusOption,
  createOrderReturnStatusDto,
  CreateOrderReturnStatusDtoType,
  orderReturnStatusDetailDto,
  OrderReturnStatusDetailDtoType,
} from "modules/order";
import {
  createReturnOrderStatus as createOrderReturnStatusService,
  deleteReturnStatus,
  getOrderReturnStatus,
} from "services/crm/order";

type ModalType = "createOrderReturnStatus";

interface State {
  modalState: {
    open: boolean;
    modalType?: ModalType;
  };
  pagination: {
    pageSize: number;
  };
  createNewReturnOrderStatusState: {
    colorSelected?: string;
  };
}

export const ExchangeStatusPageVm = () => {
  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      modalType: undefined,
    },
    pagination: {
      pageSize: 10,
    },
    createNewReturnOrderStatusState: {
      colorSelected: "#000",
    },
  });

  const pageSizeRef = useRef<number>(state.pagination.pageSize);

  const [getOrderReturnStatusExec, getOrderReturnStatusState] = useAsync(
    useCallback(
      (option: { pageNum: number; pageSize: number }) =>
        getOrderReturnStatus({ ...option }).then((res) => ({
          orderReturnStatuses: mapFrom(
            res.data.data,
            orderReturnStatusDetailDto
          ),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const { gotoPage, ...orderReturnStatusPaginationState } = usePagination({
    pageSize: state.pagination.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getOrderReturnStatusExec({ pageSize, pageNum: page }),
  });

  const {
    sortedData: orderReturnStatuses,
    toggleSortState: toggleOrderReturnStatusesBy,
  } = useSortable({
    data: getOrderReturnStatusState.data?.orderReturnStatuses,
    sortBy: {
      name: ({ name }) => name,
      updatedAt: ({ updatedAt }) => updatedAt,
    },
  });

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pagination.pageSize = pageSize;
      })
    );
    pageSizeRef.current = pageSize;
  }, []);

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.modalType = type;
      })
    );
  }, []);

  const handleCloseModal = useCallback(() => {
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.modalType = undefined;
      })
    );
  }, []);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) => {
      return state.modalState.open && state.modalState.modalType === type;
    },
    [state.modalState]
  );

  const [createOrderReturnStatusExec, createOrderReturnStatusState] = useAsync(
    createOrderReturnStatusService,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Tạo mới thành công" });
        handleCloseModal();
        getOrderReturnStatusExec({
          pageSize: pageSizeRef.current,
          pageNum: 1,
        });
      }, [handleCloseModal]),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const createReturnOrderStatus = useCallback(
    async (rawPayload: Partial<CreateOrderReturnStatusDtoType>) => {
      const orderPayload = createOrderReturnStatusDto({
        ...rawPayload,
      });
      createOrderReturnStatusExec(orderPayload);
    },
    [createOrderReturnStatusExec]
  );

  const {
    gotoFirstPage: resetPage,
    goNextPage: loadMoreReturnStatus,
    state: pulldownState,
  } = useInfinity<OrderReturnStatusDetailDtoType[]>(
    async (payload: { pageNum: number; pageSize: number }) =>
      getOrderReturnStatus({ ...payload }),
    {
      pageSize: 15,
    }
  );

  const { options: orderStatusOptionPulldown } = usePulldownHelper({
    dataSource: pulldownState.data || [],
    optionCreator: orderReturnStatusOption,
  });

  useEffect(() => {
    gotoPage(1);
    resetPage();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize]);

  const handleLoadmoreReturnStatus = async () => {
    loadMoreReturnStatus();
  };

  const handleDeleteReturnStatus = useCallback((returnStatusId: number) => {
    deleteReturnStatus(returnStatusId).then((res) => {
      if (res.status === 200) {
        toastSingleMode({ type: "success", message: "Xóa thành công" });
        getOrderReturnStatusExec({
          pageSize: pageSizeRef.current,
          pageNum: 1,
        });
      }
    });
  }, []);

  const onChangeColor = useCallback(
    (code: string) => {
      setState(
        produce((draft) => {
          draft.createNewReturnOrderStatusState.colorSelected = code;
        })
      );
    },

    [setState]
  );

  return {
    pageSize: state.pagination.pageSize,
    loading: getOrderReturnStatusState.loading,
    gotoPage,
    orderReturnStatusPaginationState,
    orderReturnStatuses: orderReturnStatuses || [],
    toggleOrderReturnStatusesBy,
    handleChangePageSize,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    modalCreator: {
      colorSelected: state.createNewReturnOrderStatusState.colorSelected,
      onChangeColor,
      createOrderReturnStatusState,
      orderStatusOptionPulldown,
      createReturnOrderStatus,
      handleLoadmoreReturnStatus,
    },
    handleDeleteReturnStatus,
  };
};
