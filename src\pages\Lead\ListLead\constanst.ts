import * as Yup from "yup";

import { assignEmployeeOption } from "modules/employee";
import { CreateLeadDTOType } from "modules/lead";

export const inputValidationSchema = Yup.object({
  phone: Yup.string()
    .required("<PERSON><PERSON> lòng nhập số điện thoại")
    .length(10, "Số điện thoại phải có 10 chử số"),
});

export type CreateLeadFormPayload = Pick<
  CreateLeadDTOType,
  "phone" | "note"
> & {
  assignedEmployee: ReturnType<typeof assignEmployeeOption>;
};
