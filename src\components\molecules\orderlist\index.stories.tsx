import { Story, Meta } from "@storybook/react/types-6-0";

import { OrderList, OrderItemProps, OrderItem } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|molecules/Orderlist",
  component: OrderList,
} as Meta;

const Template: Story<OrderItemProps> = ({
  code,
  phoneNumber,
  status,
  price,
  statusColor,
  date,
}) => (
  <OrderList>
    {Array(2)
      .fill(0)
      .map(() => (
        // eslint-disable-next-line react/jsx-key
        <OrderItem
          code={code}
          phoneNumber={phoneNumber}
          status={status}
          statusColor={statusColor}
          price={price}
          date={date}
        />
      ))}
  </OrderList>
);

export const Normal = Template.bind({});

Normal.args = {
  code: "ADB91234567675",
  phoneNumber: "0363847334",
  status: "Hoàn thành",
  statusColor: "",
  price: 125000,
  date: "23-09-20 21:30",
};
