.o-tabs {
	$animation: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

	&-main {
		.react-tabs {
			&__tab-list {
				display: flex;
				margin-bottom: rem(20);
				border: none;
			}

			&__tab {
				bottom: unset;
				width: 100%;
				margin: 0 rem(2);
				font-size: rem(14);
				line-height: rem(16);
				text-align: center;
				border: none;
				border-bottom: 1px solid $COLOR-QUARTZ;
				box-shadow: none;
				transition: all $animation;
				@include u-fw-bold;

				&::after {
					position: absolute;
					bottom: 0;
					left: 0;
					width: 100%;
					height: rem(0.5);
					content: "";
					background-color: $COLOR-DENIM;
					transition: transform $animation;
					transform: scaleX(0);
				}

				&--selected,
				&:hover {
					color: $COLOR-DENIM;
				}

				&--selected {
					&::after {
						transform: scaleX(1);
					}
				}
			}
		}
	}

	&-editing {
		.react-tabs {
			&__tab-list {
				border-color: $COLOR-DENIM;
			}

			&__tab {
				bottom: unset;
				font-family: $FONTFAMILY-ARIAL;
				font-size: rem(16);
				line-height: rem(16);
				background-color: $COLOR-TRANSPARENT;
				border-radius: unset;
				transition: all $animation;

				&[aria-selected="true"] {
					color: $COLOR-WHITE;
					background-color: $COLOR-DENIM;
					border-color: $COLOR-DENIM;
				}

				&[aria-selected="false"]:hover {
					background-color: rgba($COLOR-DENIM, 0.1);
				}
			}
		}
	}
}
