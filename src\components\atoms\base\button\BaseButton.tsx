// /* eslint-disable react/jsx-props-no-spreading */
// import { Button, ButtonProps, ConfigProvider, ThemeConfig } from "antd";
// import { COLOR } from "constants/color";

// export type BaseButtonProps = {
//   bgColor?: string;
//   hoverColor?: string;
//   theme?: NonNullable<ThemeConfig["components"]>["Button"];
// } & ButtonProps;

// export const BaseButton = (props: BaseButtonProps) => {
//   const { bgColor, hoverColor, theme, children, ...rest } = props;
//   const defaultTheme: typeof theme = {
//     colorPrimaryActive: bgColor,
//     colorPrimaryBorder: bgColor,
//     colorPrimary: bgColor,
//     colorPrimaryHover: hoverColor,
//     defaultBorderColor: bgColor,
//     defaultColor: bgColor,
//     contentFontSizeSM: 12,
//     ...theme,
//   };
//   return (
//     <ConfigProvider theme={{ components: { Button: defaultTheme } }}>
//       <Button {...rest}>{children}</Button>
//     </ConfigProvider>
//   );
// };

// BaseButton.defaultProps = {
//   bgColor: COLOR.PRIMARY[500],
//   hoverColor: COLOR.PRIMARY[400],
// };

/* eslint-disable react/jsx-props-no-spreading */
import { Button, ButtonProps, ConfigProvider, ThemeConfig } from "antd";
import { COLOR } from "constants/color";

export type BaseButtonProps = {
  bgColor?: string;
  hoverColor?: string;
  theme?: NonNullable<ThemeConfig["components"]>["Button"];
} & ButtonProps;

export const BaseButton = ({
  bgColor = COLOR.PRIMARY[500],
  hoverColor = COLOR.PRIMARY[400],
  theme,
  children,
  ...rest
}: BaseButtonProps) => {
  const defaultTheme: typeof theme = {
    colorPrimaryActive: bgColor,
    colorPrimaryBorder: bgColor,
    colorPrimary: bgColor,
    colorPrimaryHover: hoverColor,
    defaultBorderColor: bgColor,
    defaultColor: bgColor,
    contentFontSizeSM: 12,
    ...theme,
  };

  return (
    <ConfigProvider theme={{ components: { Button: defaultTheme } }}>
      <Button {...rest}>{children}</Button>
    </ConfigProvider>
  );
};
