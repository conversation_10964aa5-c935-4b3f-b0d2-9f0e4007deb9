import { getIn } from "yup";
import crmDriverV1 from "services/crm/crm-driver-v1";
import { InventoryResponse, InvertoryDto } from "../dto/inventory.dto";
import { StorageDto, StorageResponse } from "../dto/storage.dto";

const InventoryService = {
  getAllStorage: (): Promise<{ data: StorageResponse }> => {
    const url = "/locations/external/storages/getAllStorage";
    return crmDriverV1.get(url);
  },
  getInventory: (dto: InvertoryDto): Promise<{ data: InventoryResponse }> => {
    const { storeCode, ...rest } = dto ?? {};

    const url = `/products/external/item-stock-locations/get-by-store-code/${storeCode}`;
    return crmDriverV1.get(url, {
      params: {
        ...rest,
      },
    });
  },
};
export default InventoryService;
