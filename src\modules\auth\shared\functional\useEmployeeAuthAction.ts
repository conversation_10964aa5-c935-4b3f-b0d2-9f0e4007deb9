import { useCallback } from "react";

import { employeeDetailDto } from "modules/employee";
import {
  employeeLogin as employeeLoginService,
  employeeLogout as employeeLogoutService,
} from "services/crm/employee";
import { useAppDispatch } from "store";
import authSlice from "store/auth/reducer";

export const useEmployeeAuthAction = () => {
  const dispatch = useAppDispatch();

  const employeeLogoutAction = useCallback(
    () => dispatch(authSlice.actions.employeeLogout()),
    [dispatch]
  );

  // const employeeLogin = useCallback(
  // 	(emailOrPhone: string, password: string) =>
  // 		employeeLoginService({ emailOrPhone, password })
  // 			.then((res) => {
  // 				console.log(res);
  // 				return {
  // 					token: res.data.data?.accessToken,
  // 					refreshToken: res.data.data?.refreshToken,
  // 					employee: employeeDetailDto(res.data.data?.profile),
  // 				};
  // 			})
  // 			.then(({ employee, token, refreshToken }) => {
  // 				dispatch(
  // 					authSlice.actions.updateEmployeeAuthState({
  // 						employee,
  // 						token,
  // 						refreshToken,
  // 					})
  // 				);
  // 			}),
  // 	[dispatch]
  // );
  const employeeLogin = useCallback(
    (emailOrPhone: string, password: string, site: string) =>
      employeeLoginService({ emailOrPhone, password, site })
        .then((res) => {
          return {
            token: res.data.accessToken || null,
            refreshToken: res.data.refreshToken || null,
            employee: employeeDetailDto(res.data.profile),
          };
        })
        .then(({ employee, token, refreshToken }) => {
          dispatch(
            authSlice.actions.updateEmployeeAuthState({
              employee,
              token,
              refreshToken,
            })
          );
        }),
    [dispatch]
  );

  const employeeLogout = useCallback(
    (refreshToken: string) =>
      employeeLogoutService({ refreshToken }).then(employeeLogoutAction),
    [employeeLogoutAction]
  );

  return { employeeLogin, employeeLogout, employeeLogoutAction };
};
