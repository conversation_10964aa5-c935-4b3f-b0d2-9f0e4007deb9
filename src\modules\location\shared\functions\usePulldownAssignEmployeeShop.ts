import useDidMount from "helpers/react-hooks/useDidMount";
import { usePulldownAssignEmployee } from "modules/employee";

export const usePulldownAssignEmployeeShop = () => {
  const {
    loadMoreEmployee,
    assignEmployeeOptions,
    loadMoreEmployeeState,
    employees,
  } = usePulldownAssignEmployee();

  useDidMount(() => loadMoreEmployee());

  return {
    loadMoreEmployee,
    assignEmployeeOptions,
    loadMoreEmployeeState,
    listEmployee: employees,
  };
};
