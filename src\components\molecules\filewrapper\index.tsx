import React from "react";

import { Icon } from "components/atoms/icon";
import useUploadFile from "components/hooks/useUploadFile";

export interface Props {
  label: string;
  onChange?: (file: File) => void;
}

export const FileWrapper: React.FC<Props> = ({ label, onChange }) => {
  const { refInput, fileUpload, handleClearFile, onChangeFileUpload } =
    useUploadFile({
      onChange,
      extensions: ["jpg", "jpeg", "png"],
      fileSize: 1,
    });

  return (
    <div className="m-filewrapper">
      {!fileUpload ? (
        <>
          <input
            id="file-upload"
            type="file"
            ref={refInput}
            hidden
            onChange={onChangeFileUpload}
          />
          <label className="m-filewrapper_button" htmlFor="file-upload">
            <Icon iconName="upload" />
            <span className="m-filewrapper_label">{label}</span>
          </label>
        </>
      ) : (
        <div className="m-filewrapper_data">
          <span className="m-filewrapper_filename">{fileUpload.name}</span>
          <div className="m-filewrapper_filetype">
            {fileUpload.type} - {`${(fileUpload.size / 1024).toFixed()} KB`}
          </div>
          <Icon iconName="close" onClick={handleClearFile} />
        </div>
      )}
    </div>
  );
};
