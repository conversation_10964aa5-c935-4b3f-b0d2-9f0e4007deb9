import { useCallback, useRef, useState } from "react";

import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { paginationDTO, PaginationDTOType } from "modules/common/pagination";
import { EmployeeEntityType, EmployeeModel } from "modules/employee/entities";
import { assignEmployeeOption } from "modules/employee/valueObject";
import { getListEmployee } from "services/crm/employee";

export interface AssignEmployeeFunctionalOptions {
  excludePending: boolean;
}

interface State {
  employees: EmployeeEntityType[];
  pagination?: PaginationDTOType;
}

export const usePulldownAssignEmployee = (
  { excludePending }: AssignEmployeeFunctionalOptions = {
    excludePending: false,
  }
) => {
  const prevFilterRef = useRef<{ name: string }>({
    name: "",
  });

  const [state, setState] = useState<State>({
    employees: [],
    pagination: undefined,
  });
  const [loadMoreEmployee, loadMoreEmployeeState] = useAsync(
    useCallback((args: Parameters<typeof getListEmployee>[0]) => {
      const nextNameFilter = args.name || "";
      const payload = {
        ...args,
        name: nextNameFilter.trim() ? nextNameFilter : undefined,
      };

      return getListEmployee(payload).then(({ data }) => ({
        data: EmployeeModel.createMap(data.data),
        links: paginationDTO(data.links),
      }));
    }, []),
    {
      excludePending,
      onSuccess: useCallback(({ data, links }) => {
        setState((prevState) => ({
          ...prevState,
          employees: [...prevState.employees, ...data],
          pagination: links,
        }));
      }, []),
    }
  );

  const { employees } = state;

  const loadMoreEmployeeExec = useCallback(
    async (args?: Pick<Parameters<typeof getListEmployee>[0], "name">) => {
      let nextPage: number | undefined;
      const { name } = prevFilterRef.current;
      const nextNameFilter = args?.name !== undefined ? args.name : name;
      if (nextNameFilter !== name) {
        nextPage = 1;
        prevFilterRef.current = {
          ...prevFilterRef.current,
          name: nextNameFilter,
        };
        setState((prevState) => ({
          ...prevState,
          employees: [],
        }));
      } else if (!state.pagination) {
        nextPage = 1;
      } else {
        const currentPageNum = state.pagination.self.pageNum;
        if (currentPageNum + 1 <= state.pagination.last.pageNum) {
          nextPage = currentPageNum + 1;
        }
      }

      if (!nextPage) return;
      loadMoreEmployee({
        pageNum: nextPage,
        pageSize: 15,
        name: nextNameFilter,
      });
    },
    [loadMoreEmployee, state.pagination]
  );

  const {
    options: assignEmployeeOptions,
    formatOption: formatAssignEmployeeOption,
    getOptionByValue,
  } = usePulldownHelper({
    dataSource: employees,
    optionCreator: assignEmployeeOption,
    valueTrans: Number,
  });

  return {
    loadMoreEmployee: loadMoreEmployeeExec,
    loadMoreEmployeeState,
    employees,
    assignEmployeeOptions,
    formatAssignEmployeeOption,
    getOptionByValue,
  };
};
