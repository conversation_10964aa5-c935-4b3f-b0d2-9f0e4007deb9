import { useEffect, useMemo, useState } from "react";
import { LeftOutlined, SaveOutlined } from "@ant-design/icons";
import { Empty, Modal, Spin, Typography } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { Link, useParams, useSearchParams } from "react-router";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { showLoading } from "components/atoms/base/Spinner";
import { COLOR } from "constants/color";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";
import { OrderDetailDtoType } from "modules/order";
import { OrderFormExchange } from "modules/order/shared/presentational/orderFormExchange";
import { CreateExchangeOrderDto } from "services/crm/order/dto/order.dto";
import { useGetOrderStatusWithoutPagination } from "services/crm/order/hooks/order-status.hook";
import {
  useCreateOrderExchange,
  useGetOrderDetail,
} from "services/crm/order/hooks/order.hook";
import RootPageRouter from "..";
import { ChildrenPage } from "../types";
import { ExChangeOrderFormType } from "./types/ExchangeOrderForm.type";

interface InitialOrderExchangeType extends OrderDetailDtoType {
  orderIdRef: number;
  codeRef: string;
}

export default function ExChangeOrderPage() {
  const [searchUrlPath] = useSearchParams();
  const { orderId } =
    useParams<PageParamsType<ChildrenPage["exChangeOrder"]>>();
  const [orderDetail, setOrderDetailData] =
    useState<InitialOrderExchangeType | null>();
  const {
    orderDetailRefetch,
    orderDetailData,
    loading: orderDetailLoading,
  } = useGetOrderDetail({
    orderId: Number(orderId),
  });

  const pageActionType = searchUrlPath.get("action") || "view";
  const formMode = pageActionType === "exchange" ? "editable" : "view-only";
  const { createOrderExchangeRefetch } = useCreateOrderExchange();

  const handleConfirmNegativeAmount = (payload: CreateExchangeOrderDto) => {
    Modal.confirm({
      centered: true,
      title: "Xác nhận tạo đơn hàng đổi",
      content: "Bạn có chắc chắn muốn tạo đơn hàng với tổng tiền hàng âm?",
      okText: "Xác nhận",
      cancelText: "Hủy",
      onOk: () => {
        showLoading(true);
        createOrderExchangeRefetch(payload)
          .then(() => {
            showNotification({
              type: "success",
              message: "Tạo đơn hàng đổi thành công",
            });
            handleRefetch();
          })
          .catch((error) => {
            const errorApi = error?.response?.data?.errors || [];
            if (errorApi) {
              if (Array.isArray(errorApi) && errorApi?.length > 0) {
                errorApi.forEach((errorItem) => {
                  showNotification({
                    type: "error",
                    message: errorItem.title || "Chỉnh sửa đơn hàng thất bại",
                  });
                });
              } else {
                showNotification({
                  type: "error",
                  message: errorApi.title,
                });
              }
            } else {
              showNotification({
                type: "error",
                message: "Chỉnh sửa đơn hàng thất bại",
              });
            }
          })
          .finally(() => {
            showLoading(false);
          });
      },
    });
  };

  const handleSubmitExchangeOrder = (formData: ExChangeOrderFormType) => {
    const { items } = formData ?? {};
    const validMinQuantity = 0;
    const totalQuantityInItems = items?.reduce(
      (total, item) => total + (item.quantity || validMinQuantity),
      0
    );
    if (totalQuantityInItems < validMinQuantity) {
      showNotification({
        type: "warning",
        message:
          "Tổng số lượng sản phẩm trong đơn hàng không hợp lệ, Vui lòng kiểm tra lại.",
      });
      return;
    }
    const convertItem = items.map((item) => {
      return {
        skuId: item.skuId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalAmount: item.totalAmount,
        productId: item.productId,
        discountAmount: item.discountAmount,
        discountPercent: item.discountPercent,
        listedPrice: item.listedPrice,
        orderItemIdRef: _.isNaN(item.orderItemIdRef)
          ? null
          : item.orderItemIdRef,
      };
    });
    const formDataOmit = _.omit(formData, [
      "customer",
      "createdAt",
      "shippingAddressId",
    ]);

    const payload = {
      ...formDataOmit,
      birthDay: formData.birthDay ? formData.birthDay.toISOString() : null,
      items: convertItem,
      shippingCost: formData.shippingCost || 0,
      applicableFee: formData.applicableFee || 0,
      discount: formData.discount || 0,
      discountValue: formData.discountValue || 0,
      promotionValue: formData.promotionValue || 0,
    };

    if (formData.totalAmount < 0) {
      handleConfirmNegativeAmount(payload);
      return;
    }

    showLoading(true);
    createOrderExchangeRefetch(payload)
      .then((res) => {
        const response = res?.data?.data;

        showNotification({
          type: "success",
          message: "Tạo đơn hàng đổi trả thành công",
        });

        const { orderId: orderIdResponse } = response ?? {};
        if (orderIdResponse) {
          RootPageRouter.gotoChild("exChangeOrderDetail", {
            params: {
              orderId: orderIdResponse.toString(),
            },
            queryString: `?action=edit`,
          });
        }
      })
      .catch((error) => {
        const errorApi = error?.response?.data?.errors || [];
        if (errorApi) {
          if (Array.isArray(errorApi) && errorApi?.length > 0) {
            errorApi.forEach((errorItem) => {
              showNotification({
                type: "error",
                message: errorItem.title || "Tạo đơn hàng đổi trả thất bại",
              });
            });
          } else {
            showNotification({
              type: "error",
              message: errorApi.title,
            });
          }
        } else {
          showNotification({
            type: "error",
            message: "Tạo đơn hàng đổi trả thất bại",
          });
        }
      })
      .finally(() => {
        showLoading(false);
      });
    // Handle form submission
  };

  const { orderStatusData, orderStatusRefetch } =
    useGetOrderStatusWithoutPagination({
      searchText: "",
    });

  const statusDefault = useMemo(() => {
    return orderStatusData?.data?.find((item) => item.isDefault);
  }, [orderStatusData]);

  useEffect(() => {
    if (!_.isEmpty(orderDetailData?.data) && !_.isEmpty(statusDefault)) {
      const { data } = orderDetailData;
      const CloneDataOmitSomeFields = _.omit(data, [
        "orderStatusId",
        "orderStatusTaglineId",
        "orderType",
        "createdAt",
        "paid",
        "remainingAmount",
        "pay",
      ]);

      const convertItem = data?.items.map((item) => {
        return {
          ...item,
          orderItemId: null,
          totalAmount: -item.totalAmount,
          // discountAmount: -item.discountAmount,
          // discountPercent: -item.discountPercent,

          quantity: -item.quantity,
          isExchange: true,
          maxQuantity: -1,
          minQuantity: -item.quantity,
          orderItemIdRef: item.orderItemId,
        };
      });

      setOrderDetailData({
        ...CloneDataOmitSomeFields,
        pay: 0,
        remainingAmount: 0,
        paid: 0,
        code: null,
        orderId: null,
        orderStatusId: statusDefault?.orderStatusId || 1,
        orderStatusTaglineId: null,
        codeRef: data?.code,
        orderIdRef: data?.orderId,
        items: convertItem,
        createdAt: new Date(),
        orderType: 1, // Default to 1 if not provided
      } as InitialOrderExchangeType);
    } else {
      setOrderDetailData(null);
    }
  }, [orderDetailData, statusDefault]);

  useEffect(() => {
    orderDetailRefetch();
  }, [orderId]);

  useEffect(() => {
    orderStatusRefetch();
  }, []);

  const handleRefetch = () => {
    orderDetailRefetch();
  };

  const goBack = () => {
    RootPageRouter.gotoChild("listOrder");
  };

  const navigateLink = RootPageRouter.children.orderDetail.generatePath({
    orderId: orderId?.toString() || "",
  });

  return (
    <Spin spinning={orderDetailLoading} tip="Đang tải dữ liệu...">
      {orderDetailData ? (
        <OrderFormExchange
          onSubmit={handleSubmitExchangeOrder}
          initialOrderDetail={orderDetail}
          handleRefetch={handleRefetch}
          mode={formMode}
          navigateLink={navigateLink}
          cancelButton={
            <BaseButton
              type="primary"
              className="w-fit"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[600]}
              icon={<LeftOutlined />}
              onClick={goBack}
            >
              {/* Quay lại */}
            </BaseButton>
          }
          submitButton={
            <BaseButton
              type="primary"
              htmlType="submit"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[600]}
              className="w-fit"
              // disabled={updateOrderDetailState.loading}
              icon={<SaveOutlined />}
            >
              Tạo đơn hàng đổi
            </BaseButton>
          }
        />
      ) : (
        <Empty
          description={
            orderDetailLoading ? "Đang tải dữ liệu" : "Không có dữ liệu"
          }
          style={{ marginTop: "20px" }}
        />
        // <OrderForm
        //   onSubmit={handleSubmitExchangeOrder}
        //   handleRefetch={handleRefetch}
        //   mode={formMode}
        //   cancelButton={
        //     <BaseButton
        //       type="primary"
        //       className="w-fit"
        //       bgColor={COLOR.BLUE[500]}
        //       hoverColor={COLOR.BLUE[600]}
        //       icon={<LeftOutlined />}
        //       onClick={navigationHelper.goBack}
        //     >
        //       {/* Quay lại */}
        //     </BaseButton>
        //   }
        //   submitButton={
        //     formMode === "editable" && (
        //       <BaseButton
        //         type="primary"
        //         htmlType="submit"
        //         bgColor={COLOR.BLUE[500]}
        //         hoverColor={COLOR.BLUE[600]}
        //         className="w-fit"
        //         // disabled={updateOrderDetailState.loading}
        //         icon={<SaveOutlined />}
        //       >
        //         Lưu
        //       </BaseButton>
        //     )
        //   }
        // />
      )}
    </Spin>
  );
}
