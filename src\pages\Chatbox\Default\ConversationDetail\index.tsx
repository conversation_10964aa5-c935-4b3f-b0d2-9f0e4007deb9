import { useCallback, useRef } from "react";

import { But<PERSON> } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Icon } from "components/atoms/icon";
import { PhonefieldHookForm } from "components/atoms/phonefield";
import { PulldownHookForm } from "components/atoms/pulldown";
import { Text } from "components/atoms/text";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { Formfield } from "components/molecules/formfield";
import { Historynote } from "components/molecules/historynote";
import { OrderList, OrderItem } from "components/molecules/orderlist";
import { Section } from "components/organisms/section";
import { Tab, TabList, Tabs, TabPanel } from "components/organisms/tabs";
import { InfiniteScrollable } from "components/utils/infinitescrollable";
import debounce from "helpers/debounce";
import { FormContainer } from "helpers/form";
import useDidMount from "helpers/react-hooks/useDidMount";
import { usePulldownAssignEmployee } from "modules/employee";
import { onTriggerCollapedFormConversation } from "modules/lead";

import { useSelectedConversation } from "../hook";
import { CreateLeadFormPayload, inputValidationSchema } from "./constant";
import { CreateLeadChatboxPage } from "./vm";

const ConversationDetail = () => {
  const { loadMoreEmployee, assignEmployeeOptions, loadMoreEmployeeState } =
    usePulldownAssignEmployee();

  useDidMount(() => {
    loadMoreEmployee();
  });

  const {
    createLead,
    createLeadState,
    registerFormContainer,
    resetFieldsForm,
    handleOpenModalByType,
    handleOnCloseModal,
    modelTypeIsOpen,
  } = CreateLeadChatboxPage();

  const formContenRef = useRef<HTMLDivElement | null>(null);

  const selectedConversation = useSelectedConversation();

  const { client, conversationId } = selectedConversation || {};

  const onToggleFormCreate = useCallback(() => {
    onTriggerCollapedFormConversation(formContenRef.current, (isCollapsed) => {
      if (isCollapsed) handleOpenModalByType("collapsed");
      handleOnCloseModal();
    });
  }, [handleOnCloseModal, handleOpenModalByType]);

  const handleOnSubmitCreateLead = useCallback(
    (formData: CreateLeadFormPayload) => {
      const { assignedEmployee, ...rest } = formData;
      createLead({
        ...rest,
        assignedEmployeeId: assignedEmployee && Number(assignedEmployee.value),
        conversationId,
      });
    },
    [conversationId, createLead]
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onAssignEmployeePulldownInputChange = useCallback(
    debounce(
      (textSearch: string) => loadMoreEmployee({ name: textSearch }),
      200
    ),
    [loadMoreEmployee]
  );

  if (!selectedConversation) return null;

  return (
    <div className="page-chatbox_tabconversationdetail-content">
      <Tabs>
        <TabList>
          {["Khách hàng", "Ghi chú"].map((title) => (
            <Tab key={title} style={{ padding: "10px 0px" }}>
              {title}
            </Tab>
          ))}
        </TabList>
        <TabPanel>
          <div className="u-mb-10">
            <div className="d-flex align-items-center">
              <div className="u-mr-10 flex-shink-0">
                <Icon iconName="human" modifiers="tiny" />
              </div>
              <Text>{client?.name}</Text>
            </div>
          </div>
          <div className="u-mb-10">
            <div className="d-flex align-items-center">
              <div className="u-mr-10 flex-shink-0">
                <Icon iconName="phone" modifiers="tiny" />
              </div>
              <Text>0363847334</Text>
            </div>
          </div>
          <div className="u-mb-10">
            <div className="d-flex justify-content-between">
              <div
                className="u-pt-2 u-pb-2 u-pl-15 u-pr-15"
                style={{
                  backgroundColor: "#1684B9",
                  color: "#FFF",
                  borderRadius: 2,
                  cursor: "pointer",
                }}
                aria-hidden
                onClick={onToggleFormCreate}
              >
                {modelTypeIsOpen("collapsed") ? "Đóng" : "Mở"} lead
              </div>
              <div
                className="u-pt-2 u-pb-2 u-pl-15 u-pr-15"
                style={{
                  backgroundColor: "#FF6568",
                  color: "#FFF",
                  borderRadius: 2,
                  cursor: "pointer",
                }}
              >
                Tạo đơn hàng
              </div>
            </div>
          </div>
          <div
            className={`form-create-conversation ${
              modelTypeIsOpen("collapsed") && "opened"
            }`}
            ref={formContenRef}
          >
            <Heading centered>Tạo lead mới</Heading>
            <FormContainer
              validationSchema={inputValidationSchema}
              onSubmit={handleOnSubmitCreateLead}
              register={registerFormContainer}
            >
              <div className="u-mb-20">
                <Formfield label="Số điện thoại" name="phone">
                  <PhonefieldHookForm
                    placeholder="Số điện thoại"
                    name="phone"
                  />
                </Formfield>
                <Formfield label="Ghi chú" name="note">
                  <TextareafieldHookForm placeholder="Ghi chú" name="note" />
                </Formfield>
                <Formfield label="Nhân viên phân công" name="assignedEmployee">
                  <PulldownHookForm
                    name="assignedEmployee"
                    isClearable
                    isSearchable
                    placeholder="Mã nhân viên"
                    isLoading={loadMoreEmployeeState.loading}
                    triggerLoadMore={() => loadMoreEmployee()}
                    onInputChange={onAssignEmployeePulldownInputChange}
                    options={assignEmployeeOptions}
                  />
                </Formfield>
              </div>
              <div className="d-flex justify-content-center">
                <Button buttonType="textbutton" onClick={resetFieldsForm}>
                  Hủy
                </Button>
                <div className="u-ml-16">
                  <Button
                    type="submit"
                    buttonType="textbutton"
                    disabled={createLeadState.loading}
                    isLoading={createLeadState.loading}
                  >
                    Lưu
                  </Button>
                </div>
              </div>
            </FormContainer>
          </div>
          <Section>
            <Heading>Đơn hàng</Heading>
            <Section>
              <InfiniteScrollable
                height={500}
                width="100%"
                scrollType="bottom"
                onTrigger={() =>
                  new Promise<void>((resolve) => {
                    resolve();
                  })
                }
              >
                <OrderList>
                  {Array(50)
                    .fill(0)
                    .map((_) => (
                      <OrderItem
                        key={Math.random()}
                        code="ADB91234567675"
                        phoneNumber="0363847334"
                        status="Hoàn thành"
                        statusColor="#0BBA99"
                        price={125000}
                        date="23-09-20 21:30"
                      />
                    ))}
                </OrderList>
              </InfiniteScrollable>
            </Section>
          </Section>
        </TabPanel>
        <TabPanel>
          <div className="u-mr-10">
            <FormContainer validationSchema={{}}>
              <Formfield label="Ghi chú" name="note">
                <TextareafieldHookForm name="note" />
              </Formfield>
              <div className="d-flex u-mb-15 justify-content-center">
                <Button modifiers="secondary" buttonType="textbutton">
                  Hủy
                </Button>
                <div className="u-ml-20">
                  <Button buttonType="textbutton">Lưu</Button>
                </div>
              </div>
            </FormContainer>
          </div>
          <Section>
            <Heading type="h2">LỊCH SỬ</Heading>
            <Section>
              <InfiniteScrollable
                className="u-overflow-x-hidden"
                height={500}
                width="100%"
                scrollType="bottom"
                onTrigger={() =>
                  new Promise<void>((resolve) => {
                    resolve();
                  })
                }
              >
                {Array(10)
                  .fill(0)
                  .map((index) => (
                    <div className="u-mb-10 u-mr-10" key={index}>
                      <Historynote
                        author="Nguyễn Thị Thu Vân"
                        time="02/06/2021"
                        note="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. "
                      />
                    </div>
                  ))}
              </InfiniteScrollable>
            </Section>
          </Section>
        </TabPanel>
        <TabPanel>Ghi chú</TabPanel>
      </Tabs>
    </div>
  );
};

export default ConversationDetail;
