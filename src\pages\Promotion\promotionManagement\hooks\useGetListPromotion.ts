import { useCallback, useEffect, useState } from "react";
import { debounce } from "lodash";
import { useAsync } from "hooks/useAsync";
import { GetListPromotionDto, PromotionDto } from "../dtos/promotion.dto";
import promotionServices from "../services/promotion.service";

// GET LIST WIH PAGINATION AND FILTER
export const useGetListPromotion = (dto: GetListPromotionDto) => {
  const [promotionRefetch, promotionData] = useAsync(
    useCallback(() => promotionServices.getListPromotions(dto), [dto])
  );

  return {
    promotionRefetch,
    promotionData: promotionData?.data?.data,
    loading: promotionData?.loading,
  };
};

export const useGetListPromotionWithoutPagination = () => {
  const [promotionRefetch, promotionData] = useAsync(
    useCallback(
      () => promotionServices.getListPromotionsWithoutPagination(),
      []
    )
  );

  return {
    promotionRefetch,
    promotionData: promotionData?.data?.data,
    loading: promotionData?.loading,
  };
};

export const useGetListPromotionActiveWithoutPagination = () => {
  const [promotionActiveRefetch, promotionActiveData] = useAsync(
    useCallback(
      () => promotionServices.getListActivePrmomotionWithoutPagination(),
      []
    )
  );

  return {
    promotionActiveRefetch,
    promotionActiveData: promotionActiveData?.data?.data,
    loading: promotionActiveData?.loading,
  };
};

// INFINITY SCROLL PROMOTION
export const useInfinityPromotion = ({
  pageSize = 10,
  searchText = "",
  enabled = true,
}: {
  pageSize?: number;
  searchText?: string;
  enabled?: boolean;
}) => {
  const [page, setPage] = useState(1);
  const [data, setData] = useState<PromotionDto[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [debouncedSearchText, setDebouncedSearchText] = useState<string>("");

  const fetchData = useCallback(async () => {
    if (!enabled || loading || !hasMore) return;

    setLoading(true);
    try {
      const response = await promotionServices.getActivePromotion({
        pageNum: page,
        pageSize,
        inputSearch: debouncedSearchText,
      });
      const newItems = response.data?.data || [];
      const total = response?.data?.meta?.totalRecords || 0;
      // const convertedItems = newItems.map((item) => {
      //   return {
      //     ...item,
      // label: `${item.promotionCode} - ${item.promotionName}`,
      // value: item.promotionId,
      //   };
      // });
      setData((prev) => [...prev, ...newItems]);
      setTotalRecords(total);
      setHasMore((prev) => page * pageSize < total);
    } catch (err) {
      console.error("Failed to fetch status tickets:", err);
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, debouncedSearchText, enabled, loading, hasMore]);

  useEffect(() => {
    const handler = debounce(() => {
      const trimmed = searchText.trim();
      setDebouncedSearchText(trimmed);
    }, 400);

    handler();
    return () => {
      handler.cancel();
    };
  }, [searchText]);

  useEffect(() => {
    setData([]);
    setPage(1);
    setHasMore(true);
  }, [searchText, pageSize]);

  useEffect(() => {
    if (enabled) fetchData();
  }, [page, debouncedSearchText]);

  const loadMore = () => {
    if (!loading && hasMore) {
      setPage((prev) => prev + 1);
    }
  };

  return {
    data,
    loading,
    hasMore,
    totalRecords,
    loadMore,
    reset: () => {
      if (page !== 1) {
        setData([]);
      }
      setPage(1);
      setHasMore(true);
    },
  };
};
