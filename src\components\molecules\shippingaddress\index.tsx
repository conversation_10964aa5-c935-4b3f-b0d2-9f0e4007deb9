import React from "react";

import { Button } from "components/atoms/button";
import { Link } from "components/utils/link";

/* eslint-disable */
export interface Props {
	children?: React.ReactNode;
	name?: string;
	address?: string;
	phone?: string;
	editable?: boolean;
	onEdit?: () => void;
}

export const Shippingaddress: React.FC<Props> = ({
	name,
	address,
	phone,
	editable,
	onEdit,
}) => (
	<div className="m-shippingaddress">
		<div className="m-shippingaddress_title">
			<span>{name}</span>
			{editable && (
				<Button onClick={onEdit} buttonType="textbutton">
					Sửa
				</Button>
			)}
		</div>
		<div className="m-shippingaddress_info">
			<div>
				<span>Địa chỉ:</span>
				<span>{address}</span>
			</div>
			<div>
				<span>SĐT:</span>
				<span>
					<Link useNative to={`tel:+${phone}`}>
						{phone}
					</Link>
				</span>
			</div>
		</div>
	</div>
);
