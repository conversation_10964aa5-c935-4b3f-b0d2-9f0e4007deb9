import { useCallback, useEffect, useState } from "react";

import produce from "immer";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import useDerivedStateFromProps from "helpers/react-hooks/useDerivedStateFromProps";
import { useAsync } from "hooks/useAsync";
import {
  usePulldownOrderReturnStatus,
  orderReturnStatusTaglineItemListDto,
  OrderReturnStatusTaglineItemListDtoType,
  updateReturnOrderStatusTaglineDto,
  UpdateReturnOrderStatusTaglineDtoType,
} from "modules/order";
import {
  updateOrderExchangeTaglineDetail,
  getOrderExchangeTaglineDetail,
} from "services/crm/order";

interface OrderTaglineDetailPageVmProps {
  orderStatusId: number;
  orderStatusTaglineId: number;
}

interface State {
  selectedOrderStatusId?: number;
  colorSelected?: string;
}

export type OrderTaglineItem = OrderReturnStatusTaglineItemListDtoType;

export const OrderTaglineDetailPageVm = ({
  orderStatusId,
  orderStatusTaglineId,
}: OrderTaglineDetailPageVmProps) => {
  const [state, setState] = useState<State>({
    selectedOrderStatusId: undefined,
    colorSelected: "#000",
  });
  const [getOrderTaglineDetailExec, getOrderTaglineDetailState] = useAsync(
    useCallback(
      (params: { orderStatusId: number; orderStatusTaglineId: number }) =>
        getOrderExchangeTaglineDetail({ ...params }).then((res) =>
          orderReturnStatusTaglineItemListDto(res.data.data)
        ),
      []
    )
  );

  const {
    fetchOrderReturnStatus,
    orderReturnStatusOptions,
    getOptionByValue: getReturnStatusOptionById,
  } = usePulldownOrderReturnStatus({
    excludePending: true,
  });

  const selectedOrderStatus =
    getReturnStatusOptionById(state.selectedOrderStatusId) || null;

  const [updateReturnStatusTaglineExec, updateReturnStatusTaglineState] =
    useAsync(updateOrderExchangeTaglineDetail, {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Cập nhật thành công" });
      }, []),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(error?.errors?.[0]?.code);

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    });

  const orderTaglineDetail = getOrderTaglineDetailState.data;

  const handleUpdateReturnStatusTagline = useCallback(
    (rawPayload: Partial<UpdateReturnOrderStatusTaglineDtoType>) => {
      if (!orderStatusId && !orderStatusTaglineId) return;
      const updateReturnStatusTaglinePayload =
        updateReturnOrderStatusTaglineDto({
          ...orderTaglineDetail,
          ...rawPayload,
        });

      updateReturnStatusTaglineExec(
        orderStatusId,
        orderStatusTaglineId,
        updateReturnStatusTaglinePayload
      );
    },
    [
      orderStatusId,
      orderStatusTaglineId,
      updateReturnStatusTaglineExec,
      orderTaglineDetail,
    ]
  );

  const onChangeColor = useCallback(
    (code: string) => {
      setState(
        produce((draft) => {
          draft.colorSelected = code;
        })
      );
    },
    [setState]
  );

  useDerivedStateFromProps((_, nextOrderStatus) => {
    setState(
      produce((draft) => {
        draft.selectedOrderStatusId = nextOrderStatus || undefined;
      })
    );
  }, orderTaglineDetail?.returnOrderStatusId);

  useDerivedStateFromProps((_, nextColor) => {
    setState(
      produce((draft) => {
        draft.colorSelected = nextColor || undefined;
      })
    );
  }, orderTaglineDetail?.color);

  useEffect(() => {
    if (orderStatusId && orderStatusTaglineId) {
      getOrderTaglineDetailExec({
        orderStatusId,
        orderStatusTaglineId,
      });
    }
    fetchOrderReturnStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderStatusId, orderStatusTaglineId]);

  return {
    loading: getOrderTaglineDetailState.loading,
    orderTaglineDetail,
    orderReturnStatusOptions,
    selectedOrderStatus,
    handleUpdateReturnStatusTagline,
    updateReturnStatusTaglineState,
    onChangeColor,
    colorSelected: state.colorSelected,
  };
};
