import React from "react";

import ReactTooltip, { Place } from "react-tooltip";

export type { Place } from "react-tooltip";

export interface Props {
  id: string;
  description: React.ReactNode;
  place: Place;
  children: React.ReactNode;
}

export const Tooltip: React.FC<Props> = ({
  id,
  description,
  place,
  children,
}) => (
  <div className="u-tooltip">
    <div data-tip data-for={id}>
      {children}
    </div>
    <ReactTooltip place={place} id={id} effect="solid">
      <div className="u-tooltip_description">{description}</div>
    </ReactTooltip>
  </div>
);
