import React, { forwardRef } from "react";

import { Icon } from "components/atoms/icon";
import { mapModifiers } from "helpers/component";

export type Props = React.InputHTMLAttributes<HTMLInputElement>;

export const Checkbox = forwardRef<HTMLInputElement, Props>(
  ({ children, disabled, id, ...innerProps }, ref) => (
    <div className={mapModifiers("a-checkbox", disabled && "disabled")}>
      <label className="a-checkbox_label" htmlFor={id}>
        <input
          ref={ref}
          {...innerProps}
          className="a-checkbox_input"
          disabled={disabled}
          id={id}
          type="checkbox"
        />
        <div className="a-checkbox_checkmark">
          <Icon iconName="check-mark" />
        </div>
        {!!children && <div className="a-checkbox_content">{children}</div>}
      </label>
    </div>
  )
);
