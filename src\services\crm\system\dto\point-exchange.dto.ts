export interface PointExchangeResponseDto {
  data: SystemConfigPointExchangeResponseDto;
}

export interface SystemConfigPointExchangeResponseDto {
  data: PointExchangeDto;
  deletedAt: string | null;
  _id: string;
  moduleCode: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface PointExchangeDto {
  conversionRateFromPointToMoney: number;
  conversionRateFromMoneyToPoint: number;
}
