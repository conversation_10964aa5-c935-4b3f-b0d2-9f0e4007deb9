/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useMemo, useRef, useState } from "react";
import { Button, Select, SelectProps, Tooltip, Typography, Spin } from "antd";

import { removeAccents } from "helpers/remove-accents";

export type BaseSelectWithFeaturesProps = {
  showSelectAll?: boolean;
  triggerLoadMore?: (payload: { searchInputValue: string }) => Promise<void>;
  hasMore?: boolean;
  isLoading?: boolean;
} & SelectProps;

export const BaseSelectWithFeatures: React.FC<BaseSelectWithFeaturesProps> = ({
  showSelectAll = false,
  triggerLoadMore,
  hasMore = true,
  isLoading = false,
  mode,
  onChange,
  options = [],
  value,
  fieldNames,
  ...rest
}) => {
  const [loading, setLoading] = useState(isLoading);
  const inputRef = useRef("");

  // === Load More Logic ===
  const handlePopupScroll = useCallback(
    async (e: React.UIEvent<HTMLDivElement>) => {
      const target = e.target as HTMLDivElement;
      const distance =
        Math.round(target.scrollTop + target.clientHeight) >=
        target.scrollHeight;

      if (distance && hasMore && triggerLoadMore) {
        setLoading(true);
        try {
          await triggerLoadMore({ searchInputValue: inputRef.current });
        } finally {
          setLoading(false);
        }
      }
    },
    [hasMore, triggerLoadMore]
  );

  // === Filter Logic ===
  const handleFilterOption = useCallback(
    (inputValue: string, opt: any) => {
      let v = opt?.[fieldNames?.label || "label"];
      v = v ? String(v) : "";
      return removeAccents(v)
        .toLowerCase()
        .includes(removeAccents(inputValue).toLowerCase());
    },
    [fieldNames]
  );

  // === Max Tag Count with Tooltip ===
  const multipleOptions = useMemo(() => {
    let opt: SelectProps = {};
    if (mode) {
      opt = {
        maxTagCount: "responsive",
        maxTagPlaceholder: (v) => (
          <Tooltip
            // overlayClassName="fixed"
            title={v.map(({ label }, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <span key={`organization-${index}`}>
                {label}
                <br />
              </span>
            ))}
          >
            <Typography>+ {v?.length}</Typography>
          </Tooltip>
        ),
      };
    }
    return opt;
  }, [mode]);

  // === Chọn tất cả / Bỏ chọn tất cả ===
  const handleSelectAll = useCallback(() => {
    onChange?.(
      options.map((option) => option[fieldNames?.value || "value"]),
      options
    );
  }, [onChange, options, fieldNames]);

  const handleUnselectAll = useCallback(() => {
    onChange?.([], options);
  }, [onChange, options]);

  const enchancedOptions = useMemo(() => {
    if (!showSelectAll || !mode) return options;
    return [
      {
        label: !value?.length ? (
          <Button type="link" onClick={() => handleSelectAll()}>
            Chọn tất cả
          </Button>
        ) : (
          <Button type="link" onClick={() => handleUnselectAll()}>
            Bỏ chọn tất cả
          </Button>
        ),
        value: "__select_all__",
        disabled: true,
      },
      ...options,
    ];
  }, [
    mode,
    fieldNames,
    handleSelectAll,
    handleUnselectAll,
    options,
    showSelectAll,
    value?.length,
  ]);

  return (
    <Select
      allowClear
      showSearch
      mode={mode}
      onChange={onChange}
      value={value}
      options={enchancedOptions}
      fieldNames={fieldNames}
      filterOption={handleFilterOption}
      onPopupScroll={handlePopupScroll}
      popupRender={(menu) => (
        <>
          {menu}
          {loading && (
            <div style={{ textAlign: "center", padding: 8 }}>
              <Spin size="small" />
            </div>
          )}
        </>
      )}
      {...multipleOptions}
      {...rest}
    />
  );
};
