import { Story, Meta } from "@storybook/react/types-6-0";

import { Mo<PERSON>, <PERSON><PERSON> } from ".";

export default {
  title: "Components|organisms/Modal",
  component: Modal,
} as Meta;

const Template: Story<Props> = ({
  isOpen,
  isClosable,
  style,
  shouldCloseOnEsc,
  shouldCloseOnOverlayClick,
  onCloseModal,
}) => (
  <Modal
    isOpen={isOpen}
    isClosable={isClosable}
    style={style}
    shouldCloseOnEsc={shouldCloseOnEsc}
    shouldCloseOnOverlayClick={shouldCloseOnOverlayClick}
    onCloseModal={onCloseModal}
  >
    <div>{"Modalcontent".repeat(20)}</div>
  </Modal>
);

export const Normal = Template.bind({});

Normal.args = {};
