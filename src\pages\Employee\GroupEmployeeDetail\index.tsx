import { use<PERSON>allback, useMemo } from "react";

import { useSearchParams, useParams } from "react-router";

import { But<PERSON> } from "components/atoms/button";
import { Checkbox } from "components/atoms/checkbox";
import { Heading } from "components/atoms/heading";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Row, Col } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Tab, TabList, TabPanel, Tabs } from "components/organisms/tabs";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import { FormContainer } from "helpers/form";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";

import { ChildrenPage } from "../types";
import { EmployeeGroupFormType, inputValidationSchema } from "./constant";
import { EmployeeGroupPageVm } from "./vm";

const IndexPage = () => {
  const [searchUrlPath] = useSearchParams();

  const { groupId } =
    useParams<PageParamsType<ChildrenPage["groupEmployeeDetail"]>>();

  const pageActionType = searchUrlPath.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  const {
    loading,
    employeeGroupData,
    handleUpdateEmployeeGroup,
    updateEmployeeGroupState,
  } = EmployeeGroupPageVm({
    groupId: Number(groupId),
  });

  const updateEmployeeGroup = useCallback(
    (formData: EmployeeGroupFormType) =>
      handleUpdateEmployeeGroup({
        ...formData,
      }),
    [handleUpdateEmployeeGroup]
  );

  return (
    <SpinnerContainer animating={loading}>
      <General>
        <title>Sửa thông tin vai trò truy cập</title>
        <Section>
          <Heading type="h1" modifiers="primary">
            SỬA THÔNG TIN VAI TRÒ TRUY CẬP
          </Heading>

          <Section>
            <Tabs>
              <TabList>
                <Tab
                  title="Thông tin khách hàng"
                  style={{ textAlign: "center" }}
                >
                  THÔNG TIN CHUNG
                </Tab>
                <Tab
                  title="Thông tin khách hàng"
                  style={{ textAlign: "center" }}
                >
                  QUYỀN TRUY CẬP
                </Tab>
              </TabList>

              <TabPanel>
                {employeeGroupData && (
                  <FormContainer
                    validationSchema={inputValidationSchema}
                    onSubmit={updateEmployeeGroup}
                  >
                    <Formfield label="Vai trò" name="name">
                      <TextfieldHookForm
                        placeholder="Vai trò"
                        name="name"
                        defaultValue={employeeGroupData.name}
                        disabled={viewMode}
                      />
                    </Formfield>

                    <div className="d-flex justify-content-end u-mt-20">
                      <Button
                        modifiers="secondary"
                        buttonType="outline"
                        onClick={navigationHelper.goBack}
                      >
                        QUAY LẠI
                      </Button>
                      {editMode && (
                        <div className="u-ml-15">
                          <Button
                            type="submit"
                            disabled={updateEmployeeGroupState.loading}
                            isLoading={updateEmployeeGroupState.loading}
                          >
                            LƯU
                          </Button>
                        </div>
                      )}
                    </div>
                  </FormContainer>
                )}
              </TabPanel>

              <TabPanel>
                <FormContainer validationSchema={inputValidationSchema}>
                  {Array(3)
                    .fill(0)
                    .map((_i, index) => (
                      // eslint-disable-next-line react/no-array-index-key
                      <Section key={index}>
                        <Heading type="h2">QUẢN LÝ ĐƠN HÀNG</Heading>
                        <Row className="u-mt-15">
                          <Col sm="6" lg="4" xl="2">
                            <Checkbox checked>Xem danh sách</Checkbox>
                          </Col>

                          <Col
                            sm="6"
                            lg="4"
                            xl="2"
                            className="u-mt-10 u-mt-sm-0"
                          >
                            <Checkbox checked>Tạo</Checkbox>
                          </Col>

                          <Col
                            sm="6"
                            lg="4"
                            xl="2"
                            className="u-mt-10 u-mt-lg-0"
                          >
                            <Checkbox checked>Chỉnh sửa</Checkbox>
                          </Col>

                          <Col
                            sm="6"
                            lg="4"
                            xl="2"
                            className="u-mt-10 u-mt-xl-0"
                          >
                            <Checkbox checked>Xoá</Checkbox>
                          </Col>

                          <Col
                            sm="6"
                            lg="4"
                            xl="2"
                            className="u-mt-10 u-mt-xl-0"
                          >
                            <Checkbox checked>Chọn tất cả</Checkbox>
                          </Col>
                        </Row>
                      </Section>
                    ))}

                  <div className="d-flex justify-content-end u-mt-20">
                    <div className="u-mr-15">
                      <Button
                        modifiers="secondary"
                        buttonType="outline"
                        onClick={navigationHelper.goBack}
                      >
                        QUAY LẠI
                      </Button>
                    </div>
                    <Button>LƯU</Button>
                  </div>
                </FormContainer>
              </TabPanel>
            </Tabs>
          </Section>
        </Section>
      </General>
    </SpinnerContainer>
  );
};

export default IndexPage;
