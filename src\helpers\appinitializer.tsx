/* eslint-disable @typescript-eslint/no-explicit-any */
// import { useEffect } from "react";
// import { useLogto } from "@logto/react";
// import { AxiosRequestConfig } from "axios";
// import crmDriverV1 from "services/crm/crm-driver-v1";
// import { useAppDispatch } from "store";
// import authSlice from "store/auth/reducer";

export const AppInitializer = () => {
  // const { isLoading, isAuthenticated, getIdTokenClaims, getAccessToken } =
  //   useLogto();
  // const dispatch = useAppDispatch();

  // useEffect(() => {
  //   if (isAuthenticated) {
  //     (async () => {
  //       // You can perform actions after authentication here, like fetching user data
  //       const claims = await getIdTokenClaims();
  //       const token = await getAccessToken(process.env.REACT_APP_SSO_BASE_URL);
  //       const [username, site] = claims.username?.split("_") || [];
  //       dispatch(
  //         authSlice.actions.updateEmployeeAuthState({
  //           employee: {
  //             userName: username || "",
  //             name: claims.name || "",
  //             picture: claims.picture || "",
  //             site,
  //           } as any,
  //           token,
  //           refreshToken: "", // Logto does not provide refresh token by default
  //         })
  //       );
  //     })();

  //     // let isTokenRefreshing = false;
  //     // const failedQueueByInvaldiatedToken: Array<{
  //     //   resolve: (data?: any) => void;
  //     //   reject: (reason?: any) => void;
  //     // }> = [];
  //     // const responseInterceptor = crmDriverV1.interceptors.response.use(
  //     //   async (response) => {
  //     //     return response;
  //     //   },
  //     //   async (error) => {
  //     //     const originalRequest = error.config as AxiosRequestConfig;
  //     //     originalRequest.headers!.authorization = "";

  //     //     if (error.response?.status === 401) {
  //     //       // NOTE: when token is updating, return API connection queue
  //     //       if (isTokenRefreshing) {
  //     //         return new Promise((resolve, reject) => {
  //     //           failedQueueByInvaldiatedToken.push({
  //     //             resolve,
  //     //             reject,
  //     //           });
  //     //         })
  //     //           .then(() => {
  //     //             return crmDriverV1.request(originalRequest);
  //     //           })
  //     //           .catch((err) => {
  //     //             return Promise.reject(err);
  //     //           });
  //     //       }

  //     //       isTokenRefreshing = true;

  //     //       try {
  //     //       } catch (err) {}

  //     //       return new Promise((resolve, reject) => {
  //     //         const employeeSession = getEmployeeSessionInfo();

  //     //         if (employeeSession && employeeSession.refreshToken) {
  //     //           refreshToken({ refreshToken: employeeSession.refreshToken })
  //     //             .then((res) => {
  //     //               initialEmployeeSession({
  //     //                 accessToken: res.data.data?.accessToken,
  //     //                 refreshToken: res.data.data?.refreshToken,
  //     //               });
  //     //               processQueue(null, res.data.accessToken);
  //     //               resolve(crmDriverV1.request(originalRequest));
  //     //             })
  //     //             .catch((err) => {
  //     //               processQueue(err, null);
  //     //               store.dispatch(authSlice.actions.employeeLogout());
  //     //               toastSingleMode({
  //     //                 type: "warning",
  //     //                 message: "Phiên đăng nhập",
  //     //                 descripition:
  //     //                   "Phiên đăng nhập của bạn đã hết. Vui lòng đăng nhập lại để tiếp tục.",
  //     //               });
  //     //               reject(err);
  //     //             })
  //     //             .finally(() => {
  //     //               isTokenRefreshing = false;
  //     //             });
  //     //         }
  //     //       });
  //     //     }
  //     //     return Promise.reject(error);
  //     //   }
  //     // );
  //     // return () => {
  //     //   crmDriverV1.interceptors.response.eject(responseInterceptor);
  //     // };
  //   }
  //   return () => {};
  // }, [dispatch, getAccessToken, getIdTokenClaims, isAuthenticated]);

  // // useEffect(() => {
  // //   crmDriverV1.interceptors.request.use((config) => {
  // //     if (!config.headers?.authorization) {
  // //       const employeeSession = getEmployeeSessionInfo();
  // //       if (employeeSession) {
  // //         config.headers!.authorization = `Bearer ${employeeSession.accessToken}`;
  // //       }
  // //     }
  // //     return config;
  // //   });

  // //   crmDriverV1.interceptors.response.use(
  // //     async (response) => {
  // //       return response;
  // //     },
  // //     async (error) => {
  // //       const originalRequest = error.config as AxiosRequestConfig;
  // //       originalRequest.headers!.authorization = "";

  // //       if (error.response?.status === 401) {
  // //         // NOTE: when token is updating, return API connection queue
  // //         if (isTokenRefreshing) {
  // //           return new Promise((resolve, reject) => {
  // //             failedQueueByInvaldiatedToken.push({
  // //               resolve,
  // //               reject,
  // //             });
  // //           })
  // //             .then(() => {
  // //               return crmDriverV1.request(originalRequest);
  // //             })
  // //             .catch((err) => {
  // //               return Promise.reject(err);
  // //             });
  // //         }

  // //         isTokenRefreshing = true;

  // //         return new Promise((resolve, reject) => {
  // //           const employeeSession = getEmployeeSessionInfo();

  // //           if (employeeSession && employeeSession.refreshToken) {
  // //             refreshToken({ refreshToken: employeeSession.refreshToken })
  // //               .then((res) => {
  // //                 initialEmployeeSession({
  // //                   accessToken: res.data.data?.accessToken,
  // //                   refreshToken: res.data.data?.refreshToken,
  // //                 });
  // //                 processQueue(null, res.data.accessToken);
  // //                 resolve(crmDriverV1.request(originalRequest));
  // //               })
  // //               .catch((err) => {
  // //                 processQueue(err, null);
  // //                 store.dispatch(authSlice.actions.employeeLogout());
  // //                 toastSingleMode({
  // //                   type: "warning",
  // //                   message: "Phiên đăng nhập",
  // //                   descripition:
  // //                     "Phiên đăng nhập của bạn đã hết. Vui lòng đăng nhập lại để tiếp tục.",
  // //                 });
  // //                 reject(err);
  // //               })
  // //               .finally(() => {
  // //                 isTokenRefreshing = false;
  // //               });
  // //           }
  // //         });
  // //       }
  // //       return Promise.reject(error);
  // //     }
  // //   );
  // // }, []);

  // useEffect(() => {
  //   if (!isLoading) {
  //     dispatch(authSlice.actions.setLoading(isLoading));
  //   }
  // }, [dispatch, isLoading]);
  return null;
};
