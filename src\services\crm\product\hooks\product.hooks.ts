import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import { GetProductByStoreCodeDto } from "../dto/product.dto";
import productServices from "../product.service";

export const useGetProductByStoreCode = () => {
  const [productRefetch, productData] = useAsync(
    useCallback(
      (dto: GetProductByStoreCodeDto) =>
        productServices.getProductByStoreCode({
          storeCode: dto.storeCode,
          pageNum: dto.pageNum,
          pageSize: dto.pageSize,
          searchText: dto.searchText,
        }),
      []
    )
  );
  return {
    productRefetch,
    productData: productData?.data?.data,
    loading: productData?.loading,
  };
};
