import { useState, useCallback, useRef, useMemo, useEffect } from "react";

import produce from "immer";

import useDerivedStateFromProps from "helpers/react-hooks/useDerivedStateFromProps";
import useDidMount from "helpers/react-hooks/useDidMount";
import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import {
  cityOption,
  districtOption,
  wardOption,
  CityModel,
  DistrictModel,
  WardModel,
} from "modules/location";
import { getCities, getDistricts, getWards } from "services/crm/system";
import { useAppSelector } from "store";

/* eslint-disable camelcase */
interface FilterState {
  cityId?: number;
  districtId?: number;
  wardId?: number;
}
/* eslint-enable */

export const useLocationSelect = (
  options: {
    initialFilterState?: FilterState;
  } = {}
) => {
  const employeeInfo = useAppSelector((state) => state.auth.employeeInfo);
  const firstRenderRef = useRef<boolean>(true);
  const firstRender = firstRenderRef.current;
  const patchingUpdateRef = useRef<boolean>(false);
  const patchingUpdate = patchingUpdateRef.current;

  const [state, setState] = useState<FilterState>({
    cityId: undefined,
    districtId: undefined,
    wardId: undefined,
    ...options.initialFilterState,
  });

  const [getCitiesExec, getCitiesState] = useAsync(
    useCallback(
      (payload: { site: string; countryId: number }) =>
        getCities({ ...payload }).then((res) => CityModel.createMap(res.data)),
      []
    ),
    {
      excludePending: true,
    }
  );

  const [getDistrictsExec, getDistrictsState] = useAsync(
    useCallback(
      (payload: { site: string; cityId: number }) =>
        getDistricts({ ...payload }).then((res) =>
          DistrictModel.createMap(res.data)
        ),
      []
    ),
    {
      excludePending: true,
    }
  );

  const [getWardsExec, getWardsState] = useAsync(
    useCallback(
      (payload: { site: string; districtId: number }) =>
        getWards({ ...payload }).then((res) => WardModel.createMap(res.data)),
      []
    ),
    {
      excludePending: true,
    }
  );

  useDidMount(() => {
    firstRenderRef.current = false;
    getCitiesExec({ site: employeeInfo!.site, countryId: 1 });
  });

  useDerivedStateFromProps(() => {
    if (firstRender) return;
    if (state.cityId) {
      getDistrictsExec({ site: employeeInfo!.site, cityId: state.cityId });
      if (patchingUpdate) return;
      setState(
        produce((draft) => {
          draft.districtId = undefined;
          draft.wardId = undefined;
        })
      );
    }
  }, state.cityId);

  useDerivedStateFromProps(() => {
    if (firstRender) return;
    if (state.districtId) {
      getWardsExec({ site: employeeInfo!.site, districtId: state.districtId });
      if (patchingUpdate) return;
      setState(
        produce((draft) => {
          draft.wardId = undefined;
        })
      );
    }
  }, state.districtId);

  const { options: cityOptions, formatOption: formatCityOption } =
    usePulldownHelper({
      dataSource: getCitiesState.data || [],
      optionCreator: cityOption,
      valueTrans: Number,
    });

  const { options: districtOptions, formatOption: formatDistrictOption } =
    usePulldownHelper({
      dataSource: getDistrictsState.data || [],
      optionCreator: districtOption,
      valueTrans: Number,
    });

  const { options: wardOptions, formatOption: formatWardOption } =
    usePulldownHelper({
      dataSource: getWardsState.data || [],
      optionCreator: wardOption,
      valueTrans: Number,
    });

  const setFilter = useCallback(
    (keyFilter: keyof FilterState, value?: number) => {
      setState((prevState) => ({
        ...prevState,
        [keyFilter]: value,
      }));
    },
    []
  );

  const setFilters = useCallback((filterState: Partial<FilterState>) => {
    setState((prevState) => ({
      ...prevState,
      ...filterState,
    }));
  }, []);

  const batchUpdate = useCallback((updater: () => void) => {
    patchingUpdateRef.current = true;
    updater();
  }, []);

  useEffect(() => {
    patchingUpdateRef.current = false;
  });

  const selectedCity = useMemo(
    () => cityOptions?.find((city) => Number(city.value) === state.cityId),
    [cityOptions, state.cityId]
  );

  const selectedDistrict = useMemo(
    () =>
      districtOptions?.find(
        (district) => Number(district.value) === state.districtId
      ),
    [districtOptions, state.districtId]
  );

  const selectedWard = useMemo(
    () => wardOptions?.find((ward) => Number(ward.value) === state.wardId),
    [state.wardId, wardOptions]
  );

  return {
    cityOptions,
    districtOptions,
    wardOptions,
    setFilter,
    setFilters,
    formatCityOption,
    formatDistrictOption,
    formatWardOption,
    selectedCity,
    selectedDistrict,
    selectedWard,
    batchUpdate,
  };
};
