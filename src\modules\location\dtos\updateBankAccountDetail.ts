import pick from "object.pick";

import { cleanEmptyString } from "helpers/object";
import { createMapper, force, fromSchema } from "libs/adapters/dto";
import { mergeSchema } from "libs/domain";

import { BankAccountSchema } from "../entities";

export const updateBankAccountDto = createMapper(
  fromSchema(
    mergeSchema(
      pick(BankAccountSchema, [
        "bankName",
        "branchName",
        "owner",
        "accountNumber",
      ])
    )
  ),
  force((data) => ({
    ...cleanEmptyString(data),
  }))
);

export type UpdateBankAccountDtoType = ReturnType<typeof updateBankAccountDto>;
