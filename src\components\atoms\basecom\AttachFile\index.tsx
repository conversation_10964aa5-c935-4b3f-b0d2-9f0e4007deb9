/* eslint-disable @typescript-eslint/no-explicit-any */
import { forwardRef, useImperativeHandle, useRef } from "react";

import { ButtonComponent } from "@syncfusion/ej2-react-buttons";
import {
  SelectedEventArgs as UploadSelectedEventArgs,
  UploaderComponent,
  UploadingEventArgs,
} from "@syncfusion/ej2-react-inputs";
import { DialogComponent } from "@syncfusion/ej2-react-popups";
import saveAs from "file-saver";

import { convertFileSize } from "helpers/file-size";
import erpDriverV1 from "services/erp/erp-driver";

import style from "./index.module.scss";

export type SelectedEventArgs = UploadSelectedEventArgs;

export type AttachFileDialogProp<T = any> = {
  propName?: {
    fileSize?: string;
    filePath?: string;
    fileName?: string;
  };
  /**
   * Specifies the URL of insert file action.
   * The insert action send request POST
   */
  saveUrl: string;
  /**
   * Specifies the URL of remove file action.
   * The remove action send request DELETE
   * After click Delete button on Attach Dialog, then delete file with url appended file id
   */
  removeUrl: string;
  /**
   * After click Download button on Attach Dialog, then download file with url appended file path
   */
  downloadUrl: string;
  dataSource: T[];
  selected?: (e: SelectedEventArgs) => void;
  removed?: (index: number) => void;
};

export type TAttachFileDialog = {
  show(): void;
  hide(): void;
  clearUpload(): void;
};

export const AttachFileDialog = forwardRef<
  TAttachFileDialog,
  AttachFileDialogProp
>(
  (
    {
      propName,
      dataSource,
      saveUrl,
      removeUrl,
      downloadUrl,
      selected,
      removed,
    },
    ref
  ) => {
    const dialogRef = useRef<DialogComponent>(null!);
    const uploadRef = useRef<UploaderComponent>(null!);
    const fileSize = propName?.fileSize || "fileSize";
    const filePath = propName?.filePath || "filePath";
    const fileName = propName?.fileName || "fileName";
    const token = localStorage.getItem("GUMAC_CRM_TOKEN");

    useImperativeHandle(ref, () => ({
      show: handleOpen,
      hide: handleClose,
      clearUpload: () => {
        if (uploadRef.current.filesData[0]) {
          uploadRef.current.clearAll();
        }
      },
    }));

    const handleOpen = () => {
      dialogRef.current.visible = true;
    };
    const handleClose = () => {
      dialogRef.current.visible = false;
    };
    const handleUploading = (e: UploadingEventArgs) => {
      e.currentRequest!.setRequestHeader("Authorization", `Bearer ${token}`);
    };
    const handleClickDownload = (v: any) => {
      erpDriverV1
        .get(v[filePath], {
          baseURL: downloadUrl,
          responseType: "blob",
        })
        .then((res) => {
          saveAs(res.data, v[fileName]);
        });
    };
    const handleClickRemove = async (fileId: number, index: number) => {
      await erpDriverV1.delete(String(fileId), {
        baseURL: removeUrl,
      });
      if (removed) {
        removed(index);
      }
    };
    return (
      <DialogComponent
        header="Manage attachments"
        visible={false}
        height="calc(100% - 150px)"
        width="calc(100% - 50px)"
        close={handleClose}
        ref={dialogRef}
        isModal
        showCloseIcon
        delayUpdate
      >
        <div
          className="d-flex"
          style={{
            minWidth: "650px",
            height: "100%",
            overflow: "hidden",
          }}
        >
          <div className="col-6" style={{ overflowY: "auto" }}>
            <label htmlFor="note" className="mb-0">
              Attach files:
            </label>
            <UploaderComponent
              id="file"
              type="file"
              cssClass="e-upload-custom"
              ref={uploadRef}
              asyncSettings={{ saveUrl }}
              maxFileSize={5242880} // Maximum 5MB
              selected={selected}
              uploading={handleUploading}
            />
          </div>
          <div className="col-6" style={{ overflowY: "auto" }}>
            {dataSource.map((v, i) => (
              // eslint-disable-next-line react/no-array-index-key
              <div key={i} className={style.file_container}>
                <div className={style.file_side}>
                  <div className={style.file_name}>{v[fileName]}</div>
                  <div className={style.file_size}>
                    {convertFileSize(v[fileSize])}
                  </div>
                </div>
                <div className={style.button_side}>
                  <ButtonComponent
                    isPrimary
                    className="e-small mx-1"
                    onClick={() => {
                      handleClickDownload(v);
                    }}
                  >
                    Download
                  </ButtonComponent>
                  <ButtonComponent
                    className="e-small e-danger mx-1"
                    onClick={() => {
                      handleClickRemove(v.id, i);
                    }}
                  >
                    Delete
                  </ButtonComponent>
                </div>
              </div>
            ))}
          </div>
        </div>
      </DialogComponent>
    );
  }
);
