import { useCallback, useEffect, useRef, useState } from "react";
import {
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  FilterOutlined,
  SwapOutlined,
} from "@ant-design/icons";
import {
  Table,
  Button,
  Input,
  Select,
  Row,
  Col,
  Card,
  Space,
  Typography,
  Tooltip,
  Pagination,
  Modal,
  Form,
  Divider,
} from "antd";

import dayjs from "dayjs";

import { deleteLead } from "services/crm/lead";

import RootPageRouter from "../index";
import { NewLeadModal, TNewLeadModal } from "./modal";
import { LeadPageVm } from "./vm";
import type { ColumnsType } from "antd/es/table";

const { Title } = Typography;

const LeadDatatablePage = () => {
  const [isFiltering, setFiltering] = useState<boolean>(false);

  const {
    gotoPage,
    loading,
    leadData,
    leadPaginationState,
    toggleSortLeadBy,
    pageSize,
    handleChangePageSize,
    insertDatatableLead,
  } = LeadPageVm();

  const newRef = useRef<TNewLeadModal>(null);

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageSize]);

  const handleChangePulldownPageSize = useCallback(
    (value: number) => {
      handleChangePageSize(value);
    },
    [handleChangePageSize]
  );

  const showNewModal = () => {
    if (newRef.current) newRef.current.show();
  };

  const onToggleFormFilter = useCallback(() => {
    setFiltering(!isFiltering);
  }, [isFiltering]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleCreatedLead = (v: any) => {
    insertDatatableLead(v);
  };

  const showConfirmDelete = (leadId: string) => {
    Modal.confirm({
      title: "Xác nhận xóa",
      content: "Bạn có chắc chắn muốn xóa lead này?",
      okText: "Xóa",
      cancelText: "Hủy",
      onOk: () => handleConfirmDelete(leadId),
    });
  };

  const handleConfirmDelete = (leadId: string) => {
    deleteLead(leadId).then(() => {
      gotoPage(leadPaginationState.currentPage || 1);
    });
  };

  // Define table columns
  const columns: ColumnsType<Record<string, unknown>> = [
    {
      title: "STT",
      key: "index",
      width: 60,
      align: "center",
      fixed: "left",
      render: (_, __, index) => index + 1,
    },
    {
      title: "Tên khách hàng",
      dataIndex: "customerName",
      key: "customerName",
      width: 150,
    },
    {
      title: "Số điện thoại",
      dataIndex: "phone",
      key: "phone",
      width: 120,
    },
    {
      title: "Nhân viên được giao",
      key: "assignedEmployee",
      width: 150,
      render: (record) => record.assignedEmployee?.name,
    },
    {
      title: "Mã cuộc trò chuyện",
      dataIndex: "conversationId",
      key: "conversationId",
      width: 150,
      render: (conversationId) => (
        <Button type="link" style={{ padding: 0, textDecoration: "underline" }}>
          {conversationId}
        </Button>
      ),
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      width: 100,
    },
    {
      title: "Cập nhật cuối",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 120,
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortLeadBy("updateAt"),
      }),
      render: (date) => dayjs(date).format("DD/MM/YYYY"),
    },
    {
      title: "Thao tác",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="Chuyển thành Prospect">
            <Button type="default" icon={<SwapOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="Xem chi tiết">
            <Button
              type="primary"
              icon={<EyeOutlined />}
              size="small"
              onClick={() =>
                RootPageRouter.gotoChild("leadDetail", {
                  params: { leadId: record.leadId as string },
                })
              }
            />
          </Tooltip>
          <Tooltip title="Chỉnh sửa">
            <Button
              type="default"
              icon={<EditOutlined />}
              size="small"
              onClick={() =>
                RootPageRouter.gotoChild("leadDetail", {
                  params: { leadId: record.leadId as string },
                  queryString: "?action=edit",
                })
              }
            />
          </Tooltip>
          <Tooltip title="Xóa">
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              size="small"
              onClick={() => showConfirmDelete(String(record.leadId))}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: "24px" }}>
      <title>Danh sách LEAD</title>
      <NewLeadModal ref={newRef} onCreated={handleCreatedLead} />

      <Card>
        <Title level={2} style={{ marginBottom: "24px" }}>
          DANH SÁCH LEAD
        </Title>

        {/* Action Bar */}
        <Row justify="end" style={{ marginBottom: "16px" }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showNewModal}
            >
              Tạo mới
            </Button>
            <Button icon={<FilterOutlined />} onClick={onToggleFormFilter}>
              Bộ lọc
            </Button>
          </Space>
        </Row>
        {/* Filter Panel */}
        {isFiltering && (
          <Card style={{ marginBottom: "16px" }}>
            <Form layout="vertical">
              <Row gutter={[16, 16]}>
                <Col span={6}>
                  <Form.Item label="Mã Lead" name="lead">
                    <Input placeholder="Nhập mã Lead" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Số điện thoại" name="phone">
                    <Input placeholder="Nhập số điện thoại KH" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Nhân viên được giao" name="name">
                    <Input placeholder="Nhập tên nhân viên" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Mã cuộc trò chuyện" name="conversationId">
                    <Input placeholder="Nhập cuộc trò chuyện" />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Row justify="center">
                    <Space>
                      <Button onClick={onToggleFormFilter}>Đóng</Button>
                      <Button type="primary" htmlType="submit">
                        Tìm kiếm
                      </Button>
                    </Space>
                  </Row>
                </Col>
              </Row>
            </Form>
            <Divider />
          </Card>
        )}
        {/* Table */}
        <Table
          columns={columns}
          dataSource={leadData}
          rowKey="_id"
          loading={loading}
          scroll={{ x: 1200 }}
          size="small"
          bordered
          pagination={false}
        />
        {/* Pagination */}
        <Row
          justify="space-between"
          align="middle"
          style={{ marginTop: "16px" }}
        >
          <Col>
            <Space>
              <span>Số lượng hiển thị:</span>
              <Select
                value={pageSize}
                onChange={handleChangePulldownPageSize}
                style={{ width: 80 }}
                options={[
                  { label: "5", value: 5 },
                  { label: "10", value: 10 },
                  { label: "15", value: 15 },
                  { label: "20", value: 20 },
                ]}
              />
            </Space>
          </Col>
          <Col>
            {leadPaginationState.totalPage && (
              <Pagination
                current={leadPaginationState.currentPage || 1}
                total={leadPaginationState.totalPage * pageSize}
                pageSize={pageSize}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) =>
                  `${range[0]}-${range[1]} của ${total} mục`
                }
                onChange={gotoPage}
              />
            )}
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default LeadDatatablePage;
