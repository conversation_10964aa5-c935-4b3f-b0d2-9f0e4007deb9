/* eslint-disable @typescript-eslint/no-explicit-any */
// export function mappedDataToDefaultOption<
//   T extends Record<string, any>,
//   KValue extends keyof T,
//   KLabel extends keyof T
// >({
//   data,
//   keyValue,
//   keyLabel,
//   customLabel,
//   restItem = false,
// }: {
//   data: T[];
//   keyValue: KValue;
//   keyLabel: KLabel;
//   customLabel?: (item: T) => string;
//   restItem?: boolean;
// }): { label: string; value: T[KValue] }[] {
//   return data.map((item) => ({
//     ...(restItem ? item : {}),
//     label: customLabel ? customLabel(item) : String(item[keyLabel]),
//     value: item[keyValue],
//   }));
// }

export function mappedDataToDefaultOption<
  T extends Record<string, any>,
  KValue extends keyof T,
  <PERSON><PERSON><PERSON><PERSON> extends keyof T,
  R extends boolean = false
>(params: {
  data: T[];
  keyValue: KValue;
  keyLabel: KLabel;
  customLabel?: (item: T) => string;
  restItem?: R;
}): R extends true
  ? Array<T & { label: string; value: T[KValue] }>
  : Array<{ label: string; value: T[KValue] }> {
  const { data, keyValue, keyLabel, customLabel, restItem = false } = params;

  return data.map((item) => ({
    ...(restItem ? item : {}),
    label: customLabel ? customLabel(item) : String(item[keyLabel]),
    value: item[keyValue],
  })) as R extends true
    ? Array<T & { label: string; value: T[KValue] }>
    : Array<{ label: string; value: T[KValue] }>;
}

export function fromDefaltOptionToData<
  T extends Record<string, any>,
  KValue extends keyof T
>({
  data,
  value,
  valueKey,
}: {
  data: T[];
  value: T[KValue];
  valueKey: KValue;
}): Partial<T> | undefined {
  const foundItem = data.find((item) => item[valueKey] === value);

  if (!foundItem) return undefined;

  return foundItem;
}

export function convertUndefinedToNull<T extends Record<string, any>>(
  obj: T
): T {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      key,
      value === undefined || value === "" ? null : value,
    ])
  ) as T;
}
