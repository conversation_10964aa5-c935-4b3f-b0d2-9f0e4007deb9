import { forwardRef, useRef, useState, useImperativeHandle } from "react";

import { getDiffTime } from "helpers/dayjs";
import { useAppSelector } from "store";

import {
  GetCommentContext,
  GetCommentDto,
  SendCommentContext,
} from "./context";
import style from "./index.module.scss";

export type PostRef = {
  getPostId(): string;
  insertComment(comment: GetCommentDto): void;
};

export type PostProps = {
  id: string;
  title: string;
  img: string;
  time: string;
  content: string;
  onOpenComment?: (id: string) => void;
};

export const Post = forwardRef<PostRef, PostProps>(
  ({ id, title, img, time, content, onOpenComment }, ref) => {
    const employeeInfo = useAppSelector((state) => state.auth.employeeInfo);
    const offsetRef = useRef(0);
    const [open, setOpen] = useState(false);
    const [comment, setComment] = useState<GetCommentDto[]>([]);

    useImperativeHandle(ref, () => ({
      getPostId() {
        return id;
      },
      insertComment(obj) {
        if (comment) {
          setComment((prev) => {
            const arr = [...prev!];
            arr.pop();
            arr.unshift(obj);
            return arr;
          });
        }
      },
    }));

    const { sendCommentExe } = SendCommentContext(
      employeeInfo!.userName,
      employeeInfo!.site
    );
    const { getCommentExe } = GetCommentContext(
      employeeInfo!.userName,
      employeeInfo!.site
    );
    const onClickComment = () => {
      if (!open) {
        getCommentExe(id, offsetRef.current).then((res) => {
          if (res.data.length) {
            setComment(res.data);
            offsetRef.current += 1;
          }
        });
        setOpen(true);
        if (onOpenComment) onOpenComment(id);
      }
    };
    const loadMoreComment = () => {
      if (open) {
        getCommentExe(id, offsetRef.current).then((res) => {
          if (res.data.length) {
            setComment((prev) => prev.concat(res.data));
            offsetRef.current += 1;
          }
        });
      }
    };
    return (
      <div className={style.post}>
        <div className={style.header}>
          <div className={style.header_avatar}>
            <img src={img} loading="lazy" alt="Avatar" />
          </div>
          <div className={style.header_content}>
            <div className={style.header_content_title}>{title}</div>
            <div className={style.header_content_time}>{getDiffTime(time)}</div>
          </div>
        </div>
        <div className={style.content}>{content}</div>
        <div className={style.footer}>
          <div className={style.footer_stat}>12 Thích, 30 Bình luận</div>
          <div className={style.footer_btn}>
            <button type="button" className={style.btn}>
              <div className={style.icon_like} />
              <div className={style.text}>Like</div>
            </button>
            <button
              type="button"
              className={style.btn}
              onClick={onClickComment}
            >
              <div className={style.icon_comment} />
              <div className={style.text}>Comment</div>
            </button>
          </div>
          {open ? (
            <>
              <div className={style.footer_comment}>
                <div className={style.avatar}>
                  <img src={img} loading="lazy" alt="Avatar" />
                </div>
                <div className={style.input}>
                  <input
                    type="text"
                    placeholder="Viết bình luận"
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        const target = e.target as any;
                        sendCommentExe(id, target.value);
                        target.value = "";
                      }
                    }}
                  />
                </div>
              </div>
              {comment.map((v) => (
                <div key={v._id} className={style.comment}>
                  <div className={style.comment_avatar}>
                    <img src={img} loading="lazy" alt="Avatar" />
                  </div>
                  <div className={style.comment_content}>
                    <div className={style.title}>
                      {v.createdBy}
                      <span className={style.time}>
                        {" "}
                        - {getDiffTime(v.createdAt)}
                      </span>
                    </div>
                    <div className={style.text}>{v.content}</div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                className={style.comment_load}
                onClick={loadMoreComment}
              >
                Xem thêm bình luận
              </button>
            </>
          ) : null}
        </div>
      </div>
    );
  }
);
