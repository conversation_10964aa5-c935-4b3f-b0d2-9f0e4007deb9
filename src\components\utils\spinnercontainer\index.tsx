import React from "react";

import { Icon } from "components/atoms/icon";
import { mapModifiers } from "helpers/component";

export interface Props {
  children?: React.ReactNode;
  animating?: boolean;
}

export const SpinnerContainer: React.FC<Props> = ({ children, animating }) => (
  <div className={mapModifiers("u-spinner", animating && "animating")}>
    <div className="u-spinner_icon">
      <Icon iconName="loading-blue" />
    </div>
    <div className="u-spinner_overlay">{children}</div>
  </div>
);
