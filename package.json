{"name": "crm", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@dpt/react-pack": "^0.0.14", "@hookform/resolvers": "^1.3.6", "@material-ui/core": "4.12.3", "@material-ui/icons": "4.11.2", "@reduxjs/toolkit": "^1.5.1", "@syncfusion/ej2-base": "^19.4", "@syncfusion/ej2-data": "^19.4", "@syncfusion/ej2-navigations": "^19.4", "@syncfusion/ej2-notifications": "19.4", "@syncfusion/ej2-react-base": "^19.4", "@syncfusion/ej2-react-buttons": "^19.4", "@syncfusion/ej2-react-calendars": "^19.4", "@syncfusion/ej2-react-charts": "19.4", "@syncfusion/ej2-react-diagrams": "19.4", "@syncfusion/ej2-react-dropdowns": "^19.4", "@syncfusion/ej2-react-gantt": "19.4", "@syncfusion/ej2-react-grids": "^19.4", "@syncfusion/ej2-react-inputs": "^19.4", "@syncfusion/ej2-react-kanban": "19.4", "@syncfusion/ej2-react-layouts": "^19.4", "@syncfusion/ej2-react-lists": "19.4", "@syncfusion/ej2-react-navigations": "^19.4", "@syncfusion/ej2-react-popups": "19.4", "@syncfusion/ej2-react-progressbar": "19.4", "@syncfusion/ej2-react-richtexteditor": "^19.4", "@syncfusion/ej2-react-schedule": "19.4", "@syncfusion/ej2-react-splitbuttons": "^19.4", "@syncfusion/ej2-react-spreadsheet": "^19.4", "@syncfusion/ej2-react-treegrid": "^19.4", "antd": "^5.26.2", "apexcharts": "3.27.3", "axios": "^0.27", "bootstrap": "^4.6.0", "date-fns": "^2.20.2", "dayjs": "^1.11.13", "deepmerge": "^4.2.2", "fast-sort": "^3.0.1", "file-saver": "^2.0.5", "framer-motion": "4.1.17", "history": "^5.3.0", "i18next": "^23.11.5", "i18next-http-backend": "^2.2.0", "immer": "^9.0.1", "lodash": "^4.17.21", "mqtt": "^4.3.7", "object.omit": "^3.0.0", "object.pick": "^1.3.0", "pipe-ts": "^0.0.9", "rc-menu": "^8.10.7", "react": "^18.3.1", "react-apexcharts": "1.3.9", "react-bootstrap": "^2.10.10", "react-color": "^2.19.3", "react-datepicker": "^3.7.0", "react-dom": "^18.3.1", "react-hook-form": "^6.15.5", "react-i18next": "^14.1.2", "react-modal": "^3.16.3", "react-paginating": "^1.4.0", "react-quill": "^2.0.0", "react-redux": "^7.2.3", "react-router": "^7.6.3", "react-select": "^4.3.0", "react-spreadsheet": "^0.7.7", "react-tabs": "^6.1.0", "react-toastify": "^11.0.5", "react-tooltip": "^4.2.17", "react-virtualized": "^9.22.6", "reactflow": "^11.7.2", "redux": "^4.0.5", "redux-persist": "^6.0.0", "socket.io-client": "^4.0.1", "uuid": "^8.3.2", "web-vitals": "^1.1.1", "xlsx": "^0.18.5", "yup": "^1.3.2"}, "resolutions": {"@types/react": "^18.3.1"}, "scripts": {"analyze": "source-map-explorer 'build/static/js/*.js'", "start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "script:update-scss": "node scripts/update-scss.js", "script:update-error-messages": "node scripts/update-error-messages.js", "lint": "run-s lint:script lint:style", "lint:script": "eslint ./src --ext .ts --ext .tsx --color", "lint:style": "stylelint ./src/**/*.scss", "format": "run-s format:script format:style", "format:script": "eslint ./src --ext .ts --ext .tsx --color --fix", "format:style": "stylelint ./src/**/*.scss --fix", "gen:component": "hygen component new --level .", "gen:component:atom": "hygen component new --level atoms", "gen:component:molecule": "hygen component new --level molecules", "gen:component:organism": "hygen component new --level organisms", "gen:component:template": "hygen component new --level templates", "gen:component:page": "hygen component new --level pages", "storybook": "start-storybook -p 6006 -s public", "build-storybook": "build-storybook -s public"}, "lint-staged": {"*.{ts,tsx}": ["yarn lint:style", "yarn lint:script --max-warnings 0"]}, "devDependencies": {"@storybook/addon-actions": "^6.1.9", "@storybook/addon-essentials": "^6.1.9", "@storybook/addon-links": "^6.1.9", "@storybook/addon-storyshots": "^6.1.9", "@storybook/addons": "^6.1.9", "@storybook/node-logger": "^6.1.9", "@storybook/preset-create-react-app": "^3.1.5", "@storybook/react": "^6.1.9", "@testing-library/jest-dom": "^5.11.10", "@testing-library/react": "^11.2.5", "@testing-library/user-event": "^13.0.16", "@types/file-saver": "^2.0.5", "@types/node": "^12.0.0", "@types/object.omit": "^3.0.0", "@types/object.pick": "^1.3.1", "@types/react": "^18.3.1", "@types/react-datepicker": "^3.1.8", "@types/react-dom": "^18.3.1", "@types/react-modal": "^3.16.3", "@types/react-redux": "^7.1.11", "@types/react-select": "^4.0.15", "@types/react-virtualized": "^9.22.2", "@types/uuid": "^8.3.4", "@types/yup": "^0.29.9", "@whitespace/storybook-addon-html": "^2.0.1", "babel-loader": "8.1.0", "change-case": "^4.1.2", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "glob": "^7.1.6", "lint-staged": "^10.5.4", "loglevel": "^1.7.1", "npm-run-all": "^4.1.5", "paths.macro": "^3.0.1", "react-scripts": "5.0.1", "sass": "^1.89.2", "source-map-explorer": "^2.5.1", "stylelint": "^13.8.0", "stylelint-color-format": "^1.1.0", "stylelint-config-prettier": "^8.0.2", "stylelint-config-recess-order": "^2.3.0", "stylelint-prettier": "^1.1.2", "tailwindcss": "^3.0.0", "typescript": "~5.1.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}