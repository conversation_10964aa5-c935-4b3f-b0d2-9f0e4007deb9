import { Model, String, Number, Mixed, ModelValue } from "libs/domain";

export const OrderReturnStatusTaglineSchema = {
  _id: String(),
  name: String(),
  returnOrderStatusTaglineId: Number(),
  returnOrderStatusId: Number(),
  description: String(),
  color: String(),
  displayOrder: Number(),
  updatedAt: String(),
  returnOrderStatus: Mixed({ name: String() }),
};

export const OrderReturnStatusTaglineModel = new Model(
  OrderReturnStatusTaglineSchema
);

export type OrderReturnStatusTaglineEntityType = ModelValue<
  typeof OrderReturnStatusTaglineModel
>;
