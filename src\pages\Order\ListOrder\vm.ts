import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import produce from "immer";

import debounce from "helpers/debounce";
import useDidMount from "helpers/react-hooks/useDidMount";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import { usePulldownAssignEmployee } from "modules/employee";
import {
  orderDetailDto,
  OrderDetailDtoType,
  usePulldownDeliveryPartner,
  usePulldownOrderReturnStatus,
  usePulldownOrderSource,
  usePulldownOrderStatus,
  usePulldownOrderStatusTagline,
  usePulldownReturnStatusTagline,
} from "modules/order";
import { usePulldownShop } from "modules/shop";
import {
  deleteOrder,
  filterOrder,
  findOrders,
  OrderSearchPayload,
  OrderSearchPayloadV2,
} from "services/crm/order";

import RootPageRouter from "..";

export type FilterSearchPayload = Omit<
  OrderSearchPayloadV2,
  "pageNum" | "pageSize" | "customerId"
>;

interface TaglineDependencies {
  orderStatusId?: number;
  orderStatusTaglineId?: number;
  orderReturnStatusId?: number;
  orderReturnStatusTaglineId?: number;
}

interface FilterState {
  openForm: boolean;
  taglineDependencies: TaglineDependencies;
  searchPayload: FilterSearchPayload;
}
interface State {
  pagination: {
    pageSize: number;
  };
  filterState: FilterState;
}

export const useHandleTaglinePulldowns = ({
  orderStatusId,
  orderStatusTaglineId,
  orderReturnStatusId,
  orderReturnStatusTaglineId,
}: TaglineDependencies) => {
  const {
    fetchOrderStatusTagline,
    orderStatusTaglineOptions,
    getOptionByValue: getTaglineOptionById,
  } = usePulldownOrderStatusTagline({
    excludePending: true,
    orderStatusId,
  });

  const {
    fetchReturnOrderStatusTagline: fetchOrderReturnStatusTagline,
    orderReturnStatusTaglineOptions,
    getOptionByValue: getReturnStatusTaglineOptionById,
  } = usePulldownReturnStatusTagline({
    excludePending: true,
    orderReturnStatusId,
  });

  const selectedOrderStatusTagline = useMemo(
    () => getTaglineOptionById(orderStatusTaglineId) || null,
    [getTaglineOptionById, orderStatusTaglineId]
  );

  const selectedOrderReturnStatusTagline = useMemo(
    () => getReturnStatusTaglineOptionById(orderReturnStatusTaglineId) || null,
    [getReturnStatusTaglineOptionById, orderReturnStatusTaglineId]
  );

  useLayoutEffect(() => {
    fetchOrderStatusTagline();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderStatusId]);

  useLayoutEffect(() => {
    fetchOrderReturnStatusTagline();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderReturnStatusId]);

  return {
    orderStatusTaglineOptions,
    orderReturnStatusTaglineOptions,
    selectedOrderStatusTagline,
    selectedOrderReturnStatusTagline,
  };
};

export const useHandleUncontrolledPulldowns = () => {
  const { orderStatusesOptions, fetchOrderStatus } = usePulldownOrderStatus({
    excludePending: true,
    searchText: "",
  });

  const { fetchOrderReturnStatus, orderReturnStatusOptions } =
    usePulldownOrderReturnStatus({
      excludePending: true,
    });

  const { fetchOrderSources, orderSourceOptions } = usePulldownOrderSource({
    excludePending: true,
  });

  const { fetchShops, shopOptions } = usePulldownShop({ excludePending: true });

  const { fetchDeliveryPartners, deliveryPartnerOptions } =
    usePulldownDeliveryPartner({ excludePending: true });

  const { loadMoreEmployee, assignEmployeeOptions, loadMoreEmployeeState } =
    usePulldownAssignEmployee();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleLoadMoreEmployee = useCallback(
    debounce(
      (textSearch: string) => loadMoreEmployee({ name: textSearch }),
      200
    ),
    [loadMoreEmployee]
  );

  useDidMount(() => {
    fetchOrderStatus();
    fetchOrderReturnStatus();
    fetchOrderSources();
    fetchShops();
    fetchDeliveryPartners();
    loadMoreEmployee();
  });

  return {
    orderStatusOptions: orderStatusesOptions,
    orderReturnStatusOptions,
    orderSourceOptions,
    shopOptions,
    deliveryPartnerOptions,
    assignEmployeeOptions,
    loadMoreEmployee,
    loadMoreEmployeeState,
    handleLoadMoreEmployee,
  };
};

export const ListOrderPageVm = () => {
  const [state, setState] = useState<State>({
    pagination: {
      pageSize: 10,
    },
    filterState: {
      openForm: false,
      taglineDependencies: {
        orderStatusId: undefined,
        orderStatusTaglineId: undefined,
        orderReturnStatusId: undefined,
        orderReturnStatusTaglineId: undefined,
      },
      searchPayload: {},
    },
  });

  const setTaglineDependencies = useCallback(
    (properties: TaglineDependencies) => {
      setState(
        produce(({ filterState }) => {
          filterState.taglineDependencies = {
            ...filterState.taglineDependencies,
            ...properties,
          };
        })
      );
    },
    []
  );

  const isFilterResetRef = useRef<boolean | undefined>();

  const setSearchPayload = useCallback((searchPayload: FilterSearchPayload) => {
    isFilterResetRef.current = false;
    setState(
      produce((draft) => {
        draft.filterState.searchPayload = searchPayload;
      })
    );
  }, []);

  const resetSearchPayload = useCallback(() => {
    isFilterResetRef.current = true;
    setState(
      produce((draft) => {
        draft.filterState.searchPayload = {};
      })
    );
  }, []);

  const [getDatatableOrderExec, getDatatableOrderState] = useAsync(
    useCallback(
      (options: OrderSearchPayloadV2) =>
        filterOrder({ ...options }).then((res) => {
          return {
            orders: mapFrom(res.data.data, orderDetailDto),
            pagination: paginationDTO(res.data.links),
            totalRecords: res?.data?.meta?.totalRecords,
          };
        }),
      []
    ),
    {
      onSuccess: useCallback(() => {
        isFilterResetRef.current = undefined;
      }, []),
    }
  );

  const { gotoPage, ...orderPaginationState } = usePagination({
    pageSize: state.pagination.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getDatatableOrderExec({
        pageSize,
        pageNum: page,
        ...state.filterState.searchPayload,
      }),
  });

  const { sortedData: orderData, toggleSortState: toggleSortOrderBy } =
    useSortable({
      data: getDatatableOrderState.data?.orders,
      sortBy: {
        orderCode: (order) => order.code,
        createdDate: (order) => order.createdAt,
        city: (order) => order.shippingAddress?.city?.name,
        orderStatus: (order) => order.orderStatus?.name,
        customerName: (order) => order.customer?.name,
        // DATE //
        confirmDate: (order) => order.confirmDate,
        deliveryDate: (order) => order.deliveryDate,
        returnDate: (order) => order.returnDate,
        completeDate: (order) => order.completeDate,
        cancelDate: (order) => order.cancelDate,
      },
    });

  const gotoDetailOrderPage = useCallback(
    (order: OrderDetailDtoType) =>
      RootPageRouter.gotoChild("orderDetail", {
        params: { orderId: order.orderId?.toString() },
        queryString: "?action=view",
      }),
    []
  );

  const gotoEditOrderPage = useCallback(
    (order: OrderDetailDtoType) =>
      RootPageRouter.gotoChild("orderDetail", {
        params: { orderId: order.orderId?.toString() },
        queryString: "?action=edit",
      }),
    []
  );

  const gotoEditExchangeOrderPage = useCallback(
    (order: OrderDetailDtoType) =>
      RootPageRouter.gotoChild("exChangeOrderDetail", {
        params: { orderId: order.orderId?.toString() },
        queryString: "?action=edit",
      }),
    []
  );

  const goToDetailExchangeOrderPage = useCallback(
    (order: OrderDetailDtoType) =>
      RootPageRouter.gotoChild("exChangeOrderDetail", {
        params: { orderId: order.orderId?.toString() },
        queryString: "?action=view",
      }),
    []
  );

  const gotoExchangeOrderPage = useCallback(
    (order: OrderDetailDtoType) =>
      RootPageRouter.gotoChild("exChangeOrder", {
        params: { orderId: order.orderId?.toString() },
        queryString: "?action=exchange",
      }),
    []
  );

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pagination.pageSize = pageSize;
      })
    );
  }, []);

  const toggleFilterForm = useCallback(() => {
    setState(
      produce((draft) => {
        draft.filterState.openForm = !draft.filterState.openForm;
      })
    );
  }, []);

  const [deleteOrderExe] = useAsync(
    useCallback(
      ({ orderId }: { orderId: number }) => deleteOrder({ orderId }),
      []
    )
  );

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize, state.filterState.searchPayload]);

  return {
    orderPaginationState,
    gotoPage,
    toggleSortOrderBy,
    orderData: orderData || [],
    loading: getDatatableOrderState.loading,
    totalRecords: getDatatableOrderState.data?.totalRecords || 0,
    gotoDetailOrderPage,
    gotoEditOrderPage,
    gotoExchangeOrderPage,
    goToDetailExchangeOrderPage,
    gotoEditExchangeOrderPage,
    pageSize: state.pagination.pageSize,
    handleChangePageSize,
    toggleFilterForm,
    filterState: state.filterState,
    setTaglineDependencies,
    setSearchPayload,
    resetSearchPayload,
    isFilterReset: isFilterResetRef.current,
    deleteOrderExe,
    handleRefetch: () => {
      getDatatableOrderExec({
        pageNum: orderPaginationState.currentPage,
        pageSize: state.pagination.pageSize,
        ...state.filterState.searchPayload,
      });
    },
  };
};
