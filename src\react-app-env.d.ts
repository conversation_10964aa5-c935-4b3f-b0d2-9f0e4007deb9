/* eslint-disable prettier/prettier */
/// <reference types="react-scripts" />

interface Window extends Window {
  // eslint-disable-next-line no-undef
  __REDUX_DEVTOOLS_EXTENSION_COMPOSE__?: typeof compose;
}

interface RouteCustomEvent {
  type: "push" | "replace" | "goBack";
  path: string;
}

interface WindowMessage<T = string, P = unknown> {
  type: T;
  payload: P;
}

interface WindowEventMap extends WindowEventMap {
  route: CustomEvent<RouteCustomEvent>;
}

type ExtendsInterfaceAny<
  BaseInterface
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
> = BaseInterface & { [key: string]: any };

declare module "*.mp3" {
  const src: string;
  export default src;
}
