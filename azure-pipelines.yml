# Node.js with React
# Build a Node.js project that uses React.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- master

pool: BUILD-AGENT

steps:
- script: |
    nvm use 22.17.0
  displayName: 'Change node version'

- script: |
    yarn install --network-timeout 100000
  displayName: 'Install packages'

- script: |
    yarn build
  displayName: 'Build'

- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: 'build'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
    replaceExistingArchive: true

- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)'
    ArtifactName: 'drop'
    publishLocation: 'Container'