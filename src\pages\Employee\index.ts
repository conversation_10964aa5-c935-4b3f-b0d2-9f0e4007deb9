import { lazy } from "react";

import PATHS from "constants/paths";
import { createAppPage } from "libs/react";

import { ChildrenPage } from "./types";

export default createAppPage<{}, ChildrenPage>({
  name: "EmployeeManagement",
  path: PATHS.EMPLOYEES,
  childrenPages: {
    listEmployee: createAppPage({
      name: "ListEmployee",
      path: "/danh-sach-nhan-vien",
      page: () => lazy(() => import("./ListEmployee")),
    }),
    employeeDetail: createAppPage({
      name: "EmployeeDetail",
      path: "/danh-sach-nhan-vien/chi-tiet-nhan-vien/:employeeId",
      page: () => lazy(() => import("./EmployeeDetail")),
    }),
    listGroupEmployee: createAppPage({
      name: "GroupEmployee",
      path: "/nhom-nhan-vien/",
      page: () => lazy(() => import("./ListGroupEmployee")),
    }),
    groupEmployeeDetail: createAppPage({
      name: "GroupEmployeeDetail",
      path: "/nhom-nhan-vien/chi-tiet-nhom/:groupId",
      page: () => lazy(() => import("./GroupEmployeeDetail")),
    }),
  },
});
