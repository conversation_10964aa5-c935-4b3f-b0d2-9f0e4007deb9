/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { DeleteOutlined, EditOutlined, EyeOutlined } from "@ant-design/icons";
import {
  Button,
  Card,
  Col,
  Modal,
  Pagination,
  Row,
  Select,
  Space,
  Table,
  Tooltip,
  Typography,
} from "antd";

import dayjs from "dayjs";

import utc from "dayjs/plugin/utc";
import { use } from "i18next";
import _ from "lodash";
import { showNotification } from "components/atoms/base/Notification";
import {
  FilterPickerCollapse,
  FilterPickerCollapseRef,
  FilterType,
} from "components/atoms/base/shared/FilterPickerCollapse";
import { showLoading } from "components/atoms/base/Spinner";
import { BasePageProps } from "helpers/component";
import { mappedDataToDefaultOption } from "helpers/convertDataToDefaultOption.helper";
import {
  CreateUpdateCustomerModal,
  displayGenderCustomerType,
  primaryPhone,
} from "modules/customer";

import {
  useDeleteCustomerProfileV2,
  UseFindAllByFilter,
} from "services/crm/customer";
import {
  useGetCities,
  useGetDistricts,
} from "services/crm/location/hooks/location.hooks";
import { createCustomerValidateSchema } from "./constant";
import { CustomerPageVm } from "./vm";
import type { ColumnsType } from "antd/es/table";

const { Title } = Typography;

type FormFilterType = Pick<
  FilterType,
  | "successDate"
  | "purchaseNumberRange"
  | "inputSearch"
  | "customerType"
  | "gender"
  | "groupCustomer"
  | "city"
  | "district"
  | "ward"
  | "dateOfBirth"
  | "MonthOfBirth"
  | "totalPurchaseAmount"
>;

dayjs.extend(utc);

const getDaysInMonth = (month: number) => {
  if (month === 2) {
    return Array.from({ length: 29 }, (__, i) => ({
      label: `Ngày ${i + 1}`,
      value: i + 1,
    }));
  }
  if ([4, 6, 9, 11].includes(month)) {
    return Array.from({ length: 30 }, (__, i) => ({
      label: `Ngày ${i + 1}`,
      value: i + 1,
    }));
  }
  return Array.from({ length: 31 }, (__, i) => ({
    label: `Ngày ${i + 1}`,
    value: i + 1,
  }));
};

const IndexPage: React.FC<BasePageProps> = () => {
  const [searchText, setSearchText] = useState("");
  const filterCollapseRef = useRef<FilterPickerCollapseRef>(null);
  const { getProfileExe, getProfileState } = UseFindAllByFilter();
  // TODO: Implement search functionality
  // eslint-disable-next-line no-console
  // if (searchText) console.log("Search text:", searchText);
  const [monthOfBirth, setMonthOfBirth] = useState<number | null>(null);

  const monthOptions = Array.from({ length: 12 }, (__, i) => ({
    label: `Tháng ${i + 1}`,
    value: i + 1,
  }));

  const { cityData, cityRefetch } = useGetCities();
  const [cityIdValue, setCityIdValue] = useState<number | null>(null);
  const { districtData, districtRefetch } = useGetDistricts({
    cityId: cityIdValue,
  });

  const cityOptions = useMemo(() => {
    if (_.size(cityData?.data) > 0) {
      return mappedDataToDefaultOption({
        data: cityData?.data,
        keyLabel: "name",
        keyValue: "cityId",
        restItem: true,
      });
    }
    return [];
  }, [cityData]);

  const districtOptions = useMemo(() => {
    if (cityIdValue && _.size(districtData?.data) > 0) {
      return mappedDataToDefaultOption({
        data: districtData?.data,
        keyLabel: "name",
        keyValue: "districtId",
        restItem: true,
      });
    }
    return [];
  }, [cityIdValue, districtData]);

  const dayOptions = useCallback(() => {
    if (monthOfBirth) {
      return getDaysInMonth(monthOfBirth);
    }
    return Array.from({ length: 31 }, (__, i) => ({
      label: `Ngày ${i + 1}`,
      value: i + 1,
    }));
  }, [monthOfBirth]);

  const dateOfBirthOptions = useMemo(() => dayOptions(), [dayOptions]);

  const {
    loading,
    customerData,
    gotoPage,
    customerPaginationState,
    pageSize,
    handleChangePageSize,
    gotoCustomerDetailPage,
    gotoCustomerPage,
    toggleSortOrderBy,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    handleSubmitCreateCustomerProfile,
    createCustomerProfileState,
    refetch: refetchCustomer,
    totalRecords,
    handleFindProfile,
  } = CustomerPageVm();

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageSize]);

  const handleChangePulldownPageSize = useCallback(
    (value: number) => {
      handleChangePageSize(value);
    },
    [handleChangePageSize]
  );

  const onFinishCallBack = useCallback(
    (value: FormFilterType) => {
      if (value) {
        const {
          successDate,
          purchaseNumberRange,
          totalPurchaseAmount,
          MonthOfBirth: MonthOfBirthValues,
          dateOfBirth: dateOfBirthValues,
          customerType,
          ...restValue
        } = value;
        const { checked, rangeDateSuccess } = successDate || {};
        const fromDateSuccess = rangeDateSuccess?.[0]
          ? dayjs(rangeDateSuccess[0]).startOf("day").toISOString()
          : null;
        const toDateSuccess = rangeDateSuccess?.[1]
          ? dayjs(rangeDateSuccess[1]).endOf("day").toISOString()
          : null;
        const { from: purchaseFrom, to: purchaseTo } =
          purchaseNumberRange || {};
        const MonthOfBirth = MonthOfBirthValues || null;
        const dateOfBirth = dateOfBirthValues || null;
        const { from: totalAmountFrom, to: totalAmountTo } =
          totalPurchaseAmount || {};
        const payload = {
          ...restValue,
          inputSearch: restValue?.inputSearch
            ? restValue.inputSearch.trim()
            : undefined,
          successDateChecked: checked || false,
          fromDateSuccess: fromDateSuccess || undefined,
          toDateSuccess: toDateSuccess || undefined,
          monthOfBirth: Number(MonthOfBirth) || undefined,
          dateOfBirth: Number(dateOfBirth) || undefined,
          purchaseFrom: purchaseFrom || undefined,
          purchaseTo: purchaseTo || undefined,
          totalAmountFrom: totalAmountFrom || undefined,
          totalAmountTo: totalAmountTo || undefined,
          customerType:
            customerType === 0 ? false : customerType === 1 ? true : undefined,
        };
        console.log(payload, "payload");

        // getProfileExe(payload).then((res) => {
        //   if (res.status === 200) {
        //     console.log(213);
        //   }
        // });
        handleFindProfile(payload);
      } else {
        console.log("Bỏ qua payload không đầy đủ:", value);
      }
    },
    [getProfileExe]
  );

  // const onFinishCallBack = (value: any) => {
  //   const payload = {
  //     pageNum: 1,
  //     pageSize: 10,
  //     ...value,
  //   };
  //   console.log(payload, "aaaaaaaaaaaa");

  //   getProfileExe(payload).then((res) => {
  //     console.log(res, "voooooooooooooo");
  //   });
  // };

  const handleAddNew = useCallback(() => {
    // Gọi onFinishCallBack để xử lý logic
    // window.location.href = RootPage.children.addCustomer.path;
    gotoCustomerPage({
      customerId: "isAddNew",
    });
  }, []);

  const { deleteCustomerExe } = useDeleteCustomerProfileV2();
  const handleConfirmDelete = (record: any) => {
    const { customerId, name } = record;
    Modal.confirm({
      title: "Xoá khách hàng",
      content: `Bạn có chắc chắn muốn xóa khách hàng "${customerId} - ${name}" không?`,
      okText: "Xoá",
      okType: "primary",
      okButtonProps: {
        className: "hover:!bg-red-700 !bg-red-500",
      },
      cancelText: "Hủy",
      centered: true,
      onOk() {
        showLoading(true);
        deleteCustomerExe(customerId)
          .then(() => {
            showNotification({
              type: "success",
              message: "Xóa khách hàng thành công",
            });
            refetchCustomer({
              pageNum: customerPaginationState.currentPage!,
              pageSize: customerPaginationState.pageSize!,
            });
          })
          .catch((err) => {
            showNotification({
              type: "error",
              message: err.message,
            });
          })
          .finally(() => {
            showLoading(false);
          });
      },
    });
  };
  // Define table columns
  const columns: ColumnsType<Record<string, unknown>> = [
    {
      title: "STT",
      key: "index",
      width: 60,
      align: "center",
      fixed: "left",
      render: (__, ___, index) => {
        return (
          (customerPaginationState.currentPage! - 1) * pageSize + (index + 1)
        );
      },
    },
    {
      title: "Tên khách hàng",
      dataIndex: "name",
      key: "name",
      width: 200,
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("name"),
      }),
    },
    {
      title: "Số điện thoại",
      key: "phoneNumber",
      dataIndex: "phoneNumber",
      width: 150,
    },
    {
      title: "Email",
      key: "email",
      width: 200,
      render: (record) =>
        record?.emails?.find(
          (email: Record<string, unknown>) => email?.isPrimary
        )?.email,
    },
    {
      title: "Ngày sinh",
      dataIndex: "birthDay",
      key: "birthDay",
      width: 120,
      render: (date) => dayjs.utc(date).format("DD/MM/YYYY"),
    },
    {
      title: "Giới tính",
      dataIndex: "gender",
      key: "gender",
      width: 100,
      render: (gender) => displayGenderCustomerType({ gender }),
    },
    {
      title: "Cập nhật cuối",
      dataIndex: "updatedAt",
      key: "updatedAt",
      width: 150,
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("updatedAt"),
      }),
      render: (date) => dayjs.utc(date).format("DD/MM/YYYY HH:mm"),
    },
    {
      title: "Thao tác",
      key: "action",
      width: 120,
      fixed: "right",
      align: "center",
      render: (__, record) => (
        <div className="flex items-center justify-center gap-3">
          <Tooltip title="Xem chi tiết">
            <Button
              type="primary"
              icon={<EyeOutlined />}
              size="small"
              onClick={() =>
                gotoCustomerPage({
                  customerId: record.customerId as number,
                  action: "view",
                })
              }
            />
          </Tooltip>
          <Tooltip title="Chỉnh sửa">
            <Button
              type="default"
              icon={<EditOutlined />}
              size="small"
              onClick={() =>
                gotoCustomerPage({
                  customerId: record.customerId as number,
                })
              }
            />
          </Tooltip>
          <Tooltip title="Xóa">
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                handleConfirmDelete(record);
              }}
              size="small"
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  useEffect(() => {
    cityRefetch();
  }, []);

  useEffect(() => {
    if (cityIdValue) {
      districtRefetch();
    }
  }, [cityIdValue]);

  return (
    <div style={{ padding: "24px" }}>
      <title>Danh sách khách hàng</title>

      <Card
        styles={{
          body: {
            display: "flex",
            flexDirection: "column",
            gap: 16,
          },
        }}
      >
        <Title level={5} style={{ textTransform: "uppercase" }}>
          Danh sách khách hàng
        </Title>

        {/* Search and Action Bar */}
        {/* <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
          <Col xs={24} lg={12}>
            <Input
              placeholder="Tìm kiếm khách hàng..."
              prefix={<SearchOutlined />}
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col xs={24} lg={12} style={{ textAlign: "right" }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleOpenModalByType("customerCreation")}
            >
              Tạo mới
            </Button>
          </Col>
        </Row> */}

        <FilterPickerCollapse
          storageKey="listCustomer"
          onFinish={onFinishCallBack}
          customLabels={{
            inputSearch: "Họ tên/ Số điện thoại",
          }}
          defaultSelectedKeys={[
            "inputSearch",
            "customerType",
            "purchaseNumberRange",
            "city",
            "district",
            // "ward",
            "gender",
            "groupCustomer",
          ]}
          listFieldsUsed={[
            "inputSearch",
            "customerType",
            "gender",
            "groupCustomer",
            // "birthday",
            "city",
            "district",
            // "ward",
            "successDate",
            "purchaseNumberRange",
            "dateOfBirth",
            "MonthOfBirth",
            "totalPurchaseAmount",
          ]}
          listOptions={{
            dateOfBirth: dateOfBirthOptions,
            MonthOfBirth: monthOptions,
            city: cityOptions,
            district: districtOptions,
            customerType: [
              {
                label: "Khách sỉ",
                value: 1,
              },
              {
                label: "Khách lẻ",
                value: 0,
              },
            ],
          }}
          onChange={{
            MonthOfBirth: (value) => {
              if (value) {
                setMonthOfBirth(value);
              } else {
                setMonthOfBirth(null);
              }
            },
            city: (value) => {
              if (value) {
                setCityIdValue(value);
              } else {
                setCityIdValue(null);
              }
            },
          }}
          ref={filterCollapseRef}
          buttonActions={{
            add: handleAddNew,
          }}
        />

        {/* Table */}
        <Table
          columns={columns}
          dataSource={customerData}
          rowKey="customerId"
          loading={loading}
          scroll={{ x: 1500 }}
          size="small"
          bordered
          pagination={false}
        />
        {/* Pagination */}
        <Row
          justify="space-between"
          align="middle"
          style={{ marginTop: "16px" }}
        >
          <Col>
            <Space>
              <span>Số lượng hiển thị:</span>
              <Select
                value={pageSize}
                onChange={handleChangePulldownPageSize}
                style={{ width: 80 }}
                options={[5, 10, 15, 25, 30].map((size) => ({
                  label: size.toString(),
                  value: size,
                }))}
              />
            </Space>
          </Col>
          <Col>
            {customerPaginationState.totalPage && (
              <Pagination
                current={customerPaginationState.currentPage || 1}
                total={totalRecords || 0}
                pageSize={pageSize}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) =>
                  `${range[0]}-${range[1]} của ${total} mục`
                }
                onChange={gotoPage}
              />
            )}
          </Col>
        </Row>
      </Card>

      <CreateUpdateCustomerModal
        open={modalTypeIsOpen("customerCreation")}
        inputValidationSchema={createCustomerValidateSchema}
        handlerCreateCustomer={handleSubmitCreateCustomerProfile}
        onClose={handleCloseModal}
        loading={createCustomerProfileState.loading}
      />
    </div>
  );
};

export default IndexPage;
