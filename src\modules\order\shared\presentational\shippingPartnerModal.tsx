import * as Yup from "yup";

import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Modal } from "components/organisms/modal";
import { FormContainer } from "helpers/form";
import { useAsync } from "hooks/useAsync";
import { CreateDeliveryPartnerDtoType } from "modules/order";

export interface ShippingPartnerModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (e: CreateDeliveryPartnerDtoType) => Promise<unknown>;
  formValidateSchema?: Yup.ObjectSchema<Yup.AnyObject>;
}

export const ShippingPartnerModal: React.FC<ShippingPartnerModalProps> = ({
  open,
  onClose,
  formValidateSchema,
  onSubmit,
}) => {
  const [SubmitExec, SubmitState] = useAsync(onSubmit);

  return (
    <Modal
      style={{ content: { maxWidth: 700 } }}
      isOpen={open}
      onCloseModal={onClose}
      isClosable={false}
    >
      <FormContainer
        validationSchema={formValidateSchema}
        onSubmit={SubmitExec}
      >
        <Heading centered type="h1">
          TẠO MỚI ĐƠN VỊ VẬN CHUYỂN
        </Heading>
        <Formfield label="Mã đơn vị vận chuyển" name="code">
          <TextfieldHookForm
            name="code"
            placeholder="Nhập mã đơn vị vận chuyển"
          />
        </Formfield>
        <Formfield label="Tên đơn vị vận chuyển" name="name">
          <TextfieldHookForm
            name="name"
            placeholder="Nhập tên đơn vị vận chuyển"
          />
        </Formfield>
        <div className="d-flex justify-content-end u-mt-20">
          <div className="u-mr-15">
            <Button
              buttonType="outline"
              modifiers="secondary"
              onClick={onClose}
            >
              HỦY
            </Button>
          </div>
          <Button
            type="submit"
            isLoading={SubmitState.loading}
            disabled={SubmitState.loading}
          >
            LƯU
          </Button>
        </div>
      </FormContainer>
    </Modal>
  );
};
