import { useEffect, useCallback } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { employeeGroupDto, EmployeeGroupDtoType } from "modules/employee/dtos";
import {
  updateEmployeeGroupDto,
  UpdateEmployeeGroupDtoType,
} from "modules/employee/dtos/updateEmployeeGroup";
import {
  getEmployeeGroup,
  updateEmployeeGroupById,
} from "services/crm/employee";

interface EmployeeDetailPageVmProps {
  groupId: number;
}

export type EmployeeGroupItem = EmployeeGroupDtoType;

export const EmployeeGroupPageVm = ({ groupId }: EmployeeDetailPageVmProps) => {
  const [getEmployeeGroupByIdExec, getEmployeeGroupByIdState] = useAsync(
    useCallback(
      (params: Parameters<typeof getEmployeeGroup>[0]) =>
        getEmployeeGroup({ ...params }).then((res) =>
          employeeGroupDto(res.data.data)
        ),
      []
    )
  );

  const [updateEmployeeGroupExec, updateEmployeeGroupState] = useAsync(
    updateEmployeeGroupById,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Cập nhật thành công" });
      }, []),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(error?.errors?.[0]?.code);

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const employeeGroupDetail = getEmployeeGroupByIdState.data;

  const handleUpdateEmployeeGroup = useCallback(
    (rawPayload: Partial<UpdateEmployeeGroupDtoType>) => {
      if (!employeeGroupDetail?.employeeGroupId) return;
      const updateEmployeeGroupPayload = updateEmployeeGroupDto({
        ...employeeGroupDetail,
        ...rawPayload,
      });

      updateEmployeeGroupExec(
        employeeGroupDetail.employeeGroupId,
        updateEmployeeGroupPayload
      );
    },
    [updateEmployeeGroupExec, employeeGroupDetail]
  );

  useEffect(() => {
    getEmployeeGroupByIdExec({ groupId });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groupId]);

  return {
    loading: getEmployeeGroupByIdState.loading,
    employeeGroupData: getEmployeeGroupByIdState.data,
    handleUpdateEmployeeGroup,
    updateEmployeeGroupState,
  };
};
