import * as Yup from "yup";

import {
  orderStatusOption,
  CreateOrderStatusTaglineDtoType,
} from "modules/order";

export const inputValidationSchema = Yup.object({
  name: Yup.string().required("Vui lòng nhập tên tagline"),
});

export type CreateOrderStatusTaglineFormPayload = Pick<
  CreateOrderStatusTaglineDtoType,
  "description" | "name" | "displayOrder" | "color"
> & {
  orderStatus: ReturnType<typeof orderStatusOption>;
};
