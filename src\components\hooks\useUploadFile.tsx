import React, { useCallback, useRef, useState } from "react";

export interface Props {
  fileSize?: number;
  extensions: Array<string>;
  onChange?: (file: File) => void;
  onChangeFileMulti?: (file: File[]) => void;
  keepPrevData?: boolean;
}

const useUploadImage = (props: Props) => {
  const { fileSize, extensions, onChange, onChangeFileMulti, keepPrevData } =
    props;

  const [fileUpload, setFileUpload] = useState<File[] | null>(null);
  const refInput = useRef<HTMLInputElement | null>(null);

  const handleClearFile = useCallback(() => {
    setFileUpload(null);

    if (refInput.current) {
      refInput.current.value = "";
    }
  }, []);

  const handleRemoveFile = useCallback(
    (position: number) => {
      if (!fileUpload || fileUpload?.length === 0) return;
      setFileUpload(fileUpload.filter((_, index) => index !== position));
    },
    [fileUpload]
  );

  const onChangeFileUpload = useCallback(
    (ele: React.ChangeEvent<HTMLInputElement>) => {
      const fileList = ele.target.files && Array.from(ele.target.files);

      if (fileList) {
        const sizeFile =
          fileList.reduce<number>((acc, curr) => acc + curr.size, 0) / 1048576;
        const extensionCheck = fileList.every((file) =>
          extensions.some((extension) => file.name.includes(extension))
        );

        if (sizeFile <= (fileSize || 1) && extensionCheck) {
          if (!keepPrevData) {
            setFileUpload(fileList);
          }

          if (keepPrevData) {
            setFileUpload(fileUpload ? [...fileUpload, ...fileList] : fileList);
          }

          if (onChange) {
            onChange(fileList[0]);
          }

          if (onChangeFileMulti) {
            onChangeFileMulti(fileList);
          }

          if (refInput.current) {
            refInput.current.value = "";
          }
        } else {
          handleClearFile();
          // TODO: Replace with a `toastify` function
          // eslint-disable-next-line no-alert
          alert(
            `Định dạng file phải là ${extensions.join(
              " "
            )} và không được lớn hơn ${fileSize || 1}MB`
          );
        }
      }
    },
    [
      extensions,
      fileSize,
      fileUpload,
      handleClearFile,
      keepPrevData,
      onChange,
      onChangeFileMulti,
    ]
  );

  return {
    fileUpload: fileUpload?.[0],
    fileUploadMulti: fileUpload,
    refInput,
    handleClearFile,
    onChangeFileUpload,
    handleRemoveFile,
  };
};

export default useUploadImage;
