/* eslint-disable @typescript-eslint/no-explicit-any */
import { mergeSchema } from "../merge";
import { Schema, schemaParser } from "../schema";
import { IObject, SchemaType } from "../types";

/**
 * This function will merge multiple `schema` into one and return a `Function` of type `SchemaTypeParser<R>`
 * @function ExtendSchema
 * @param `first` - Object
 * @param `second` - Object
 * @returnType `SchemaTypeParser<R>`
 * @return A `Function` returns a `schemaParser` object
 */
export function ExtendSchema<O extends IObject, Ex extends IObject>(
  modelSchema: SchemaType<O>,
  extraSchema?: SchemaType<Ex>
) {
  return (value: any, extraConfig?: any) => {
    const schema = new Schema(
      mergeSchema(modelSchema, extraSchema || {}) as SchemaType<O & Ex>
    );
    return schemaParser(schema, value, extraConfig) || undefined;
  };
}
