import React, {
  useState,
  useCallback,
  useEffect,
  useRef,
  forwardRef,
} from "react";

import { SketchPicker, SketchPickerProps, ColorResult } from "react-color";

import { mapModifiers } from "helpers/component";

export interface SketchProps extends SketchPickerProps {
  defaultColor?: string;
  onChangeClolor?: (color: string) => void;
  disabled?: boolean;
}

export const ColorPicker = forwardRef<SketchPicker, SketchProps>(
  ({ defaultColor, onChangeClolor, disabled, ...innerProps }, ref) => {
    const colorPickerRef = useRef<HTMLDivElement | null>(null);
    const [isShowSketch, setShowSketch] = useState<boolean>(false);
    const [currentColor, setCurrentColor] = useState<string>(
      defaultColor || "#000000"
    );

    const handleChangeColor = useCallback(
      (value: ColorResult) => {
        const { rgb } = value;
        const { r, g, b, a } = rgb;
        const colorCombineValue = `rgba(${r},${g},${b},${a})`;
        setCurrentColor(colorCombineValue);

        if (onChangeClolor) {
          onChangeClolor(colorCombineValue);
        }
      },
      [onChangeClolor]
    );

    const onToggleColorPicker = useCallback(
      () => setShowSketch(!isShowSketch),
      [isShowSketch]
    );

    useEffect(() => {
      const colorPickerTarget = colorPickerRef.current;
      if (!colorPickerTarget) return undefined;

      const handleClick = (e: MouseEvent) => {
        if (!colorPickerTarget.contains(e.target as Node) && isShowSketch) {
          setShowSketch(false);
        }
      };

      window.addEventListener("click", handleClick);

      return () => {
        window.removeEventListener("click", handleClick);
      };
    }, [isShowSketch]);

    return (
      <div
        className={mapModifiers("a-colorpicker", disabled && "disabled")}
        ref={colorPickerRef}
      >
        <div
          aria-hidden
          className="a-colorpicker_wrapper"
          onClick={onToggleColorPicker}
        >
          <span
            className="a-colorpicker_line"
            style={{
              backgroundColor: currentColor,
            }}
          />
        </div>
        {isShowSketch && (
          <div className="a-colorpicker_sketch">
            <SketchPicker
              ref={ref}
              {...innerProps}
              color={currentColor}
              onChange={handleChangeColor}
            />
          </div>
        )}
      </div>
    );
  }
);

ColorPicker.defaultProps = {
  defaultColor: "#000000",
};
