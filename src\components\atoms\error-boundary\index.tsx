/* eslint-disable react/destructuring-assignment */
import { Component, ReactNode } from "react";

import * as navigationHelper from "helpers/navigation";

import style from "./index.module.scss";

interface Props {
  children?: ReactNode;
}

interface State {
  hasError: boolean;
  message: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      message: "",
    };
    this.handleClick = this.handleClick.bind(this);
  }

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI.
    return { hasError: true, message: error.message };
  }

  public handleClick() {
    this.setState({ hasError: false, message: "" });
    navigationHelper.replace("/");
  }

  public render() {
    if (this.state.hasError) {
      return (
        <div className={style.content}>
          <div className={style.notfound}>
            <div className={style.notfound_404} />
            <h1>ERROR</h1>
            <h2>Oops! Page Not Be Found</h2>
            <p>{this.state.message}</p>
            <button type="button" onClick={this.handleClick}>
              Quay về trang chính
            </button>
          </div>
        </div>
      );
    }
    return this.props.children;
  }
}

export default ErrorBoundary;
