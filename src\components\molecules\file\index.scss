.m-file {
	$root: &;

	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: rem(10);
	transition: background-color 0.3s;

	&:hover {
		background-color: $COLOR-ANTIQUE-WHITE;
	}

	.a-checkbox {
		position: absolute;
		top: 0;
		right: 0;
	}

	&_picture {
		width: 100%;
		max-width: rem(140);
		filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));

		.a-image {
			width: 100%;
		}
	}

	&_content {
		margin-top: rem(17.88);
		font-family: $FONTFAMILY-ARIAL;
		font-size: rem(14);
		line-height: rem(20);
		color: $COLOR-QUARTZ;
		@include u-fw-bold;
	}

	&-floatcheckbox {
		.a-checkbox {
			z-index: 1;
		}
	}
}
