/* eslint-disable @typescript-eslint/no-explicit-any */

import { IObject } from "./types";

type TUnionToIntersection<U> = (
  U extends any ? (k: U) => void : never
) extends (k: infer I) => void
  ? I
  : never;

/**
 * A function that checks `any data` is an` object` or not
 * @function isObject
 * @description Checks whether a data is an `object` or `not`
 * @param `obj` - Any data
 * @returnType Boolean
 */
const isObject = (obj: any) => {
  if (typeof obj === "object" && obj !== null) {
    if (typeof Object.getPrototypeOf === "function") {
      const prototype = Object.getPrototypeOf(obj);
      return prototype === Object.prototype || prototype === null;
    }

    return Object.prototype.toString.call(obj) === "[object Object]";
  }

  return false;
};

/**
 * This function will merge multiple `schema` into one.
 * @function mergeSchema
 * @description Merge multiple objects into one
 * @param `objects` - A list of objects such as multi-parameter
 * @returnType `Object`
 */
export const mergeSchema = <T extends IObject[]>(
  ...objects: T
): TUnionToIntersection<T[number]> =>
  objects.reduce((result, current) => {
    Object.keys(current || {}).forEach((key) => {
      if (Array.isArray(result[key]) && Array.isArray(current[key])) {
        result[key] = Array.from(new Set(result[key].concat(current[key])));
      } else if (isObject(result[key]) && isObject(current[key])) {
        result[key] = mergeSchema(result[key], current[key]);
      } else {
        result[key] = current[key];
      }
    });

    return result;
  }, {}) as any;

export default mergeSchema;
