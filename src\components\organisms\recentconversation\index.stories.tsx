import { Story, Meta } from "@storybook/react/types-6-0";

import { RecentConversation as RecentConversationItem } from "components/molecules/recentconversation";
import { MessageEntityType, MessageModel } from "modules/chat";

import { RecentConversation, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|organisms/RecentConversation",
  component: RecentConversation,
} as Meta;

const Template: Story<
  Props<{
    name: string;
    avatar: string;
    message: Pick<MessageEntityType, "content" | "createdAt">;
  }>
> = ({ modifiers, conversations }) => (
  <div style={{ width: 400, height: 700 }}>
    <RecentConversation
      modifiers={modifiers}
      conversations={conversations}
      onConversationRender={(conversation) => (
        <RecentConversationItem
          avatar={conversation.avatar}
          alt={conversation.name}
          name={conversation.name}
          message={{
            ...conversation.message,
            creatorType: "employee",
          }}
        />
      )}
    />
  </div>
);

export const Normal = Template.bind({});

Normal.args = {
  conversations: Array(50)
    .fill(true)
    .map((_, index) => ({
      name: `Khách hàng ${index}`,
      avatar: `https://avatars.githubusercontent.com/u/18642063?s=64&v=4`,
      message: MessageModel.create({
        _id: "6081552575a3570e40ec00f3",
        messageId: "44be45ed-88ec-4e79-89ad-858a9ce475ba",
        sender: { id: "102496038607569" },
        recipient: { id: "4172256639491478" },
        content: {
          mid: "m_NXxWxb-GgxyFXiSMWvmggSnlTZVA-vSYVr98nFHhaUr65IkDikuxFS9PRCygG0z4rFGWVInuAGvtmszXmcszWw",
          text: "hihi",
          attachments: [],
          metadata: {
            fromSocketId: "gpy8_DcBcx0JB6inAAAJ",
            tempId: "_mid_89pv59jce",
            media: { type: "" },
          },
        },
        conversationId: "a0e709b7-be89-4e1b-9de0-eac7688a5d57",
        createdAt: new Date("2021-04-22T10:51:17.579Z"),
        updatedAt: new Date("2021-04-22T10:51:17.579Z"),
        creatorAvatar: "/static/media/logo.b3bbb694.png",
      }),
    })),
};
