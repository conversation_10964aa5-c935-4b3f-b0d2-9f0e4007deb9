/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { Checkbox, DatePicker, Flex, Form, Input, Modal } from "antd";
import { Dayjs } from "dayjs";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import BaseSelectGroupCustomer from "components/atoms/base/select/shared/BaseGroupCustomer";
import BaseSelectCity from "components/atoms/base/select/shared/BaseSelectCity.share";
import BaseSelectDistrict from "components/atoms/base/select/shared/BaseSelectDistrict.share";
import { COLOR } from "constants/color";
import { getCities, getDistricts, getWards } from "services/crm/system";
import { useAppSelector } from "store";

export interface CustomerUpserModalRef {
  open: (customerId?: number) => void;
  close: () => void;
}

export interface CustomerUpserModalProps {
  onFinishForm: (data: any) => void;
}

export type FormCustomerUpserType = {
  name: string;
  birthDay: Dayjs | undefined;
  gender: number;
  phoneNumber: string;
  phoneNumber2: string | undefined;
  dontSendSMS: boolean;
  email: string | undefined;
  address: string | undefined;
  cityId: number | undefined;
  districtId: number | undefined;
  wardId: number | undefined;
  height: string | undefined;
  weight: string | undefined;
  waist: string | undefined;
  note: string | undefined;
  customerType: string | undefined;
};

export const CustomerUpserModal = forwardRef<
  CustomerUpserModalRef,
  CustomerUpserModalProps
>((props, ref) => {
  const [open, setOpen] = useState(false);
  const customerId = useRef<number | null>(null);
  const [form] = Form.useForm<FormCustomerUpserType>();
  const cityIdValue = Form.useWatch("cityId", form);

  useImperativeHandle(ref, () => ({
    open: (id?: number) => {
      if (id) {
        customerId.current = id;
      }
      setOpen(true);
    },
    close: onCancel,
  }));

  const onCancel = () => {
    form.resetFields();
    customerId.current = null;
    setOpen(false);
  };

  const handlePhone1Change = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const onlyNums = e.target.value.replace(/\D/g, "");
      form.setFieldsValue({ phoneNumber: onlyNums });
    },
    [form]
  );
  const handlePhone2Change = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const onlyNums = e.target.value.replace(/\D/g, "");
      form.setFieldsValue({ phoneNumber2: onlyNums });
    },
    [form]
  );

  const onFinish = (values: FormCustomerUpserType) => {
    const { birthDay } = values;
    const formatValues = {
      ...values,
      birthDay: birthDay ? birthDay.toISOString() : undefined,
    };
    props.onFinishForm({ ...formatValues, customerId: customerId.current });
  };

  useEffect(() => {
    if (customerId.current) {
      // Fetch customer data and set form values if customerId is provided
      // This part is omitted for brevity, but you would typically fetch the customer data here
      // and set the form values using form.setFieldsValue(customerData);
    }
  }, [customerId.current]); // Ensure to run this effect when customerId changes

  return (
    <Modal
      open={open}
      onCancel={onCancel}
      destroyOnHidden
      title={customerId.current ? "Cập nhật khách hàng" : "Thêm khách hàng"}
      maskClosable={false}
      width={880}
      footer={
        <Flex align="center" justify="end" gap={16}>
          <BaseButton type="text" onClick={onCancel}>
            Hủy
          </BaseButton>
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[600]}
            onClick={() => form.submit()}
          >
            Lưu
          </BaseButton>
        </Flex>
      }
      centered
    >
      <Form
        form={form}
        onFinish={onFinish}
        layout="vertical"
        initialValues={{
          gender: 1,
          customerType: false,
          dontSendSMS: false,
        }}
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Form.Item
            label="Tên khách hàng"
            name="name"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập tên khách hàng",
              },
            ]}
          >
            <Input placeholder="Nhập tên khách hàng" />
          </Form.Item>

          {/* <Form.Item
          label="Mã thẻ vip"
          name="vipCode"
        >
          <Input placeholder="Nhập mã thẻ vip" />
        </Form.Item> */}
          <div className="col-span-1 grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/** GIỚI TÍNH */}
            <Form.Item
              label="Giới tính"
              name="gender"
              // rules={[{ required: true, message: "Vui lòng chọn giới tính" }]}
            >
              <BaseSelect
                placeholder="Chọn giới tính"
                options={[
                  { label: "Nam", value: 1 },
                  { label: "Nữ", value: 0 },
                ]}
              />
            </Form.Item>

            <Form.Item
              label="Ngày sinh"
              name="birthDay"
              rules={[
                {
                  required: true,
                  message: "Vui lòng chọn ngày sinh",
                },
              ]}
            >
              <DatePicker
                placeholder="Chọn ngày sinh"
                format="DD/MM/YYYY"
                className="w-full"
              />
            </Form.Item>
          </div>

          <Form.Item
            label="Email"
            name="email"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập email",
              },
              {
                type: "email",
                message: "Vui lòng nhập địa chỉ email hợp lệ",
              },
            ]}
          >
            <Input placeholder="Nhập địa chỉ email" />
          </Form.Item>
          <div className="col-span-1 grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Form.Item
              name="customerType"
              label="Loại khách hàng"
              valuePropName="checked"
            >
              <Checkbox>Khách sỉ</Checkbox>
            </Form.Item>

            <Form.Item
              name="dontSendSMS"
              label="Không gửi tin nhắn"
              valuePropName="checked"
            >
              <Checkbox>Không gửi SMS</Checkbox>
            </Form.Item>
          </div>

          <Form.Item
            label="Số điện thoại"
            name="phoneNumber"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập số điện thoại",
              },
              {
                pattern: /^\d{10}$/,
                message: "Số điện thoại phải gồm đúng 10 chữ số",
              },
            ]}
          >
            <Input
              placeholder="Nhập số điện thoại"
              maxLength={10}
              type="tel"
              onChange={handlePhone1Change}
            />
          </Form.Item>

          <Form.Item
            label="Số điện thoại 2"
            name="phoneNumber2"
            rules={[
              {
                pattern: /^\d{10}$/,
                message: "Số điện thoại phải gồm đúng 10 chữ số",
              },
            ]}
          >
            <Input
              placeholder="Nhập số điện thoại 2"
              maxLength={10}
              type="tel"
              onChange={handlePhone2Change}
            />
          </Form.Item>

          <Form.Item label="Nhóm khách hàng" name="groupCustomer">
            {/* <Input placeholder="Nhập nhóm khách hàng" allowClear /> */}
            <BaseSelectGroupCustomer placeholder="Chọn nhóm khách hàng" />
          </Form.Item>

          <Form.Item
            label="Địa chỉ"
            name="address"
            rules={[{ required: true, message: "Vui lòng nhập địa chỉ" }]}
          >
            <Input placeholder="Nhập địa chỉ" allowClear />
          </Form.Item>

          <Form.Item
            label="Tỉnh/Thành phố"
            name="cityId"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn tỉnh/thành phố",
              },
            ]}
          >
            <BaseSelectCity placeholder="Chọn tỉnh/thành phố" />
          </Form.Item>

          <Form.Item
            label="Quận/Huyện"
            name="districtId"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn quận/huyện",
              },
            ]}
          >
            <BaseSelectDistrict
              placeholder="Chọn quận/huyện"
              cityId={cityIdValue}
            />
          </Form.Item>

          {/* <Form.Item
            label="Phường/Xã"
            name="wardId"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn phường/xã",
              },
            ]}
          >
            <BaseSelect
              placeholder="Chọn phường/xã"
              options={listWard}
              fieldNames={{ label: "ward_name", value: "ward_id" }}
            />
          </Form.Item> */}

          <div className="col-span-1 lg:col-span-2 grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/** CHIỀU CAO */}
            <Form.Item label="Chiều cao" name="height">
              <Input placeholder="Nhập chiều cao" allowClear />
            </Form.Item>
            {/** CÂN NẶNG */}
            <Form.Item label="Cân nặng" name="weight">
              <Input placeholder="Nhập cân nặng" allowClear />
            </Form.Item>
            {/** VÒNG EO */}
            <Form.Item label="Vòng eo" name="waist">
              <Input placeholder="Nhập vòng eo" allowClear />
            </Form.Item>
          </div>
          <div className="col-span-1 lg:col-span-2">
            <Form.Item label="Ghi chú" name="note">
              <Input.TextArea placeholder="Nhập ghi chú" allowClear rows={3} />
            </Form.Item>
          </div>
        </div>
      </Form>
    </Modal>
  );
});
