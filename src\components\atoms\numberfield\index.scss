.a-numberfield {
	$root: &;
	position: relative;

	&_input {
		width: 100%;
		height: rem(48);
		padding: 0 rem(12);
		font-size: rem(14);
		line-height: rem(16);
		color: $COLOR-QUARTZ;
		background-color: $COLOR-WHITE;
		border: 1px solid $COLOR-PLATINUM-4;
		border-radius: 2px;
		outline: none;
		transition: all 0.3s ease-in-out;
		appearance: none;

		&::placeholder {
			color: $COLOR-LIGHT-GRAY;
		}

		&:hover,
		&:focus {
			color: $COLOR-QUARTZ;
			border-color: $COLOR-QUARTZ;
		}

		#{$root}-disabled & {
			color: $COLOR-QUARTZ;
			background-color: $COLOR-WHITE-SMOKE-2;
		}

		#{$root}-error & {
			color: $COLOR-CINNABAR;
			border: 1px solid $COLOR-CINNABAR;
			box-shadow: none;

			&:focus {
				box-shadow: 0px 0px 4px $COLOR-CINNABAR;
			}
		}
	}

	&-disabled {
		pointer-events: none;
	}

	&_errormessage {
		margin-top: rem(5);
		margin-left: rem(10);
		font-size: rem(13);
		color: $COLOR-CINNABAR;
		@include u-fw-light;
	}
}
