import React from "react";

import { actions } from "@storybook/addon-actions";
import { Story, Meta } from "@storybook/react/types-6-0";

import { Pagination, Props } from ".";

export default {
  title: "Components|molecules/Pagination",
  component: Pagination,
} as Meta;

const Template: Story<Props> = ({
  total,
  pageCount,
  defaultCurrentPage,
  onPageChange,
}) => (
  <Pagination
    total={total}
    pageCount={pageCount}
    defaultCurrentPage={defaultCurrentPage}
    onPageChange={onPageChange}
  />
);

export const Normal = Template.bind({});

Normal.args = {
  total: 100,
  pageCount: 5,
  defaultCurrentPage: 1,
  onPageChange: (pageNum: number) => {
    actions(`Page num: ${pageNum}`);
  },
};
