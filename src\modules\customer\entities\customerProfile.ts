/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Enum,
  Model,
  String,
  Number,
  Boolean,
  Array,
  Mixed,
  ModelValue,
} from "libs/domain";

export const CustomerProfileSchema = {
  _id: String(),
  customerId: Number(),
  memberPoints: Number(),
  accountType: Number(),
  name: String(),
  phoneNumber: String(),
  phoneNumber2: String(),
  email: String(),
  weight: String(),
  height: String(),
  waist: String(),
  isVerified: Boolean(),
  gender: Enum({ values: [0, 1, 2] }),
  birthDay: (raw: any) => new Date(raw),
  updatedAt: (raw: any) => new Date(raw),
  phoneNumbers: Array(
    Mixed({
      customerPhoneNumberId: Number(),
      phoneNumber: String(),
      isPrimary: Boolean(),
    })
  ),
  emails: Array(
    Mixed({
      customerEmailId: Number(),
      email: String(),
      isPrimary: Boolean(),
    })
  ),
};

export const CustomerProfileModel = new Model(CustomerProfileSchema);
export type CustomerProfileEntityType = ModelValue<typeof CustomerProfileModel>;

export const displayGenderCustomerType = (
  payload: Pick<CustomerProfileEntityType, "gender">
) => {
  switch (payload.gender) {
    case 0: {
      return "Nữ";
    }
    case 1: {
      return "Nam";
    }
    default:
      return "";
  }
};

export const primaryPhone = (
  payload?: ExtendsInterfaceAny<Pick<CustomerProfileEntityType, "phoneNumbers">>
) =>
  payload?.phoneNumbers?.find((phoneNumber) => phoneNumber?.isPrimary)
    ?.phoneNumber;

export const primaryEmail = (
  payload?: Pick<CustomerProfileEntityType, "emails">
) => payload?.emails?.find((email) => email?.isPrimary)?.email;

// NOTE: 0 female, 1 male, 2 other
export type Gender = 0 | 1 | 2;
