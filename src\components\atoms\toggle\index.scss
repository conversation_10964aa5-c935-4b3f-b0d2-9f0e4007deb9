.a-toggle {
	$root: &;
	$animation: 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;

	display: inline-flex;
	align-items: center;
	margin-bottom: 0;
	cursor: pointer;

	> span {
		position: relative;
		display: inline-flex;
		min-width: rem(48);
		height: rem(22);
		margin: rem(2);
		background-color: $COLOR-PLATINUM-2;
		border-radius: 15px;
		transition: all $animation;

		&:before {
			position: absolute;
			top: -1px;
			width: rem(24);
			height: rem(24);
			content: "";
			background-color: $COLOR-WHITE;
			border: 1px solid rgba($COLOR-BLACK, 0.16);
			border-radius: 50%;
			transition: all $animation;
			transform: translateX(-2px);
		}
	}

	.a-text {
		margin-right: rem(16);
		margin-bottom: 0;
	}

	> input {
		position: absolute;
		pointer-events: none;
		opacity: 0;

		&:checked ~ span {
			background-color: $COLOR-PASTEL-ORANGE;
			&:before {
				transform: translateX(rem(24));
			}
		}
	}
}
