import { mapFrom } from "libs/adapters/dto";
import { createAsyncAction } from "libs/adapters/stores/core";
import { getAssignConversation } from "services/crm/chat";

import { assignConversationItemDto } from "../dtos";

export const getAssignConversationEventCreator = () =>
  createAsyncAction((options: { pageNum: number; pageSize: number }) =>
    getAssignConversation({
      ...options,
    }).then((res) => mapFrom(res.data?.data, assignConversationItemDto))
  );
