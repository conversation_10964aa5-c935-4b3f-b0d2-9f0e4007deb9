import { fromSchema, createMapper } from "libs/adapters/dto";
import { mergeSchema, Array, Mixed } from "libs/domain";

import { EmployeeSchema } from "../entities";
import { GroupEmployeeSchema } from "../entities/groupEmployee";

export const employeeDetailDto = createMapper(
  // fromSchema(mergeSchema(EmployeeSchema))
  fromSchema(
    mergeSchema(EmployeeSchema, {
      employeeGroups: Array(Mixed(GroupEmployeeSchema)),
    })
  )
);

export type EmployeeDetailDtoType = ReturnType<typeof employeeDetailDto>;
