/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useLayoutEffect } from "react";

import { Image } from "components/atoms/image";
import { Spinner } from "components/atoms/spinner";
import { Text } from "components/atoms/text";
import { BoxTextMessage } from "components/molecules/boxmessage";
import { Message } from "components/molecules/message";
import { Chatbox } from "components/organisms/chatbox";
import { ChatInput, FileStateType } from "components/organisms/chatinput";
import { useChatInput } from "components/organisms/chatinput/hook";
import { alert } from "contexts/dialog";
import { usePendingFromActions } from "hooks/usePendingFromActions";
import { messageTypeByContent } from "modules/chat";

import {
  useChatboxPageContext,
  useSelectedConversation,
  useSelectedMessageConversation,
} from "../hook";

const MessageBox = () => {
  const {
    loadmoreConversationMessage,
    getConversationMessageEvent,
    sendConversationMessage,
  } = useChatboxPageContext();

  const selectedConversation = useSelectedConversation();
  const { activeConversationMessages, hasLoadMore } =
    useSelectedMessageConversation();

  const isMessageLoading = usePendingFromActions(getConversationMessageEvent);

  const { register, reset: resetChatInput } = useChatInput();

  const isMessageFetching =
    isMessageLoading && activeConversationMessages.length === 0;

  const isMessageLoadingMore =
    isMessageLoading && activeConversationMessages.length !== 0;

  const handleSendMessage = useCallback(
    (text: string, file: FileStateType[]) => {
      sendConversationMessage(text, file);
      resetChatInput();
    },
    [sendConversationMessage, resetChatInput]
  );

  useLayoutEffect(() => {
    resetChatInput();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedConversation]);

  if (!selectedConversation) return null;

  const { client } = selectedConversation;
  return (
    <>
      <div className="flex page-chatbox_clientheader">
        <Image aspectRatio="1x1" src={client.profilePic} alt={client.name} />
        <Text>{client.name}</Text>
      </div>
      <div className="page-chatbox_listmessage">
        {isMessageFetching ? (
          <Spinner hasContainer />
        ) : (
          <Chatbox
            messages={activeConversationMessages}
            hasMore={hasLoadMore}
            loadMorePending={isMessageLoadingMore}
            onMessageRender={(message: any) => (
              <div style={{ padding: "0 16px" }}>
                <BoxTextMessage
                  src={message.creatorAvatar}
                  contentLeft={message.creatorType === "employee"}
                  createdAt={message.createdAt}
                >
                  <Message message={message} />
                </BoxTextMessage>
              </div>
            )}
            onFloatCommingMessageRender={(message) => {
              const messageType = messageTypeByContent(message.content);
              return (
                <span>
                  {(messageType === "text" && message?.content?.text) ||
                    (messageType === "image" && "Vừa nhận một ảnh mới") ||
                    (messageType === "video" && "Vừa nhận một video mới") ||
                    (messageType === "file" && "Vừa nhận một tập tin mới")}
                </span>
              );
            }}
            shouldFloatMessage={(message) => message.creatorType === "customer"}
            onLoadMore={loadmoreConversationMessage}
            diffMessage={(prevMessage, nextMessage) =>
              prevMessage._id !== nextMessage._id
            }
          />
        )}
      </div>
      <ChatInput
        register={register}
        onSendMessage={handleSendMessage}
        onUploadFileError={({ message }) =>
          alert({ title: "Cảnh báo", contents: message, okLabel: "Ok" })
        }
      />
    </>
  );
};

export default MessageBox;
