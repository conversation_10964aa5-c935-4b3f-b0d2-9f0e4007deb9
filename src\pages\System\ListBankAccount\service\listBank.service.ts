import crmDriverV1 from "services/crm/crm-driver-v1";
import {
  BankDto,
  GetListBankWithoutPaginationResponseDto,
} from "../dto/bank.dto";

const listBankService = {
  deleteBankAccount: (bankAccountId: number) => {
    return crmDriverV1.delete(
      `/locations/external/bank-accounts/${bankAccountId}`
    );
  },

  getListBankWithPagination: (): Promise<{
    data: GetListBankWithoutPaginationResponseDto;
  }> => {
    return crmDriverV1.get("/locations/external/bank-accounts");
  },
};
export default listBankService;
