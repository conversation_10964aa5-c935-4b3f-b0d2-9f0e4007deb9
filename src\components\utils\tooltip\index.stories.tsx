import { Story, Meta } from "@storybook/react/types-6-0";

import imgIconInfo from "assets/images/icons/info.svg";

import { Tooltip, Props } from ".";

export default {
  title: "Components|utils/Tooltip",
  component: Tooltip,
} as Meta;

const Template: Story<Props> = ({ id, description, place, children }) => (
  <div style={{ margin: 200 }}>
    <Tooltip id={id} description={description} place={place}>
      {children}
    </Tooltip>
  </div>
);

export const Normal = Template.bind({});

Normal.args = {
  id: "id",
  description: (
    <div>
      <p><PERSON><PERSON>, ipsum dolor sit amet consectetur adipisicing elit.</p>
      <p>
        Reiciendis, quaerat! Voluptates nulla alias modi fuga quasi reiciendis.
      </p>
    </div>
  ),
  place: "bottom",
  children: <img src={imgIconInfo} alt="" />,
};
