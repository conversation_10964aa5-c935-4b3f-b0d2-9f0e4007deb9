/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useMemo } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import * as navigationHelper from "helpers/navigation";
import { useAsync } from "hooks/useAsync";
import useInfinity from "hooks/useInfinity";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { mapFrom } from "libs/adapters/dto";
import {
  orderReturnStatusOption,
  updateReturnOrderStatusDto,
  UpdateReturnOrderStatusDtoType,
  orderReturnStatusDetailDto,
  OrderReturnStatusDetailDtoType,
} from "modules/order";
import {
  getOrderReturnStatus,
  getReturnOrderStatusDetail,
  updateOrderReturnStatus,
} from "services/crm/order";

interface ReturnStatusDetailPageVmProps {
  returnOrderStatusId: number;
}

export const ReturnStatusDetailPageVm = ({
  returnOrderStatusId,
}: ReturnStatusDetailPageVmProps) => {
  const [getReturnStatusExec, getReturnOrderState] = useAsync(
    useCallback(
      (params: { returnOrderStatusId: number }) =>
        getReturnOrderStatusDetail({ ...params }).then((res) =>
          orderReturnStatusDetailDto(res.data.data)
        ),
      []
    )
  );

  const [updateOrderReturnStatusExec, updateOrderReturnStatusState] = useAsync(
    updateOrderReturnStatus,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Cập nhật thành công" });
        navigationHelper.goBack();
      }, []),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const returnOrderStatusDetail = getReturnOrderState.data;

  const orderReturnStatusOptions = useMemo(
    () =>
      mapFrom(
        returnOrderStatusDetail?.prevReturnOrderStatus || [],
        orderReturnStatusOption
      ),
    [returnOrderStatusDetail?.prevReturnOrderStatus]
  );

  const handleUpdateOrderReturnStatus = useCallback(
    (rawPayload: Partial<UpdateReturnOrderStatusDtoType>) => {
      if (!returnOrderStatusDetail?.returnOrderStatusId) return;
      const updateOrderReturnStatusPayload = updateReturnOrderStatusDto({
        ...returnOrderStatusDetail,
        ...rawPayload,
      });

      updateOrderReturnStatusExec(
        returnOrderStatusDetail.returnOrderStatusId,
        updateOrderReturnStatusPayload
      );
    },
    [returnOrderStatusDetail, updateOrderReturnStatusExec]
  );

  const {
    gotoFirstPage: getPrevOrderReturnStatus,
    goNextPage: loadMoreOrderReturnStatus,
    state: pulldownState,
  } = useInfinity<OrderReturnStatusDetailDtoType[]>(
    async (payload: { pageNum: number; pageSize: number }) =>
      getOrderReturnStatus({ ...payload }),
    {
      pageSize: 15,
    }
  );

  const { options: orderStatusOptionPulldown } = usePulldownHelper({
    dataSource: pulldownState.data || [],
    optionCreator: orderReturnStatusOption,
  });

  const handleLoadmoreOrderReturnStatus = async () => {
    loadMoreOrderReturnStatus();
  };

  useEffect(() => {
    getReturnStatusExec({ returnOrderStatusId });
    getPrevOrderReturnStatus();
  }, [returnOrderStatusId]);

  return {
    loading: getReturnOrderState.loading,
    returnOrderStatusDetail,
    orderReturnStatusOptions,
    updateOrderReturnStatusState,
    handleUpdateOrderReturnStatus,
    orderStatusOptionPulldown,
    handleLoadmoreOrderReturnStatus,
  };
};
