import crmDriverV1 from "services/crm/crm-driver-v1";
import {
  GroupCustomerDto,
  GroupCustomerGetPage,
} from "../GroupCustomerDetail/dto/groupCustomer.dto";

const groupCustomerService = {
  // Define your service methods here
  getGroupCustomerById: async (groupCustomerId) => {
    // Fetch group customer by ID logic
    const url = `customers/external/group-customers/${groupCustomerId}`;
    return crmDriverV1.get(url);
  },
  createGroupCustomer: async (data: GroupCustomerDto) => {
    // Create new group customer logic
    const url = "customers/external/group-customers";
    return crmDriverV1.post(url, data);
  },
  updateGroupCustomer: async (groupCustomerId, data) => {
    // Update existing group customer logic
    const url = `customers/external/group-customers/${groupCustomerId}`;
    return crmDriverV1.put(url, data);
  },
  deleteGroupCustomer: async (groupCustomerId: number) => {
    // Delete group customer logic
    const url = `customers/external/group-customers/${groupCustomerId}`;
    return crmDriverV1.delete(url);
  },
  getGroupCustomers: async (params: GroupCustomerGetPage) => {
    // Fetch list of group customers logic
    const url = "customers/external/group-customers";
    return crmDriverV1.get(url, { params });
  },

  getAllEmployee: async () => {
    const url = "employees/external/profile/findAllEmployees";
    return crmDriverV1.get(url);
  },
};
export default groupCustomerService;
