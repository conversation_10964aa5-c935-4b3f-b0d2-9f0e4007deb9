import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusSquareOutlined,
} from "@ant-design/icons";
import { Card, Button, Collapse, CollapseProps, Space, Tooltip } from "antd";
import { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";

// import { But<PERSON> } from "components/atoms/button";
import { ButtonFilter } from "components/atoms/buttonfilter";
import { Checkbox } from "components/atoms/checkbox";
import { Divider } from "components/atoms/divider";
import { Heading } from "components/atoms/heading";
import { Pulldown, PulldownHookForm } from "components/atoms/pulldown";
import { Tag } from "components/atoms/tag";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Pagination } from "components/molecules/pagination";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Table, Tbody, Td, Th, Thead, Tr } from "components/organisms/table";
import { General } from "components/pages/general";
import { FormContainer } from "helpers/form";
import { CreateTicketModal } from "modules/system";
import PaginationSection from "pages/Common/paginationSection";
import TableManipulation from "pages/Common/tableManipulation";

import RootPageRouter from "../index";
import { CreateTikcetValidation } from "./constant";
import { ListTicketPageVm } from "./vm";
import "./index.css";

const ListTicketPage = () => {
  const {
    isOpenFormFilter,
    toggleFilterForm,
    handleOpenModalByType,
    handleOnCloseModal,
    modalTypeIsOpen,
  } = ListTicketPageVm();
  const items: CollapseProps["items"] = [
    {
      key: "1",
      label: "Lọc dữ liệu",
      children: (
        <>
          <FormContainer validationSchema={{}}>
            <Row>
              <Col md="6" lg="3" className="u-mb-15 u-mb-lg-0">
                <Formfield label="Mã ticket" name="ticketId">
                  <TextfieldHookForm name="ticketId" />
                </Formfield>
              </Col>
              <Col md="6" lg="3" className="u-mb-15 u-mb-lg-0">
                <Formfield label="Trạng thái" name="status">
                  <PulldownHookForm name="status" />
                </Formfield>
              </Col>
              <Col md="6" lg="3">
                <Formfield label="Độ ưu tiên" name="priority">
                  <PulldownHookForm name="priority" />
                </Formfield>
              </Col>
              <Col
                md="6"
                lg="3"
                className="d-flex align-items-end u-mt-15 u-mt-md-15"
              >
                <div className="d-flex flex-md-column">
                  <div className="u-mr-20 u-mr-md-0">
                    <Checkbox name="isMyTicket">Ticket của tôi</Checkbox>
                  </div>
                  <Checkbox name="isOverTime">Quá hạn</Checkbox>
                </div>
              </Col>
              <Col lg="9" className="u-mb-15" />
              <Col lg="3" className="u-mb-15 flexend">
                <div className="d-flex justify-content-center u-mt-20">
                  <Button
                    danger
                    size="large"
                    // buttonType="outline"
                    // modifiers="secondary"
                    onClick={toggleFilterForm}
                    type="primary"
                  >
                    Đóng
                  </Button>
                  <div className="u-ml-20">
                    <Button htmlType="submit" type="primary" size="large">
                      Tìm kiếm
                    </Button>
                  </div>
                </div>
              </Col>
            </Row>
          </FormContainer>
          <Section>
            <Divider type="dash" />
          </Section>
        </>
      ),
    },
  ];

  const columnItems: ColumnsType<Record<string, unknown>> = [
    {
      title: "STT",
      key: "index",
      width: 60,
      align: "center",
      fixed: "left",
      render: (_, __, index) => index + 1,
    },
    {
      title: "Mã ticket",
      dataIndex: "ticketId",
      key: "ticketId",
      align: "center",
      width: 120,
    },
    {
      title: "Tiêu đề",
      dataIndex: "title",
      key: "title",
      align: "center",
      width: 200,
    },
    {
      title: "Độ ưu tiên",
      dataIndex: "priority",
      key: "priority",
      align: "center",
      width: 120,
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      align: "center",
      width: 120,
    },
    {
      title: "Người tạo",
      dataIndex: "createdBy",
      key: "createdBy",
      align: "center",
      width: 120,
    },
    {
      title: "Phân công",
      dataIndex: "assignedTo",
      key: "assignedTo",
      align: "center",
      width: 120,
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdDate",
      key: "createdDate",
      align: "center",
      width: 120,
      render: (date) => dayjs(date).format("DD/MM/YYYY HH:mm"),
    },
    {
      title: "Ngày kết thúc",
      dataIndex: "endDate",
      key: "endDate",
      align: "center",
      width: 120,
      render: (date) => dayjs(date).format("DD/MM/YYYY HH:mm"),
    },
    {
      title: "Thao tác",
      key: "actions",
      align: "center",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="Xem chi tiết">
            <Button
              type="primary"
              icon={<EyeOutlined rev={undefined} />}
              size="small"
              // onClick={() => gotoDetailTicketPage(record)}
            />
          </Tooltip>
          <Tooltip title="Chỉnh sửa">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              // onClick={() => gotoEditTicketPage(record)}
            />
          </Tooltip>
          <Tooltip title="Xóa">
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              size="small"
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <General>
      <title key="title">Danh sách ticket</title>
      <Section>
        {/* <Heading type="h1" modifiers="primary">
          DANH SÁCH TICKET
        </Heading> */}
        <Card
          title="Danh sách ticket"
          extra={
            <Button
              type="primary"
              size="large"
              icon={
                <div>
                  <PlusSquareOutlined />
                </div>
              }
              onClick={() => handleOpenModalByType("createTicket")}
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              Tạo mới
            </Button>
          }
          style={{ marginLeft: "2.5%", marginRight: "2.5%" }}
        >
          <Section>
            <div className="backgroundW">
              <Collapse accordion items={items} />
            </div>
          </Section>
          <Section>
            <Table scroll={{ x: 1800, y: 610 }}>
              <Thead>
                <Tr>
                  <Th colSpan={1} modifiers="center" stickyLeft>
                    STT
                  </Th>
                  <Th colSpan={2} modifiers="center" isSortable>
                    Mã ticket
                  </Th>
                  <Th colSpan={2} modifiers="center">
                    Tiêu đề
                  </Th>
                  <Th colSpan={2} modifiers="center">
                    Độ ưu tiên
                  </Th>
                  <Th colSpan={2} modifiers="center">
                    Trạng thái
                  </Th>
                  <Th colSpan={2} modifiers="center">
                    Người tạo
                  </Th>
                  <Th colSpan={2} modifiers="center">
                    Phân công
                  </Th>
                  <Th colSpan={2} modifiers="center" isSortable>
                    Ngày tạo
                  </Th>
                  <Th colSpan={2} modifiers="center" isSortable>
                    Ngày kết thúc
                  </Th>
                  <Th colSpan={2} modifiers="center" stickyRight>
                    Thao tác
                  </Th>
                </Tr>
              </Thead>
              <Tbody>
                {Array(10)
                  .fill(0)
                  .map((_, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <Tr key={index}>
                      <Td modifiers="center" stickyLeft>
                        {index + 1}
                      </Td>
                      <Td colSpan={2} modifiers="center">
                        1003
                      </Td>
                      <Td colSpan={2} modifiers="center">
                        Cần hỗ trợ đổi size
                      </Td>
                      <Td colSpan={2} modifiers="center">
                        <Tag color="#FF6568">Cao</Tag>
                      </Td>
                      <Td colSpan={2} modifiers="center">
                        <Tag color="#0BBA99">Chưa xử lý</Tag>
                      </Td>
                      <Td colSpan={2} modifiers="center">
                        Vy Nguyễn
                      </Td>
                      <Td colSpan={2} modifiers="center">
                        Team CSKH
                      </Td>
                      <Td colSpan={2} modifiers="center">
                        22/02/2021 17:09
                      </Td>
                      <Td colSpan={2} modifiers="center">
                        22/02/2021 17:09
                      </Td>
                      <Td colSpan={2} modifiers="center" stickyRight>
                        <TableManipulation
                          infoAction={{
                            id: `info`,
                            action: () =>
                              RootPageRouter.gotoChild("listTicket", {}),
                          }}
                          editAction={{
                            id: `edit`,
                            action: () =>
                              RootPageRouter.gotoChild("listTicket", {}),
                          }}
                        />
                      </Td>
                    </Tr>
                  ))}
              </Tbody>
            </Table>
          </Section>
          <Section>
            <PaginationSection
              appearanceOption={
                <Pulldown
                  placeholder="Số lượng hiển thị"
                  options={[5, 10, 15, 25, 30].map((size) => ({
                    label: `${size}`,
                    value: `${size}`,
                  }))}
                />
              }
              paginateOption={
                <Pagination
                  modifiers="center"
                  total={5}
                  pageCount={5}
                  onPageChange={() => {}}
                  defaultCurrentPage={1}
                />
              }
            />
          </Section>
        </Card>
      </Section>
      {/* <Section>
        <div className="d-flex justify-content-end">
          <div className="u-mr-12">
            <ButtonFilter onClick={toggleFilterForm} />
          </div>
          <Button onClick={() => handleOpenModalByType("createTicket")}>
            Tạo mới
          </Button>
        </div>
      </Section> */}
      {/* <Section>
        {isOpenFormFilter && (
          <>
            <FormContainer validationSchema={{}}>
              <Row>
                <Col md="6" lg="3" className="u-mb-15 u-mb-lg-0">
                  <Formfield label="Mã ticket" name="ticketId">
                    <TextfieldHookForm name="ticketId" />
                  </Formfield>
                </Col>
                <Col md="6" lg="3" className="u-mb-15 u-mb-lg-0">
                  <Formfield label="Trạng thái" name="status">
                    <PulldownHookForm name="status" />
                  </Formfield>
                </Col>
                <Col md="6" lg="3">
                  <Formfield label="Độ ưu tiên" name="priority">
                    <PulldownHookForm name="priority" />
                  </Formfield>
                </Col>
                <Col
                  md="6"
                  lg="3"
                  className="d-flex align-items-end u-mt-15 u-mt-md-15"
                >
                  <div className="d-flex flex-md-column">
                    <div className="u-mr-20 u-mr-md-0">
                      <Checkbox name="isMyTicket">Ticket của tôi</Checkbox>
                    </div>
                    <Checkbox name="isOverTime">Quá hạn</Checkbox>
                  </div>
                </Col>
                <Col lg="12">
                  <div className="d-flex justify-content-center u-mt-20">
                    <Button
                      // buttonType="outline"
                      // modifiers="secondary"
                      onClick={toggleFilterForm}
                    >
                      Đóng
                    </Button>
                    <div className="u-ml-20">
                      <Button htmlType="submit">Tìm kiếm</Button>
                    </div>
                  </div>
                </Col>
              </Row>
            </FormContainer>
            <Section>
              <Divider type="dash" />
            </Section>
          </>
        )}
      </Section> */}
      {/* <Section>
        <Table scroll={{ x: 1800, y: 610 }}>
          <Thead>
            <Tr>
              <Th colSpan={1} modifiers="center" stickyLeft>
                STT
              </Th>
              <Th colSpan={2} modifiers="center" isSortable>
                Mã ticket
              </Th>
              <Th colSpan={2} modifiers="center">
                Tiêu đề
              </Th>
              <Th colSpan={2} modifiers="center">
                Độ ưu tiên
              </Th>
              <Th colSpan={2} modifiers="center">
                Trạng thái
              </Th>
              <Th colSpan={2} modifiers="center">
                Người tạo
              </Th>
              <Th colSpan={2} modifiers="center">
                Phân công
              </Th>
              <Th colSpan={2} modifiers="center" isSortable>
                Ngày tạo
              </Th>
              <Th colSpan={2} modifiers="center" isSortable>
                Ngày kết thúc
              </Th>
              <Th colSpan={2} modifiers="center" stickyRight>
                Thao tác
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {Array(10)
              .fill(0)
              .map((_, index) => (
                // eslint-disable-next-line react/no-array-index-key
                <Tr key={index}>
                  <Td modifiers="center" stickyLeft>
                    {index + 1}
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    1003
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    Cần hỗ trợ đổi size
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    <Tag color="#FF6568">Cao</Tag>
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    <Tag color="#0BBA99">Chưa xử lý</Tag>
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    Vy Nguyễn
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    Team CSKH
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    22/02/2021 17:09
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    22/02/2021 17:09
                  </Td>
                  <Td colSpan={2} modifiers="center" stickyRight>
                    <TableManipulation
                      infoAction={{
                        id: `info`,
                        action: () =>
                          RootPageRouter.gotoChild("listTicket", {}),
                      }}
                      editAction={{
                        id: `edit`,
                        action: () =>
                          RootPageRouter.gotoChild("listTicket", {}),
                      }}
                    />
                  </Td>
                </Tr>
              ))}
          </Tbody>
        </Table>
      </Section> */}
      {/* <Section>
        <PaginationSection
          appearanceOption={
            <Pulldown
              placeholder="Số lượng hiển thị"
              options={[5, 10, 15, 25, 30].map((size) => ({
                label: `${size}`,
                value: `${size}`,
              }))}
            />
          }
          paginateOption={
            <Pagination
              modifiers="center"
              total={5}
              pageCount={5}
              onPageChange={() => {}}
              defaultCurrentPage={1}
            />
          }
        />
      </Section> */}
      <CreateTicketModal
        open={modalTypeIsOpen("createTicket")}
        onClose={handleOnCloseModal}
        CreateTikcetValidation={CreateTikcetValidation}
      />
    </General>
  );
};
export default ListTicketPage;
