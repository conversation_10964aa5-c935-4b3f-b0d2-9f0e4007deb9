import { useState, useCallback } from "react";

import produce from "immer";
import RootPageRouter from "..";

type ModalType = "createTicket";

interface State {
  modalState: {
    open: boolean;
    type?: ModalType;
  };
  filterState: {
    isFiltering: boolean;
  };
}

export const ListTicketPageVm = () => {
  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      type: undefined,
    },
    filterState: {
      isFiltering: false,
    },
  });

  const toggleFilterForm = useCallback(() => {
    setState(
      produce((draft) => {
        draft.filterState.isFiltering = !draft.filterState.isFiltering;
      })
    );
  }, []);

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.type = type;
      })
    );
  }, []);

  const handleOnCloseModal = useCallback(() => {
    if (!state.modalState.open) return;
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.type = undefined;
      })
    );
  }, [state.modalState.open]);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) =>
      state.modalState.open && state.modalState.type === type,
    [state.modalState.open, state.modalState.type]
  );

  const handleGoToPage = useCallback(
    ({ ticketId, action }: { ticketId: number | string; action?: string }) =>
      RootPageRouter.gotoChild("ticketDetail", {
        params: { ticketId: ticketId.toString() },
        queryString: action ? `?action=${action}` : undefined,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  return {
    isOpenFormFilter: state.filterState.isFiltering,
    toggleFilterForm,
    handleOpenModalByType,
    handleOnCloseModal,
    modalTypeIsOpen,
    handleGoToPage,
  };
};
