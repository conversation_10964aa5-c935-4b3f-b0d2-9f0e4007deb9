import React from "react";

import { Image } from "components/atoms/image";
import { mapModifiers } from "helpers/component";
import {
  MessageCreatorType,
  MessageEntityType,
  messageTypeByContent,
} from "modules/chat";

type Modifiers = "selected";
export type MessagePropsType = Pick<
  MessageEntityType,
  "content" | "createdAt"
> & {
  creatorType: MessageCreatorType;
};
export interface Props {
  avatar: string;
  name: string;
  alt: string;
  message: MessagePropsType;
  onRenderContent?: (message: MessagePropsType) => React.ReactNode;
  onSelect?: () => void;
  modifiers?: Modifiers | Modifiers[];
}

export const RecentConversation: React.FC<Props> = ({
  avatar,
  name,
  message,
  alt,
  onSelect,
  modifiers,
  onRenderContent,
}) => {
  const messageType = messageTypeByContent(message.content);

  const handleRenderContentDefault = (content: React.ReactNode) => {
    if (onRenderContent) {
      return onRenderContent(message);
    }

    return content;
  };

  return (
    <div
      aria-hidden
      className={mapModifiers("m-recentconversation", modifiers)}
      onClick={onSelect}
    >
      <div className="m-recentconversation_wrapimage">
        <Image aspectRatio="1x1" src={avatar} alt={alt} />
      </div>
      <div className="m-recentconversation_content">
        <span className="m-recentconversation_name">{name}</span>
        <span className="m-recentconversation_message">
          {message.creatorType === "customer" ? `${name}: ` : `Bạn: `}
          {messageType === "text" &&
            handleRenderContentDefault(message.content.text)}
          {messageType === "image" &&
            handleRenderContentDefault("Đã gửi một hình ảnh")}
          {messageType === "video" &&
            handleRenderContentDefault("Đã gửi một video")}
          {messageType === "file" &&
            handleRenderContentDefault("Đã gửi một tập tin")}
        </span>
      </div>
    </div>
  );
};
