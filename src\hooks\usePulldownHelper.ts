import { useCallback, useMemo } from "react";

import { mapFrom } from "libs/adapters/dto";

export interface PulldownHelperHookProps<Item, ValueType> {
  dataSource: Item[];
  optionCreator: (item: Item) => { label: string; value: string };
  valueTrans?: (value: string) => ValueType;
}

export const usePulldownHelper = <Item, ValueType>({
  dataSource,
  optionCreator,
  valueTrans,
}: PulldownHelperHookProps<Item, ValueType>) => {
  const options = useMemo(
    () =>
      mapFrom(dataSource || [], optionCreator).map(({ value, label }) => ({
        value: value || "",
        label: label || "",
      })),
    [dataSource, optionCreator]
  );

  const getOptionByValue = useCallback(
    (optionValue?: ValueType) =>
      optionValue &&
      options.find(
        ({ value }) => (valueTrans ? valueTrans(value) : value) === optionValue
      ),
    [options, valueTrans]
  );

  const getOptionDataByValue = useCallback(
    (pulldownValue?: string) =>
      pulldownValue
        ? dataSource.find((optionDataSource) => {
            const { value } = optionCreator(optionDataSource);
            if (value === pulldownValue) {
              return optionDataSource;
            }
            return undefined;
          })
        : undefined,
    [dataSource, optionCreator]
  );

  const formatOption = (payload: Item) => optionCreator(payload);

  return {
    options,
    getOptionByValue,
    formatOption,
    getOptionDataByValue,
  };
};
