/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useState } from "react";
import { debounce } from "lodash";
import { useAsync } from "hooks/useAsync";
import crmDriverV1 from "services/crm/crm-driver-v1";

export interface GetListMasterDataTicketDto {
  pageNum: number;
  pageSize: number;
  searchText: string;
}

export const useGetListMasterDataTicket = () => {
  const [refetch, data] = useAsync(
    useCallback(
      (dto: GetListMasterDataTicketDto) =>
        crmDriverV1.get(
          `media/external/master-data-tickets/list?pageNum=${dto.pageNum}&pageSize=${dto.pageSize}&searchText=${dto.searchText}`
        ),
      []
    )
  );

  return {
    data: data?.data?.data,
    refetch,
  };
};

export const useInfinityMasterDataTicket = ({
  pageSize = 10,
  searchText = "",
  enabled = true,
}: {
  pageSize?: number;
  searchText?: string;
  enabled?: boolean;
}) => {
  const [page, setPage] = useState(1);
  const [data, setData] = useState<any[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [debouncedSearchText, setDebouncedSearchText] = useState<string>("");

  const fetchData = useCallback(async () => {
    if (!enabled || loading || !hasMore) return;

    setLoading(true);
    try {
      const response = await crmDriverV1.get(
        `media/external/master-data-tickets/list?pageNum=${page}&pageSize=${pageSize}&searchText=${debouncedSearchText}`
      );
      const newItems = response.data?.data || [];
      const total = response?.data?.meta?.totalRecords || 0;

      setData((prev) => [...prev, ...newItems]);
      setTotalRecords(total);
      setHasMore((prev) => page * pageSize < total);
    } catch (err) {
      console.error("Failed to fetch status tickets:", err);
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, debouncedSearchText, enabled, loading, hasMore]);

  useEffect(() => {
    const handler = debounce(() => {
      const trimmed = searchText.trim();
      setDebouncedSearchText(trimmed);
    }, 400);

    handler();
    return () => {
      handler.cancel();
    };
  }, [searchText]);

  useEffect(() => {
    setData([]);
    setPage(1);
    setHasMore(true);
  }, [searchText, pageSize]);

  useEffect(() => {
    if (enabled) fetchData();
  }, [page, debouncedSearchText]);

  const loadMore = () => {
    if (!loading && hasMore) {
      setPage((prev) => prev + 1);
    }
  };

  return {
    data,
    loading,
    hasMore,
    totalRecords,
    loadMore,
    reset: () => {
      if (page !== 1) {
        setData([]);
      }
      setPage(1);
      setHasMore(true);
    },
  };
};

export const getListMasterDataTicket = (dto: {
  pageNum: number;
  pageSize: number;
  searchText?: string;
}) => {
  return crmDriverV1.get(
    `media/external/master-data-tickets/list?pageNum=${dto.pageNum}&pageSize=${dto.pageSize}&searchText=${dto.searchText}`
  );
};
