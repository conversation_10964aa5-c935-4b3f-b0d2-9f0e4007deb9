import React from "react";

import { <PERSON>, Meta } from "@storybook/react/types-6-0";

import { Image, Props } from ".";

export default {
  title: "Components|atoms/Image",
  component: Image,
} as Meta;

const Template: Story<Props> = ({ src, alt, aspectRatio, modifiers, lazy }) => (
  <Image
    src={src}
    alt={alt}
    aspectRatio={aspectRatio}
    modifiers={modifiers}
    lazy={lazy}
  />
);

export const Normal = Template.bind({});

Normal.args = {
  src: "http://via.placeholder.com/1000x1000&text=cardDummy",
  alt: "NameImage",
  aspectRatio: "1x1",
  lazy: false,
  modifiers: undefined,
};
