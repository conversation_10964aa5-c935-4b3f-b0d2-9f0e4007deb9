import React, { useMemo } from "react";

import { mapModifiers } from "helpers/component";

export type HeadingType = "h1" | "h2" | "h3" | "h4";
type Modifiers = "primary" | "uppercase";

export interface Props {
  modifiers?: Modifiers | Modifiers[];
  type?: HeadingType;
  centered?: true;
  children?: React.ReactNode;
}

export const Heading: React.FC<Props> = ({
  children,
  type,
  centered,
  modifiers,
}) => {
  const renderHeadingText = useMemo(() => {
    switch (type) {
      case "h1":
        return <h1 className="a-heading_text">{children}</h1>;
      case "h2":
        return <h2 className="a-heading_text">{children}</h2>;
      case "h3":
        return <h3 className="a-heading_text">{children}</h3>;
      case "h4":
        return <h4 className="a-heading_text">{children}</h4>;
      default:
        return null;
    }
  }, [type, children]);

  return (
    <div
      className={mapModifiers(
        "a-heading",
        `type${type}`,
        centered && "centered",
        modifiers
      )}
    >
      <div className="a-heading_text">{renderHeadingText}</div>
    </div>
  );
};

Heading.defaultProps = {
  type: "h2",
};
