@import "assets/scss/_variables.scss";

.post {
	margin: 1em 0;
	background-color: rgb(255, 255, 255);
	border: 1px solid #e3e3e3;
	padding: 0.7em;
}

.header {
	display: flex;
	height: 3.5em;
	margin-bottom: 1em;

	&_avatar {
		border-radius: 50%;
		overflow: hidden;
		width: 3.5em;
		height: 3.5em;
		min-width: 3.5em;
		min-height: 3.5em;
	}

	&_content {
		padding-left: 1em;
		flex-grow: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;

		&_title {
			font-size: 1.1em;
			font-weight: 500;
			&:hover {
				text-decoration: underline;
				cursor: pointer;
				color: $COLOR-DPT-BLUE;
			}
		}

		&_time {
			font-size: 0.9em;
		}
	}
}

.content {
	height: 3em;
}

.footer {
	display: flex;
	flex-direction: column;
	&_stat {
		font-size: 0.9em;
	}
	&_action {
		display: block;
	}
	&_btn {
		position: relative;
		display: flex;
		font-size: 14px;
		margin-top: 1em;
		border: 0;
		height: 2.5em;
		border-radius: 0.6em;
		background-color: transparent;
		&::after {
			content: "";
			position: absolute;
			top: -0.5em;
			left: 10%;
			width: 80%;
			border-top: 1px solid #e3e3e3;
		}
		& .btn {
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 14px;
			margin: 0 0.2em;
			border: 0;
			width: 8em;
			border-radius: 0.6em;
			background-color: transparent;
			transition: background-color 0.2s ease-out;
			& .icon_like {
				background-image: url("~assets/images/icons/post-like.png");
				width: 1.4em;
				height: 1.4em;
				background-size: contain;
				background-repeat: no-repeat;
			}
			& .icon_comment {
				background-image: url("~assets/images/icons/post-comment.png");
				width: 1.4em;
				height: 1.4em;
				background-size: contain;
				background-repeat: no-repeat;
			}
			& .text {
				font-weight: 500;
				padding-left: 0.5em;
			}
			&:hover {
				background-color: #e1e1e1;
			}
		}
	}
	&_comment {
		display: flex;
		position: relative;
		margin-top: 1em;
		&::after {
			content: "";
			position: absolute;
			top: -0.5em;
			left: 10%;
			width: 80%;
			border-top: 1px solid #e3e3e3;
		}
		& .avatar {
			border-radius: 50%;
			overflow: hidden;
			width: 2.5em;
		}
		& .input {
			flex-grow: 1;
			margin-left: 1em;

			& input {
				height: 100%;
				border-radius: 1em;
				width: 100%;
				border: none;
				padding: 0 1em;
				background-color: #e3e3e3;

				&:focus-visible {
					outline: none;
				}
			}
		}
	}
	& .comment {
		display: flex;
		position: relative;
		margin-top: 0.5em;
		&_avatar {
			border-radius: 50%;
			overflow: hidden;
			width: 2.5em;
			height: 2.5em;
			min-width: 2.5em;
			min-height: 2.5em;
		}
		&_content {
			display: flex;
			flex-direction: column;
			margin-left: 1em;
			border-radius: 0.7em;
			padding: 0.5em 0.7em;
			background-color: #e3e3e3;
			& .title {
				font-weight: 500;
				& .time {
					font-weight: 400;
					font-size: 0.9em;
					color: grey;
				}
			}
			& .text {
			}
		}
	}
	& .comment_load {
		text-align: left;
		color: #1c86c8;
		font-weight: 500;
		border: 0;
		background-color: transparent;
		&:hover {
			text-decoration: underline;
		}
	}
}
