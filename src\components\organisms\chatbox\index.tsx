/* eslint-disable no-underscore-dangle */
import React, {
  CSSProperties,
  ReactElement,
  ReactNode,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";

// eslint-disable-next-line react/no-deprecated
import { render } from "react-dom";

import {
  AutoSizer,
  List,
  CellMeasurerCache,
  CellMeasurer,
  InfiniteLoader,
  Grid,
} from "components/molecules/list";
import useDerivedStateFromProps from "helpers/react-hooks/useDerivedStateFromProps";
import useDidMount from "helpers/react-hooks/useDidMount";

import { executeAsyncTask, sIdCreator } from "./helper";

export interface Props<T> {
  hasMore?: boolean;
  threshold?: number;
  messages: T[];
  onMessageRender: (messageData: T) => ReactNode;
  onFloatCommingMessageRender?: (messageData: T) => ReactElement;
  shouldFloatMessage?: (messageData: T) => boolean;
  loadMorePending?: boolean;
  onLoadMore?: () => void;
  diffMessage?: (prevMessage: T, nextMessage: T) => boolean;
}

interface ListExtra extends List {
  Grid: Grid & {
    _rowStartIndex: number;
    _rowStopIndex: number;
    _renderedRowStartIndex: number;
    _renderedRowStopIndex: number;
    _scrollingContainer: HTMLDivElement;
  };
}

interface CellProps {
  style: CSSProperties;
  onLoaded?: (element: HTMLDivElement) => void;
}

const Cell: React.FC<React.PropsWithChildren<CellProps>> = ({
  style,
  children,
  onLoaded,
}) => {
  const rowRef = useRef<HTMLDivElement>(null);

  useDidMount(() => {
    executeAsyncTask(() => {
      if (rowRef.current && onLoaded) {
        onLoaded(rowRef.current);
      }
    });
  });

  return (
    <div style={style} ref={rowRef}>
      <div className="o-chatbox-cell">{children}</div>
    </div>
  );
};

export const Chatbox = <T,>({
  hasMore = true,
  threshold = 1,
  messages,
  onMessageRender,
  onFloatCommingMessageRender,
  shouldFloatMessage,
  loadMorePending,
  onLoadMore,
  diffMessage,
}: Props<T>) => {
  const [forceRenderState, forceRender] = useState({});
  const cellCacheRef = useRef(new CellMeasurerCache({ defaultHeight: 109 }));
  const listRef = useRef<ListExtra | null>();
  const scrollBottomElementRef = useRef<HTMLDivElement>(null);
  const shouldDisableLoadmoreRef = useRef<{ sid: string; disable: boolean }>();
  const queueEventAfterEffect = useRef<Function[]>([]);
  const prevMessageDataRef = useRef<T[]>();
  const floatMessageElementRef = useRef<HTMLDivElement>(null);
  const forceScrollIntoIndexRef =
    useRef<number | undefined | "last">(undefined);

  const setScrollBottomStickyVisible = useCallback((visible: boolean) => {
    const scrollBottomElement = scrollBottomElementRef.current;
    if (!scrollBottomElement) return;
    if (!visible) {
      if (scrollBottomElement) {
        Object.assign(scrollBottomElement.style, {
          display: "none",
        });
      }
    } else {
      Object.assign(scrollBottomElement.style, {
        display: "initial",
      });
    }
  }, []);

  const allowLoadmoreAction = () =>
    hasMore && !loadMorePending && !shouldDisableLoadmoreRef.current?.disable;

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const needShowScrollBottomSticky = useCallback(() => {
    const listInstance = listRef.current;
    const scrollBottomElement = scrollBottomElementRef.current;
    if (!listInstance || !scrollBottomElement) return false;
    const {
      _renderedRowStopIndex: renderedRowStopIndex,
      _renderedRowStartIndex: renderedRowStartIndex,
      _scrollingContainer: scrollingContainer,
    } = listInstance.Grid;
    const { length: dataSize } = messages;

    let needShow = true;
    if (dataSize === 0) needShow = false;
    if (renderedRowStartIndex === 0 && renderedRowStopIndex === 0)
      needShow = false;
    if (renderedRowStopIndex === dataSize - 1) needShow = false;

    const scrollStateAtBottom =
      scrollingContainer.scrollTop + scrollingContainer.clientHeight ===
      scrollingContainer.scrollHeight;

    if (scrollStateAtBottom) needShow = false;
    if (
      scrollingContainer.scrollTop === 0 &&
      scrollingContainer.scrollHeight !== scrollingContainer.clientHeight
    )
      needShow = true;

    return needShow;
  }, [messages]);

  const handleOnScroll = () => {
    const needShow = needShowScrollBottomSticky();
    setScrollBottomStickyVisible(needShow);
    if (!needShow && floatMessageElementRef.current) {
      Object.assign(floatMessageElementRef.current.style, {
        display: "none",
      });
    }
  };

  const scrollIntoLastRow = (smooth: boolean) => {
    const list = listRef.current;
    if (!list) return;
    if (smooth) {
      executeAsyncTask(() => {
        const scrollingContainer = list.Grid?._scrollingContainer;
        if (scrollingContainer) {
          const scrollTop =
            scrollingContainer.scrollHeight - scrollingContainer.clientHeight;
          scrollingContainer.scrollTo({ top: scrollTop, behavior: "smooth" });
        }
      });
    } else {
      list.scrollToRow(messages.length - 1);
      forceScrollIntoIndexRef.current = "last";
      queueEventAfterEffect.current = [
        ...queueEventAfterEffect.current,
        () => {
          forceScrollIntoIndexRef.current = undefined;
        },
      ];
      forceRender({});
    }
  };

  useDerivedStateFromProps((prevMessage, nextMessage) => {
    if (!prevMessage || prevMessage.length === 0) {
      const sid = sIdCreator();
      shouldDisableLoadmoreRef.current = {
        sid,
        disable: true,
      };
      queueEventAfterEffect.current = [
        ...queueEventAfterEffect.current,
        () => {
          executeAsyncTask(() => {
            if (shouldDisableLoadmoreRef.current?.sid === sid) {
              shouldDisableLoadmoreRef.current.disable = false;
            }
          });
        },
      ];
    } else if (!needShowScrollBottomSticky()) {
      scrollIntoLastRow(true);
    }
  }, messages);

  const handleLoadMore = async () => {
    if (!allowLoadmoreAction()) return;

    if (onLoadMore) {
      onLoadMore();
    }
  };

  const [shouldRenderFloatMessage, commingMessages] = useMemo(() => {
    if (!prevMessageDataRef.current || !messages || messages.length === 0)
      return [false, []];

    const prevLastMessage =
      prevMessageDataRef.current[prevMessageDataRef.current.length - 1];
    const nextLastMessage = messages[messages.length - 1];

    if (!prevLastMessage || !nextLastMessage) return [false, []];

    let diff = false;
    if (diffMessage) {
      diff = diffMessage(prevLastMessage, nextLastMessage);
    } else {
      diff = prevLastMessage !== nextLastMessage;
    }
    if (diff && needShowScrollBottomSticky()) {
      return [true, messages.slice(prevMessageDataRef.current.length)];
    }

    return [false, []];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [messages]);

  const scrollIndexAfterAppendMessage = useMemo(() => {
    const prevMessageData = prevMessageDataRef.current;
    if (forceScrollIntoIndexRef.current === "last") return messages.length - 1;
    if (forceScrollIntoIndexRef.current) return forceScrollIntoIndexRef.current;
    const scrollTo =
      !prevMessageData || prevMessageData.length === 0
        ? messages.length - 1
        : -1;

    return scrollTo;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    messages,
    forceRenderState,
    loadMorePending,
    forceScrollIntoIndexRef.current,
  ]);

  const scrollIndexAfterLoadmore = useMemo(() => {
    const list = listRef.current;
    if (
      list &&
      prevMessageDataRef.current &&
      prevMessageDataRef.current.length !== 0 &&
      messages.length !== 0
    ) {
      const prevLastMessage =
        prevMessageDataRef.current[prevMessageDataRef.current.length - 1];
      const nextLastMessage = messages[messages.length - 1];

      let diff = false;
      if (diffMessage && prevLastMessage && nextLastMessage) {
        diff = diffMessage(prevLastMessage, nextLastMessage);
      } else {
        diff = prevLastMessage !== nextLastMessage;
      }

      if (!diff)
        return (
          messages.length -
          prevMessageDataRef.current.length -
          list.Grid._renderedRowStartIndex
        );
    }

    return -1;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [messages.length]);

  useLayoutEffect(() => {
    prevMessageDataRef.current = messages;
  }, [messages]);

  useEffect(() => {
    const needShow = needShowScrollBottomSticky();
    setScrollBottomStickyVisible(needShow);
  }, [needShowScrollBottomSticky, setScrollBottomStickyVisible]);

  useEffect(() => {
    if (
      shouldRenderFloatMessage &&
      onFloatCommingMessageRender &&
      commingMessages.length !== 0 &&
      floatMessageElementRef.current
    ) {
      let shouldFloat = true;
      const floatMessage = commingMessages[commingMessages.length - 1];
      if (shouldFloatMessage) shouldFloat = shouldFloatMessage(floatMessage);
      if (shouldFloat) {
        const floatElement = onFloatCommingMessageRender(floatMessage);
        Object.assign(floatMessageElementRef.current.style, {
          display: "initial",
        });
        render(floatElement, floatMessageElementRef.current);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldRenderFloatMessage, commingMessages]);

  useEffect(() => {
    queueEventAfterEffect.current.forEach((event) => event());
    queueEventAfterEffect.current = [];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queueEventAfterEffect.current]);

  return (
    <div className="o-chatbox">
      {loadMorePending && (
        <div className="o-chatbox-sticky o-chatbox-sticky_center o-chatbox-sticky_top">
          <div className="o-chatbox-spinner">
            <div className="o-chatbox-bounce_first" />
            <div className="o-chatbox-bounce_middle" />
            <div className="o-chatbox-bounce_last" />
          </div>
        </div>
      )}
      <InfiniteLoader
        isRowLoaded={({ index }) => {
          if (index > 0) {
            return true;
          }
          return false;
        }}
        loadMoreRows={handleLoadMore}
        rowCount={messages.length}
        threshold={threshold}
      >
        {({ registerChild, onRowsRendered }) => (
          <AutoSizer>
            {({ width, height }) => {
              return (
                <List
                  ref={(ref) => {
                    registerChild(ref);
                    listRef.current = ref as ListExtra;
                  }}
                  onRowsRendered={onRowsRendered}
                  deferredMeasurementCache={cellCacheRef.current}
                  height={height}
                  width={width}
                  rowCount={messages.length}
                  rowHeight={cellCacheRef.current.rowHeight}
                  onScroll={handleOnScroll}
                  scrollToIndex={
                    scrollIndexAfterAppendMessage !== -1
                      ? scrollIndexAfterAppendMessage
                      : scrollIndexAfterLoadmore
                  }
                  rowRenderer={({ index, key, parent, style }) => {
                    return (
                      <CellMeasurer
                        cache={cellCacheRef.current}
                        columnIndex={0}
                        key={key}
                        rowIndex={index}
                        parent={parent}
                      >
                        {({ registerChild: registerRow, measure }) => (
                          <Cell
                            style={style}
                            onLoaded={(element) => {
                              if (registerRow) {
                                registerRow(element);
                                measure();
                              }
                            }}
                          >
                            {onMessageRender(messages[index])}
                          </Cell>
                        )}
                      </CellMeasurer>
                    );
                  }}
                />
              );
            }}
          </AutoSizer>
        )}
      </InfiniteLoader>
      <div className="o-chatbox-float o-chatbox-sticky o-chatbox-sticky_center">
        <div className="o-chatbox-float_message" ref={floatMessageElementRef} />
      </div>
      <div
        aria-hidden
        ref={scrollBottomElementRef}
        onClick={() => scrollIntoLastRow(false)}
        className="o-chatbox-sticky o-chatbox-sticky_center o-chatbox-sticky_bottom"
      >
        <div className="o-chatbox-scrollbottom" />
      </div>
    </div>
  );
};
