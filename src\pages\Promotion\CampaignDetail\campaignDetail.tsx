/* eslint-disable react-hooks/rules-of-hooks */
import { useCallback, useEffect, useMemo, useState } from "react";
import { ArrowLeftOutlined, SaveOutlined } from "@ant-design/icons";
import { Card, Col, Form, Input, Row, Select } from "antd";

import { useLocation, useNavigate, useParams } from "react-router";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import BaseSelectGroupCustomer from "components/atoms/base/select/shared/BaseGroupCustomer";
import BaseSelectEmployee from "components/atoms/base/select/shared/BaseSelectEmploye";
import { showLoading } from "components/atoms/base/Spinner";
import { Heading } from "components/atoms/heading";
import { COLOR } from "constants/color";
import { UseGetAllEmployee } from "pages/Customer/hook/useGetAllCustomer";
import { UseGetGroupCustomers } from "pages/Customer/hook/useGetGroupCustomer";
import {
  UseCreateCampaign,
  UseGetCampaignById,
  UseUpdateCampaign,
} from "../hook/useCreateCampaign";
import { CampaignDto } from "./dto/campaign.dto";

interface CampaignDetailData {
  id?: number;
  campaignCode: string;
  campaignName: string;
  groupCustomer: number;
  assignedUser: number;
  activeStatus: number;
}

const { Option } = Select;

// Mock data for shops
const mockShops = [
  { id: 1, name: "Shop A" },
  { id: 2, name: "Shop B" },
  { id: 3, name: "Shop C" },
];

// Mock data for users
const mockUsers = [
  { id: 1, name: "Nguyễn Văn A" },
  { id: 2, name: "Trần Thị B" },
  { id: 3, name: "Lê Văn C" },
];

// Mock data for status
const mockStatus = [
  { value: 1, label: "Hoạt động", color: "#52c41a" },
  { value: 2, label: "Không hoạt động", color: "#ff4d4f" },
];

export default function CampaignDetail() {
  const [form] = Form.useForm();
  const { campaignId } = useParams<{ campaignId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const { createCampaignExec } = UseCreateCampaign();
  const { updateCampaignExec } = UseUpdateCampaign();
  const { getCampaignByIdExec } = UseGetCampaignById();

  const [groupCustomers, setGroupCustomers] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [campaignData, setCampaignData] =
    useState<CampaignDetailData | null>(null);
  const isAddNew = useMemo(() => {
    return campaignId === "isAddNew";
  }, [location.pathname, campaignId]);

  const isView = searchParams.get("action") === "view";

  const goBack = useCallback(() => {
    navigate(-1);
    // eslint-disable-next-line
  }, [navigate]);

  const handleSave = useCallback(
    async (value: CampaignDto) => {
      showLoading(true);
      if (isAddNew) {
        createCampaignExec(value)
          .then((res) => {
            if (res.status === 200) {
              showNotification({
                type: "success",
                message: "Tạo chiến dịch thành công",
              });
            }
            goBack();
          })
          .catch((error) => {
            console.error("Error creating campaign:", error);
            showNotification({
              type: "error",
              message:
                error?.response?.data?.errors?.[0]?.message ||
                "Tạo chiến dịch thất bại",
            });
          })
          .finally(() => {
            showLoading(false);
          });
        return;
      }
      updateCampaignExec(campaignId, value)
        .then((res) => {
          if (res.status === 200) {
            showNotification({
              type: "success",
              message: "Cập nhật chiến dịch thành công",
            });
          }
        })
        .catch((error) => {
          console.error("Error updating campaign:", error);
          showNotification({
            type: "error",
            message:
              error?.response?.data?.errors?.[0]?.message ||
              "Cập nhật chiến dịch thất bại",
          });
        })
        .finally(() => {
          showLoading(false);
        });
    },
    [campaignId, updateCampaignExec]
  );

  useEffect(() => {
    const getData = async () => {
      if (!isAddNew) {
        const result = await getCampaignByIdExec(campaignId);
        if (result.status === 200) {
          const {
            assignedUser,
            campaignCode,
            campaignName,
            groupCustomer,
            activeStatus,
          } = result.data?.data as CampaignDetailData;

          form.setFieldsValue({
            assignedUser,
            campaignCode,
            campaignName,
            groupCustomer,
            activeStatus,
          });
        }
      }
    };
    getData();
  }, []);

  return (
    <>
      <title>Chi tiết chiến dịch</title>
      <div className="p-4 flex flex-col gap-4">
        <div className="flex items-center justify-start gap-3">
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={goBack}
            icon={<ArrowLeftOutlined rev={undefined} />}
          />
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={() => {
              form.submit();
            }}
            disabled={isView && !isAddNew}
            icon={<SaveOutlined rev={undefined} />}
          >
            Lưu
          </BaseButton>
        </div>
        <Card title="Thông tin chiến dịch" className="shadow-md rounded-lg">
          <Form
            form={form}
            layout="vertical"
            disabled={!isAddNew && isView}
            onFinish={handleSave}
            initialValues={{
              status: "active",
            }}
          >
            <div style={{ marginBottom: 24 }}>
              <Heading type="h4">Thông tin chiến dịch</Heading>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="Mã chiến dịch"
                    name="campaignCode"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng nhập mã chiến dịch",
                      },
                    ]}
                  >
                    <Input placeholder="Nhập mã chiến dịch" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Tên chiến dịch"
                    name="campaignName"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng nhập tên chiến dịch",
                      },
                    ]}
                  >
                    <Input placeholder="Nhập tên chiến dịch" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="Nhóm khách hàng"
                    name="groupCustomer"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn nhóm khách hàng",
                      },
                    ]}
                  >
                    {/* <Select placeholder="Chọn nhóm khách hàng">
                      {mockShops.map((shop) => (
                        <Option key={shop.id} value={shop.id}>
                          {shop.name}
                        </Option>
                      ))}
                    </Select> */}

                    <BaseSelectGroupCustomer />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Người phụ trách"
                    name="assignedUser"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn người phụ trách",
                      },
                    ]}
                  >
                    {/* <Select placeholder="Chọn người phụ trách">
                      {mockUsers.map((user) => (
                        <Option key={user.id} value={user.id}>
                          {user.name}
                        </Option>
                      ))}
                    </Select> */}
                    <BaseSelectEmployee />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="Trạng thái hoạt động"
                    name="activeStatus"
                    rules={[
                      { required: true, message: "Vui lòng chọn trạng thái" },
                    ]}
                  >
                    <Select placeholder="Chọn trạng thái">
                      {mockStatus.map((status) => (
                        <Option key={status.value} value={status.value}>
                          {status.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </div>
          </Form>
        </Card>
      </div>
    </>
  );
}
