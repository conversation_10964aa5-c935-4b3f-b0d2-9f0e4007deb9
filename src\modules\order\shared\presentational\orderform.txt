 <Row>
              <Col xl="4" className="u-mb-20">
                <Heading type="h4">
                  KHÁCH HÀNG:{" "}
                  <span style={{ color: "rgb(22, 132, 185)" }}>
                    {state.controlledProperties.customer?.name}
                  </span>
                </Heading>

                <Section>
                  <Row>
                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Điện thoại" name="phone">
                        <Phonefield
                          name="phone"
                          disabled={viewMode}
                          value={
                            primaryPhone(state.controlledProperties.customer) ||
                            ""
                          }
                          onFocus={handleOnFocusCustomerPhoneInput}
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Email" name="email">
                        <Textfield
                          name="email"
                          disabled
                          value={
                            primaryEmail(state.controlledProperties.customer) ||
                            ""
                          }
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Họ Tên" name="fullname">
                        <Textfield
                          name="fullname"
                          disabled
                          value={state.controlledProperties.customer?.name}
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Ngày sinh" name="birthday">
                        <Calendar
                          disabled
                          value={
                            state.controlledProperties.customer?.birthDay &&
                            new Date(
                              state.controlledProperties.customer?.birthDay
                            )
                          }
                        />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Giới tính" name="sex">
                        <div className="d-flex align-items-center">
                          <div className="u-pr-13">
                            <Radio
                              onChange={bindSilent("gender")}
                              name="gender"
                              value="GENDER_MALE"
                              readOnly
                              disabled
                              checked={
                                state.controlledProperties.customer?.gender ===
                                1
                              }
                            >
                              Nam
                            </Radio>
                          </div>
                          <Radio
                            onChange={bindSilent("gender")}
                            name="gender"
                            value="GENDER_FEMALE"
                            disabled
                            readOnly
                            checked={
                              state.controlledProperties.customer?.gender === 0
                            }
                          >
                            Nữ
                          </Radio>
                        </div>
                      </Formfield>
                    </Col>

                    <Col xs="12" className="u-mb-15">
                      <Formfield label="Địa chỉ" name="address">
                        <PulldownHookForm
                          name="shippingAddress"
                          register={shippingAddressSelectRef}
                          isSearchable
                          options={customerShippingAddressOptions}
                          isDisabled={viewMode}
                          isLoading={fetchCustomerShippingAddressLoading}
                          value={
                            state.customerShippingAddress &&
                            formatShippingAddressOption(
                              state.customerShippingAddress
                            )
                          }
                          defaultValue={
                            state.customerShippingAddress &&
                            formatShippingAddressOption(
                              state.customerShippingAddress
                            )
                          }
                          onChange={handleChangeShippingAddress}
                          placeholder="Địa chỉ"
                        />
                      </Formfield>
                    </Col>
                    {state.customerShippingAddress && (
                      <Col lg="12" className="u-mb-15">
                        <SpinnerContainer
                          animating={
                            getSingleCustomerShippingAddressState.loading
                          }
                        >
                          <Shippingaddress
                            name={state.customerShippingAddress.name}
                            address={displayFullAddress(
                              state.customerShippingAddress
                            )}
                            phone={state.customerShippingAddress.phoneNumber}
                            editable={
                              (editMode || createMode) &&
                              !firstSelectedRef.current
                            }
                            onEdit={() =>
                              handleOpenModalByType(
                                "customerUpdateShippingModal"
                              )
                            }
                          />
                        </SpinnerContainer>
                      </Col>
                    )}

                    {(editMode || createMode) &&
                      state.controlledProperties.customer && (
                        <Col
                          lg="12"
                          className="d-flex justify-content-center u-mt-20"
                        >
                          <Button
                            buttonType="textbutton"
                            onClick={() =>
                              handleOpenModalByType(
                                "customerCreateShippingModal"
                              )
                            }
                          >
                            Thêm địa chỉ mới
                          </Button>
                        </Col>
                      )}
                  </Row>
                </Section>
              </Col>

              <Col xl="4" className="u-mb-20">
                <Heading type="h4">ĐƠN HÀNG</Heading>

                <Section>
                  <Row>
                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Danh mục" name="category">
                        <Textfield name="category" disabled={viewMode} />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Shop" name="shop">
                        <Pulldown
                          isSearchable
                          options={[]}
                          isDisabled={viewMode}
                          placeholder="Shop"
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Nguồn" name="orderSource">
                        <PulldownHookForm
                          name="orderSource"
                          placeholder="Nguồn"
                          isDisabled={viewMode}
                          options={orderSourceOptions}
                          defaultValue={
                            initialOrderDetail?.orderSource &&
                            formatOrderSourceOption(
                              initialOrderDetail.orderSource
                            )
                          }
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Ngày tạo" name="createdAt">
                        <Calendar
                          disabled={viewMode}
                          defaultValue={
                            initialOrderDetail?.createdAt &&
                            new Date(initialOrderDetail.createdAt)
                          }
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Vận chuyển" name="shipping">
                        <Pulldown
                          isSearchable
                          options={[]}
                          isDisabled={viewMode}
                          placeholder="Vận chuyển"
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Mã bưu kiện" name="zipcode">
                        <Textfield name="zipcode" disabled={viewMode} />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Trạng thái" name="status">
                        <PulldownHookForm
                          name="orderStatus"
                          isSearchable={false}
                          isDisabled={viewMode}
                          options={orderStatusesOptions}
                          defaultValue={selectedOrderStatus}
                          value={selectedOrderStatus}
                          placeholder="Trạng thái"
                          onChange={(option) =>
                            option?.value &&
                            setOrderProperties({
                              selectedOrderStatusId: Number(option.value),
                            })
                          }
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Tagline" name="Tagline">
                        <PulldownHookForm
                          name="orderStatusTagline"
                          isSearchable={false}
                          isDisabled={viewMode}
                          options={orderStatusTaglineOptions}
                          value={selectedStatusTagline}
                          defaultValue={selectedStatusTagline}
                          onChange={(option) =>
                            option?.value &&
                            setOrderProperties({
                              selectedOrderStatusTaglineId: Number(
                                option.value
                              ),
                            })
                          }
                          placeholder="Tagline"
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield
                        label="Trạng thái đổi hàng"
                        name="barterstatus"
                      >
                        <PulldownHookForm
                          name="returnStatus"
                          isSearchable={false}
                          options={orderReturnStatusOptions}
                          value={selectedReturnStatus}
                          defaultValue={selectedReturnStatus}
                          onChange={(option) =>
                            option?.value &&
                            setOrderProperties({
                              selectedOrderReturnStatusId: Number(option.value),
                            })
                          }
                          isDisabled={viewMode}
                          placeholder="Trạng thái đổi hàng"
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Tagline đổi trả" name="Tagline">
                        <PulldownHookForm
                          name="returnStatusTagline"
                          isSearchable={false}
                          options={orderReturnStatusTaglineOptions}
                          value={selectedReturnStatusTagline}
                          defaultValue={selectedReturnStatusTagline}
                          onChange={(option) =>
                            option?.value &&
                            setOrderProperties({
                              selectedOrderReturnStatusTaglineId: Number(
                                option.value
                              ),
                            })
                          }
                          isDisabled={viewMode}
                          placeholder="Tagline đổi trả"
                        />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Phân công" name="assignment">
                        <PulldownHookForm
                          name="assignedEmployee"
                          isClearable
                          isSearchable
                          isDisabled={viewMode}
                          triggerLoadMore={() => loadMoreEmployee()}
                          defaultValue={
                            initialOrderDetail?.assignedEmployee &&
                            formatAssignEmployeeOption(
                              initialOrderDetail.assignedEmployee
                            )
                          }
                          isLoading={loadMoreEmployeeState.loading}
                          onInputChange={onAssignEmployeePulldownInputChange}
                          options={assignEmployeeOptions}
                          placeholder="Phân công"
                        />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Ghi chú giao hàng" name="deliveryNote">
                        <TextareafieldHookForm
                          name="deliveryNote"
                          defaultValue={initialOrderDetail?.deliveryNote}
                          disabled={viewMode}
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Tổng khối lượng (kg)" name="weight">
                        <NumberfieldHookForm
                          name="weight"
                          disabled={viewMode}
                          defaultValue={initialOrderDetail?.weight}
                        />
                      </Formfield>
                    </Col>

                    <Col lg="6" className="u-mb-15">
                      <Formfield label="Kích thước đóng gói (cm)" name="size">
                        <TextfieldHookForm
                          defaultValue={initialOrderDetail?.size}
                          disabled={viewMode}
                          name="size"
                        />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Ghi chú đơn hàng" name="orderNote">
                        <TextareafieldHookForm
                          name="orderNote"
                          disabled={viewMode}
                          defaultValue={initialOrderDetail?.orderNote}
                        />
                      </Formfield>
                    </Col>

                    <Col lg="4" className="u-mb-15">
                      <Formfield label="Chiều cao (cm)" name="customerHeight">
                        <NumberfieldHookForm
                          name="customerHeight"
                          disabled={viewMode}
                        />
                      </Formfield>
                    </Col>

                    <Col lg="4" className="u-mb-15">
                      <Formfield label="Cân nặng (kg)" name="customerWeight">
                        <NumberfieldHookForm
                          disabled={viewMode}
                          name="customerWeight"
                        />
                      </Formfield>
                    </Col>

                    <Col lg="4" className="u-mb-15">
                      <Formfield label="Vòng eo (cm)" name="customerWaist">
                        <NumberfieldHookForm
                          disabled={viewMode}
                          name="customerWaist"
                        />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Ghi chú API" name="apiNote">
                        <TextareafieldHookForm
                          name="apiNote"
                          disabled={viewMode}
                          defaultValue={initialOrderDetail?.apiNote}
                        />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Chuyển đến shop" name="shopSent">
                        <PulldownHookForm
                          name="shopSent"
                          placeholder="Chọn shop"
                          isDisabled={viewMode}
                          isSearchable
                          options={[
                            { label: "Shop1", value: "1" },
                            { label: "Shop2", value: "2" },
                          ]}
                        />
                      </Formfield>
                    </Col>
                  </Row>
                </Section>
              </Col>

              <Col xl="4">
                <Heading type="h4">THANH TOÁN</Heading>
                <Section>
                  <Row>
                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Tổng" name="total">
                        <Textfield
                          name="total"
                          disabled
                          value={state.controlledProperties.totalAmount}
                        />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Giảm giá" name="discount">
                        <Textfield name="discount" disabled />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Phí ship" name="shippingCost">
                        <NumberfieldHookForm
                          name="shippingCost"
                          onChange={handleOnShippingCostInputChange}
                          defaultValue={initialOrderDetail?.shippingCost}
                          disabled={viewMode}
                        />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Phụ thu" name="applicableFee">
                        <NumberfieldHookForm
                          name="applicableFee"
                          defaultValue={initialOrderDetail?.applicableFee}
                          disabled={viewMode}
                          onChange={handleOnApplicableFeeInputChange}
                        />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield
                        label="Sử dụng khuyến mãi"
                        name="promotioncode"
                      >
                        <PulldownHookForm
                          name="promotioncode"
                          id="promotioncode"
                          placeholder="Mã khuyến mãi"
                          isMultiSelect
                          isSearchable
                          isDisabled={viewMode}
                          options={[
                            { label: "test1", value: "1" },
                            { label: "test2", value: "2" },
                            { label: "test3", value: "3" },
                          ]}
                          defaultValue={[
                            { label: "test1", value: "1" },
                            { label: "test2", value: "2" },
                          ]}
                        />

                        {/* <Row>
													<Col lg="12" className="u-mb-15">
														<Selectedchoice
														color="#F2AF4A"
														onDelete={() => {}}
														title="VOUCHER12"
													/>
													</Col>
												</Row> */}
                      </Formfield>
                    </Col>
                    <Col lg="12" className="u-mb-15">
                      <Textfield disabled />
                    </Col>

                    <Col lg="12">
                      <Formfield
                        label="Sử dụng điểm tích lũy"
                        name="useaccumulatedpoints"
                      >
                        <Row>
                          <Col lg="6" className="u-mb-15">
                            <Textfield
                              name="useaccumulatedpoints"
                              disabled={viewMode}
                            />
                          </Col>

                          <Col lg="6" className="u-mb-15">
                            <Textfield name="useaccumulatedpoints" disabled />
                          </Col>
                        </Row>
                      </Formfield>
                    </Col>

                    <Col lg="12">
                      <Formfield
                        label="Áp dụng mã giới thiệu"
                        name="applyreferralcode"
                      >
                        <Row>
                          <Col lg="6" className="u-mb-15">
                            <Textfield
                              name="applyreferralcode"
                              disabled={viewMode}
                            />
                          </Col>

                          <Col lg="6" className="u-mb-15">
                            <Textfield name="applyreferralcode" disabled />
                          </Col>
                        </Row>
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Thanh toán" name="pay">
                        <Textfield name="pay" disabled />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Đã thanh toán" name="paid">
                        <Textfield name="paid" disabled />
                      </Formfield>
                    </Col>

                    <Col lg="12" className="u-mb-15">
                      <Formfield label="Còn lại" name="rest">
                        <Textfield name="rest" disabled />
                      </Formfield>
                    </Col>
                  </Row>
                </Section>
              </Col>
            </Row>





