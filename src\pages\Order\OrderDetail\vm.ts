import { useCallback, useEffect } from "react";

import { showNotification } from "components/atoms/base/Notification";
import { toastSingleMode } from "components/atoms/toastify";
import { convertUndefinedToNull } from "helpers/convertDataToDefaultOption.helper";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import {
  orderDetailDto,
  OrderDetailDtoType,
  updateOrderDto,
  UpdateOrderDtoType,
} from "modules/order";
import {
  getOrderDetail,
  updateOrderDetail as updateOrderDetailService,
} from "services/crm/order";

export type OrderItem = OrderDetailDtoType;

export interface OrderDetailPageVmProps {
  orderId: number;
}

export const OrderDetailPageVm = ({ orderId }: OrderDetailPageVmProps) => {
  const [getOrderDetailExec, getOrderDetailState] = useAsync(
    useCallback(
      (options: { orderId: number }) =>
        getOrderDetail({ ...options }).then((res) =>
          orderDetailDto(res.data.data)
        ),
      []
    )
  );

  const [updateOrderDetailServiceExec, updateOrderDetailState] = useAsync(
    updateOrderDetailService,
    {
      onSuccess: useCallback(
        () =>
          // toastSingleMode({ type: "success", message: "Cập nhật thành công" }),
          showNotification({
            type: "success",
            message: "Cập nhật thành công",
          }),
        []
      ),
      onFailed: useCallback((error) => {
        const errorApi = error?.response?.data?.errors?.[0];
        if (errorApi) {
          showNotification({
            type: "error",
            message: errorApi.detail || errorApi.title,
          });
        }
        // const errMessage = getErrorMessageViaErrCode(
        //   error?.response?.data?.errors?.[0]?.code
        // );
        // toastSingleMode({
        //   type: "error",
        //   message: errMessage.translation.title,
        //   descripition: errMessage.translation.detail,
        // });
      }, []),
    }
  );

  const orderDetail = getOrderDetailState.data;

  const handleUpdateOrderDetail = useCallback(
    (rawPayload: Partial<UpdateOrderDtoType>) => {
      if (!orderDetail?.orderId) return;
      const updateOrderPayload = updateOrderDto({
        ...orderDetail,
        ...rawPayload,
      });

      const satisfyUpdateOrderPayload =
        convertUndefinedToNull(updateOrderPayload);

      updateOrderDetailServiceExec(
        orderDetail.orderId,
        satisfyUpdateOrderPayload as UpdateOrderDtoType
      )
        .then(() => {
          // Refetch order detail after update
          getOrderDetailExec({ orderId: orderDetail.orderId });
        })
        .catch((error) => {
          // const errMessage = getErrorMessageViaErrCode(
          //   error?.response?.data?.errors?.[0]?.code
          // );
          // toastSingleMode({
          //   type: "error",
          //   message: errMessage.translation.title,
          //   descripition: errMessage.translation.detail,
          // });
        });
    },
    [updateOrderDetailServiceExec, orderDetail]
  );

  useEffect(() => {
    if (orderId && !Number.isNaN(orderId)) {
      getOrderDetailExec({ orderId });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderId]);

  return {
    orderDetail,
    handleUpdateOrderDetail,
    updateOrderDetailState,
    handleRefetchOrderDetail: getOrderDetailExec,
    loading: getOrderDetailState.loading,
  };
};
