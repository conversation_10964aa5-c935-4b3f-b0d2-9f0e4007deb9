import { Story, Meta } from "@storybook/react/types-6-0";

import imgSearchBlue from "assets/images/icons/search-blue.svg";

import { Textfield, Props } from ".";

export default {
  title: "Components|atoms/Textfield",
  component: Textfield,
} as Meta;

const Template: Story<Props> = ({
  placeholder,
  disabled,
  iconSrc,
  errorMessage,
}) => (
  <Textfield
    placeholder={placeholder}
    disabled={disabled}
    iconSrc={iconSrc}
    errorMessage={errorMessage}
  />
);

export const Normal = Template.bind({});
export const WithIcon = Template.bind({});

Normal.args = {
  placeholder: "Sample",
  disabled: false,
  errorMessage: "",
};

WithIcon.args = {
  placeholder: "Sample",
  disabled: false,
  iconSrc: imgSearchBlue,
  errorMessage: "",
};
