import { useCallback, useMemo } from "react";

import { useAsync } from "hooks/useAsync";
import { mapFrom } from "libs/adapters/dto";
import { employeeGroupOption } from "modules/employee";
import { EmployeeDetailDtoType } from "modules/employee/dtos";
import { GroupEmployeeModel } from "modules/employee/entities/groupEmployee";
import { getEmployeeGroups } from "services/crm/employee";

export interface PulldownEmployeeGroupFunctionalOptions {
  excludePending: boolean;
}

export const usePulldownEmployeeGroup = ({
  excludePending = false,
}: PulldownEmployeeGroupFunctionalOptions) => {
  const [fetchEmployeeGroups, fetchEmployeeGroupsState] = useAsync(
    useCallback(
      () =>
        getEmployeeGroups().then((res) =>
          GroupEmployeeModel.createMap(res.data.data)
        ),
      []
    ),
    {
      excludePending,
    }
  );

  const employeeGroups = useMemo(
    () => fetchEmployeeGroupsState.data || [],
    [fetchEmployeeGroupsState.data]
  );

  const employeeGroupOptions = useMemo(
    () => mapFrom(employeeGroups || [], employeeGroupOption),
    [employeeGroups]
  );

  const getOptionByValue = useCallback(
    (employeeGroupId?: number) =>
      employeeGroupId &&
      employeeGroupOptions.find(
        ({ value }) => Number(value) === employeeGroupId
      ),
    [employeeGroupOptions]
  );

  const formatEmployeeGroupOptions = useCallback(
    (payload: EmployeeDetailDtoType["employeeGroups"]) =>
      mapFrom(payload || [], employeeGroupOption),
    []
  );

  return {
    employeeGroupOptions,
    employeeGroups,
    fetchEmployeeGroups,
    getOptionByValue,
    formatEmployeeGroupOptions,
  };
};
