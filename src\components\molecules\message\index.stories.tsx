import { Story, Meta } from "@storybook/react/types-6-0";

import { MessageModel } from "modules/chat";

import { Message, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|molecules/Message",
  component: Message,
} as Meta;

const Template: Story<Props> = () => (
  <div>
    <Message
      message={MessageModel.create({
        _id: "6081556675a3570e40ec0193",
        messageId: "ef777115-6f68-4501-b3e4-d907f41aa64c",
        sender: { id: "102496038607569" },
        recipient: { id: "4172256639491478" },
        content: {
          mid: "m_Kk4EmjKw-hntUcADPFb2yCnlTZVA-vSYVr98nFHhaUr8ObnRSjsfknyjj7jLpYHdwmcDtJZijKf-zcLTuG2Gxw",
          text: "",
          attachments: [
            {
              type: "image",
              payload: {
                url: "https://scontent.xx.fbcdn.net/v/t1.15752-9/175930765_742079949811915_8989704260415110273_n.png?_nc_cat=111&ccb=1-3&_nc_sid=58c789&_nc_ohc=goEbnkXNX80AX_7q0xA&_nc_ad=z-m&_nc_cid=0&_nc_ht=scontent.xx&oh=0b1c4bf2956c4b09bfc9f7abd10350db&oe=60A6BB15",
              },
            },
          ],
          metadata: {
            fromSocketId: "B_ll9gAcnIO7DMipAAAL",
            tempId: "_mid_lz6gz8sax",
            media: { width: 940, height: 891, type: "image" },
          },
        },
        conversationId: "a0e709b7-be89-4e1b-9de0-eac7688a5d57",
        createdAt: new Date("2021-04-22T10:52:23.001Z"),
        updatedAt: new Date("2021-04-22T10:52:23.001Z"),
        creatorType: "employee",
        creatorAvatar: "/static/media/logo.b3bbb694.png",
      })}
    />
    <Message
      message={MessageModel.create({
        _id: "6081552575a3570e40ec00f3",
        messageId: "44be45ed-88ec-4e79-89ad-858a9ce475ba",
        sender: { id: "102496038607569" },
        recipient: { id: "4172256639491478" },
        content: {
          mid: "m_NXxWxb-GgxyFXiSMWvmggSnlTZVA-vSYVr98nFHhaUr65IkDikuxFS9PRCygG0z4rFGWVInuAGvtmszXmcszWw",
          text: "hihi",
          attachments: [],
          metadata: {
            fromSocketId: "gpy8_DcBcx0JB6inAAAJ",
            tempId: "_mid_89pv59jce",
            media: { type: "" },
          },
        },
        conversationId: "a0e709b7-be89-4e1b-9de0-eac7688a5d57",
        createdAt: new Date("2021-04-22T10:51:17.579Z"),
        updatedAt: new Date("2021-04-22T10:51:17.579Z"),
        creatorType: "employee",
        creatorAvatar: "/static/media/logo.b3bbb694.png",
      })}
    />
  </div>
);

export const Normal = Template.bind({});

Normal.args = {};
