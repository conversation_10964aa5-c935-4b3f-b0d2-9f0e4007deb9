import { AppPage } from "libs/react";

export type ChildrenPage = {
  index: AppPage;
  listOrder: AppPage;
  listPaymentTransaction: AppPage;
  paymentTransactionDetail: AppPage<{ paymentTransactionId: string }>;
  createOrder: AppPage;
  orderDetail: AppPage<{ orderId: string }>;
  exChangeOrder: AppPage<{ orderId: string }>;
  exChangeOrderDetail: AppPage<{ orderId: string }>;
  listOrderStatus: AppPage;
  orderStatusDetail: AppPage<{ orderStatusCode: string }>;
  listOrderStatusTagline: AppPage<{
    orderStatusId: string;
    orderStatusCode: string;
  }>;
  orderStatusTaglineDetail: AppPage<{
    orderStatusId: string;
    taglineId: string;
  }>;
  listOrderReturnStatus: AppPage;
  orderReturnStatusDetail: AppPage<{ returnStatusId: string }>;
  listSourceManagement: AppPage;
  orderSourceDetail: AppPage<{ sourceId: string }>;
  listOrderReturnStatusTagline: AppPage<{ returnStatusId: string }>;
  orderReturnStatusTaglineDetail: AppPage<{
    returnStatusId: string;
    taglineId: string;
  }>;
  listDeliveryPartner: AppPage;
  deliveryPartnerDetail: AppPage<{ deliveryPartnerId: string }>;
};
