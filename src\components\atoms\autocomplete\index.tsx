import React, {
  useState,
  forwardRef,
  useCallback,
  useRef,
  useEffect,
} from "react";

import { IconName, Icon } from "components/atoms/icon";
import { InfiniteScrollable } from "components/utils/infinitescrollable";

export type Option = { label: string; value: string };

export interface Props extends React.InputHTMLAttributes<HTMLInputElement> {
  iconSrc?: IconName;
  options: Option[];
  closeListAfterSelected?: boolean;
  clearInputAfterSelected?: boolean;
  onInputChange?: (filter: string) => void;
  onSelectedItem?: (optionSelected: Option) => void;
  triggerOnLoadmore?: () => Promise<unknown>;
  visibleOptionCount?: number;
  onRenderItem?: (label: string) => React.ReactNode;
}

export const AutoComplete = forwardRef<HTMLInputElement, Props>(
  (
    {
      iconSrc,
      options,
      closeListAfterSelected,
      clearInputAfterSelected,
      onInputChange: onInputChangeProps,
      onSelectedItem,
      triggerOnLoadmore,
      onRenderItem,
      visibleOptionCount = 7,
      ...innerProps
    },
    ref
  ) => {
    const textSearchRef = useRef<HTMLDivElement | null>(null);

    const [textSearch, setTextSearch] = useState<string>();
    const [isOpenOptions, setOpenOption] = useState(false);

    const isOverflow = options.length > visibleOptionCount;

    const onInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value: filter } = e.target;
        setTextSearch(filter);
        if (onInputChangeProps) {
          onInputChangeProps(filter);
        }
      },
      [onInputChangeProps]
    );

    const onSelected = useCallback(
      (e: React.MouseEvent) => {
        const { title: label, value } = e.target as HTMLInputElement;
        setTextSearch(label);

        if (onSelectedItem) {
          onSelectedItem({ label, value });
        }

        if (closeListAfterSelected) {
          setOpenOption(false);
        }

        if (clearInputAfterSelected) {
          setTextSearch("");
        }
      },
      [clearInputAfterSelected, closeListAfterSelected, onSelectedItem]
    );

    useEffect(() => {
      const textSearchTarget = textSearchRef.current;
      if (!textSearchTarget) return undefined;

      const handleClick = (e: MouseEvent) => {
        if (!textSearchTarget.contains(e.target as Node) && isOpenOptions) {
          setOpenOption(false);
        }
      };

      window.addEventListener("click", handleClick);

      return () => {
        window.removeEventListener("click", handleClick);
      };
    }, [isOpenOptions]);

    const handleLoadmore = useCallback(async () => {
      if (triggerOnLoadmore) {
        triggerOnLoadmore();
      }
    }, [triggerOnLoadmore]);

    return (
      <div className="a-autocomplete" ref={textSearchRef}>
        <div className="a-autocomplete_wrapped-input">
          <input
            className="a-autocomplete_input"
            onChange={onInputChange}
            value={textSearch}
            onFocus={() => setOpenOption(true)}
            ref={ref}
            {...innerProps}
          />
          {iconSrc && (
            <div className="a-autocomplete_icon">
              <Icon iconName="search-blue" />
            </div>
          )}
        </div>
        {isOpenOptions && (
          <ul className="a-autocomplete_options">
            <InfiniteScrollable
              scrollType="bottom"
              height={isOverflow ? 230 : "auto"}
              width="100%"
              onTrigger={handleLoadmore}
            >
              {options.map((item) => (
                <li
                  aria-hidden
                  className="a-autocomplete_option-item"
                  key={item.value}
                  onClick={onSelected}
                  title={item.label}
                  value={item.value}
                >
                  {!onRenderItem ? item.label : onRenderItem(item.label)}
                </li>
              ))}
              {!options.length && (
                <li className="a-autocomplete_option-item empty">
                  Không tìm thấy
                </li>
              )}
            </InfiniteScrollable>
          </ul>
        )}
      </div>
    );
  }
);

AutoComplete.defaultProps = {
  closeListAfterSelected: true,
  clearInputAfterSelected: false,
};
