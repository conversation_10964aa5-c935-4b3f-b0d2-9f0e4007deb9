import pick from "object.pick";

import { createMapper, fromSchema, merge } from "libs/adapters/dto";

import { OrderStatusTaglineSchema } from "../entities/orderStatusTagline";

export const createOrderStatusTaglineDto = createMapper(
  fromSchema(
    pick(OrderStatusTaglineSchema, [
      "orderStatusId",
      "name",
      "description",
      "displayOrder",
      "color",
    ])
  ),
  merge((data) => ({
    description: data.description || undefined,
  }))
);

export type CreateOrderStatusTaglineDtoType = ReturnType<
  typeof createOrderStatusTaglineDto
>;
