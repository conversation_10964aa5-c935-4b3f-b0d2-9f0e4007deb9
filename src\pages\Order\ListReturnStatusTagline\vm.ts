import { useCallback, useEffect, useState } from "react";

import produce from "immer";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import {
  orderReturnStatusDetailDto,
  createReturnOrderStatusTaglineDto,
  createReturnOrderStatusTaglineDtoType,
  orderReturnStatusTaglineItemListDto,
} from "modules/order";
import {
  createReturnOrderStatusTagline as createReturnOrderStatusTaglineService,
  getListOrderExchangeTagline,
  getReturnOrderStatusDetail,
} from "services/crm/order";

type ModalType = "createOrderReturnStatusTagline";

interface State {
  modalState: {
    open: boolean;
    modalType?: ModalType;
  };
  pagination: {
    pageSize: number;
  };
  createNewReturnOrderStatusTaglineState: {
    colorSelected?: string;
  };
}

export interface OrderReturnStatusTaglineListPageVmProps {
  returnOrderStatusId: number;
}

export const OrderReturnStatusTaglineListPageVm = ({
  returnOrderStatusId,
}: OrderReturnStatusTaglineListPageVmProps) => {
  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      modalType: undefined,
    },
    pagination: {
      pageSize: 10,
    },
    createNewReturnOrderStatusTaglineState: {
      colorSelected: "#000",
    },
  });

  const [getReturnStatusTaglineExec, getReturnStatusTaglineState] = useAsync(
    useCallback(
      (options: {
        returnOrderStatusId: number;
        pageNum: number;
        pageSize: number;
      }) =>
        getListOrderExchangeTagline({ ...options }).then((res) => ({
          data: mapFrom(res.data.data, orderReturnStatusTaglineItemListDto),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const [getReturnOrderStatusByIdExec, getReturnOrderStatusByIdState] =
    useAsync(
      useCallback(
        (payload: { returnOrderStatusId: number }) =>
          getReturnOrderStatusDetail({ ...payload }).then((res) =>
            orderReturnStatusDetailDto(res.data.data)
          ),
        []
      ),
      {
        excludePending: true,
      }
    );

  const { gotoPage, ...orderReturnStatusTaglineListPaginationState } =
    usePagination({
      pageSize: state.pagination.pageSize,
      actionOnPageChange: ({ page, pageSize }) =>
        getReturnStatusTaglineExec({
          returnOrderStatusId,
          pageSize,
          pageNum: page,
        }),
    });

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pagination.pageSize = pageSize;
      })
    );
  }, []);

  useEffect(() => {
    getReturnOrderStatusByIdExec({ returnOrderStatusId });
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize]);

  const {
    sortedData: orderReturnStatusTaglineListData,
    toggleSortState: toggleSortOrderBy,
  } = useSortable({
    data: getReturnStatusTaglineState.data?.data,
    sortBy: {
      name: (data) => data.name,
      updatedAt: (data) => data.updatedAt,
    },
  });

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.modalType = type;
      })
    );
  }, []);

  const handleCloseModal = useCallback(() => {
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.modalType = undefined;
      })
    );
  }, []);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) => {
      return state.modalState.open && state.modalState.modalType === type;
    },
    [state.modalState]
  );

  const onChangeColor = useCallback(
    (code: string) => {
      setState(
        produce((draft) => {
          draft.createNewReturnOrderStatusTaglineState.colorSelected = code;
        })
      );
    },

    [setState]
  );

  const [createReturnOrderStatusTaglineExec] = useAsync(
    createReturnOrderStatusTaglineService,
    {
      excludePending: true,
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Tạo mới thành công" });

        handleCloseModal();

        gotoPage(1);
      }, [gotoPage, handleCloseModal]),

      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error.response?.data?.errors?.[0]?.code
        );

        toastSingleMode({
          type: "error",

          message: errMessage.translation.title,

          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const createReturnOrderStatusTagline = useCallback(
    async (rawPayload: Partial<createReturnOrderStatusTaglineDtoType>) => {
      const returnOrderStatusTaglinePayload = createReturnOrderStatusTaglineDto(
        {
          ...rawPayload,
        }
      );
      const { returnOrderStatusId: _returnOrderStatusId, ...payload } =
        returnOrderStatusTaglinePayload;
      await createReturnOrderStatusTaglineExec(_returnOrderStatusId, payload);
    },
    [createReturnOrderStatusTaglineExec]
  );

  return {
    gotoPage,
    handleChangePageSize,
    pageSize: state.pagination.pageSize,
    orderReturnStatusTaglineListPaginationState,
    orderReturnStatusTaglineListData: orderReturnStatusTaglineListData || [],
    orderReturnStatusTaglineListDataLoading:
      getReturnStatusTaglineState.loading,
    toggleSortOrderBy,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    modalCreator: {
      colorSelected: state.createNewReturnOrderStatusTaglineState.colorSelected,
      onChangeColor,
      createReturnOrderStatusTagline,
    },
    returnOrderStatus: getReturnOrderStatusByIdState.data || undefined,
  };
};
