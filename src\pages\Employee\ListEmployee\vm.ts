import { useCallback, useState } from "react";

import produce from "immer";

import { toastSingleMode } from "components/atoms/toastify";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import { employeeDetailDto, usePulldownEmployeeGroup } from "modules/employee";
import { getListEmployee, createEmployee } from "services/crm/employee";

import RootPageRouter from "../index";

type ModalType = "employeeCreation";

interface State {
  pageSize: number;
  modalState: {
    open: boolean;
    type?: ModalType;
  };
}

export const ListEmployeePageVm = () => {
  const [state, setState] = useState<State>({
    pageSize: 10,
    modalState: {
      open: false,
      type: "employeeCreation",
    },
  });

  const handleOpenModalByType = useCallback(
    (type: ModalType) =>
      setState(
        produce((draft) => {
          draft.modalState.open = true;
          draft.modalState.type = type;
        })
      ),
    []
  );

  const handleOnCloseModal = useCallback(
    () =>
      setState(
        produce((draft) => {
          draft.modalState.open = false;
          draft.modalState.type = undefined;
        })
      ),
    []
  );

  const [getListEmployeeExec, getListEmployeeState] = useAsync(
    useCallback(
      (pageNum: number, pageSize: number) =>
        getListEmployee({ pageNum, pageSize }).then((res) => ({
          employees: mapFrom(res.data.data, employeeDetailDto),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const { gotoPage, ...employeePaginationState } = usePagination({
    pageSize: state.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getListEmployeeExec(page, pageSize),
  });

  const { sortedData: listEmployee, toggleSortState: toggleSortEmployeeBy } =
    useSortable({
      data: getListEmployeeState.data?.employees,
      sortBy: {
        name: (employee) => employee.name,
        email: (employee) => employee.email,
        updatedAt: (employee) => employee.updatedAt,
      },
    });

  const [createEmployeeExec, createEmployeeState] = useAsync(createEmployee, {
    onFailed: useCallback(
      (error) =>
        toastSingleMode({
          type: "error",
          descripition: error?.errors?.[0]?.title,
          message: "Error",
        }),
      []
    ),
    onSuccess: useCallback(() => {
      handleOnCloseModal();
      toastSingleMode({
        type: "success",
        message: "Tạo mới thành công",
      });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []),
  });

  const { employeeGroupOptions, fetchEmployeeGroups } =
    usePulldownEmployeeGroup({
      excludePending: true,
    });

  const gotoDetailEmployeePage = useCallback(
    (employeeId: number) =>
      RootPageRouter.gotoChild("employeeDetail", {
        params: { employeeId: employeeId.toString() },
        queryString: "?action=view",
      }),
    []
  );

  const gotoEditEmployeePage = useCallback(
    (employeeId: number) =>
      RootPageRouter.gotoChild("employeeDetail", {
        params: { employeeId: employeeId.toString() },
        queryString: "?action=edit",
      }),
    []
  );

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pageSize = pageSize;
      })
    );
  }, []);

  return {
    employeePaginationState,
    gotoPage,
    listEmployee: listEmployee || [],
    toggleSortEmployeeBy,
    loading: getListEmployeeState.loading,
    pageState: state,
    handleOpenModalByType,
    handleOnCloseModal,
    fetchEmployeeGroups,
    employeeGroupOptions,
    createEmployeeExec,
    createEmployeeState,
    gotoDetailEmployeePage,
    gotoEditEmployeePage,
    handleChangePageSize,
    pageSize: state.pageSize,
  };
};
