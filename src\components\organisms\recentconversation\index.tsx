import React, { CSSProperties, useRef } from "react";

import {
  AutoSizer,
  CellMeasurer,
  CellMeasurerCache,
  InfiniteLoader,
  List,
} from "components/molecules/list";
import { mapModifiers } from "helpers/component";

type Modifier = "foo" | "bar";

/* eslint-disable */
export interface Props<ConversationType> {
  modifiers?: Modifier | Array<Modifier>;
  children?: React.ReactNode;
  threshold?: number;
  onLoadMore?: () => void;
  isLoadmorePending?: boolean;
  conversations: ConversationType[];
  overscanRowCount?: number;
  onConversationRender: (conversation: ConversationType) => React.ReactElement;
}

interface CellProps {
  style: CSSProperties;
}

const Cell: React.FC<React.PropsWithChildren<CellProps>> = ({
  style,
  children,
}) => (
  <div style={style}>
    <div className="o-recentconversation-cell">{children}</div>
  </div>
);

export const RecentConversation = <ConversationType,>({
  modifiers,
  isLoadmorePending = false,
  threshold = 1,
  overscanRowCount = 0,
  onLoadMore,
  conversations,
  onConversationRender,
}: Props<ConversationType>) => {
  const cellCacheRef = useRef(new CellMeasurerCache({}));

  const handleLoadMore = async () => {
    if (isLoadmorePending) return;
    if (onLoadMore) {
      handleLoadMore();
    }
  };

  return (
    <div className={mapModifiers("o-recentconversation", modifiers)}>
      <InfiniteLoader
        isRowLoaded={({ index }) => {
          if (index > 0) {
            return true;
          }
          return false;
        }}
        loadMoreRows={handleLoadMore}
        rowCount={conversations.length}
        threshold={threshold}
      >
        {({ registerChild, onRowsRendered }) => (
          <AutoSizer>
            {({ width, height }) => {
              return (
                <List
                  ref={(ref) => {
                    registerChild(ref);
                  }}
                  onRowsRendered={onRowsRendered}
                  deferredMeasurementCache={cellCacheRef.current}
                  height={height}
                  width={width}
                  rowCount={conversations.length}
                  overscanRowCount={overscanRowCount}
                  rowHeight={cellCacheRef.current.rowHeight}
                  rowRenderer={({ index, key, parent, style }) => {
                    return (
                      <CellMeasurer
                        cache={cellCacheRef.current}
                        columnIndex={0}
                        key={key}
                        rowIndex={index}
                        parent={parent}
                      >
                        <Cell style={style}>
                          {onConversationRender(conversations[index])}
                        </Cell>
                      </CellMeasurer>
                    );
                  }}
                />
              );
            }}
          </AutoSizer>
        )}
      </InfiniteLoader>
    </div>
  );
};
