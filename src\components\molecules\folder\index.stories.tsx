import { Story, Meta } from "@storybook/react/types-6-0";

import { Folder, Props } from ".";

export default {
  title: "Components|molecules/Folder",
  component: Folder,
} as Meta;

const Template: Story<Props> = ({ name, totalFile, onClick, onRename }) => (
  <Folder
    name={name}
    totalFile={totalFile}
    onClick={onClick}
    onRename={onRename}
  />
);

export const Normal = Template.bind({});

Normal.args = {
  name: "File 01",
  totalFile: 10,
  onClick: () => {},
  onRename: () => {},
};
