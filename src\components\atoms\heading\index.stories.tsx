import { <PERSON>, <PERSON>a } from "@storybook/react/types-6-0";

import { Heading, Props } from ".";

export default {
  title: "Components|atoms/Heading",
  component: Heading,
} as Meta;

const Template: Story<Props> = ({ type, centered, children, modifiers }) => (
  <Heading type={type} centered={centered} modifiers={modifiers}>
    {children}
  </Heading>
);

export const Normal = Template.bind({});

Normal.args = {
  type: "h1",
  children:
    "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labor",
};
