/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

import { Controller, useFormContext } from "react-hook-form";
import Select, {
  ActionMeta,
  InputActionMeta,
  Props as SelectProps,
} from "react-select";

import { ConnectForm, UseFormProps } from "helpers/form";
import { useForceUpdate } from "hooks/useForceUpdate";

export type SelectProp = SelectProps;

export interface ExternalRegister {
  reset: () => void;
}

export interface Props extends SelectProps {
  name?: string;
  errorMessage?: string;
  controlHeight?: number;
  hasMore?: boolean;
  threshold?: 0 | 10 | 20 | 30;
  isMultiSelect?: boolean;
  triggerLoadMore?: (payload: { searchInputValue: string }) => Promise<unknown>;
  register?: React.MutableRefObject<ExternalRegister | undefined>;
}

export const Pulldown: React.FC<Props> = ({
  name,
  isDisabled,
  errorMessage,
  hasMore = true,
  threshold = 0,
  controlHeight = 48,
  triggerLoadMore,
  isMulti: _isMulti,
  isMultiSelect,
  onInputChange: onInputChangeProps,
  register,
  ...innerProps
}) => {
  const forceUpdate = useForceUpdate();
  const loading = useRef(false);
  const selectRef = useRef<Select>(null);
  const [isExpanded, setExpanded] = useState(false);
  const prevInputValueRef = useRef<string>("");

  const triggerCallback = useCallback(
    async (element) => {
      if (loading.current) return;
      const { scrollTop, clientHeight, scrollHeight } = element;
      const distance =
        Math.round(scrollTop + clientHeight + threshold) >= scrollHeight;

      if (triggerLoadMore && hasMore && distance) {
        loading.current = true;
        forceUpdate();
        triggerLoadMore({
          searchInputValue: selectRef.current?.state?.inputValue || "",
        }).finally(() => {
          loading.current = false;
          forceUpdate();
        });
      }
    },
    [forceUpdate, hasMore, threshold, triggerLoadMore]
  );

  const onInputChange = useCallback(
    (newValue: string, actionMeta: InputActionMeta) => {
      const prevInputValue = prevInputValueRef.current;
      const currentInputValue = (
        selectRef?.current?.select?.inputRef as unknown as HTMLInputElement
      )?.value;

      if (prevInputValue === currentInputValue) return;
      if (onInputChangeProps) {
        onInputChangeProps(newValue, actionMeta);
      }
    },
    [onInputChangeProps]
  );

  useEffect(() => {
    const elem = selectRef.current?.select.menuListRef;
    if (!elem) return undefined;
    let onTriggerCallback: () => Promise<void> | undefined;
    if (triggerLoadMore) {
      onTriggerCallback = () => triggerCallback(elem);
      (elem as unknown as HTMLElement).addEventListener(
        "scroll",
        onTriggerCallback
      );
    }

    return () => {
      if (onTriggerCallback) {
        (elem as unknown as HTMLElement).removeEventListener(
          "scroll",
          onTriggerCallback
        );
      }
    };
  }, [triggerCallback, triggerLoadMore, isExpanded]);

  const onMenuOpen = useCallback(() => {
    setExpanded(true);
  }, []);

  const onMenuClose = useCallback(() => {
    setExpanded(false);
  }, []);

  useImperativeHandle(
    register,
    () => ({
      reset: () => {
        const reactSelect = selectRef.current;
        if (reactSelect) {
          reactSelect.select?.clearValue();
        }
      },
    }),
    []
  );

  return (
    <div className="a-pulldown">
      <Select
        ref={selectRef}
        name={name}
        isDisabled={isDisabled}
        isLoading={loading.current}
        isMulti={isMultiSelect as unknown as false | undefined}
        {...innerProps}
        onInputChange={onInputChange}
        onMenuOpen={onMenuOpen}
        onMenuClose={onMenuClose}
        styles={{
          control: (provided, state) => ({
            ...provided,
            borderColor: errorMessage
              ? "rgb(241, 67, 54)"
              : "rgb(232, 232, 232)",
            boxShadow: state.isFocused
              ? "0px 0px 0px 0.5px rgb(74, 74, 74)"
              : undefined,
            borderRadius: 2,
            backgroundColor: isDisabled
              ? "rgb(248, 248, 248)"
              : "rgb(255, 255, 255)",
            height: isMultiSelect ? undefined : controlHeight,
            minHeight: controlHeight,
            transition: "all 0.3s ease-in-out",
            "&:hover": {
              borderColor: errorMessage
                ? "rgb(241, 67, 54)"
                : "rgb(74, 74, 74)",
            },
            "&:focus-within": {
              borderColor: errorMessage
                ? "rgb(241, 67, 54)"
                : "rgb(74, 74, 74)",
              boxShadow: errorMessage ? "0px 0px 4px rgb(241, 67, 54)" : "none",
            },
          }),
          singleValue: (provided) => ({
            ...provided,
            color: "rgb(74, 74, 74)",
          }),
          placeholder: (provided) => ({
            ...provided,
            fontSize: 14,
            lineHeight: 16,
            color: "rgb(207, 207, 207)",
          }),
          indicatorSeparator: (provided) => ({
            ...provided,
            display: "none",
          }),
          indicatorsContainer: (provided) => ({
            ...provided,
            color: isDisabled ? "rgb(207, 207, 207)" : "rgb(74, 74, 74)",
            "& .a-icon": {
              cursor: "pointer",
              width: 12,
              height: 12,
              "&:hover": {
                opacity: "0.7",
              },
            },
          }),
          option: (provided, state) => ({
            ...provided,
            color: state.isSelected ? "rgb(22, 132, 185)" : "rgb(74, 74, 74)",
            backgroundColor: state.isSelected
              ? "rgba(22, 132, 185, .15)"
              : "rgb(255, 255, 255)",
            transition: "all 0.1s ease-in-out",
            "&:hover": {
              color: "rgb(255, 255, 255)",
              backgroundColor: "rgb(22, 132, 185)",
            },
          }),
          menu: (provided) => ({
            ...provided,
            borderRadius: 2,
            marginTop: 6,
          }),
          menuList: (provided) => ({
            ...provided,
            padding: 0,
            "&::after": {
              height: !loading.current ? 0 : 36.8,
              visibility: !loading.current ? "hidden" : "visible",
            },
          }),

          multiValue: (provided) => ({
            ...provided,
            alignItems: "center",
            borderRadius: 13,
            backgroundColor: "rgb(255, 101, 104)",
          }),
          multiValueLabel: (provided) => ({
            ...provided,
            padding: "6px 0 6px",
            paddingLeft: 10,
            fontFamily: "Roboto",
            fontSize: 12,
            color: "#ffffff",
          }),
          multiValueRemove: (provided) => ({
            ...provided,
            padding: 2,
            margin: "4px 5px 4px 4px",
            borderRadius: "50%",
            color: "#ffffff",
            cursor: "pointer",
            transition: "all 0.15s",
          }),
          valueContainer: (provided) => ({
            ...provided,
            paddingTop: 6,
            paddingBottom: 6,
          }),
        }}
      />
      {errorMessage && (
        <div className="a-pulldown_errormessage">{errorMessage}</div>
      )}
    </div>
  );
};

interface PulldownHookFormProps extends Props {
  name: string;
}

export const PulldownHookForm: React.FC<PulldownHookFormProps> = (props) => {
  const { name, onChange, value } = props;
  const { watch, setValue } = useFormContext();
  const firstRenderRef = useRef<boolean>(true);
  const formValue = watch(name);
  useEffect(() => {
    if (onChange && !firstRenderRef.current) {
      onChange(
        formValue,
        null as unknown as ActionMeta<{ label: string; value: string }>
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [name, formValue]);

  useEffect(() => {
    if (!firstRenderRef.current) {
      setValue(name, value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [name, JSON.stringify(value)]);

  useEffect(() => {
    firstRenderRef.current = false;
  }, []);

  return (
    <ConnectForm>
      {
        (({ control, errors }: UseFormProps) => (
          <Controller
            as={
              <Pulldown
                // eslint-disable-next-line react/jsx-props-no-spreading
                {...props}
                errorMessage={
                  errors[props.name] ? errors[props.name].message : undefined
                }
              />
            }
            defaultValue={props.defaultValue}
            options={props.options}
            control={control}
            name={props.name}
          />
        )) as any
      }
    </ConnectForm>
  );
};
