import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import { UpdatePromotionDto } from "../dtos/promotion.dto";
import promotionServices from "../services/promotion.service";

export const useUpdatePromotion = () => {
  const [updatePromotionExe] = useAsync(
    useCallback(
      (data: UpdatePromotionDto) => promotionServices.updatePromotion(data),
      []
    )
  );

  return { updatePromotionExe };
};
