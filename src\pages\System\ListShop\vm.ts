/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useRef, useState } from "react";

import produce from "immer";

import { showNotification } from "components/atoms/base/Notification";
import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import { useLocationSelect, usePulldownStorages } from "modules/location";
import { createShopDto, shopDetailDto } from "modules/shop";
import { useGetCities } from "services/crm/location/hooks/location.hooks";
import {
  createShop as createShopService,
  getShops,
} from "services/crm/location/shop";
import { ShopFilterPayload } from "services/crm/system";

import RootPageRouter from "..";
import { CreateShopFormPayload } from "./constant";
import { DeleteShop } from "./hook/useDeleteShop";

type ModalType = "createShop";

export type FilterShopPayload = Omit<ShopFilterPayload, "pageNum" | "pageSize">;

interface FilterState {
  searchPayload?: FilterShopPayload;
}

interface State {
  modalState: {
    open: boolean;
    modalType?: ModalType;
  };
  pagination: {
    pageSize: number;
  };
  filterState: FilterState;
}

export const ShopPageVm = () => {
  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      modalType: undefined,
    },
    pagination: {
      pageSize: 10,
    },
    filterState: {
      searchPayload: undefined,
    },
  });
  const { deleteShopExe } = DeleteShop();
  const isFilter = useRef<boolean>(false);
  const isSearch = useRef<boolean>(false);
  const { cityRefetch, cityData, loading } = useGetCities();
  const setFilterSearchPayload = useCallback(
    (searchPayload: FilterShopPayload) =>
      setState(
        produce((draft) => {
          draft.filterState.searchPayload = searchPayload;
        })
      ),
    []
  );

  const [getShopsExec, getShopsState] = useAsync(
    useCallback(
      (options: ShopFilterPayload) =>
        getShops({ ...options }).then((res) => {
          isFilter.current = false;
          isSearch.current = false;
          return {
            data: mapFrom(res.data.data, shopDetailDto),
            pagination: paginationDTO(res.data.links),
          };
        }),
      []
    )
  );

  const handleCloseModal = useCallback(() => {
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.modalType = undefined;
      })
    );
  }, []);

  const [createShopExec] = useAsync(
    useCallback(async (formPayload: CreateShopFormPayload) => {
      const {
        city,
        district,
        ward,
        type,
        storages,
        bankAccounts,
        employees,
        isFranchisedShop,
        ...rest
      } = formPayload;

      const dataCreateShop = createShopDto({
        ...rest,
        cityId: city.value,
        districtId: district.value,
        wardId: ward.value,
        type: type.value,
        storageIds: storages.map((item) => item.value),
        bankAccountIds: bankAccounts.map((item) => item.bankAccountId),
        employeeIds: employees.map((item) => item.employeeId),
        isFranchisedShop,
      });

      await createShopService(dataCreateShop);
    }, []),
    {
      excludePending: true,
      onFailed: useCallback((error: any) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Tạo mới thành công" });
        handleCloseModal();
        RootPageRouter.children.listShop.goto();
      }, [handleCloseModal]),
    }
  );

  const { gotoPage, ...shopPaginationState } = usePagination({
    pageSize: state.pagination.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getShopsExec({
        pageSize,
        pageNum: page,
        ...state.filterState.searchPayload,
      }),
  });

  const handleFilterSearchPayload = (value) => {
    getShopsExec({
      pageSize: state.pagination.pageSize,
      pageNum: 1,
      ...value,
    });
  };

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize, state.filterState.searchPayload]);

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pagination.pageSize = pageSize;
      })
    );
    // pageSizeRef.current = pageSize;
  }, []);

  const { sortedData: listShop, toggleSortState: toggleSortShopBy } =
    useSortable({
      data: getShopsState.data?.data,
      sortBy: {
        name: (shop) => shop.name,
        updatedAt: (shop) => shop.updatedAt,
        franchise: (shop) => shop.isFranchisedShop,
        displayOrder: (shop) => shop.displayOrder,
      },
    });

  const { storageOptions } = usePulldownStorages({
    excludePending: false,
  });

  const { cityOptions } = useLocationSelect();

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.modalType = type;
      })
    );
  }, []);

  const gotoShopPage = useCallback(
    ({ shopId, action }: { shopId: number | string; action?: string }) =>
      RootPageRouter.gotoChild("shopDetailV2", {
        params: { shopId: shopId.toString() },
        queryString: action ? `?action=${action}` : undefined,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const modalTypeIsOpen = useCallback(
    (type: ModalType) => {
      return state.modalState.open && state.modalState.modalType === type;
    },
    [state.modalState]
  );

  const handleDeleteShop = async (id: number) => {
    deleteShopExe(id)
      .then((result) => {
        if (result.status === 200) {
          toastSingleMode({
            type: "success",
            message: "Xóa cửa hàng thành công",
          });
          getShopsExec({
            pageSize: 10,
            pageNum: 1,
          });
        } else {
          toastSingleMode({
            type: "error",
            message: "Xóa cửa hàng thất bại",
          });
        }
      })
      .catch((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.detail
        );
        console.log(error, "errorerror");

        showNotification({
          type: "error",
          message: error?.response?.data?.errors?.[0]?.detail,
        });
      });
  };

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize, state.filterState.searchPayload]);

  useEffect(() => {
    cityRefetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return {
    shopPaginationState,
    listShop: listShop || [],
    toggleSortShopBy,
    loading: getShopsState.loading,
    pageSize: state.pagination.pageSize,
    handleChangePageSize,
    gotoPage,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    createShop: createShopExec,
    setFilterSearchPayload,
    storageOptions,
    cityOptions,
    filterState: state.filterState.searchPayload,
    isFilter,
    isSearch,
    gotoShopPage,
    handleDeleteShop,
    handleFilterSearchPayload,
    cityData,
  };
};
