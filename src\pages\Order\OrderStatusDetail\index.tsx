import React, { use<PERSON><PERSON>back, useMemo, useRef } from "react";

import { useSearchParams, useParams } from "react-router";

import { Button } from "components/atoms/button";
import { ColorPicker } from "components/atoms/colorpicker";
import { Heading } from "components/atoms/heading";
import { NumberfieldHookForm } from "components/atoms/numberfield";
import { PulldownHookForm } from "components/atoms/pulldown";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Toggle } from "components/atoms/toggle";
import { Formfield } from "components/molecules/formfield";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import { BasePageProps } from "helpers/component";
import { FormContainer } from "helpers/form";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";

import { ChildrenPage } from "../types";
import { OrderStatusFormType, OrderStatusValidationSchema } from "./constant";
import { OrderStatusDetailPageVm } from "./vm";

const IndexPage: React.FC<BasePageProps> = () => {
  const defaultOrderStatusRef = useRef<HTMLInputElement | null>(null);
  const colorPickerRef = useRef<string>();
  const [searchParams] = useSearchParams();

  const { orderStatusCode } =
    useParams<PageParamsType<ChildrenPage["orderStatusDetail"]>>();

  const {
    loading,
    orderStatusDetail,
    orderStatusOptions,
    updateOrderStatusState,
    handleUpdateOrderStatus,
    orderStatusOptionPulldown,
    handleLoadmoreOrderStatus,
  } = OrderStatusDetailPageVm({
    orderStatusCode,
  });

  const pageActionType = searchParams.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;
  const onSubmitOrderStatusUpdate = useCallback(
    (formData: OrderStatusFormType) => {
      handleUpdateOrderStatus({
        ...formData,
        isDefault: defaultOrderStatusRef.current?.checked,
        displayOrder: Number(formData.displayOrder),
        color: colorPickerRef.current,
        prevOrderStatusIds: formData.prevOrderStatus?.map((item) =>
          Number(item.value)
        ),
      });
    },
    [handleUpdateOrderStatus]
  );

  const { name, color, displayOrder, description, isDefault } =
    orderStatusDetail || {};

  return (
    <SpinnerContainer animating={loading}>
      <General>
        <title key="title">Sửa thông tin trạng thái đơn hàng</title>
        <Section>
          <Heading modifiers="primary" type="h1">
            SỬA THÔNG TIN TRẠNG THÁI ĐƠN HÀNG
          </Heading>
          <Section>
            {!loading && (
              <FormContainer
                validationSchema={
                  editMode ? OrderStatusValidationSchema : undefined
                }
                onSubmit={onSubmitOrderStatusUpdate}
              >
                <Row>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Tên trạng thái" name="name">
                      <TextfieldHookForm
                        name="name"
                        placeholder="Tên trạng thái"
                        defaultValue={name}
                        readOnly={viewMode}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Màu sắc" name="color">
                      <ColorPicker
                        defaultColor={color}
                        onChangeClolor={(colorpicker) => {
                          colorPickerRef.current = colorpicker;
                        }}
                        disabled={viewMode}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Thứ tự hiển thị" name="displayOrder">
                      <NumberfieldHookForm
                        name="displayOrder"
                        placeholder="Thứ tự hiển thị"
                        defaultValue={displayOrder}
                        readOnly={viewMode}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Trạng thái trước" name="prevOrderStatus">
                      <PulldownHookForm
                        placeholder="Trạng thái trước"
                        name="prevOrderStatus"
                        isMultiSelect
                        defaultValue={orderStatusOptions}
                        options={orderStatusOptionPulldown}
                        isDisabled={viewMode}
                        triggerLoadMore={handleLoadmoreOrderStatus}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Mô tả" name="description">
                      <TextareafieldHookForm
                        name="description"
                        placeholder="Mô tả"
                        defaultValue={description}
                        readOnly={viewMode}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="12">
                    <Toggle
                      label="Mặc định"
                      defaultChecked={isDefault}
                      ref={defaultOrderStatusRef}
                      disabled={viewMode}
                    />
                  </Col>
                </Row>
                <div className="d-flex justify-content-end u-mt-20">
                  <div className="u-mr-15">
                    <Button
                      buttonType="outline"
                      modifiers="secondary"
                      onClick={navigationHelper.goBack}
                    >
                      QUAY LẠI
                    </Button>
                  </div>
                  {editMode && (
                    <Button
                      type="submit"
                      isLoading={updateOrderStatusState.loading}
                      disabled={updateOrderStatusState.loading}
                    >
                      Lưu
                    </Button>
                  )}
                </div>
              </FormContainer>
            )}
          </Section>
        </Section>
      </General>
    </SpinnerContainer>
  );
};

export default IndexPage;
