import { <PERSON>, <PERSON>a } from "@storybook/react/types-6-0";

import { Numberfield, Props } from ".";

export default {
  title: "Components|atoms/Numberfield",
  component: Numberfield,
} as Meta;

const Template: Story<Props> = ({ placeholder, disabled, errorMessage }) => (
  <Numberfield
    placeholder={placeholder}
    disabled={disabled}
    errorMessage={errorMessage}
  />
);

export const Normal = Template.bind({});

Normal.args = {
  placeholder: "12",
  disabled: false,
  errorMessage: "",
};
