import { lazy } from "react";

import PATHS from "constants/paths";
import { createAppPage } from "libs/react";

import { ChildrenPage } from "./types";

export default createAppPage<{}, ChildrenPage>({
  name: "CustomerManagement",
  path: PATHS.CUSTOMERS,
  childrenPages: {
    listCustomer: createAppPage({
      name: "ListCustomer",
      path: "/DanhSachKhachHang",
      page: () => lazy(() => import("./ListCustomer")),
    }),
    customerDetail: createAppPage({
      name: "CustomerDetail",
      path: "/DanhSachKhachHang/chi-tiet/:customerId",
      page: () => lazy(() => import("./CustomerDetail")),
    }),
    addCustomer: createAppPage({
      name: "CreateCustomer",
      path: "/DanhSachKhachHang/them-khach-hang",
      page: () => lazy(() => import("./CustomerDetail/PartnerDetailV2")),
    }),
    customerDetailV2: createAppPage({
      name: "CustomerDetailV2",
      path: "/DanhSachKhachHang/chi-tiet-khach-hang/:customerId",
      page: () => lazy(() => import("./CustomerDetail/PartnerDetailV2")),
    }),
    groupCustomer: createAppPage({
      name: "GroupCustomer",
      path: "/NhomKhachHang",
      page: () => lazy(() => import("./Group-customer/groupCustomer")),
    }),
    groupCustomerDetail: createAppPage({
      name: "GroupCustomerDetail",
      path: "/NhomKhachHang/chi-tiet/:groupCustomerId",
      page: () =>
        lazy(() => import("./GroupCustomerDetail/groupCustomerDetail")),
    }),
  },
});
