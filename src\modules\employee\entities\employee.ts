import { Model, ModelValue, Number, String } from "libs/domain";

export const EmployeeSchema = {
  employeeId: Number(),
  employeeGroupId: Number(),
  name: String(),
  userName: String(),
  site: String(),
  phoneNumber: String(),
  email: String(),
  picture: String(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  // updatedAt: (raw: any) => new Date(raw),
  updatedAt: String(),
};

export const EmployeeModel = new Model(EmployeeSchema);
export type EmployeeEntityType = ModelValue<typeof EmployeeModel>;
