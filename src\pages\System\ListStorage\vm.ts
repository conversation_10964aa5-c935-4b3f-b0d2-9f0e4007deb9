/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useState } from "react";

import produce from "immer";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { paginationDTO } from "modules/common/pagination";
import { StorageModel } from "modules/location/entities";
import { createStorage, getListStorage } from "services/crm/system";

type ModalType = "storageCreation";

interface State {
  pageSize: number;
  modal: {
    open: boolean;
    type?: ModalType;
  };
}

export const ListStoragePageVm = () => {
  const [state, setState] = useState<State>({
    pageSize: 10,
    modal: {
      open: false,
      type: "storageCreation",
    },
  });

  const [getListStorageExec, getListStorageState] = useAsync(
    useCallback(
      (pageNum: number, pageSize: number) =>
        getListStorage({ pageNum, pageSize }).then((res) => ({
          listStorage: StorageModel.createMap(res.data.data),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const { gotoPage, ...listStoragePaginationState } = usePagination({
    pageSize: state.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getListStorageExec(page, pageSize),
  });

  const { sortedData: listStorage, toggleSortState: toggleListStorageBy } =
    useSortable({
      data: getListStorageState.data?.listStorage,
      sortBy: {
        name: ({ name }) => name,
        code: ({ code }) => code,
        updatedAt: ({ updatedAt }) => updatedAt,
      },
    });

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pageSize = pageSize;
      })
    );
  }, []);

  const handleOpenModalByType = useCallback(
    (type: ModalType) =>
      setState(
        produce((draft) => {
          draft.modal.open = true;
          draft.modal.type = type;
        })
      ),
    []
  );

  const handleCloseModal = useCallback(
    () =>
      setState(
        produce((draft) => {
          draft.modal.open = false;
          draft.modal.type = undefined;
        })
      ),
    []
  );

  const [createStorageExec, createStorageState] = useAsync(createStorage, {
    onFailed: useCallback((error: any) => {
      const errMessage = getErrorMessageViaErrCode(
        error?.response?.data?.errors?.[0]?.code
      );
      toastSingleMode({
        type: "error",
        message: errMessage.translation.title,
        descripition: errMessage.translation.detail,
      });
    }, []),
    onSuccess: useCallback(() => {
      handleCloseModal();
      toastSingleMode({
        type: "success",
        message: "Tạo mới thành công",
      });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []),
  });

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pageSize]);

  return {
    loading: getListStorageState.loading,
    gotoPage,
    listStoragePaginationState,
    listStorage: listStorage || [],
    toggleListStorageBy,
    pageSize: state.pageSize,
    modalState: state.modal,
    handleChangePageSize,
    handleOpenModalByType,
    handleCloseModal,
    createStorageExec,
    createStorageState,
  };
};
