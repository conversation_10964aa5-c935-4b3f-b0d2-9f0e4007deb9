import * as Yup from "yup";

import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { PhonefieldHookForm } from "components/atoms/phonefield";
import { Radio, useRadioProvider } from "components/atoms/radio";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Calendar, useCalendarProvider } from "components/molecules/calendar";
import { Formfield } from "components/molecules/formfield";
import { Modal } from "components/organisms/modal";
import { FormContainer } from "helpers/form";
import { Gender } from "modules/customer";

export interface CustomerCreateModalProps<DataForm> {
  open: boolean;
  onClose: () => void;
  loading: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  inputValidationSchema?: Yup.ObjectSchema<Yup.AnyObject>;
  handlerCreateCustomer: (formData: DataForm) => void;
}

export const CreateUpdateCustomerModal = <DataForm,>({
  open,
  onClose,
  loading,
  inputValidationSchema,
  handlerCreateCustomer,
}: CustomerCreateModalProps<DataForm>) => {
  const { setDate } =
    useCalendarProvider<{
      birthDay: Date;
    }>();

  const { bindSilent } =
    useRadioProvider<{
      gender: Gender;
    }>();

  return (
    <Modal
      style={{ content: { maxWidth: 700 } }}
      onCloseModal={onClose}
      isClosable={false}
      isOpen={open}
    >
      <Heading type="h1" centered>
        TẠO MỚI KHÁCH HÀNG
      </Heading>
      <FormContainer
        validationSchema={inputValidationSchema}
        onSubmit={handlerCreateCustomer}
      >
        <Formfield label="Họ và tên" name="name">
          <TextfieldHookForm name="name" placeholder="Nhập họ và tên" />
        </Formfield>

        <Formfield label="Số điện thoại" name="phoneNumber">
          <PhonefieldHookForm
            name="phoneNumber"
            placeholder="Nhập số điện thoại"
          />
        </Formfield>

        <Formfield label="Email" name="email">
          <TextfieldHookForm name="email" placeholder="Nhập email" />
        </Formfield>

        <Formfield label="Ngày sinh" name="birthDay">
          <Calendar onDateChange={(date) => setDate("birthDay", date)} />
        </Formfield>

        <Formfield label="Giới tính" name="gender">
          <div className="d-flex align-items-center">
            <div className="u-pr-13">
              <Radio
                onChange={() => bindSilent("gender")}
                name="gender"
                value={1}
                defaultChecked
                readOnly
              >
                Nam
              </Radio>
            </div>
            <div>
              <Radio
                onChange={() => bindSilent("gender")}
                name="gender"
                value={0}
                readOnly
              >
                Nữ
              </Radio>
            </div>
          </div>
        </Formfield>

        <div className="d-flex justify-content-end u-mt-20">
          <div className="u-mr-15">
            <Button
              buttonType="outline"
              modifiers="secondary"
              onClick={onClose}
            >
              HUỶ
            </Button>
          </div>
          <Button type="submit" disabled={loading} isLoading={loading}>
            LƯU
          </Button>
        </div>
      </FormContainer>
    </Modal>
  );
};
