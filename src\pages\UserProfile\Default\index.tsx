import React from "react";

import { Button } from "components/atoms/button";
import { Checkbox } from "components/atoms/checkbox";
import { Heading } from "components/atoms/heading";
import { PhonefieldHookForm } from "components/atoms/phonefield";
import { PulldownHookForm } from "components/atoms/pulldown";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Tabs, TabList, Tab, TabPanel } from "components/organisms/tabs";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import { BasePageProps } from "helpers/component";
import { FormContainer } from "helpers/form";
import * as navigationHelper from "helpers/navigation";
import { useAppSelector } from "store";

import { dummyUserPermissions, inputValidationSchema } from "./constant";
import { UserProfilePageVm } from "./vm";

const IndexPage: React.FC<BasePageProps> = () => {
  const { loading, employeeInfo } = useAppSelector((state) => state.auth);

  const { formatEmployeeGroupOptions } = UserProfilePageVm();

  return (
    <SpinnerContainer animating={loading}>
      <General>
        <title key="title">Thông tin cá nhân</title>

        <Section>
          <Heading type="h1" modifiers="primary">
            THÔNG TIN CÁ NHÂN
          </Heading>
          <Section>
            <Tabs>
              <TabList>
                {["THÔNG TIN CHUNG", "QUYỀN TRUY CẬP"].map((title) => (
                  <Tab key={title}>{title}</Tab>
                ))}
              </TabList>

              <TabPanel>
                {employeeInfo && (
                  <FormContainer validationSchema={inputValidationSchema}>
                    <Row>
                      <Col lg="12" className="u-mb-15">
                        <Formfield label="Vai trò truy cập" name="roles">
                          <PulldownHookForm
                            placeholder="Chọn vai trò truy cập"
                            name="roles"
                            id="roles"
                            isMultiSelect
                            isDisabled
                            defaultValue={formatEmployeeGroupOptions(
                              employeeInfo.employeeGroups
                            )}
                          />
                        </Formfield>
                      </Col>

                      <Col lg="12" className="u-mb-15">
                        <Formfield label="Họ và tên" name="name">
                          <TextfieldHookForm
                            placeholder="Nhập họ và tên"
                            name="name"
                            id="name"
                            defaultValue={employeeInfo.name}
                          />
                        </Formfield>
                      </Col>

                      <Col lg="12" className="u-mb-15">
                        <Formfield label="Email" name="email">
                          <TextfieldHookForm
                            placeholder="Nhập email"
                            name="email"
                            id="email"
                            defaultValue={employeeInfo.email}
                          />
                        </Formfield>
                      </Col>

                      <Col lg="12">
                        <Formfield label="Số điện thoại" name="phoneNumber">
                          <PhonefieldHookForm
                            placeholder="Nhập số điện thoại"
                            name="phoneNumber"
                            id="phoneNumber"
                            defaultValue={employeeInfo.phoneNumber}
                          />
                        </Formfield>
                      </Col>
                    </Row>
                  </FormContainer>
                )}
              </TabPanel>

              <TabPanel>
                {/* TODO: Missing API */}
                {dummyUserPermissions.map((item, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <Section key={index}>
                    <Heading modifiers="primary">{item.role}</Heading>
                    <Row className="u-mt-15">
                      {item.permissions.map((permission) => (
                        <Col key={permission.name} lg="3">
                          <Checkbox
                            defaultChecked={permission.isActive}
                            disabled
                          >
                            {permission.name}
                          </Checkbox>
                        </Col>
                      ))}
                    </Row>
                  </Section>
                ))}
              </TabPanel>
            </Tabs>
          </Section>
          <Section>
            <div className="d-flex justify-content-end">
              <Button
                modifiers="secondary"
                buttonType="outline"
                onClick={navigationHelper.goBack}
              >
                QUAY LẠI
              </Button>
              <div className="u-ml-15">
                <Button type="submit">LƯU</Button>
              </div>
            </div>
          </Section>
        </Section>
      </General>
    </SpinnerContainer>
  );
};
export default IndexPage;
