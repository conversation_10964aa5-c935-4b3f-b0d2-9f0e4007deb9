import { forwardRef, useImperativeHandle, useRef, useState } from "react";

import { ButtonComponent } from "@syncfusion/ej2-react-buttons";
import { TextBoxComponent } from "@syncfusion/ej2-react-inputs";
import { DialogComponent } from "@syncfusion/ej2-react-popups";

type Props = {
  onAddNote?: (note: string) => void;
};

export type TAddNote = {
  show(): void;
  hide(): void;
};

export const AddNote = forwardRef<TAddNote, Props>(({ onAddNote }, ref) => {
  const [visible, setVisible] = useState(false);
  const textRef = useRef<TextBoxComponent>(null);

  useImperativeHandle(ref, () => ({
    show: handleShow,
    hide: handleHide,
  }));

  const handleShow = () => {
    setVisible(true);
  };

  const handleHide = () => {
    setVisible(false);
    if (textRef.current) textRef.current.value = "";
  };

  const handleSave = () => {
    if (onAddNote && textRef.current && textRef.current.value) {
      onAddNote(textRef.current.value);
      handleHide();
    }
  };

  return (
    <DialogComponent
      header="Thêm note"
      visible={visible}
      width="500px"
      close={handleHide}
      showCloseIcon
      isModal
      content={() => (
        <>
          <div className="m-2">
            <TextBoxComponent
              ref={textRef}
              multiline
              cssClass="e-input-required"
            />
          </div>
          <div className="d-flex justify-content-end m-2">
            <ButtonComponent
              className="mx-1"
              style={{ width: "70px" }}
              onClick={handleHide}
            >
              Huỷ
            </ButtonComponent>
            <ButtonComponent
              className="mx-1"
              style={{ width: "70px" }}
              isPrimary
              onClick={handleSave}
            >
              Lưu
            </ButtonComponent>
          </div>
        </>
      )}
    />
  );
});
