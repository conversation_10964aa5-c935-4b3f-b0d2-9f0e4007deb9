import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import { GetListItemLocationStockBySkyIdDto } from "../dtos/item-location-stock.dto";
import locationService from "../location.service";

export const useGetItemStockLocationBySkuId = () => {
  const [getItemStockLocationRefetch, getItemStockLocationData] = useAsync(
    useCallback((dto: GetListItemLocationStockBySkyIdDto) => {
      return locationService.getListLocationStock(dto);
    }, [])
  );
  return {
    getItemStockLocationRefetch,
    getItemStockLocationData: getItemStockLocationData?.data?.data,
    loading: getItemStockLocationData?.loading,
  };
};
