import { useEffect } from "react";

import { ActionSubscribeCallback, Event } from "../core";

export const useEvent = <P>(
  event: Event<P>,
  subscriber: ActionSubscribeCallback<P>
) => {
  useEffect(() => {
    const currentSubscriber = subscriber;
    const currentEvent = event;

    currentEvent.subscribe(currentSubscriber);
    return () => {
      currentEvent.unsubscribe(currentSubscriber);
    };
  }, [event, subscriber]);
};
