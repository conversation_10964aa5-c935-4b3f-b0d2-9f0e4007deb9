.o-accordion {
	$root: &;
	position: relative;

	&_close {
		position: absolute;
		top: rem(-11);
		right: rem(-11);
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: $COLOR-CINNABAR-2;
		border-radius: rem(100);
		.a-icon {
			background-size: rem(12);
		}
		&:hover {
			cursor: pointer;
			opacity: 0.8;
		}
	}

	&_title {
		font-family: $FONTFAMILY-ARIAL;
		@include u-fw-bold;
		line-height: rem(18);

		#{$root}-default & {
			font-size: rem(14);
			color: $COLOR-QUARTZ;
		}

		#{$root}-highlight & {
			font-size: rem(20);
			color: $COLOR-WHITE;
		}
	}

	&_content {
		width: 100%;
		height: 0;
		padding-right: rem(41);
		padding-left: rem(40);
		overflow: hidden;
		background-color: $COLOR-WHITE;
		transition: all 0.2s ease;
		#{$root}-default & {
			border: 0px solid $COLOR-PLATINUM-6;
		}
		#{$root}-highlight & {
			border: 0px solid $COLOR-DENIM;
			border-top: 0;
		}
	}

	&_collapse {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: rem(16) rem(21);
		cursor: pointer;

		#{$root}-default & {
			border-left: rem(4) solid $COLOR-DENIM;
			transition: all 0.3s ease-in-out;
			&:hover {
				background-color: $COLOR-BLACK-2;
			}

			& > .a-icon-caret-down {
				width: rem(12);
				height: rem(12);
				transform: rotate(0deg);
				animation-name: rotate-caret-inactive;
				animation-duration: 0.25s;
			}

			&.active > .a-icon-caret-down {
				transform: rotate(180deg);
				animation-name: rotate-caret-active;
				animation-duration: 0.25s;
			}
		}

		#{$root}-highlight & {
			background-color: $COLOR-DENIM;
			border-radius: rem(5);

			& > .a-icon-plus {
				width: rem(20);
				height: rem(20);
				animation-name: rotate-caret-inactive;
				animation-duration: 0.25s;
			}

			&.active > .a-icon-plus {
				background-image: url("~assets/images/icons/minus.svg");
				animation-name: rotate-caret-active;
				animation-duration: 0.25s;
			}
		}
	}

	&-default {
		background-color: $COLOR-BLACK-1;
		border-top: 1px solid $COLOR-PLATINUM-6;
		border-bottom: 1px solid $COLOR-PLATINUM-6;
		&.active {
			border-bottom: 0;
		}
	}

	&-highlight {
		background-color: $COLOR-DENIM;
		border-radius: rem(5);
		&.active {
			border-radius: rem(5) rem(5) 0 0;
		}
	}

	&_wrapped {
		display: flex;
	}
}

@keyframes rotate-caret-active {
	0% {
		transform: rotate(90deg);
	}
	100% {
		transform: rotate(180deg);
	}
}

@keyframes rotate-caret-inactive {
	0% {
		transform: rotate(90deg);
	}
	100% {
		transform: rotate(0deg);
	}
}

@keyframes rotate-plus-active {
	0% {
		transform: rotate(30deg);
	}
	100% {
		transform: rotate(90deg);
	}
}

@keyframes rotate-plus-inactive {
	0% {
		transform: rotate(90deg);
	}
	100% {
		transform: rotate(180deg);
	}
}
