import React, { useEffect, useRef, useState } from "react";
import { DeleteFilled, EditFilled, EyeFilled } from "@ant-design/icons";
import { Flex, Modal, Table, Typography } from "antd";
import { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import _ from "lodash";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import {
  FilterPickerCollapse,
  FilterType,
} from "components/atoms/base/shared/FilterPickerCollapse";
import { showLoading } from "components/atoms/base/Spinner";
import { COLOR } from "constants/color";
import RootPageRouter from "..";
import { useCreatePromotion } from "./hooks/useCreatePromotion";
import { useDeletePromotion } from "./hooks/useDeletePromotion";
import { useGetListPromotion } from "./hooks/useGetListPromotion";
import { useUpdatePromotion } from "./hooks/useUpdatePromotion";
import PromotionUpsertModal, {
  PromotionsUpsertModalRefType,
} from "./modal/PromotionsUpsertModal";

const { Title, Text } = Typography;

type PromotionFilterType = Pick<FilterType, "inputSearch" | "rangeDateObject">;

export interface PromotionDataType {
  key: number | string;
  promotionId: number;
  promotionCode: string;
  promotionName: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
}

interface FilterValuesType {
  inputSearch?: string | null;
  startDate?: string | null;
  endDate?: string | null;
}

export default function PromotionManagementPage() {
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [dataSource, setDataSource] = useState<PromotionDataType[]>([]);
  const [filterValues, setFilterValues] = useState<FilterValuesType>({
    inputSearch: "",
    startDate: null,
    endDate: null,
  });

  const promotionUpsertModalRef = useRef<PromotionsUpsertModalRefType>(null);

  const handleFinishFilter = (values: PromotionFilterType) => {
    setFilterValues({
      inputSearch: values.inputSearch || "",
      startDate: values.rangeDateObject.from
        ? dayjs(values.rangeDateObject.from).startOf("day").toISOString()
        : null,
      endDate: values.rangeDateObject.to
        ? dayjs(values.rangeDateObject.to).endOf("day").toISOString()
        : null,
    });
    setPage(1);
  };

  const handleNavigateToPromotionDetail = (
    promotionId?: number,
    mode?: "view" | "edit"
  ) => {
    if (promotionId) {
      RootPageRouter.gotoChild("promotionDetail", {
        params: { promotionId: String(promotionId) },
        queryString: `?action=${mode || "view"}`,
      });
    } else {
      RootPageRouter.gotoChild("promotionDetail", {
        params: { promotionId: "AddNew" },
      });
    }
  };
  const { promotionData, loading, promotionRefetch } = useGetListPromotion({
    pageNum: page,
    pageSize,
    ...filterValues,
  });
  const { createPromotionExe } = useCreatePromotion();
  const { updatePromotionExe } = useUpdatePromotion();
  const { deletePromotionExe } = useDeletePromotion();

  const handleConfirmDelete = (record: PromotionDataType) => {
    Modal.confirm({
      centered: true,
      title: "Xác nhận xóa",
      content: `Bạn có chắc chắn muốn xóa chương trình khuyến mãi "${record.promotionName}"?`,
      okText: "Xóa",
      cancelText: "Hủy",
      okButtonProps: {
        className: "bg-red-500 hover:!bg-red-700 text-white",
      },
      onOk: () => {
        showLoading(true);
        deletePromotionExe({ promotionId: record.promotionId })
          .then(() => {
            promotionRefetch();
            showNotification({
              type: "success",
              message: "Xóa khuyến mãi thành công",
            });
          })
          .catch((err) => {
            const errorApi = err?.response?.data?.errors || [];
            if (errorApi.length > 0) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              errorApi.forEach((error: any) => {
                showNotification({
                  type: "error",
                  message: error.detail,
                });
              });
            }
          })
          .finally(() => {
            showLoading(false);
          });
      },
    });
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleCreateOrUpdatePrmotion = (values: any) => {
    const payload = {
      ...values,
      startDate: dayjs(values.startDate).startOf("day").toISOString(),
      endDate: dayjs(values.endDate).endOf("day").toISOString(),
    };
    showLoading(true);
    if (values.promotionId) {
      updatePromotionExe(payload)
        .then(() => {
          promotionUpsertModalRef.current?.close();
          promotionRefetch();
          showNotification({
            type: "success",
            message: "Cập nhật khuyến mãi thành công",
          });
        })
        .catch((err) => {
          const errorApi = err?.response?.data?.errors || [];
          if (errorApi.length > 0) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            errorApi.forEach((error: any) => {
              showNotification({
                type: "error",
                message: error.title,
              });
            });
          }
        })
        .finally(() => {
          showLoading(false);
        });
      return;
    }
    createPromotionExe(payload)
      .then(() => {
        promotionUpsertModalRef.current?.close();
        promotionRefetch();
        showNotification({
          type: "success",
          message: "Tạo khuyến mãi thành công",
        });
      })
      .catch((err) => {
        const errorApi = err?.response?.data?.errors || [];
        if (errorApi.length > 0) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          errorApi.forEach((error: any) => {
            showNotification({
              type: "error",
              message: error.title,
            });
          });
        }
      })
      .finally(() => {
        showLoading(false);
      });
  };

  const columns: ColumnsType<PromotionDataType> = [
    {
      title: "STT",
      dataIndex: "ordinalNumber",
      key: "ordinalNumber",
      width: 100,
      align: "center",
    },

    {
      title: "Mã khuyến mãi",
      dataIndex: "promotionCode",
      key: "promotionCode",
      width: 144,
      align: "center",
    },

    {
      title: "Tên khuyến mãi",
      dataIndex: "promotionName",
      key: "promotionName",
      width: 224,
      align: "left",
    },

    {
      title: "Ngày bắt đầu",
      dataIndex: "startDate",
      key: "startDate",
      width: 144,
      align: "center",
      render: (date) => dayjs(date).format("DD/MM/YYYY"),
    },

    {
      title: "Ngày kết thúc",
      dataIndex: "endDate",
      key: "endDate",
      width: 144,
      align: "center",
      render: (date) => dayjs(date).format("DD/MM/YYYY"),
    },

    {
      title: "Trạng thái",
      dataIndex: "isActive",
      key: "isActive",
      width: 100,
      align: "center",
      render: (isActive) => (
        <span
          style={{
            color: isActive ? COLOR.GREEN[500] : COLOR.RED[500],
          }}
        >
          {isActive ? "Đang kích hoạt" : "Chưa kích hoạt"}
        </span>
      ),
    },

    {
      title: "Thao tác",
      key: "action",
      width: 100,
      align: "center",
      render: (__, record) => (
        <Flex justify="center" gap={12}>
          {/* <BaseButton
            onClick={() =>
              // handleNavigateToPromotionDetail(record.promotionId, "view")
              
            }
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            icon={<EyeFilled />}
          /> */}
          <BaseButton
            onClick={
              () => promotionUpsertModalRef.current?.open(record)
              // handleNavigateToPromotionDetail(record.promotionId, "edit")
            }
            type="primary"
            bgColor={COLOR.GREEN[500]}
            hoverColor={COLOR.GREEN[700]}
            icon={<EditFilled />}
          />
          <BaseButton
            onClick={() => {
              handleConfirmDelete(record);
            }}
            type="primary"
            bgColor={COLOR.RED[500]}
            hoverColor={COLOR.RED[700]}
            icon={<DeleteFilled />}
          />
        </Flex>
      ),
    },
  ];

  useEffect(() => {
    promotionRefetch();
    // Here you can implement the logic to fetch data based on filterValues
  }, [filterValues, page, pageSize]);

  useEffect(() => {
    if (_.size(promotionData?.data) > 0) {
      const convertData = promotionData?.data.map((item, index) => ({
        ...item,
        key: item.promotionId,
        ordinalNumber: (page - 1) * pageSize + index + 1,
        startDate: new Date(item.startDate),
        endDate: new Date(item.endDate),
      }));
      setDataSource(convertData);
    } else {
      setDataSource([]);
    }
  }, [promotionData]);

  return (
    <>
      <div className="flex bg-white flex-col gap-4 m-8 p-6 rounded-xl">
        <div className="flex flex-col gap-2">
          <Title className="m-0" level={4}>
            Quản lý chương trình khuyến mãi
          </Title>
          <Text>Danh sách các chương trình khuyến mãi hiện có</Text>
        </div>

        <FilterPickerCollapse
          onFinish={handleFinishFilter}
          customLabels={{
            inputSearch: "Tìm kiếm",
          }}
          defaultSelectedKeys={["inputSearch", "rangeDateObject"]}
          listFieldsUsed={["inputSearch", "rangeDateObject"]}
          buttonActions={{
            add: () => {
              promotionUpsertModalRef.current?.open();
              // handleNavigateToPromotionDetail();
            },
          }}
        />

        <Table
          loading={loading}
          tableLayout="auto"
          scroll={{ x: "max-content" }}
          columns={columns}
          dataSource={dataSource}
          pagination={{
            current: page,
            pageSize,
            total: promotionData?.meta?.totalRecords || 0,
            onChange: (offset, limit) => {
              setPage(offset);
              setPageSize(limit);
            },
            showSizeChanger: true,
            showTotal: (total) => `Tổng số ${total} bản ghi`,
            pageSizeOptions: [5, 10, 20, 50, 100],
            onShowSizeChange: (__, size) => {
              setPage(1);
              setPageSize(size);
            },
          }}
        />
      </div>
      <PromotionUpsertModal
        ref={promotionUpsertModalRef}
        onSubmit={handleCreateOrUpdatePrmotion}
      />
    </>
  );
}
