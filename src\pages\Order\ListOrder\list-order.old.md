```jsx
const {
    gotoPage,
    toggleSortOrderBy,
    orderData,
    loading,
    orderPaginationState,
    gotoDetailOrderPage,
    gotoEditOrderPage,
    pageSize,
    handleChangePageSize,
    filterState: { openForm, taglineDependencies },
    toggleFilterForm,
    setTaglineDependencies,
    setSearchPayload,
    resetSearchPayload,
    isFilterReset,
    deleteOrderExe,
    handleRefetch,
    totalRecords,
  } = ListOrderPageVm();

 const {
    orderReturnStatusTaglineOptions,
    orderStatusTaglineOptions,
    selectedOrderReturnStatusTagline,
    selectedOrderStatusTagline,
  } = useHandleTaglinePulldowns(taglineDependencies);

  const {
    orderStatusOptions,
    orderReturnStatusOptions,
    orderSourceOptions,
    shopOptions,
    deliveryPartnerOptions,
    assignEmployeeOptions,
    handleLoadMoreEmployee,
    loadMoreEmployee,
    loadMoreEmployeeState,
  } = useHandleUncontrolledPulldowns();

  const { getDate, setDate } =
    useCalendarProvider<{
      creationDates?: [Date, Date];
      changeDates?: [Date, Date];
    }>();


const items: CollapseProps["items"] = [
  {
    key: "1",
    label: "Lọc dữ liệu",
    children: (
      <Form form={form} onFinish={handleSubmitOrderSearch} layout="vertical">
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Form.Item name="creationDates" label="Ngày tạo">
              <RangePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col xs={24} lg={12}>
            <Form.Item name="code" label="Mã đơn hàng">
              <Input placeholder="Nhập mã đơn hàng" />
            </Form.Item>
          </Col>
          <Col xs={24} lg={6}>
            <Form.Item name="orderStatusId" label="Trạng thái đơn hàng">
              <BaseSelect
                placeholder="Chọn trạng thái"
                options={orderStatusOptions}
              />
            </Form.Item>
          </Col>
          <Col xs={24} lg={6}>
            <Form.Item name="orderStatusTaglineId" label="Tagline đơn hàng">
              <BaseSelect
                options={orderStatusTaglineOptions}
                value={selectedOrderStatusTagline}
              />
            </Form.Item>
          </Col>

          <Col xs={24} lg={6}>
            <Form.Item name="orderReturnStatusId" label="Trạng thái đổi trả">
              <BaseSelect
                placeholder="Chọn trạng thái đổi trả"
                options={orderReturnStatusOptions}
              />
            </Form.Item>
          </Col>

          <Col xs={24} lg={6}>
            <Form.Item
              name="orderReturnStatusTaglineId"
              label="Tagline đổi trả"
            >
              <BaseSelect
                placeholder="Chọn tagline đổi trả"
                options={orderReturnStatusTaglineOptions}
                value={selectedOrderReturnStatusTagline}
              />
            </Form.Item>
          </Col>

          <Col xs={24} lg={12}>
            <Form.Item name="changeDates" label="Ngày chuyển trạng thái">
              <RangePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>

          <Col xs={24} lg={12}>
            <Form.Item name="deliveryPartnerId" label="Hình thức giao hàng">
              <BaseSelect
                placeholder="Chọn đơn vị vận chuyển"
                options={deliveryPartnerOptions}
              />
            </Form.Item>
          </Col>

          <Col xs={24} lg={12}>
            <Form.Item name="originalShopId" label="Đơn hàng Shop">
              <BaseSelect placeholder="Chọn shop" options={shopOptions} />
            </Form.Item>
          </Col>

          <Col xs={24} lg={12}>
            <Form.Item name="deliveryShopId" label="Đơn shop chuyển đến">
              <BaseSelect
                placeholder="Chọn shop chuyển đến"
                options={shopOptions}
              />
            </Form.Item>
          </Col>

          <Col xs={24} lg={12}>
            <Form.Item name="city" label="Tỉnh thành">
              <BaseSelect placeholder="Chọn tỉnh thành" />
            </Form.Item>
          </Col>

          <Col xs={24} lg={6}>
            <Form.Item name="orderSourceId" label="Nguồn đơn">
              <BaseSelect
                placeholder="Chọn nguồn đơn"
                options={orderSourceOptions}
              />
            </Form.Item>
          </Col>

          <Col xs={24} lg={6}>
            <Form.Item name="assignedEmployeeId" label="Phân công">
              <BaseSelect
                placeholder="Chọn nhân viên"
                options={assignEmployeeOptions}
              />
            </Form.Item>
          </Col>
        </Row>
        <div className="backgroundW">
          <Section>
            <div className="backgroundW">
              <Row justify="end" gutter={[16, 16]}>
                <Col xs={24} lg={18}></Col>
                <Col xs={24} lg={6} style={{ textAlign: "right" }}>
                  <Space>
                    <BaseButton
                      htmlType="submit"
                      type="primary"
                      size="large"
                      loading={loading && isFilterReset === false}
                      disabled={loading}
                      className="w-fit"
                      bgColor={COLOR.BLUE[500]}
                      hoverColor={COLOR.BLUE[700]}
                      icon={<SearchOutlined rev="undefined" />}
                    >
                      TÌM KIẾM
                    </BaseButton>
                    <BaseButton
                      onClick={() => {
                        handleResetFilter();
                      }}
                      type="primary"
                      size="large"
                      loading={loading && !!isFilterReset}
                      disabled={loading}
                      danger
                      className="w-fit"
                      bgColor={COLOR.RED[500]}
                      hoverColor={COLOR.RED[700]}
                    >
                      ĐẶT LẠI
                    </BaseButton>
                  </Space>
                </Col>
              </Row>
            </div>
          </Section>
        </div>
      </Form>
    ),
  },
];

const itemsCollapse: CollapseProps["items"] = [
  {
    key: "1",
    label: "Lọc dữ liệu",
    children: (
      <Form form={form} onFinish={handleSubmitOrderSearch} layout="vertical">
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={6}>
            <Form.Item name="orderStatusId" label="Trạng thái đơn hàng">
              <BaseSelect
                placeholder="Chọn trạng thái"
                options={orderStatusOptions}
              />
            </Form.Item>
          </Col>
          <Col xs={24} lg={12}>
            <Form.Item name="code" label="Mã đơn hàng">
              <Input placeholder="Nhập mã đơn hàng" />
            </Form.Item>
          </Col>
          <Col xs={24} lg={12}>
            <Form.Item name="changeDates" label="Ngày chuyển trạng thái">
              <RangePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col xs={24} lg={12}>
            <Form.Item name="changeDateShop" label="Ngày chuyển shop">
              <RangePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col xs={24} lg={6}>
            <Form.Item name="orderSourceId" label="Nguồn đơn">
              <BaseSelect
                placeholder="Chọn nguồn đơn"
                options={orderSourceOptions}
              />
            </Form.Item>
          </Col>

          <Col xs={24} lg={12}>
            <Form.Item name="changeOrderDate" label="Ngày đổi hàng">
              <RangePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>

          <Col xs={24} lg={6}>
            <Form.Item name="orderReturnStatusId" label="Trạng thái đổi trả">
              <BaseSelect
                placeholder="Chọn trạng thái đổi trả"
                options={orderReturnStatusOptions}
              />
            </Form.Item>
          </Col>
        </Row>
        <div className="backgroundW">
          <Section>
            <div className="backgroundW">
              <Row justify="end" gutter={[16, 16]}>
                {/* <Col xs={24} lg={18}></Col> */}
                <Col xs={24} lg={12} style={{ textAlign: "right" }}>
                  <Space>
                    <BaseButton
                      type="primary"
                      size="large"
                      // style={{
                      //   display: "flex",
                      //   alignItems: "center",
                      //   justifyContent: "center",
                      // }}
                      loading={loading && isFilterReset === false}
                      disabled={loading}
                      className="w-fit"
                      bgColor={COLOR.RED[500]}
                      hoverColor={COLOR.RED[700]}
                      icon={<PlusSquareOutlined rev={undefined} />}
                      onClick={() => {
                        window.location.href =
                          RootPageRouter.children.createOrder.path;
                      }}
                    >
                      Tạo mới
                    </BaseButton>

                    <BaseButton
                      // htmlType="submit"
                      type="primary"
                      size="large"
                      loading={loading && isFilterReset === false}
                      disabled={loading}
                      className="w-fit"
                      bgColor={COLOR.YELLOW[500]}
                      hoverColor={COLOR.YELLOW[700]}
                      icon={<SearchOutlined rev="undefined" />}
                    >
                      TÌM KIẾM
                    </BaseButton>
                    <BaseButton
                      onClick={handleResetFilter}
                      type="primary"
                      size="large"
                      loading={loading && !!isFilterReset}
                      disabled={loading}
                      // danger
                      className="w-fit"
                      bgColor={COLOR.BLUE[500]}
                      hoverColor={COLOR.BLUE[700]}
                    >
                      ĐẶT LẠI
                    </BaseButton>
                  </Space>
                </Col>
              </Row>
            </div>
          </Section>
        </div>
      </Form>
    ),
  },
];
```
