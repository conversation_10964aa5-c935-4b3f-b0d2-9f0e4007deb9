import React, { CSSProperties } from "react";

import { FileMetadataData } from "helpers/file";

export interface ClipboardFile {
  file: File;
  blob: Blob;
  metadata?: FileMetadataData;
}

export type EditableElement = HTMLDivElement;

export interface Register {
  getter: () => {
    value: string;
  };
  setter: () => {
    setValue: (nextValue: string) => void;
  };
}

export interface Props {
  placeholder?: string;
  placeholderStyle?: CSSProperties;
  defaultValue?: string;
  width?: number | string;
  height?: number | string;
  onChange?: (value: string) => void;
  onPasteFile?: (clipboardFiles: ClipboardFile[]) => void;
  onPasteFileError?: (error: unknown, file: File) => void;
  onKeyPress?: React.KeyboardEventHandler<HTMLDivElement>;
  onKeyDown?: React.KeyboardEventHandler<HTMLDivElement>;
  onKeyUp?: React.KeyboardEventHandler<HTMLDivElement>;
  onEnter?: (value: string) => void;
  register?: (registerValue: Register) => void;
}
