import { lazy } from "react";

import PATHS from "constants/paths";
import { createAppPage } from "libs/react";

import { ChildrenPage } from "./types";

export default createAppPage<{}, ChildrenPage>({
  name: "OrderManagement",
  path: PATHS.ORDER_MANAGEMENT,
  childrenPages: {
    index: createAppPage({
      name: "OrderManagementIndex",
      path: "/QuanLyDonHang",
      page: () => lazy(() => import("./OrderManagement")),
    }),
    listOrder: createAppPage({
      name: "ListOrder",
      path: "/DanhSachDonHang",
      page: () => lazy(() => import("./ListOrder")),
    }),
    listPaymentTransaction: createAppPage({
      name: "ListPaymentTransaction",
      path: "/GiaoDichThanhToan",
      page: () => lazy(() => import("./ListPaymentTransaction")),
    }),
    paymentTransactionDetail: createAppPage({
      name: "PaymentTransactionDetail",
      path: "/GiaoDichThanhToan/ChiTietGiaoDichThanhToan/:paymentTransactionId",
      page: () => lazy(() => import("./PaymentTransactionDetail")),
    }),
    createOrder: createAppPage({
      name: "CreateOrder",
      path: "/DanhSachDonHang/tao-moi",
      page: () => lazy(() => import("./CreateOrder")),
    }),
    orderDetail: createAppPage({
      name: "OrderDetail",
      path: "/DanhSachDonHang/chi-tiet-don-hang/:orderId",
      page: () => lazy(() => import("./OrderDetail")),
    }),
    exChangeOrder: createAppPage({
      name: "ExChangeOrder",
      path: "/DanhSachDonHang/doi-tra/:orderId",
      page: () => lazy(() => import("./ExchangeOrder/ExChangeOrderPage")),
    }),
    exChangeOrderDetail: createAppPage({
      name: "ExChangeOrderDetail",
      path: "/DanhSachDonHang/chi-tiet-don-hang-doi/:orderId",
      page: () =>
        lazy(() => import("./ExchangeOrderDetail/ExchangeOrderDetailPage")),
    }),
    listOrderStatus: createAppPage({
      name: "ListOrderStatus",
      path: "/TrangThaiDonHang",
      page: () => lazy(() => import("./ListOrderStatus/ListOrderStatusV2")),
    }),
    orderStatusDetail: createAppPage({
      name: "OrderStatusDetail",
      path: "/TrangThaiDonHang/chi-tiet-trang-thai/:orderStatusCode",
      page: () => lazy(() => import("./OrderStatusDetail/OrderStatusDetailV2")),
    }),
    // TODO: Path need to be change after API update
    listOrderStatusTagline: createAppPage({
      name: "ListOrderStatusTagline",
      path: "/TrangThaiDonHang/danh-sach-tagline/:orderStatusId/:orderStatusCode",
      page: () => lazy(() => import("./ListOrderStatusTagline")),
    }),
    // TODO: Path need to be change after API update
    orderStatusTaglineDetail: createAppPage({
      name: "OrderStatusTaglineDetail",
      path: "/TrangThaiDonHang/:orderStatusId/chi-tiet-tagline/:taglineId",
      page: () => lazy(() => import("./OrderStatusTaglineDetail")),
    }),
    listOrderReturnStatus: createAppPage({
      name: "ListOrderReturnStatus",
      path: "/TrangThaiDoiTra",
      page: () => lazy(() => import("./ListReturnStatus")),
    }),
    orderReturnStatusDetail: createAppPage({
      name: "OrderReturnStatusDetail",
      path: "/TrangThaiDoiTra/chi-tiet-trang-thai/:returnStatusId",
      page: () => lazy(() => import("./ReturnStatusDetail")),
    }),
    listOrderReturnStatusTagline: createAppPage({
      name: "ListOrderReturnStatusTagline",
      path: "/TrangThaiDoiTra/danh-sach-tagline/:returnStatusId",
      page: () => lazy(() => import("./ListReturnStatusTagline")),
    }),
    orderReturnStatusTaglineDetail: createAppPage({
      name: "ListOrderReturnStatusTagline",
      path: "/TrangThaiDoiTra/danh-sach-tagline/:returnStatusId/chi-tiet-tagline/:taglineId",
      page: () => lazy(() => import("./ReturnStatusTaglineDetail")),
    }),
    listDeliveryPartner: createAppPage({
      name: "ListDeliveryPartner",
      path: "/DonViVanChuyen",
      page: () => lazy(() => import("./ListDeliveryPartner")),
    }),
    deliveryPartnerDetail: createAppPage({
      name: "DeliveryPartnerDetail",
      path: "/DonViVanChuyen/chi-tiet-don-vi-van-chuyen/:deliveryPartnerId",
      page: () => lazy(() => import("./DeliveryPartnerDetail")),
    }),
    listSourceManagement: createAppPage({
      name: "ListSource",
      path: "/NguonDon",
      page: () => lazy(() => import("./ListSource")),
    }),
    orderSourceDetail: createAppPage({
      name: "OrderSourceDetail",
      path: "/NguonDon/chi-tiet-nguon-don/:sourceId",
      page: () => lazy(() => import("./OrderSourceDetail")),
    }),
  },
});
