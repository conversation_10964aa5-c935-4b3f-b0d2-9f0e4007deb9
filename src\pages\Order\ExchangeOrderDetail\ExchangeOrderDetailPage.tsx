import React, { useEffect, useMemo, useState } from "react";
import { LeftOutlined, SaveOutlined } from "@ant-design/icons";
import { Empty, Modal, Spin } from "antd";
import _ from "lodash";
import { useParams, useSearchParams } from "react-router";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { showLoading } from "components/atoms/base/Spinner";
import { COLOR } from "constants/color";
import { convertUndefinedToNull } from "helpers/convertDataToDefaultOption.helper";
import { PageParamsType } from "libs/react";
import { OrderDetailDtoType } from "modules/order";
import { OrderFormExchange } from "modules/order/shared/presentational/orderFormExchange";
import { UpdateOrderExchangeDto } from "services/crm/order/dto/order.dto";
import {
  useGetOrderDetail,
  useUpdateOrderExchange,
} from "services/crm/order/hooks/order.hook";
import RootPageRouter from "..";
import { ExChangeOrderFormType } from "../ExchangeOrder/types/ExchangeOrderForm.type";
import { ChildrenPage } from "../types";

export interface OrderDetailExchangeDtoType extends OrderDetailDtoType {
  orderIdRef: number;
  codeRef: string;
}

export default function ExchangeOrderDetailPage() {
  const [searchUrlPath] = useSearchParams();
  const { orderId } =
    useParams<PageParamsType<ChildrenPage["exChangeOrderDetail"]>>();

  const {
    orderDetailRefetch,
    orderDetailData,
    loading: orderDetailLoading,
  } = useGetOrderDetail({
    orderId: Number(orderId),
  });

  const [orderDetail, setOrderDetailData] =
    useState<OrderDetailExchangeDtoType | null>();

  const pageActionType = searchUrlPath.get("action") || "view";
  const formMode = pageActionType === "edit" ? "editable" : "view-only";

  const navigateLink = useMemo(() => {
    if (orderDetail) {
      return RootPageRouter.children.orderDetail.generatePath({
        orderId: orderDetail.orderIdRef?.toString() || "",
      });
    }
    return "";
  }, [orderDetail]);

  const handleRefetch = () => {
    orderDetailRefetch();
  };

  const goBack = () => {
    RootPageRouter.gotoChild("listOrder");
  };

  const { updateOrderExchangeRefetch } = useUpdateOrderExchange();

  const handleConfirmNegativeAmount = (payload: UpdateOrderExchangeDto) => {
    Modal.confirm({
      centered: true,
      title: "Xác nhận lưu đơn hàng đổi",
      content: "Bạn có chắc chắn muốn lưu đơn hàng với tổng tiền hàng âm?",
      okText: "Xác nhận",
      cancelText: "Hủy",
      onOk: () => {
        showLoading(true);
        updateOrderExchangeRefetch(payload)
          .then(() => {
            showNotification({
              type: "success",
              message: "Chỉnh sửa đơn hàng thành công",
            });
            handleRefetch();
          })
          .catch((error) => {
            const errorApi = error?.response?.data?.errors || [];
            if (errorApi) {
              if (Array.isArray(errorApi) && errorApi?.length > 0) {
                errorApi.forEach((errorItem) => {
                  showNotification({
                    type: "error",
                    message: errorItem.title || "Chỉnh sửa đơn hàng thất bại",
                  });
                });
              } else {
                showNotification({
                  type: "error",
                  message: errorApi.title,
                });
              }
            } else {
              showNotification({
                type: "error",
                message: "Chỉnh sửa đơn hàng thất bại",
              });
            }
          })
          .finally(() => {
            showLoading(false);
          });
      },
    });
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSubmitExchangeOrder = (formData: ExChangeOrderFormType) => {
    const { items } = formData ?? {};
    const validMinQuantity = 0;
    const totalQuantityInItems = items?.reduce(
      (total, item) => total + (item.quantity || validMinQuantity),
      0
    );
    if (totalQuantityInItems < validMinQuantity) {
      showNotification({
        type: "warning",
        message:
          "Tổng số lượng sản phẩm trong đơn hàng không hợp lệ, Vui lòng kiểm tra lại.",
      });
      return;
    }

    const convertItem = items.map((item) => {
      return {
        skuId: item.skuId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalAmount: item.totalAmount,
        productId: item.productId,
        discountAmount: item.discountAmount,
        discountPercent: item.discountPercent,
        listedPrice: item.listedPrice,
        orderItemIdRef:
          _.isNaN(item.orderItemIdRef) || !item.orderItemIdRef
            ? undefined
            : item.orderItemIdRef,
      };
    });
    const formDataOmit = _.omit(formData, [
      "shippingAddressId",
      "customer",
      "createdAt",
    ]);

    const satisfyUpdateOrderPayload = convertUndefinedToNull(formDataOmit);

    const payload = {
      ...satisfyUpdateOrderPayload,
      orderId: orderDetailData?.data?.orderId,
      birthDay: formData.birthDay ? formData.birthDay.toISOString() : null,
      items: convertItem,
      shippingCost: formData.shippingCost || 0,
      applicableFee: formData.applicableFee || 0,
      discount: formData.discount || 0,
      discountValue: formData.discountValue || 0,
      promotionValue: formData.promotionValue || 0,
    };

    if (formData.totalAmount < 0) {
      handleConfirmNegativeAmount(payload);
      return;
    }

    showLoading(true);
    updateOrderExchangeRefetch(payload)
      .then((res) => {
        showNotification({
          type: "success",
          message: "Chỉnh sửa đơn hàng thành công",
        });

        handleRefetch();
      })
      .catch((error) => {
        const errorApi = error?.response?.data?.errors || [];
        if (errorApi) {
          if (Array.isArray(errorApi) && errorApi?.length > 0) {
            errorApi.forEach((errorItem) => {
              showNotification({
                type: "error",
                message: errorItem.title || "Tạo đơn hàng đổi trả thất bại",
              });
            });
          } else {
            showNotification({
              type: "error",
              message: errorApi.title,
            });
          }
        } else {
          showNotification({
            type: "error",
            message: "Tạo đơn hàng đổi trả thất bại",
          });
        }
      })
      .finally(() => {
        showLoading(false);
      });
  };

  useEffect(() => {
    if (!_.isEmpty(orderDetailData?.data)) {
      const { data } = orderDetailData;

      const convertItem = data?.items.map((item) => {
        const { quantity } = item;
        if (quantity < 0) {
          return {
            isExchange: true,
            ...item,
          };
        }
        return {
          ...item,
        };
      });

      setOrderDetailData({
        ...data,
        items: convertItem,
      } as OrderDetailExchangeDtoType);
    } else {
      setOrderDetailData(null);
    }
  }, [orderDetailData]);

  useEffect(() => {
    orderDetailRefetch();
  }, [orderId]);

  return (
    <Spin spinning={orderDetailLoading} tip="Đang tải dữ liệu...">
      {orderDetailData ? (
        <OrderFormExchange
          onSubmit={handleSubmitExchangeOrder}
          initialOrderDetail={orderDetail}
          handleRefetch={handleRefetch}
          mode={formMode}
          navigateLink={navigateLink}
          cancelButton={
            <BaseButton
              type="primary"
              className="w-fit"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[600]}
              icon={<LeftOutlined />}
              onClick={goBack}
            >
              {/* Quay lại */}
            </BaseButton>
          }
          submitButton={
            <BaseButton
              type="primary"
              htmlType="submit"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[600]}
              className="w-fit"
              // disabled={updateOrderDetailState.loading}
              icon={<SaveOutlined />}
            >
              Lưu
            </BaseButton>
          }
        />
      ) : (
        <Empty
          description={
            orderDetailLoading ? "Đang tải dữ liệu" : "Không có dữ liệu"
          }
          style={{ marginTop: "20px" }}
        />
      )}
    </Spin>
  );
}
