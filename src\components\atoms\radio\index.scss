.a-radio {
	$root: &;

	position: relative;
	display: inline-block;

	&_input {
		position: absolute;
		opacity: 0;

		&:checked + #{$root}_body {
			background-color: $COLOR-DENIM;
			border: 0;
		}
	}

	&_label {
		display: flex;
		align-items: center;
		margin: 0;
		cursor: pointer;
	}

	&_content {
		margin-left: rem(6);
		font-size: rem(14);
	}

	&_body {
		position: relative;
		display: flex;
		flex: 0 0 auto;
		align-items: center;
		justify-content: center;
		width: 18px;
		height: 18px;
		background-color: $COLOR-WHITE;
		border: 1px solid $COLOR-QUARTZ;
		border-radius: 100%;

		#{$root}_label:hover & {
			opacity: 0.7;
		}

		#{$root}-disabled & {
			opacity: 0.7;
		}
	}

	&_inner {
		width: 4px;
		height: 4px;
		background-color: $COLOR-WHITE;
		border-radius: 100%;
	}

	&-disabled {
		pointer-events: none;
	}
}
