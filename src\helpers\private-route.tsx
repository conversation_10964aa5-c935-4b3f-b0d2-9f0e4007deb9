import { Navigate, Outlet, useLocation } from "react-router";
import { MainLayout } from "components/templates/mainlayout";
import PATHS from "constants/paths";
import { useAppSelector } from "store";

export const PrivateRouteWrapper = () => {
  const status = useAppSelector((state) => state.auth.status);
  const location = useLocation();

  if (status === "UNAUTH") {
    return (
      <Navigate
        to={PATHS.LOGIN}
        replace
        state={{ previousLocation: location.pathname }}
      />
    );
  }

  return (
    <MainLayout>
      <Outlet />
    </MainLayout>
  );
};
