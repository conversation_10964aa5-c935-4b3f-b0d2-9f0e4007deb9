import { produce } from "immer";
import { pipe } from "pipe-ts";

import { Schema, schemaParser } from "libs/domain";
import { SchemaType } from "libs/domain/schema/types";
import { merge as mergeUtils } from "libs/utils/merge";

/**
 * According to the `schema`. This function will return a function that takes an `argument` such as `Any data` and returns a `parserData` object.
 * @function fromSchema
 * @description Here is a `Currying` function that returns a `parserData` through the `schemaParser` method
 * @param schema typeof `SchemaType<O>`
 * @returnType Function
 * @return A `Function` returns a `parserData` object
 */
export const fromSchema = <O extends { [key: string]: unknown }>(
  schema: SchemaType<O>
) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const parser = (value: any) =>
    schemaParser(new Schema(schema), value, { safeMode: true });

  return parser;
};

/**
 * This function are just abstractions that wrap a value into a context and allows mapping over this value. Mapping means applying a function to a value to get another value.
 * @function mapFrom
 * @description Here is a `Functor` function that returns a list of `Input`
 * @param data - List of `Any data`
 * @param mapper - A method takes `Input` as an argument
 * @return List of data get from `mapper` function
 */
export const mapFrom = <Input = unknown, R = unknown>(
  data: Input[],
  mapper: (item: Input) => R
) => {
  // eslint-disable-next-line no-underscore-dangle
  let _dataSource = data;
  if (!_dataSource || !Array.isArray(_dataSource)) _dataSource = [];

  return _dataSource.map(mapper);
};

/**
 * Instance of [pipe](https://www.npmjs.com/package/pipe-ts)
 */
export const createMapper = pipe;

/**
 * This function takes an `argument` as a `mapper` function and returns a `function` that takes an `argument` and combines it with the `Return Value` of the `mapper` function.
 * @function merge
 * @description The value of the `mapper` function may NOT include the value of` Input`
 * @param mapper - A function takes an `argument`
 * @returnType `Function`
 * @returns A `Function` takes an argument and combines it with the `Return Value` of the `mapper` function
 */
export const merge =
  <Input = unknown, R = unknown>(mapper: (item: Input) => R) =>
  (input: Input) =>
    mergeUtils(input, mapper(input));

/**
 * This function takes an `argument` as a `mapper` function and returns a `function` that takes an `argument` and combines it with the `Empty` object.
 * @function force
 * @param mapper - A function takes an `argument`
 * @returnType `Function`
 * @returns A `Function` takes an argument and combines it with the `Empty` object
 */
export const force =
  <Input = unknown, R = unknown>(mapper: (item: Input) => R) =>
  (input: Input) =>
    mergeUtils({}, mapper(input));

/**
 * This function takes an `argument` as a `mapper` function and returns a `function` that takes an `argument` and combines it with the `Return Value` of the `mapper` function.
 * @function patch
 * @description The value of the `mapper` function must include the value of` Input`
 * @param mapper - A function takes an `argument`
 * @returnType `Function`
 * @returns A `Function` takes an argument and combines it with the `Return Value` of the `mapper` function
 */
export const patch =
  <Input = unknown>(mapper: (item: Input) => void) =>
  (input: Input) =>
    produce(input, (draft: Input) => mapper(draft));
