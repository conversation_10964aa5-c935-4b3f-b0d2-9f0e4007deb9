/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { forwardRef } from "react";

import { mapModifiers } from "helpers/component";
import { ConnectForm, UseFormProps } from "helpers/form";

export interface Props
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  errorMessage?: string;
  row?: number;
}

export const Textareafield = forwardRef<HTMLTextAreaElement, Props>(
  ({ disabled, row = 3, errorMessage, ...innerProps }, ref) => (
    <div
      className={mapModifiers(
        "a-textareafield",
        disabled && "disabled",
        errorMessage && "error"
      )}
    >
      <textarea
        className="a-textareafield_input"
        rows={row}
        {...innerProps}
        ref={ref}
      />
      {errorMessage && (
        <div className="a-textareafield_errormessage">{errorMessage}</div>
      )}
    </div>
  )
);

interface TextareafieldHookFormProps extends Props {
  name: string;
}

export const TextareafieldHookForm: React.FC<
  React.PropsWithChildren<TextareafieldHookFormProps>
> = (props) => (
  <ConnectForm>
    {
      (({ register, errors }: UseFormProps) => (
        <Textareafield
          ref={register}
          errorMessage={
            errors[props.name] ? errors[props.name].message : undefined
          }
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...props}
        />
      )) as any
    }
  </ConnectForm>
);
