/* eslint-disable prettier/prettier */

import imgSearchBlue from "assets/images/icons/search-blue.svg";
import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Pulldown } from "components/atoms/pulldown";
import { Tag } from "components/atoms/tag";
import { Textfield } from "components/atoms/textfield";
import { Pagination } from "components/molecules/pagination";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Table, Tbody, Td, Th, Thead, Tr } from "components/organisms/table";
import { General } from "components/pages/general";
import { PriorityTicketModal } from "modules/ticket";
import PaginationSection from "pages/Common/paginationSection";
import TableManipulation from "pages/Common/tableManipulation";

import RootPageRouter from "../index";
import { PriorityTicketPageVm } from "./vm";

const PriorityTicketPage = () => {
  const {
    handleOpenModalByType,
    modalTypeIsOpen,
    handleCloseModal,
  } = PriorityTicketPageVm();

  return (
    <General>
      <title key="title">Danh sách ticket</title>
      <Section>
        <Heading type="h1" modifiers="primary">
          DANH SÁCH MỨC ĐỘ ƯU TIÊN TICKET
        </Heading>
        <Section>
          <Row className="d-flex">
            <Col
              className="ml-auto"
              xs={{ span: 12, order: 2 }}
              lg={{ span: 6, order: 1 }}
            >
              <Textfield iconSrc={imgSearchBlue} placeholder="Tìm kiếm" />
            </Col>
            <Col
              className="d-flex justify-content-end u-mb-15 u-mb-lg-0"
              xs={{ span: 12, order: 1 }}
              lg={{ span: 6, order: 2 }}
            >
              <Button onClick={() => handleOpenModalByType("priorityTicket")}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Section>
        <Section>
          <Table>
            <Thead>
              <Tr>
                <Th modifiers="center" stickyLeft>
                  STT
                </Th>
                <Th modifiers="center">Tên mức độ</Th>
                <Th modifiers="center">Màu sắc</Th>
                <Th modifiers="center">Thứ tự hiển thị</Th>
                <Th modifiers="center">Cập nhật cuối</Th>
                <Th modifiers="center" stickyRight>
                  Thao tác
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {Array(10)
                .fill(0)
                .map((_, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <Tr key={index}>
                    <Td modifiers="center" stickyLeft>
                      {index + 1}
                    </Td>
                    <Td modifiers="center">Bình thường</Td>
                    <Td modifiers="center">
                      <Tag color="#F9BDD2">Chưa xử lý</Tag>
                    </Td>
                    <Td modifiers="center">1</Td>
                    <Td modifiers="center">22/02/2021 17:09</Td>
                    <Td modifiers="center" stickyRight>
                      <TableManipulation
                        infoAction={{
                          id: `info`,
                          action: () =>
                            RootPageRouter.gotoChild("priorityTicket", {}),
                        }}
                        editAction={{
                          id: `edit`,
                          action: () =>
                            RootPageRouter.gotoChild("priorityTicket", {}),
                        }}
                      />
                    </Td>
                  </Tr>
                ))}
            </Tbody>
          </Table>
        </Section>
        <Section>
          <PaginationSection
            appearanceOption={
              <Pulldown
                placeholder="Số lượng hiển thị"
                options={[5, 10, 15, 25, 30].map((size) => ({
                  label: `${size}`,

                  value: `${size}`,
                }))}
              />
            }
            paginateOption={
              <Pagination
                modifiers="center"
                total={5}
                pageCount={5}
                onPageChange={() => {}}
                defaultCurrentPage={1}
              />
            }
          />
        </Section>
      </Section>
      <PriorityTicketModal
        open={modalTypeIsOpen("priorityTicket")}
        onClose={handleCloseModal}
      />
    </General>
  );
};

export default PriorityTicketPage;
