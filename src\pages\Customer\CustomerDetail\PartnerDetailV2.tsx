/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-unused-expressions */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { ArrowLeftOutlined, SaveOutlined } from "@ant-design/icons";
import { Typography } from "@material-ui/core";
import {
  Card,
  Checkbox,
  DatePicker,
  Descriptions,
  Form,
  Input,
  Tabs,
  TabsProps,
} from "antd";
import dayjs, { Dayjs } from "dayjs";
import _ from "lodash";
import { useLocation, useNavigate, useParams } from "react-router";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import BaseSelectGroupCustomer from "components/atoms/base/select/shared/BaseGroupCustomer";
import BaseSelectCity from "components/atoms/base/select/shared/BaseSelectCity.share";
import BaseSelectDistrict from "components/atoms/base/select/shared/BaseSelectDistrict.share";
import { showLoading } from "components/atoms/base/Spinner";
import { COLOR } from "constants/color";
import { primaryEmail } from "modules/customer";
import {
  CreateCustomerV2,
  GetCustomerProfileV2,
  UpdateCustomerProfileV2,
} from "services/crm/customer";
import { useAppSelector } from "store/index";
import {
  CustomerAddress,
  CustomerDto,
  CustomerResponse,
} from "../Dto/customer.dto";
import { CustomerPageVm } from "../ListCustomer/vm";
import { PartnerTabKeys, PartnerTabLabels } from "./enums/partnerTabs.enum";
import PartnerPointHistory from "./tabs/PartnerPointHistory";
import PartnerPurchaseHistory from "./tabs/PartnerPurchaseHistory";
import PartnerSMSHistory from "./tabs/PartnerSMSHistory";

export interface ActionType {
  view: "view";
  edit: "edit";
  add: "add";
}

interface FormType extends Omit<CustomerDto, "birthDay"> {
  code: number | string | undefined;
  birthDay: Dayjs | undefined;
}

export default function PartnerDetailV2() {
  const [form] = Form.useForm();
  const tabsRef = useRef<HTMLDivElement>(null);
  const employeeInfo = useAppSelector((state) => state.auth.employeeInfo);
  // params - history - location //
  const { customerId } = useParams<{ customerId: string }>();
  const { gotoCustomerPage } = CustomerPageVm();
  // history - location //
  const navigate = useNavigate();
  const location = useLocation();
  const cityIdValue = Form.useWatch("cityId", form);

  const isAddNew = useMemo(() => {
    return (
      location.pathname.includes("/them-khach-hang") ||
      customerId === "isAddNew"
    );
  }, [location.pathname, customerId]);

  const searchParams = new URLSearchParams(location.search);
  const isView = searchParams.get("action") === "view";

  // list state //
  const [activeKey, setActiveKey] = useState<PartnerTabKeys>(
    PartnerTabKeys.PURCHASE_HISTORY
  );

  const [addressList, setAddressList] = useState<CustomerAddress[]>([]);

  const { getCustomerExe, getCustomerState } = GetCustomerProfileV2();

  const { updateCustomerExe } = UpdateCustomerProfileV2();

  const goBack = useCallback(() => {
    navigate(-1);
    // eslint-disable-next-line
  }, [navigate]);

  const { createCustomerExe } = CreateCustomerV2();

  const itemsTabs: TabsProps["items"] = [
    {
      key: PartnerTabKeys.PURCHASE_HISTORY,
      label: (
        <Typography>
          {PartnerTabLabels[PartnerTabKeys.PURCHASE_HISTORY]}
        </Typography>
      ),
      children: (
        <PartnerPurchaseHistory activeKey={activeKey} customerId={customerId} />
      ),
    },
    {
      key: PartnerTabKeys.SMS_HISTORY,
      label: (
        <Typography>{PartnerTabLabels[PartnerTabKeys.SMS_HISTORY]}</Typography>
      ),
      children: <PartnerSMSHistory activeKey={activeKey} />,
    },
    {
      key: PartnerTabKeys.POINT_HISTORY,
      label: (
        <Typography>
          {PartnerTabLabels[PartnerTabKeys.POINT_HISTORY]}
        </Typography>
      ),
      children: (
        <PartnerPointHistory
          activeKey={activeKey}
          customerId={customerId}
          memberPoint={getCustomerState?.data?.memberPoints || 0}
        />
      ),
    },

    // {
    //   key: PartnerTabKeys.SHIPPING_ADDRESS,
    //   label: (
    //     <Typography>
    //       {PartnerTabLabels[PartnerTabKeys.SHIPPING_ADDRESS]}
    //     </Typography>
    //   ),
    //   children: (
    //     <PartnerShippingAddress
    //       activeKey={activeKey}
    //       addressList={addressList}
    //     />
    //   ),
    // },
  ];

  const handlePhone1Change = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const onlyNums = e.target.value.replace(/\D/g, "");
      form.setFieldsValue({ phoneNumber: onlyNums });
    },
    [form]
  );
  const handlePhone2Change = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const onlyNums = e.target.value.replace(/\D/g, "");
      form.setFieldsValue({ phoneNumber2: onlyNums });
    },
    [form]
  );
  const handleSubmit = useCallback((value: FormType) => {
    const { code, ...rest } = value;
    const payload = {
      ...rest,
      birthDay: rest.birthDay ? rest.birthDay.toISOString() : undefined,
    };
    showLoading(true);

    if (isAddNew) {
      createCustomerExe(payload)
        .then(async (res) => {
          if (res.status === 200) {
            showNotification({
              type: "success",
              message: "Tạo mới khách hàng thành công",
            });
            // const { customerId } = res.data.data;
            // gotoCustomerPage({
            //   customerId: String(customerId),
            // });
            goBack();
            // history.goBack();
          }
        })
        .catch((err) => {
          const apiError = err?.response?.data?.errors;
          console.log(apiError, "apiError");

          if (apiError) {
            apiError.forEach((item: any) => {
              showNotification({
                type: "error",
                message: item?.detail || item?.title,
              });
            });
          }
        })
        .finally(() => {
          showLoading(false);
        });
      return;
    }
    updateCustomerExe(Number(customerId), payload)
      .then((res) => {
        if (res && res.status === 200) {
          showNotification({
            type: "success",
            message: "Cập nhật thông tin khách hàng thành công",
          });
          // history.goBack();
        }
      })
      .catch((err) => {
        const apiError = err?.response?.data?.errors;
        if (apiError) {
          apiError.forEach((item: any) => {
            showNotification({
              type: "error",
              message: item?.detail || item?.title,
            });
          });
        }
      })
      .finally(() => {
        showLoading(false);
      });
  }, []);

  useEffect(() => {
    const getData = async () => {
      if (!isAddNew) {
        const result = await getCustomerExe(Number(customerId));
        if (result.status === 200) {
          const {
            customerId,
            address,
            phoneNumber,
            phoneNumber2,
            emails,
            ...restProps
          } =
            (result.data?.data as CustomerResponse) || ({} as CustomerResponse);
          const isDefaultAddress = _.find(address, { isDefault: true });
          setAddressList(address || []);
          const isPrimaryEmail = primaryEmail(result.data?.data);
          form.setFieldsValue({
            ...restProps,
            code: customerId,
            birthDay: restProps.birthDay
              ? dayjs(restProps.birthDay)
              : undefined,
            phoneNumber,
            phoneNumber2,
            email: isPrimaryEmail || undefined,
            cityId: isDefaultAddress ? isDefaultAddress.cityId : undefined,
            districtId: isDefaultAddress
              ? isDefaultAddress.districtId
              : undefined,
            wardId: isDefaultAddress ? isDefaultAddress.wardId : undefined,
            address: isDefaultAddress ? isDefaultAddress.address : undefined,
          });
        }
      }
    };
    getData();
  }, []);
  return (
    <>
      <title>Partner Detail</title>
      <div className="p-4 flex flex-col gap-4">
        <div className="flex items-center justify-start gap-3">
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={goBack}
            icon={<ArrowLeftOutlined rev={undefined} />}
          />
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={() => {
              form.submit();
            }}
            icon={<SaveOutlined rev={undefined} />}
          >
            Lưu
          </BaseButton>
        </div>
        <Card title="Thông tin khách hàng" className="shadow-md rounded-lg">
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              gender: 1,
              customerType: false,
              dontSendSMS: false,
            }}
            onFinish={handleSubmit}
            disabled={!isAddNew && isView}
          >
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-3">
              {/** MÃ KHÁCH HÀNG */}
              <Form.Item label="Mã khách hàng" name="code">
                <Input disabled placeholder="Mã khách hàng" />
              </Form.Item>
              {/** TÊN KHÁCH HÀNG */}
              <Form.Item
                label="Tên khách hàng"
                name="name"
                rules={[
                  {
                    required: true,
                    message: "Vui lòng nhập tên khách hàng",
                  },
                ]}
              >
                <Input placeholder="Nhập tên khách hàng" />
              </Form.Item>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                {/** GIỚI TÍNH */}
                <Form.Item
                  label="Giới tính"
                  name="gender"
                  // rules={[{ required: true, message: "Vui lòng chọn giới tính" }]}
                >
                  {/* <Radio.Group>
                  <Radio value={1}>Nam</Radio>
                  <Radio value={2}>Nữ</Radio>
                </Radio.Group> */}
                  <BaseSelect
                    placeholder="Chọn giới tính"
                    options={[
                      { label: "Nam", value: 1 },
                      { label: "Nữ", value: 0 },
                    ]}
                  />
                </Form.Item>

                {/** NGÀY SINH */}
                <Form.Item
                  label="Ngày sinh"
                  name="birthDay"
                  rules={[
                    {
                      required: true,
                      message: "Vui lòng chọn ngày sinh",
                    },
                  ]}
                >
                  <DatePicker
                    placeholder="Chọn ngày sinh"
                    format="DD/MM/YYYY"
                    className="w-full"
                  />
                </Form.Item>
              </div>
              <Form.Item label="Nhóm khách hàng" name="groupCustomer">
                {/* <Input placeholder="Nhập nhóm khách hàng" allowClear /> */}
                <BaseSelectGroupCustomer placeholder="Chọn nhóm khách hàng" />
              </Form.Item>
              {/** EMAIL */}
              <Form.Item
                label="Email"
                name="email"
                rules={[
                  {
                    required: true,
                    message: "Vui lòng nhập email",
                  },
                  {
                    type: "email",
                    message: "Vui lòng nhập địa chỉ email hợp lệ",
                  },
                ]}
              >
                <Input placeholder="Nhập email" allowClear type="email" />
              </Form.Item>
              {/** SỐ ĐIỆN THOẠI */}
              <Form.Item
                label="Số điện thoại"
                name="phoneNumber"
                rules={[
                  {
                    required: true,
                    message: "Vui lòng nhập số điện thoại",
                  },
                  {
                    pattern: /^\d{10}$/,
                    message: "Số điện thoại phải gồm đúng 10 chữ số",
                  },
                ]}
              >
                <Input
                  placeholder="Nhập số điện thoại"
                  maxLength={10}
                  type="tel"
                  onChange={handlePhone1Change}
                />
              </Form.Item>
              {/** SỐ ĐIỆN THOẠI 2 */}
              <Form.Item
                label="Số điện thoại 2"
                name="phoneNumber2"
                rules={[
                  {
                    pattern: /^\d{10}$/,
                    message: "Số điện thoại phải gồm đúng 10 chữ số",
                  },
                ]}
              >
                <Input
                  placeholder="Nhập số điện thoại 2"
                  maxLength={10}
                  type="tel"
                  onChange={handlePhone2Change}
                />
              </Form.Item>
              <div className="grid grid-cols-2 gap-3">
                {/** LOẠI KHÁCH HÀNG */}
                <Form.Item
                  name="customerType"
                  label="Loại khách hàng"
                  valuePropName="checked"
                >
                  <Checkbox>Khách sỉ</Checkbox>
                </Form.Item>
                {/* <Form.Item name="customerType" label="Loại khách hàng">
                  <BaseSelect placeholder="Chọn loại khách hàng" options={[]} />
                </Form.Item> */}

                {/** KHÔNG GỬI TIN NHẮN */}
                <Form.Item
                  name="dontSendSMS"
                  label="Không gửi tin nhắn"
                  valuePropName="checked"
                >
                  <Checkbox>Không gửi SMS</Checkbox>
                </Form.Item>
              </div>

              {/** TỈNH/ THÀNH PHỐ */}
              <div className="col-span-1 lg:col-span-2">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                  <Form.Item
                    label="Tỉnh/Thành phố"
                    name="cityId"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn tỉnh/thành phố",
                      },
                    ]}
                  >
                    <BaseSelectCity placeholder="Chọn tỉnh/thành phố" />
                  </Form.Item>
                  {/** PHƯỜNG/ XÃ */}
                  <Form.Item
                    label="Phường/ Xã"
                    name="districtId"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn phường/xã",
                      },
                    ]}
                  >
                    <BaseSelectDistrict
                      placeholder="Chọn phường/xã"
                      cityId={cityIdValue}
                    />
                  </Form.Item>
                  {/** PHƯỜNG/ XÃ */}
                  {/* <Form.Item
                    label="Phường/Xã"
                    name="wardId"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn phường/xã",
                      },
                    ]}
                  >
                    <BaseSelect
                      placeholder="Chọn phường/xã"
                      options={listWard}
                      fieldNames={{ label: "ward_name", value: "ward_id" }}
                    />
                  </Form.Item> */}
                </div>
              </div>

              <div className="col-span-1 lg:col-span-2">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
                  {/** CHIỀU CAO */}
                  <Form.Item label="Chiều cao" name="height">
                    <Input placeholder="Nhập chiều cao" allowClear />
                  </Form.Item>
                  {/** CÂN NẶNG */}
                  <Form.Item label="Cân nặng" name="weight">
                    <Input placeholder="Nhập cân nặng" allowClear />
                  </Form.Item>
                  {/** VÒNG EO */}
                  <Form.Item label="Vòng eo" name="waist">
                    <Input placeholder="Nhập vòng eo" allowClear />
                  </Form.Item>
                </div>
              </div>

              {/** ĐỊA CHỈ */}
              <div className="col-span-1 lg:col-span-2">
                <Form.Item
                  label="Địa chỉ"
                  name="address"
                  rules={[{ required: true, message: "Vui lòng nhập địa chỉ" }]}
                >
                  <Input.TextArea
                    placeholder="Nhập địa chỉ"
                    allowClear
                    rows={3}
                  />
                </Form.Item>
              </div>

              {/** GHI CHÚ */}
              <div className="col-span-1 lg:col-span-2">
                <Form.Item label="Ghi chú" name="note">
                  <Input.TextArea
                    placeholder="Nhập ghi chú"
                    allowClear
                    rows={3}
                  />
                </Form.Item>
              </div>
            </div>
          </Form>
          {/** SỐ LẦN MUA TRONG NĂM */}
          <div className="mt-4">
            <Descriptions
              bordered
              column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 2 }}
              size="small"
              labelStyle={{ fontWeight: "bold" }}
            >
              <Descriptions.Item label="Tổng Số Lần Mua">1</Descriptions.Item>
              <Descriptions.Item label="Tổng lần mua trong năm">
                1
              </Descriptions.Item>
              <Descriptions.Item label="Tổng số tiền trong năm">
                1
              </Descriptions.Item>
              <Descriptions.Item label="Tổng SP Đã Mua/Số tiền">
                1
              </Descriptions.Item>
            </Descriptions>
          </div>
        </Card>

        <div ref={tabsRef}>
          <Tabs
            className="bg-white rounded-lg shadow-md px-4"
            items={itemsTabs}
            activeKey={activeKey}
            onChange={(key) => {
              setActiveKey(key as PartnerTabKeys);
              setTimeout(() => {
                tabsRef.current?.scrollIntoView({
                  behavior: "smooth",
                  block: "start",
                });
              }, 0);
            }}
          />
        </div>
      </div>
    </>
  );
}
