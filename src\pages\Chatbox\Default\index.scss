.page-chatbox {
	$root: &;
	$rootTabconversationdetail: #{$root}-tabconversationdetail;
	$animation: cubic-bezier(0.4, 0, 0.2, 1);
	$recent_width: rem(321);
	$chatinput_height: rem(245);
	$conversationdetail_width: rem(311);
	$clientheader_height: rem(50);
	position: relative;

	display: flex;
	width: 100%;
	height: 100%;
	background-color: $COLOR-WHITE-SMOKE;

	&_filterbar {
		width: 100%;
		max-width: rem(85);
		height: 100%;
		padding-right: rem(6);
		overflow-y: auto;
		background-color: $COLOR_WHITE;

		/* Hide scrollbar for Chrome, Safari and Opera */
		&::-webkit-scrollbar {
			display: none;
		}

		/* Hide scrollbar for IE, Edge and Firefox */
		& {
			-ms-overflow-style: none; /* IE and Edge */
			scrollbar-width: none; /* Firefox */
		}

		button {
			width: 100%;
			height: 100%;
			max-height: rem(64);
			background-color: unset;
			border: none;
			border-top-right-radius: rem(12);
			border-bottom-right-radius: rem(12);
			outline: none;
			transition: all 0.3s ease-in-out;

			&:hover {
				background-color: $COLOR-LAVENDER-BLUE-1;
			}
		}
	}

	&_recent {
		width: $recent_width;
		height: 100%;
		background-color: $COLOR-ISABELLINE;
		border-radius: rem(11);

		@include spSmall {
			width: 20%;
			max-width: rem(80);
			margin-right: auto;
			margin-left: auto;
		}

		& > :first-child {
			height: 100%;
		}

		.a-textfield-withicon,
		.m-recentconversation_content {
			@include spSmall {
				display: none;
			}
		}

		.m-recentconversation {
			@include spSmall {
				align-items: center;
				justify-content: center;
				padding: rem(11) 0;
			}
		}
	}

	&_messagebox {
		width: calc(100% - #{$recent_width} - #{$conversationdetail_width});
		height: 100%;
		padding: 0px rem(12);
		overflow: hidden;
		background-color: $COLOR-WHITE-SMOKE;
		border-radius: rem(11);

		@include tab {
			width: calc(100% - #{$recent_width});
		}

		@include spSmall {
			width: 80%;
		}
	}

	&_clientheader {
		align-items: center;
		padding: 15px;
		box-shadow: 0px 3.5px 2.5px rgba($color: $COLOR_PLATINUM, $alpha: 0.5);

		& > .a-image {
			width: calc(#{$clientheader_height} - #{rem(5)});
			height: calc(#{$clientheader_height} - #{rem(5)});
			border-radius: 50%;
		}

		& > .a-text {
			margin-left: rem(10);
		}
	}

	&_listmessage {
		position: relative;
		height: calc(100% - #{$chatinput_height} - #{$clientheader_height});
	}

	&_conversationdetail {
		position: relative;
		width: $conversationdetail_width;
		height: 100%;
		overflow: hidden scroll;
		background-color: $COLOR_WHITE;
		border-radius: rem(11);

		@include tab {
			position: unset;
			width: 0;
		}

		.form-create-conversation {
			height: 0px;
			padding: 0px rem(16);
			overflow: hidden;
			background-color: $COLOR_WHITE;
			box-shadow: 0px rem(3.5) rem(2.5)
				rgba($color: $COLOR_PLATINUM, $alpha: 0.5);
			transition: all 0.3s ease-in-out;
			&.opened {
				height: auto;
				padding: rem(16);
				margin-bottom: rem(16);
				transition: all 0.3s ease-in-out;
			}
		}
	}

	&_tabconversationdetail-content {
		padding: rem(15);
	}

	&_customerinfo {
		display: flex;
		min-height: rem(100);
		margin-top: rem(16);

		& > p:first-of-type {
			line-height: 1.6;
		}

		& .a-icon {
			margin-right: rem(10);
		}
	}

	&_orderactions {
		margin-left: auto;
	}

	&_orderaction {
		padding: rem(2) rem(10);
		margin-left: rem(7);
		color: $COLOR_WHITE;
		cursor: pointer;
	}

	&_tabavigation {
		position: absolute;
		top: 0;
		right: 0;
		z-index: z("pagechatbox", "tabavigation");
		display: none;
		align-items: center;
		justify-content: center;
		height: rem(47);
		cursor: pointer;

		@include tab {
			display: flex;
			width: rem(53);
			background-color: $COLOR_WHITE;
			border-top-left-radius: 18px;
			border-bottom-left-radius: 18px;
			transition: opacity 0.3s $animation;
		}

		&:hover {
			opacity: 0.7;
		}
	}

	&_wraptabavigation {
		position: relative;
		width: rem(28);
		height: rem(22);

		span {
			position: absolute;
			left: 0;
			width: 100%;
			max-width: rem(32);
			height: rem(2);
			background-color: $COLOR-CHARLESTON-GREEN;
			border-radius: rem(20);
			transition: 0.4s $animation;

			&:first-child {
				transform: translateY(#{rem(12)}) translateX(0) rotate(45deg);
			}

			&:nth-child(2) {
				top: 50%;
				opacity: 0;
				transform: translateY(-50%);
			}

			&:nth-child(3) {
				top: 100%;
				transform: translateY(-#{rem(10)}) translateX(0) rotate(-45deg);
			}
		}

		#{$rootTabconversationdetail}-closed & {
			span {
				&:first-child {
					top: 0;
					transform: translateY(0) translateX(0) rotate(0deg);
				}

				&:nth-child(2) {
					top: 50%;
					opacity: 1;
					transform: translateY(0);
				}

				&:nth-child(3) {
					top: 100%;
					transform: translateY(0);
				}
			}
		}
	}

	&_wraptabconversationdetail {
		width: $conversationdetail_width;
		height: 100%;
		padding: 0;
		margin: 0;
		background-color: $COLOR-WHITE;
		transition: 0.4s $animation;

		@include tab {
			position: absolute;
			right: 0;
			height: calc(100% - #{rem(55)});
			margin-top: rem(52);
			overflow-y: auto;
			box-shadow: 0px 4px 4px $COLOR-BLACK-3;

			#{$rootTabconversationdetail}-closed & {
				display: none;
			}
		}
	}

	.m-orderlist {
		padding-bottom: rem(22);
	}
}
