.m-calendar {
	$root: &;
	position: relative;
	font-family: $FONTFAMILY-ROBOTO;

	&_wrappermonthyear {
		display: none !important;
	}

	&_datecalendar {
		position: fixed !important;
		z-index: z("calendar", "datecalendar");
		width: rem(250) !important;
		width: 100%;
		transform: translate3d(rem(-125), rem(50), 0px) !important;

		.react-datepicker__triangle {
			left: 50% !important;
			border-bottom-color: $COLOR-WHITE !important;
		}

		.react-datepicker {
			width: rem(250) !important;
		}
	}

	&_monthyearcalendar {
		position: fixed !important;
		z-index: z("calendar", "monthyearcalendar");
		width: rem(250) !important;
		width: 100%;
		height: 100%;
		transform: translate3d(rem(-125), rem(60), 0px) !important;

		.react-datepicker {
			width: rem(250) !important;
			box-shadow: none !important;
		}

		.react-datepicker__triangle {
			display: none;
		}
	}

	.react-datepicker__navigation--previous {
		left: 32px !important;
	}

	.react-datepicker__navigation--next {
		right: 32px !important;
	}

	.react-datepicker__current-month {
		cursor: pointer;
	}

	.react-datepicker {
		font-family: $FONTFAMILY-ROBOTO;
		border-color: $COLOR-PLATINUM-4;
		box-shadow: 0 rem(8) rem(24) $COLOR-MANATEE;

		&-wrapper {
			width: 100%;
		}

		&__input {
			&-container {
				input {
					width: 100%;
					max-width: 100%;
					min-height: rem(36);
					padding: rem(16) rem(40) rem(16) rem(12);
					font: 400 rem(14) $FONTFAMILY-ROBOTO;
					font-size: rem(14);
					line-height: rem(16);
					color: $COLOR-QUARTZ;
					background: $COLOR-WHITE url("~assets/images/icons/calendar.svg")
						no-repeat center right 15px/18px auto;
					border: 0;
					border: 1px solid $COLOR-PLATINUM-4;
					border-radius: 0;
					outline: 0;
					-webkit-appearance: none;

					&::placeholder {
						font-family: $FONTFAMILY-BASIC;
						opacity: 0.5;
					}
				}
			}
		}

		&__header {
			display: flex;
			flex-direction: column;
			align-items: center;
			background-color: transparent;
			border: none;
		}

		&__navigation {
			top: rem(12);
			border: none;
			border-bottom: 3px solid $COLOR-BLACK;
			border-left: 3px solid $COLOR-BLACK;
			outline: 0;
			box-shadow: none;
			&--next {
				right: rem(20);
				transform: rotate(225deg);
			}
			&--previous {
				left: rem(20);
				transform: rotate(45deg);
			}
		}

		&__month {
			border: unset;
			outline: 0;
			box-shadow: none;

			&-container {
				width: 100%;
			}

			&-text--keyboard-selected {
				color: unset;
				background-color: unset;
			}

			&--selected,
			&--in-range,
			&--in-selecting-range,
			&--keyboard-selected {
				color: $COLOR-WHITE;
				background: $COLOR-DENIM;
			}

			&-text {
				border: rem(1) solid $COLOR-TRANSPARENT;
				transition: all 0.3s;

				&:not(.react-datepicker__month--selected):not(.react-datepicker__month--in-range):hover {
					background-color: $COLOR-TRANSPARENT;
					border-color: $COLOR-DENIM;
				}

				&.react-datepicker__month--selected,
				&.react-datepicker__month--in-range:hover {
					background: $COLOR-DENIM;
				}
			}
		}

		&__day {
			border: rem(1) solid $COLOR-TRANSPARENT;
			outline: 0;
			box-shadow: none;
			transition: all 0.3s;

			&--selected,
			&--in-range,
			&--keyboard-selected {
				background: $COLOR-DENIM;
			}

			&--in-selecting-range:not(.react-datepicker__day--selecting-range-start) {
				background: $COLOR-DENIM;
				opacity: 0.7;
			}

			&--outside-month {
				color: $COLOR-PLATINUM-6;
			}

			&-name {
				font-weight: 700;
			}

			&:not(.react-datepicker__day--selected):not(.react-datepicker__day--in-selecting-range):not(.react-datepicker__day--in-range):hover {
				background-color: $COLOR-TRANSPARENT;
				border-color: $COLOR-DENIM;
			}
		}
	}

	&-disabled {
		.react-datepicker__input-container input {
			color: $COLOR-QUARTZ;
			background-color: $COLOR-WHITE-SMOKE-2;
		}
	}
}

.o-modal_body {
	.m-calendar {
		&_datecalendar {
			position: absolute !important;
			top: 0 !important;
			left: 50% !important;
		}

		&_monthyearcalendar {
			position: absolute !important;
			top: -10px !important;
			left: 50% !important;
		}
	}
}
