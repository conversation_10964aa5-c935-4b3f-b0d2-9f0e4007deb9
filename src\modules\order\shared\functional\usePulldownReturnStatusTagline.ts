import { useCallback, useMemo } from "react";

import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import {
  OrderReturnStatusTaglineModel,
  orderReturnStatusTaglineOption,
} from "modules/order";
import { getOrderReturnStatusTagline } from "services/crm/order";

export interface PulldownOrderReturnStatusTaglineFunctionalOptions {
  excludePending: boolean;
  orderReturnStatusId?: number;
}

export const usePulldownReturnStatusTagline = ({
  excludePending = false,
  orderReturnStatusId,
}: PulldownOrderReturnStatusTaglineFunctionalOptions) => {
  const [fetchReturnOrderStatusTagline, fetchOrderReturnStatusTaglineState] =
    useAsync(
      useCallback(
        async () =>
          orderReturnStatusId &&
          getOrderReturnStatusTagline(orderReturnStatusId).then((res) =>
            OrderReturnStatusTaglineModel.createMap(res.data.data)
          ),
        [orderReturnStatusId]
      ),
      {
        excludePending,
      }
    );

  const orderReturnStatusTaglines = useMemo(
    () => fetchOrderReturnStatusTaglineState.data || [],
    [fetchOrderReturnStatusTaglineState.data]
  );

  const {
    options: orderReturnStatusTaglineOptions,
    getOptionByValue,
    formatOption: formatOrderStatusTaglineOption,
  } = usePulldownHelper({
    dataSource: orderReturnStatusTaglines,
    optionCreator: orderReturnStatusTaglineOption,
    valueTrans: Number,
  });

  return {
    orderReturnStatusTaglineOptions,
    orderReturnStatusTaglines,
    fetchReturnOrderStatusTagline,
    getOptionByValue,
    formatOrderStatusTaglineOption,
    fetchOrderReturnStatusTaglineState,
  };
};
