import { Story, Meta } from "@storybook/react/types-6-0";

import { Uploadfile, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|molecules/Uploadfile",
  component: Uploadfile,
} as Meta;

const Template: Story<Props> = ({ onChange }) => (
  <Uploadfile onChange={onChange} />
);

export const Normal = Template.bind({});

Normal.args = {};
