/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect } from "react";
import { ColorPicker, Form, Input, InputNumber, Modal, Space } from "antd";
import { Color } from "antd/es/color-picker";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { showLoading } from "components/atoms/base/Spinner";
// import { Modal } from "components/organisms/modal";
import { COLOR } from "constants/color";
import {
  UseCreateStatusTicket,
  useUpdateStatusTicket,
} from "../hooks/useCreateStatusTicket";
import { StatusTicketDataType } from "../StatusTicketPage.V2";

interface StatusTicketModalProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  refetch: () => void;
  dataModal: StatusTicketDataType | null;
  setDataModal: React.Dispatch<
    React.SetStateAction<StatusTicketDataType | null>
  >;
}

export const StatusTicketModal = ({
  open,
  dataModal,
  setOpen,
  setDataModal,
  refetch,
}: StatusTicketModalProps) => {
  const [form] = Form.useForm();
  const { createStatusTicketExe } = UseCreateStatusTicket();
  const { updateStatusTicketExe } = useUpdateStatusTicket();
  const handleSubmit = (values: any) => {
    const color =
      typeof values?.color === "string"
        ? values.color
        : (values.color as unknown as Color).toRgbString();

    const payload = {
      ...values,
      color,
    };
    showLoading(true);
    if (dataModal?.statusTicketId) {
      updateStatusTicketExe({
        ...payload,
        statusTicketId: dataModal.statusTicketId,
      })
        .then(() => {
          showNotification({
            type: "success",
            message: "Cập nhật thành công",
          });
          refetch();
          onClose();
        })
        .catch((err) => {
          const errors = err?.response?.data?.errors;
          if (errors && errors.length > 0) {
            errors.forEach((e: any) => {
              showNotification({
                type: "error",
                message: e.title,
              });
            });
            return;
          }
          showNotification({
            type: "error",
            message: errors.title,
          });
        })
        .finally(() => {
          showLoading(false);
        });
      return;
    }
    createStatusTicketExe(payload)
      .then((res) => {
        showNotification({
          type: "success",
          message: "Tạo thành công",
        });
        refetch();
        onClose();
      })
      .catch((err) => {
        const errors = err?.response?.data?.errors;
        if (errors && errors.length > 0) {
          errors.forEach((e: any) => {
            showNotification({
              type: "error",
              message: e.title,
            });
          });
          return;
        }
        showNotification({
          type: "error",
          message: errors.title,
        });
      })
      .finally(() => {
        showLoading(false);
      });
  };

  const onClose = () => {
    form.resetFields();
    setDataModal(null);
    setOpen(false);
  };

  useEffect(() => {
    if (dataModal) {
      const { createdAt, updatedAt, ...restProps } = dataModal;
      form.setFieldsValue(restProps);
    }
  }, [dataModal]);

  return (
    <Modal
      title={
        dataModal?.statusTicketId
          ? "Chỉnh sửa trạng thái ticket"
          : "Tạo mới trạng thái ticket"
      }
      open={open}
      onCancel={onClose}
      centered
      footer={
        <Space align="center">
          <BaseButton
            type="primary"
            bgColor={COLOR.GRAY[500]}
            hoverColor={COLOR.GRAY[600]}
            onClick={onClose}
          >
            Hủy
          </BaseButton>
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={() => form.submit()}
          >
            Lưu
          </BaseButton>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ color: "#000" }}
        onFinish={handleSubmit}
      >
        <Form.Item
          rules={[
            {
              required: true,
              message: "Vui lòng nhập tên trạng thái",
            },
          ]}
          label="Tên trạng thái"
          name="name"
        >
          <Input placeholder="Nhập tên trạng thái" />
        </Form.Item>
        <Form.Item label="Màu sắc" name="color">
          <ColorPicker />
        </Form.Item>
        <Form.Item
          rules={[
            {
              required: true,
              message: "Vui lòng nhập thứ tự hiển thị",
            },
          ]}
          label="Thứ tự hiển thị"
          name="displayOrder"
        >
          <InputNumber placeholder="Nhập thứ tự hiển thị" className="w-full" />
        </Form.Item>
      </Form>
    </Modal>
  );
};
