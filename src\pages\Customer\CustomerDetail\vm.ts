import { useCallback, useState } from "react";

import { useRadioProvider } from "components/atoms/radio";
import { toastSingleMode } from "components/atoms/toastify";
import { useCalendarProvider } from "components/molecules/calendar";
import { confirm } from "contexts/dialog";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { customerDTO, Gender } from "modules/customer";
import {
  createCustomerEmail,
  createCustomerPhoneNumber,
  deleteCustomerEmail,
  deleteCustomerPhoneNumber,
  getCustomerProfile,
  updateCustomerEmail,
  updateCustomerMainInfo,
  updateCustomerPhoneNumber,
} from "services/crm/customer";

const notifyOnSuccessUpdate = () =>
  toastSingleMode({
    type: "success",
    message: "Cập nhật thành công",
  });

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const notifyOnFailed = (error: any) => {
  const errMessage = getErrorMessageViaErrCode(
    error?.response?.data?.errors?.[0]?.code
  );
  toastSingleMode({
    type: "error",
    message: errMessage.translation.title,
    descripition: errMessage.translation.detail,
  });
};

const useCreateContact = <P, A extends unknown[]>(
  createContact: (...payload: A) => Promise<P>
) => {
  const [isOpenCreationInput, setOpenCreationInput] = useState(false);

  const toggleCreationInput = useCallback(
    () => setOpenCreationInput(!isOpenCreationInput),
    [isOpenCreationInput]
  );

  const [createContactExec, createContactState] = useAsync(createContact, {
    onFailed: useCallback(notifyOnFailed, []),
    onSuccess: useCallback(() => {
      toggleCreationInput();
      toastSingleMode({
        type: "success",
        message: "Thêm thành công",
      });
    }, [toggleCreationInput]),
  });

  return [
    isOpenCreationInput,
    toggleCreationInput,
    createContactExec,
    createContactState,
  ] as const;
};

const useDeleteContact = <P, A extends unknown[]>(
  deleteContact: (...payload: A) => Promise<P>
) => {
  const [deleteContactExec, deleteContactState] = useAsync(deleteContact, {
    onFailed: useCallback(notifyOnFailed, []),
    onSuccess: useCallback(
      () =>
        toastSingleMode({
          type: "success",
          message: "Xoá thành công",
        }),
      []
    ),
  });

  const handleDeleteContact = useCallback(
    (modalDescription: string, ...payload: A) =>
      confirm({
        title: "Xoá",
        contents: modalDescription,
        okLabel: "Xoá",
        cancelLabel: "Đóng",
      }).then(() => deleteContactExec(...payload)),
    [deleteContactExec]
  );

  return [handleDeleteContact, deleteContactState] as const;
};

const useUpdateContact = <P, A extends unknown[]>(
  updateContact: (...payload: A) => Promise<P>
) => {
  const [updateContactExec] = useAsync(updateContact, {
    onFailed: useCallback(notifyOnFailed, []),
    onSuccess: useCallback(notifyOnSuccessUpdate, []),
  });

  const handleUpdateContact = useCallback(
    (modalDescription: string, ...payload: A) =>
      confirm({
        title: "Cập nhật",
        contents: modalDescription,
        okLabel: "Cập nhật",
        cancelLabel: "Đóng",
      }).then(() => updateContactExec(...payload)),
    [updateContactExec]
  );
  return [handleUpdateContact] as const;
};

export const CustomerDetailPageVm = () => {
  const { bindSilent, getCheckedValueGroup, setCheckedValue } =
    useRadioProvider<{
      gender: Gender;
    }>();

  const { getDate, setDate } =
    useCalendarProvider<{
      birthDay: Date;
    }>();

  const [getProfileExec, getProfileState] = useAsync(
    useCallback(
      (payload: { customerId: number }) =>
        getCustomerProfile({ ...payload }).then((res) =>
          customerDTO(res.data.data)
        ),
      []
    ),
    {
      onFailed: useCallback(notifyOnFailed, []),
      onSuccess: (res) => {
        setCheckedValue("gender", res.gender as Gender);
        setDate("birthDay", res.birthDay);
      },
    }
  );

  const [updateMainInfoExec, updateMainInfoState] = useAsync(
    updateCustomerMainInfo,
    {
      onFailed: useCallback(notifyOnFailed, []),
      onSuccess: useCallback(notifyOnSuccessUpdate, []),
    }
  );

  const [
    isOpenPhoneNumberInput,
    togglePhoneNumberInput,
    createPhoneNumber,
    phoneCreationState,
  ] = useCreateContact(createCustomerPhoneNumber);

  const [isOpenEmailInput, toggleEmailInput, createEmail, emailCreationState] =
    useCreateContact(createCustomerEmail);

  const [handleDeletePhoneNumber, phoneNumberDeletionState] = useDeleteContact(
    deleteCustomerPhoneNumber
  );

  const [handleDeleteEmail, emailDeletionState] =
    useDeleteContact(deleteCustomerEmail);

  const [handleUpdatePhoneNumber] = useUpdateContact(updateCustomerPhoneNumber);

  const [handleUpdateEmail] = useUpdateContact(updateCustomerEmail);

  return {
    bindSilent,
    getCheckedValueGroup,
    setDate,
    getDate,
    profile: {
      get: getProfileExec,
      loading: getProfileState.loading,
      data: getProfileState.data,
    },
    mainInfo: {
      update: updateMainInfoExec,
      stateOfUpdate: updateMainInfoState,
    },
    phoneNumber: {
      isOpenCreationInput: isOpenPhoneNumberInput,
      toggleCreationInput: togglePhoneNumberInput,
      create: createPhoneNumber,
      handleUpdate: handleUpdatePhoneNumber,
      handleDelete: handleDeletePhoneNumber,
      creationState: phoneCreationState,
      deletionState: phoneNumberDeletionState,
    },
    email: {
      isOpenCreationInput: isOpenEmailInput,
      toggleCreationInput: toggleEmailInput,
      create: createEmail,
      handleUpdate: handleUpdateEmail,
      handleDelete: handleDeleteEmail,
      creationState: emailCreationState,
      deletionState: emailDeletionState,
    },
  };
};
