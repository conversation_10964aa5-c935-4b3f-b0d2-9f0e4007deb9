import React from "react";

import { Icon } from "components/atoms/icon";
import { Link } from "components/utils/link";

export const OrderList: React.FC<React.PropsWithChildren> = ({ children }) => (
  <ul className="m-orderlist">{children}</ul>
);

export interface OrderItemProps {
  code: string;
  phoneNumber: string;
  status?: string;
  statusColor: string;
  price?: number;
  date: string;
}

export const OrderItem: React.FC<OrderItemProps> = ({
  code,
  phoneNumber,
  status,
  price,
  statusColor,
  date,
}) => (
  <li className="m-orderlist_item">
    <div className="m-orderlist_title">
      <div className="m-orderlist_wrapcode">
        <Icon iconName="cart" />
        <span className="m-orderlist_code">{code}</span>
      </div>
      <div className="m-orderlist_phone">
        <Icon iconName="phone" />
        <Link useNative to={`tel:${phoneNumber}`}>
          {phoneNumber}
        </Link>
      </div>
    </div>
    <div
      className="m-orderlist_status"
      style={{ backgroundColor: statusColor }}
    >
      <span>{status}</span>
    </div>
    <div className="m-orderlist_content">
      <span className="m-orderlist_price">{price} VNĐ</span>
      <span className="m-orderlist_date">{date}</span>
    </div>
  </li>
);
