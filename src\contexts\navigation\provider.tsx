import React, { ReactNode, useEffect } from "react";

import { useNavigate } from "react-router";

interface Props {
  children: ReactNode;
}

export const AppNavigationProvider: React.FC<Props> = (props) => {
  const { children } = props;
  const navigate = useNavigate();

  useEffect(() => {
    const navigationEventController = (e: CustomEvent) => {
      const { detail = {} } = e;
      switch (detail.type) {
        case "push": {
          navigate(detail.path, { state: detail.state });
          break;
        }
        case "replace": {
          navigate(detail.path, { state: detail.state, replace: true });
          break;
        }
        case "goBack": {
          navigate(-1);
          break;
        }
        case "newPage": {
          window.open(detail.path);
          break;
        }
        default:
          break;
      }
    };

    window.addEventListener("route", navigationEventController);

    return () => {
      window.removeEventListener("route", navigationEventController);
    };
  }, [navigate]);

  return children;
};
