import React, { useState, useCallback, useImperativeHandle } from "react";

import ReactPagination from "react-paginating";

import { Icon } from "components/atoms/icon";
import { mapModifiers } from "helpers/component";

type Modifiers = "center";

export interface PaginationReference {
  setPage: React.Dispatch<React.SetStateAction<number>>;
  currentPage: number;
  reset: () => void;
}
export interface Props {
  total: number;
  pageCount?: number;
  onPageChange: (pageNum: number) => void;
  defaultCurrentPage?: number;
  ref?: PaginationReference;
  modifiers?: Modifiers | Modifiers[];
}

export const Pagination = React.forwardRef<PaginationReference, Props>(
  ({ total, pageCount, defaultCurrentPage, onPageChange, modifiers }, ref) => {
    const [currentPageNum, setCurrentPageNum] = useState(
      defaultCurrentPage && defaultCurrentPage < total ? defaultCurrentPage : 1
    );

    const onChangePageFactory = useCallback(
      (page?: number) => {
        if (typeof page !== "undefined" && page !== currentPageNum) {
          setCurrentPageNum(page);
          onPageChange(page);
        }
      },
      [currentPageNum, onPageChange]
    );

    useImperativeHandle(
      ref,
      () => ({
        setPage: setCurrentPageNum,
        currentPage: currentPageNum,
        reset: () => {
          setCurrentPageNum(1);
        },
      }),
      [currentPageNum]
    );

    return (
      <ReactPagination
        total={total}
        pageCount={pageCount}
        currentPage={currentPageNum}
        limit={1}
      >
        {({
          pages,
          currentPage,
          hasNextPage,
          hasPreviousPage,
          previousPage,
          nextPage,
          totalPages,
          getPageItemProps,
        }) => (
          <ul className={mapModifiers("m-pagination_wrapper", modifiers)}>
            <li
              className={mapModifiers(
                "m-pagination_first",
                !hasPreviousPage && "disabled"
              )}
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...(getPageItemProps({
                total,
                pageValue: 1,
                onPageChange: onChangePageFactory,
              }) as React.LiHTMLAttributes<HTMLLIElement>)}
            >
              <Icon iconName="angle-left" />
              <Icon iconName="angle-left" />
            </li>

            <li
              className={mapModifiers(
                "m-pagination_previous",
                !hasPreviousPage && "disabled"
              )}
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...(getPageItemProps({
                total,
                pageValue: hasPreviousPage ? previousPage : 1,
                onPageChange: onChangePageFactory,
              }) as React.LiHTMLAttributes<HTMLLIElement>)}
            >
              <Icon iconName="angle-left" />
            </li>

            {pages.map((page, index) => (
              <li
                // eslint-disable-next-line react/no-array-index-key
                key={index}
                // eslint-disable-next-line react/jsx-props-no-spreading
                {...(getPageItemProps({
                  total,
                  pageValue: page,
                  onPageChange: onChangePageFactory,
                  className:
                    currentPage === page ? "m-pagination_actived" : undefined,
                }) as React.LiHTMLAttributes<HTMLLIElement>)}
              >
                <span>{page}</span>
              </li>
            ))}

            <li
              className={mapModifiers(
                "m-pagination_next",
                !hasNextPage && "disabled"
              )}
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...(getPageItemProps({
                total,
                pageValue: hasNextPage ? nextPage : totalPages,
                onPageChange: onChangePageFactory,
              }) as React.LiHTMLAttributes<HTMLLIElement>)}
            >
              <Icon iconName="angle-right" />
            </li>

            <li
              className={mapModifiers(
                "m-pagination_last",
                !hasNextPage && "disabled"
              )}
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...(getPageItemProps({
                total,
                pageValue: totalPages,
                onPageChange: onChangePageFactory,
              }) as React.LiHTMLAttributes<HTMLLIElement>)}
            >
              <Icon iconName="angle-right" />
              <Icon iconName="angle-right" />
            </li>
          </ul>
        )}
      </ReactPagination>
    );
  }
);
