import { useState } from "react";

import { Story, Meta } from "@storybook/react/types-6-0";

import { Pulldown, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|atoms/Pulldown",
  component: Pulldown,
} as Meta;

const options = [
  { value: "chocolate", label: "Chocolate" },
  { value: "strawberry", label: "Strawberry" },
  { value: "vanilla", label: "Vanilla" },
  { value: "chocolate", label: "Chocolate" },
  { value: "strawberry", label: "Strawberry" },
  { value: "vanilla", label: "Vanilla" },
  { value: "chocolate", label: "Chocolate" },
  { value: "strawberry", label: "Strawberry" },
  { value: "strawberry", label: "Strawberry" },
  { value: "strawberry", label: "Strawberry" },
];

const optionsPush = [
  { value: "chocolate", label: "Chocolate" },
  { value: "strawberry", label: "Strawberry" },
];

const Template: Story<Props> = ({
  name,
  placeholder,
  isSearchable,
  isDisabled,
  isClearable,
  errorMessage,
  onChange,
  isMulti,
  ...innerProps
}) => {
  const [hasMore, setHasMore] = useState(true);

  const triggerLoadMore = () => {
    return new Promise<void>((resolve, reject) => {
      if (options.length < 20) {
        setTimeout(() => {
          options.push(...optionsPush);
          resolve();
        }, 500);
      } else {
        resolve();
        setHasMore(false);
      }
    });
  };

  return (
    <Pulldown
      name={name}
      options={options}
      isSearchable={isSearchable}
      placeholder={placeholder}
      isDisabled={isDisabled}
      isClearable={isClearable}
      errorMessage={errorMessage}
      onChange={onChange}
      triggerLoadMore={triggerLoadMore}
      threshold={0} // 10 | 20 | 30 max away from bottom will trigger func loadMore
      hasMore={hasMore}
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...innerProps}
    />
  );
};

export const Normal = Template.bind({});

Normal.args = {
  isSearchable: true,
  placeholder: "Placeholder",
  isDisabled: false,
  isClearable: false,
};
