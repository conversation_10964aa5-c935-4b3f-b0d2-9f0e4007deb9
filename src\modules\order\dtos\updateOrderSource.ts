import pick from "object.pick";

import { cleanEmptyString } from "helpers/object";
import { createMapper, force, fromSchema } from "libs/adapters/dto";
import { mergeSchema } from "libs/domain";

import { OrderSourceSchema } from "../entities/orderSource";

export const updateOrderSourceDetailDto = createMapper(
  fromSchema(mergeSchema(pick(OrderSourceSchema, ["name"]))),

  force((data) => ({
    ...cleanEmptyString(data),
  }))
);

export type UpdateOrderSourceDetailDtoType = ReturnType<
  typeof updateOrderSourceDetailDto
>;
