import { useCallback, useEffect, useRef, useState } from "react";

import produce from "immer";

import { debounce } from "lodash";
import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import {
  orderStatusItemListDto,
  orderStatusTaglineItemListDto,
  createOrderStatusTaglineDto,
  CreateOrderStatusTaglineDtoType,
} from "modules/order";
import {
  createOrderStatusTagline as createOrderStatusTaglineService,
  getListOrderStatusTagline,
  getOrderStatusByCode,
} from "services/crm/order";

type ModalType = "createStatusTagline";

interface State {
  modalState: {
    open: boolean;
    modalType?: ModalType;
  };
  createModalState: {
    colorSelected?: string;
  };
  pagination: {
    pageSize: number;
  };

  searchText: string;
}

export interface OrderStatusTaglineListPageVmProps {
  orderStatusId: number;
  orderStatusCode: string;
}

export const OrderStatusTaglineListPageVm = ({
  orderStatusId,
  orderStatusCode,
}: OrderStatusTaglineListPageVmProps) => {
  const [searchInputValue, setSearchInputValue] = useState<string>("");
  const isInitialMount = useRef<boolean>(true);

  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      modalType: undefined,
    },
    createModalState: {
      colorSelected: "#000",
    },
    pagination: {
      pageSize: 10,
    },
    searchText: "",
  });

  const [getOrderStatusTaglineListExec, getOrderStatusTaglineListState] =
    useAsync(
      useCallback(
        (options: {
          orderStatusId: number;
          pageNum: number;
          pageSize: number;
          searchText?: string;
        }) =>
          getListOrderStatusTagline({
            ...options,
            ...(options.searchText && { inputSearch: options.searchText }),
          }).then((res) => ({
            data: mapFrom(res.data.data, orderStatusTaglineItemListDto),
            pagination: paginationDTO(res.data.links),
          })),
        []
      )
    );

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.modalType = type;
      })
    );
  }, []);

  const handleCloseModal = useCallback(() => {
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.modalType = undefined;
      })
    );
  }, []);

  const [getOrderStatusByCodeExec, getOrderStatusByCodeState] = useAsync(
    useCallback(
      (payload: { orderStatusCode: string }) =>
        getOrderStatusByCode({ ...payload }).then((res) =>
          orderStatusItemListDto(res.data.data)
        ),
      []
    ),
    {
      excludePending: true,
    }
  );

  const { gotoPage, ...orderStatusTaglineListPaginationState } = usePagination({
    pageSize: state.pagination.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getOrderStatusTaglineListExec({
        orderStatusId,
        pageSize,
        pageNum: page,
        searchText: state.searchText,
      }),
  });

  const [createTaglineServiceExec] = useAsync(createOrderStatusTaglineService, {
    excludePending: true,

    onSuccess: useCallback(() => {
      toastSingleMode({ type: "success", message: "Tạo mới thành công" });
      handleCloseModal();
      gotoPage(1);
    }, [gotoPage, handleCloseModal]),

    onFailed: useCallback((error) => {
      const errMessage = getErrorMessageViaErrCode(
        error.response?.data?.errors?.[0]?.code
      );

      toastSingleMode({
        type: "error",
        message: errMessage.translation.title,
        descripition: errMessage.translation.detail,
      });
    }, []),
  });

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pagination.pageSize = pageSize;
      })
    );
  }, []);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) => {
      return state.modalState.open && state.modalState.modalType === type;
    },
    [state.modalState]
  );

  useEffect(() => {
    getOrderStatusByCodeExec({ orderStatusCode });
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize]);

  const {
    sortedData: orderStatusTaglineListData,
    toggleSortState: toggleSortOrderBy,
  } = useSortable({
    data: getOrderStatusTaglineListState.data?.data,
    sortBy: {
      name: (data) => data.name,
      updatedAt: (data) => data.updatedAt,
    },
  });

  const onChangeColor = useCallback(
    (code: string) => {
      setState(
        produce((draft) => {
          draft.createModalState.colorSelected = code;
        })
      );
    },

    [setState]
  );

  const createOrderStatusTagline = useCallback(
    async (rawPayload: Partial<CreateOrderStatusTaglineDtoType>) => {
      const orderStatusTaglinePayload = createOrderStatusTaglineDto({
        ...rawPayload,
      });

      const { orderStatusId: _orderStatusId, ...payload } =
        orderStatusTaglinePayload;

      await createTaglineServiceExec(_orderStatusId, payload);
    },
    [createTaglineServiceExec]
  );

  const debouncedUpdateSearch = useCallback(
    debounce((searchText: string) => {
      console.log("🚀 Debounced setState with:", searchText);

      setState(
        produce((draft) => {
          draft.searchText = searchText;
        })
      );
    }, 500),
    []
  );

  const handleSearchInputChange = useCallback(
    (value: string) => {
      console.log("⌨️ Input onChange:", value);

      // Update input value ngay lập tức
      setSearchInputValue(value);

      // Debounce việc setState
      debouncedUpdateSearch(value);
    },
    [debouncedUpdateSearch]
  );

  const handleClearSearch = useCallback(() => {
    console.log("🗑️ Input cleared");

    setSearchInputValue("");
    debouncedUpdateSearch("");
  }, [debouncedUpdateSearch]);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // console.log("🔄 searchText changed, calling API:", state.searchText);

    gotoPage(1);
    getOrderStatusTaglineListExec({
      orderStatusId,
      pageSize: state.pagination.pageSize,
      pageNum: 1,
      searchText: state.searchText,
    });
  }, [state.searchText]);

  useEffect(() => {
    return () => {
      debouncedUpdateSearch.cancel();
    };
  }, [debouncedUpdateSearch]);

  return {
    gotoPage,
    handleChangePageSize,
    toggleSortOrderBy,
    orderStatusTaglineListData: orderStatusTaglineListData || [],
    getOrderStatusTaglineListLoading: getOrderStatusTaglineListState.loading,
    pageSize: state.pagination.pageSize,
    orderStatusTaglineListPaginationState,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    colorSelected: state.createModalState.colorSelected,
    orderStatus: getOrderStatusByCodeState.data || undefined,
    onChangeColor,
    createOrderStatusTagline,

    handleSearchInputChange,
    handleClearSearch,
  };
};
