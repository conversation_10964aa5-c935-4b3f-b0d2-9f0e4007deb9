/* eslint-disable @typescript-eslint/no-explicit-any */
import { String, Model, Array, Mixed, ModelValue, Number } from "libs/domain";

export const MetadataFileSchema = {
  width: Number(),
  height: Number(),
};

export const MessageSchema = {
  _id: String(),
  messageId: String(),
  sender: { id: String() },
  recipient: { id: String() },
  content: {
    mid: String(),
    text: String(),
    attachments: Array(
      Mixed({
        type: String(),
        payload: {
          url: String(),
        },
      })
    ),
    metadata: {
      fromSocketId: String({ defaultValue: "" }),
      tempId: String({ defaultValue: "" }),
      media: {
        width: Number(),
        height: Number(),
        type: String(),
        originalName: String(),
      },
    },
  },
  conversationId: String(),
  createdAt: (raw: any) => raw && new Date(raw),
  updatedAt: (raw: any) => raw && new Date(raw),
};

export const createTempMessageId = () => {
  return `_mid_${Math.random().toString(36).substr(2, 9)}`;
};

export const messageTypeByContent = (content: MessageEntityType["content"]) => {
  if (content.text) return "text";
  if (content.metadata?.media?.type === "image") return "image";
  if (content.metadata?.media?.type === "video") return "video";
  if (content.metadata?.media?.type === "file") return "file";

  return null;
};

export const messageTypeByExtension = (ext: string) => {
  if (ext.match(/^image\/*/)) return "image";
  if (ext.match(/^video\/*/)) return "video";

  return "file";
};

export const createTempTextMessage = (payload: {
  senderId: string;
  text?: string;
  conversationId: string;
  files?: {
    blob: Blob;
    file: File;
    metadata?: {
      width?: number;
      height?: number;
    };
  }[];
}) =>
  MessageModel.create({
    _id: createTempMessageId(),
    sender: { id: payload.senderId },
    content: {
      text: payload.text,
      attachments: (payload.files || []).map(({ blob }) => ({
        type: messageTypeByExtension(blob.type),
        payload: {
          url: URL.createObjectURL(blob),
        },
      })),
      metadata: payload.files?.[0] && {
        media: {
          width: payload.files?.[0]?.metadata?.width,
          height: payload.files?.[0]?.metadata?.height,
          type: messageTypeByExtension(payload.files?.[0].blob.type),
          originalName: payload.files?.[0]?.file?.name,
        },
      },
    },
    conversationId: payload.conversationId,
    createdAt: new Date(),
    updatedAt: new Date(),
  });

export const MessageModel = new Model(MessageSchema);
export type MessageEntityType = ModelValue<typeof MessageModel>;
