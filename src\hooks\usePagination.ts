import { useCallback, useState } from "react";

import { PaginationDTOType } from "modules/common/pagination";

type PaginationState = {
  totalPage?: number;
  currentPage?: number;
  pageSize: number;
};

export interface PaginationHookProps {
  pageSize: number;
  actionOnPageChange: (payload: { page: number; pageSize: number }) => Promise<{
    pagination: PaginationDTOType;
  }>;
}

export const usePagination = ({
  pageSize,
  actionOnPageChange,
}: PaginationHookProps) => {
  const [state, setState] = useState<PaginationState>({
    pageSize,
  });

  const gotoPage = useCallback(
    async (page: number) => {
      const { totalPage } = state;
      if (totalPage && page > totalPage) return;
      const { pagination } =
        (await actionOnPageChange({
          page,
          pageSize,
        })) || {};

      setState((prevState) => ({
        ...prevState,
        currentPage: pagination?.self?.pageNum,
        totalPage: pagination?.last?.pageNum,
        pageSize: pagination?.self?.pageSize,
      }));
    },
    [actionOnPageChange, pageSize, state]
  );

  return {
    ...state,
    gotoPage,
  };
};
