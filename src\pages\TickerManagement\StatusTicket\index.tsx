/* eslint-disable prettier/prettier */
import imgSearchBlue from "assets/images/icons/search-blue.svg";
import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Pulldown } from "components/atoms/pulldown";
import { Tag } from "components/atoms/tag";
import { Textfield } from "components/atoms/textfield";
import { Pagination } from "components/molecules/pagination";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Table, Tbody, Td, Th, Thead, Tr } from "components/organisms/table";
import { General } from "components/pages/general";
import { CreateStatusTicketModal } from "modules/system";
import PaginationSection from "pages/Common/paginationSection";
import TableManipulation from "pages/Common/tableManipulation";

import RootPageRouter from "../index";
import { CreateStatusTicketValidation } from "./constant";
import { StatusTicketPageVm } from "./vm";

const StatusTicketPage = () => {
  const {
    handleOpenModalByType,
    handleOnCloseModal,
    modalTypeIsOpen,
  } = StatusTicketPageVm();

  return (
    <General>
      <title key="title">Danh sách trạng thái ticket</title>
      <Section>
        <Heading type="h1" modifiers="primary">
          DANH SÁCH TRẠNG THÁI TICKET
        </Heading>
        <Section>
          <Row className="d-flex">
            <Col
              className="ml-auto"
              xs={{ span: 12, order: 2 }}
              lg={{ span: 6, order: 1 }}
            >
              <Textfield iconSrc={imgSearchBlue} placeholder="Tìm kiếm" />
            </Col>
            <Col
              className="d-flex justify-content-end u-mb-15 u-mb-lg-0"
              xs={{ span: 12, order: 1 }}
              lg={{ span: 6, order: 2 }}
            >
              <Button
                onClick={() => handleOpenModalByType("createStatusTicket")}
              >
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Section>
        <Section>
          <Table>
            <Thead>
              <Tr>
                <Th modifiers="center" stickyLeft>
                  STT
                </Th>
                <Th modifiers="center">Tên trạng thái</Th>
                <Th modifiers="center">Màu sắc</Th>
                <Th modifiers="center">Thứ tự hiển thị</Th>
                <Th modifiers="center">Cập nhật cuối</Th>
                <Th modifiers="center" stickyRight>
                  Thao tác
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {Array(10)
                .fill(0)
                .map((_, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <Tr key={index}>
                    <Td modifiers="center" stickyLeft>
                      1
                    </Td>
                    <Td modifiers="center">Đang xử lý</Td>
                    <Td modifiers="center">
                      <Tag color="#0BBA99" />
                    </Td>
                    <Td modifiers="center">1</Td>
                    <Td modifiers="center">22/02/2021 17:09</Td>
                    <Td modifiers="center" stickyRight>
                      <TableManipulation
                        editAction={{
                          id: "1",
                          action: () =>
                            RootPageRouter.gotoChild("statusTicketDetails", {
                              params: {
                                ticketId: index.toString(),
                              },
                            }),
                        }}
                        deleteAction={{
                          id: "1",
                          action: () =>
                            RootPageRouter.gotoChild("statusTicket"),
                        }}
                      />
                    </Td>
                  </Tr>
                ))}
            </Tbody>
          </Table>
        </Section>
        <Section>
          <PaginationSection
            appearanceOption={
              <Pulldown
                placeholder="Số lượng hiển thị"
                options={[5, 10, 15, 25, 30].map((size) => ({
                  label: `${size}`,
                  value: `${size}`,
                }))}
              />
            }
            paginateOption={
              <Pagination
                modifiers="center"
                total={5}
                pageCount={5}
                onPageChange={() => {}}
                defaultCurrentPage={1}
              />
            }
          />
        </Section>
        <CreateStatusTicketModal
          open={modalTypeIsOpen("createStatusTicket")}
          onClose={handleOnCloseModal}
          CreateStatusTicketValidation={CreateStatusTicketValidation}
        />
      </Section>
    </General>
  );
};

export default StatusTicketPage;
