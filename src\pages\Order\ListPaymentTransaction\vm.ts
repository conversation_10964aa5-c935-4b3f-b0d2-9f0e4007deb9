import { useCallback, useState, useEffect } from "react";

import produce from "immer";

import { useFormContainer } from "helpers/form";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import { orderPaymentDto } from "modules/order";
import { getListOrderPayment } from "services/crm/order";

import RootPageRouter from "..";

export interface FilterState {
  orderCode?: string;
  filterStatus?: number;
  filterMethod?: number;
  filterCreatedAtFrom?: string;
  filterCreatedAtTo?: string;
}

interface State {
  openForm: boolean;
  filterState: FilterState;
  pagination: {
    pageSize: number;
  };
}

export const ListPaymentTransactionPageVm = () => {
  const { setValue, register } = useFormContainer();

  const [state, setState] = useState<State>({
    openForm: false,
    filterState: {
      orderCode: undefined,
      filterStatus: undefined,
      filterMethod: undefined,
      filterCreatedAtFrom: undefined,
      filterCreatedAtTo: undefined,
    },
    pagination: {
      pageSize: 10,
    },
  });

  const [getListOrderPaymentExec, getListOrderPaymentState] = useAsync(
    useCallback(
      (payload: {
        pageSize: number;
        pageNum: number;
        orderCode?: string;
        filterStatus?: number;
        filterMethod?: number;
        filterCreatedAtFrom?: string;
        filterCreatedAtTo?: string;
      }) =>
        getListOrderPayment({ ...payload }).then((res) => ({
          data: mapFrom(res.data.data, orderPaymentDto),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const toggleFilterForm = useCallback(() => {
    setState(
      produce((draft) => {
        draft.openForm = !draft.openForm;
      })
    );
  }, []);

  const gotoDetailPaymentTransaction = useCallback(
    (paymentTransactionId: number) =>
      RootPageRouter.gotoChild("paymentTransactionDetail", {
        params: { paymentTransactionId: paymentTransactionId.toString() },
      }),
    []
  );

  const { gotoPage, ...listOrderPaymentPaginationState } = usePagination({
    pageSize: state.pagination.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getListOrderPaymentExec({
        pageSize,
        pageNum: page,
        ...state.filterState,
      }),
  });

  const {
    sortedData: orderPaymentData,
    toggleSortState: toggleSortOrderPaymentBy,
  } = useSortable({
    data: getListOrderPaymentState.data?.data,
    sortBy: {
      createdAt: (data) => data.createdAt,
    },
  });

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize, state.filterState]);

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pagination.pageSize = pageSize;
      })
    );
  }, []);

  const handleChangeFilter = useCallback(
    (filterData: Omit<FilterState, "openForm">) => {
      setState(
        produce((draft) => {
          draft.filterState = {
            ...draft.filterState,
            ...filterData,
          };
        })
      );
    },
    []
  );

  const onHandleResetFilterState = useCallback(() => {
    setValue("orderCode", undefined);
    setValue("filterStatus", null);
    setValue("filterMethod", null);

    handleChangeFilter({
      orderCode: undefined,
      filterStatus: undefined,
      filterMethod: undefined,
      filterCreatedAtFrom: undefined,
      filterCreatedAtTo: undefined,
    });
  }, [handleChangeFilter, setValue]);

  return {
    toggleFilterForm,
    orderPaymentData,
    toggleSortOrderPaymentBy,
    filterFormOpen: state.openForm,
    getListOrderPaymentLoading: getListOrderPaymentState.loading,
    listOrderPaymentPaginationState,
    gotoDetailPaymentTransaction,
    handleChangePageSize,
    gotoPage,
    pageSize: state.pagination.pageSize,
    handleChangeFilter,
    onHandleResetFilterState,
    registerFormContainer: register,
  };
};
