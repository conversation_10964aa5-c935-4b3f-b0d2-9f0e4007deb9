import { useCallback, useEffect, useMemo, useRef } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import {
  orderStatusTaglineItemListDto,
  updateOrderStatusTaglineDto,
  UpdateOrderStatusTaglineDtoType,
} from "modules/order";
import {
  getOrderStatusTaglineDetail,
  updateOrderStatusTagline,
} from "services/crm/order";

export interface OrderStatusTaglineDetailPageVmProps {
  orderStatusId: number;
  taglineId: number;
}

export const OrderStatusTaglineDetailPageVm = ({
  orderStatusId,
  taglineId,
}: OrderStatusTaglineDetailPageVmProps) => {
  const selectedColorRef = useRef<string | undefined>();

  const setSelectedColor = useCallback((color: string) => {
    selectedColorRef.current = color;
  }, []);

  const [getOrderStatusTaglineDetailExec, getOrderStatusTaglineDetailState] =
    useAsync(
      useCallback(
        (payload: { orderStatusId: number; taglineId: number }) =>
          getOrderStatusTaglineDetail({ ...payload }).then((res) =>
            orderStatusTaglineItemListDto(res.data.data)
          ),
        []
      ),
      {
        onSuccess: useCallback((res) => {
          setSelectedColor(res.color);
          // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []),
        onFailed: useCallback((error) => {
          const errMessage = getErrorMessageViaErrCode(
            error?.response?.data?.errors?.[0]?.code
          );
          toastSingleMode({
            type: "error",
            message: errMessage.translation.title,
            descripition: errMessage.translation.detail,
          });
        }, []),
      }
    );

  const [updateOrderStatusTaglineExec, updateOrderStatusTaglineState] =
    useAsync(updateOrderStatusTagline, {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Cập nhật thành công" });
      }, []),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(error?.errors?.[0]?.code);

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    });

  const orderStatusTaglineDetail = useMemo(
    () => getOrderStatusTaglineDetailState.data,
    [getOrderStatusTaglineDetailState.data]
  );

  const handleUpdateOrderStatusTagline = useCallback(
    (rawPayload: Partial<UpdateOrderStatusTaglineDtoType>) => {
      const updateOrderStatusTaglinePayload = updateOrderStatusTaglineDto({
        ...orderStatusTaglineDetail,
        ...rawPayload,
      });

      updateOrderStatusTaglineExec(
        orderStatusId,
        taglineId,
        updateOrderStatusTaglinePayload
      );
    },
    [
      taglineId,
      orderStatusId,
      orderStatusTaglineDetail,
      updateOrderStatusTaglineExec,
    ]
  );

  useEffect(() => {
    if (
      orderStatusId &&
      taglineId &&
      !Number.isNaN(orderStatusId) &&
      !Number.isNaN(taglineId)
    ) {
      getOrderStatusTaglineDetailExec({ orderStatusId, taglineId });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderStatusId, taglineId]);

  return {
    orderStatusTaglineDetail,
    loading: getOrderStatusTaglineDetailState.loading,
    handleUpdateOrderStatusTagline,
    updateOrderStatusTaglineState,
    setSelectedColor,
    selectedColorRef,
  };
};
