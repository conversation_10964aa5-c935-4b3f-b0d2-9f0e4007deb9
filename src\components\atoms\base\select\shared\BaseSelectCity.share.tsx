/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useMemo } from "react";
import _ from "lodash";
import { mappedDataToDefaultOption } from "helpers/convertDataToDefaultOption.helper";
import { useGetCities } from "services/crm/location/hooks/location.hooks";
import { BaseSelect, BaseSelectProps } from "../BaseSelect";

export type BaseSelectCityProps = {} & BaseSelectProps;

export default function BaseSelectCity(props: BaseSelectCityProps) {
  const { ...rest } = props;
  const { cityRefetch, cityData, loading } = useGetCities();

  const cityOptions = useMemo(() => {
    if (_.size(cityData?.data) > 0) {
      const result = mappedDataToDefaultOption({
        data: cityData?.data,
        keyLabel: "name",
        keyValue: "cityId",
        restItem: true,
      });
      return result;
    }
    return [];
  }, [cityData]);

  useEffect(() => {
    cityRefetch();
  }, []);

  return <BaseSelect options={cityOptions} loading={loading} {...rest} />;
}
