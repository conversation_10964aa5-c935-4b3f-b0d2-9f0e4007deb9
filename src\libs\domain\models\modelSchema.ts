/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-underscore-dangle */
import { Schema, schemaParser } from "../schema";
import { SchemaType } from "../schema/types";

export interface ModelConfig {
  safeMode?: boolean;
}

const defaultConfig: Partial<ModelConfig> = {
  safeMode: true,
};

/**
 * @class Model
 * @description This is a `Model` class
 * @param `o` - Object on which to generate the `schema`.
 * @property _config: A private property, it contains the `Model` config
 * @property _schema: A private property, it contains the `Model` schema
 * @method create: Create a `schemaParser`
 * @method createMap: Map to the `create` method
 * @method schema: Get the `schema`
 * @return The instance of `Model` class
 */
export class Model<O extends { [key: string]: unknown }> {
  private _config: ModelConfig;

  private _schema: Schema<O>;

  constructor(schema: SchemaType<O>, config?: ModelConfig) {
    this._config = { ...(config || {}), ...defaultConfig };
    this._schema = new Schema(schema);
  }

  create = (dataSource: any) => {
    return schemaParser(this._schema, dataSource, this._config);
  };

  createMap = (dataSource: any[]) => {
    let _dataSource = dataSource;
    if (!_dataSource || !Array.isArray(_dataSource)) _dataSource = [];

    return _dataSource.map(this.create);
  };

  get schema() {
    return Object.freeze(this._schema.schema);
  }
}
