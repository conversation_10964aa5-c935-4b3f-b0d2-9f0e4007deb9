import { PaginationDTOType } from "modules/common/pagination";
import { MetaTotalRecords } from "pages/TickerManagement/StatusTicket/dto/get-status-ticket.dto";

export interface GetListDeliveryPartnerDto {
  pageNum?: number;
  pageSize?: number;
}

export interface GetListDeliveryPartnerDtoResponseDto extends MetaTotalRecords {
  data: DeliveryPartnerDto[];
  links: PaginationDTOType;
}

export interface GetListDeliveryPartnerWithoutPaginationResponseDto {
  data: DeliveryPartnerDto[];
}

export interface DeliveryPartnerDto {
  deliveryPartnerId: number;
  name: string;
  code: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
