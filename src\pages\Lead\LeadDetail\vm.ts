import { useState, useCallback, useEffect } from "react";

import { useAsync } from "hooks/useAsync";
import { useForceUpdate } from "libs/adapters/stores/react";
import { employeeGroupDto, EmployeeGroupDtoType } from "modules/employee";
import { leadDTO } from "modules/lead";
import { getEmployeeGroup } from "services/crm/employee";
import { getLeadDetail, updateLeadNote } from "services/crm/lead";

interface LeadDetailVmProps {
  leadId: string;
}

interface State {
  creatorsGroup?: EmployeeGroupDtoType;
  assignedEmployeeGroup?: EmployeeGroupDtoType;
}

export const LeadDetailPageVm = ({ leadId }: LeadDetailVmProps) => {
  const [state, setState] = useState<State>();

  const [forceState, forceExe] = useForceUpdate();
  const [getLeadDetailExec, getLeaDetailState] = useAsync(
    useCallback(
      (payload: { leadId: string }) =>
        getLeadDetail({ ...payload }).then((res) => leadDTO(res.data.data)),
      []
    )
  );

  const [getEmployeeGroupByIdExec, getEmployeeGroupByIdState] = useAsync(
    (payload: { groupId: number }) => getEmployeeGroup({ ...payload })
  );

  useEffect(() => {
    if (leadId) {
      getLeadDetailExec({ leadId }).then(async (res) => {
        const { creators, assignedEmployee } = res;
        if (creators && assignedEmployee) {
          const cretorsGroup = getEmployeeGroup({
            groupId: creators?.[0].employeeGroupId,
          });
          const assignEmployeeGroup = getEmployeeGroup({
            groupId: assignedEmployee.employeeGroupId,
          });
          const data = await Promise.allSettled([
            cretorsGroup,
            assignEmployeeGroup,
          ]);

          const result: Array<EmployeeGroupDtoType | undefined> = [];

          data.forEach((item) => {
            result.push(
              item.status === "fulfilled"
                ? employeeGroupDto(item.value.data.data)
                : undefined
            );
          });

          setState({
            creatorsGroup: result[0],
            assignedEmployeeGroup: result[1],
          });
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [leadId, forceState]);

  return {
    leadDetail: getLeaDetailState.data,
    groupEmployee: state,
    isLoading: getLeaDetailState.loading,
    getEmployeeGroupById: getEmployeeGroupByIdExec,
    insertLeadNote: updateLeadNote,
    employeeGroupState: getEmployeeGroupByIdState,
    forceUpdate: forceExe,
  };
};
