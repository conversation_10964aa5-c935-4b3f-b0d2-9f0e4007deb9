import {
  CreateDeliveryPartnerDtoType,
  CreateOrderDtoType,
  CreateOrderReturnStatusDtoType,
  CreateOrderSourceDtoType,
  UpdateOrderStatusDtoType,
  UpdateReturnOrderStatusTaglineDtoType,
  CreateOrderStatusDtoType,
  CreateOrderStatusTaglineDtoType,
  createReturnOrderStatusTaglineDtoType,
  UpdateDeliveryPartnerDetailDtoType,
  UpdateOrderDtoType,
  UpdateOrderSourceDetailDtoType,
  UpdateOrderStatusTaglineDtoType,
  UpdateReturnOrderStatusDtoType,
} from "modules/order";

import crmDriverV1 from "../crm-driver-v1";

export interface OrderSearchPayload {
  pageNum: number;
  pageSize: number;
  customerId?: string;
  orderStatusId?: number;
  orderStatusTaglineId?: number;
  orderSourceId?: number;
  code?: string;
  returnOrderStatusId?: number;
  returnOrderStatusTaglineId?: number;
  createdDateStarted?: string;
  createdDateEnded?: string;
  changeOrderStatusDateStarted?: string;
  changeOrderStatusDateEnded?: string;
  assignedEmployeeId?: number;
  originalShopId?: number;
  deliveryShopId?: number;
  deliveryPartnerId?: number;
}

export interface OrderSearchPayloadV2 {
  pageNum: number;
  pageSize: number;
  // FILTER
  startChangeStatusDate?: string;
  endChangeStatusDate?: string;
  startChangeOrderDate?: string;
  endChangeOrderDate?: string;
  startChangeShopDate?: string;
  endChangeShopDate?: string;
  startCreatedAt?: string;
  endCreatedAt?: string;

  inputSearch?: string;
  orderStatusId?: number[] | string[];
  orderSourceId?: number[] | string[];
  // orderReturnStatusId?: number[] |string[];
  // orderStatusTaglineId?: number[] |string[];
  // orderReturnStatusTaglineId?: number[] |string[];
  paymentMethod?: string;
  deliveryMethod?: string[] | number[];
  employeeId?: number[] | string[];
  franchiseStore?: number[] | string[];
}

export const findOrders = (payload: OrderSearchPayload) =>
  crmDriverV1.get("/orders/external", {
    params: {
      ...payload,
    },
  });

export const filterOrder = (payload: OrderSearchPayloadV2) => {
  return crmDriverV1.post("/orders/external/filterOrders", payload);
};

export const getOrderDetail = (payload: { orderId: number }) =>
  crmDriverV1.get(`/orders/external/${payload.orderId}`);

export const getOrderStatus = (params: { searchText: string }) =>
  crmDriverV1.get("/orders/external/order-status", {
    params: {
      searchText: params.searchText || "",
    },
  });

export const deleteOrder = (payload: { orderId: number }) => {
  return crmDriverV1.delete(`/orders/external/delete/${payload.orderId}`);
};

export const getListOrderStatus = (payload?: {
  pageNum: number;
  pageSize: number;
  searchText?: string;
}) =>
  crmDriverV1.get(`/orders/external/order-status`, {
    params: {
      ...payload,
    },
  });

export const getOrderTagline = (payload: { orderStatusId: number }) =>
  crmDriverV1.get(
    `/orders/external/order-status/${payload.orderStatusId}/taglines`
  );

export const createOrderStatusTagline = (
  orderStatusId: number,
  payload: Omit<CreateOrderStatusTaglineDtoType, "orderStatusId">
) => {
  return crmDriverV1.post(
    `/orders/external/order-status/${orderStatusId}/taglines`,
    payload
  );
};

export const createReturnOrderStatusTagline = (
  returnOrderStatusId: number,
  payload: Omit<createReturnOrderStatusTaglineDtoType, "returnOrderStatusId">
) =>
  crmDriverV1.post(
    `/orders/external/return-order-status/${returnOrderStatusId}/return-order-status-taglines`,
    payload
  );

export const getOrderReturnStatus = (payload?: {
  pageNum?: number;
  pageSize?: number;
}) =>
  crmDriverV1.get("/orders/external/return-order-status", {
    params: { ...payload },
  });

export const getListOrderStatusTagline = (payload: {
  orderStatusId: number;
  pageNum: number;
  pageSize: number;
}) => {
  const { orderStatusId, ...innerPayload } = payload;
  return crmDriverV1.get(
    `/orders/external/order-status/${payload.orderStatusId}/taglines`,
    {
      params: innerPayload,
    }
  );
};

// TODO: Update late by getting relevant data by Id instead of by Code
export const getOrderStatusByCode = (payload: { orderStatusCode: string }) =>
  crmDriverV1.get(`/orders/external/order-status/${payload.orderStatusCode}`);

export const getOrderStatusTagline = (payload: { orderStatusId: number }) =>
  crmDriverV1.get(`/orders/external/order-status/${payload.orderStatusId}`);

export const getOrderSourceDetail = (payload: { orderSourceId: number }) =>
  crmDriverV1.get(`/orders/external/order-sources/${payload.orderSourceId}`);

export const getOrderReturnStatusTagline = (returnStatusId: number) =>
  crmDriverV1.get(
    `/orders/external/return-order-status/${returnStatusId}/return-order-status-taglines`
  );

export const getReturnOrderStatusDetail = (payload: {
  returnOrderStatusId: number;
}) =>
  crmDriverV1.get(
    `/orders/external/return-order-status/${payload.returnOrderStatusId}`
  );

export const getListOrderExchangeTagline = (payload: {
  returnOrderStatusId: number;
  pageNum: number;
  pageSize: number;
}) => {
  const { returnOrderStatusId, ...innerPayload } = payload;
  return crmDriverV1.get(
    `/orders/external/return-order-status/${payload.returnOrderStatusId}/return-order-status-taglines`,
    {
      params: innerPayload,
    }
  );
};

export const getOrderExchangeTaglineDetail = (payload: {
  orderStatusId: number;
  orderStatusTaglineId: number;
}) => {
  return crmDriverV1.get(
    `/orders/external/return-order-status/${payload.orderStatusId}/return-order-status-taglines/${payload.orderStatusTaglineId}`
  );
};

export const updateOrderExchangeTaglineDetail = (
  orderStatusId: number,
  orderStatusTaglineId: number,
  payload: UpdateReturnOrderStatusTaglineDtoType
) => {
  return crmDriverV1.put(
    `/orders/external/return-order-status/${orderStatusId}/return-order-status-taglines/${orderStatusTaglineId}`,
    payload
  );
};

export const createDeliveryPartner = (payload: CreateDeliveryPartnerDtoType) =>
  crmDriverV1.post(`/orders/external/delivery-partners`, payload);

export const getListDeliveryPartner = (payload?: {
  pageNum?: number;
  pageSize?: number;
}) =>
  crmDriverV1.get(`/orders/external/delivery-partners`, {
    params: {
      ...payload,
    },
  });

export const getDeliveryPartnerDetail = (payload: {
  deliveryPartnerId: number;
}) =>
  crmDriverV1.get(
    `/orders/external/delivery-partners/${payload.deliveryPartnerId}`
  );

export const updateDeliveryPartnerDetail = (
  deliveryPartnerId: number,
  payload: UpdateDeliveryPartnerDetailDtoType
) =>
  crmDriverV1.put(
    `/orders/external/delivery-partners/${deliveryPartnerId}`,
    payload
  );

export const updateOrderStatus = (
  orderStatusId: number,
  payload: UpdateOrderStatusDtoType
) => crmDriverV1.put(`/orders/external/order-status/${orderStatusId}`, payload);

export const updateOrderDetail = (
  orderId: number,
  payload: UpdateOrderDtoType
) => crmDriverV1.put(`/orders/external/${orderId}`, payload);

export const updateOrderReturnStatus = (
  returnOrderStatusId: number,
  payload: UpdateReturnOrderStatusDtoType
) =>
  crmDriverV1.put(
    `/orders/external/return-order-status/${returnOrderStatusId}`,
    payload
  );

// export const createOrder = (payload: CreateOrderDtoType) =>
//   crmDriverV1.post("/orders/external", payload);
export const createOrder = (payload: CreateOrderDtoType) =>
  crmDriverV1.post("/orders/external/createV2", payload);

export const createOrderStatus = (payload: CreateOrderStatusDtoType) =>
  crmDriverV1.post("/orders/external/order-status", payload);

export const createReturnOrderStatus = (
  payload: CreateOrderReturnStatusDtoType
) => crmDriverV1.post("/orders/external/return-order-status", payload);

export const getListOrderSource = (payload?: {
  pageNum?: number;
  pageSize?: number;
}) =>
  crmDriverV1.get("/orders/external/order-sources", {
    params: { ...payload },
  });

export const createOrderSource = (payload: CreateOrderSourceDtoType) =>
  crmDriverV1.post("/orders/external/order-sources", { ...payload });

export const updateOrderSource = (
  orderSourceId: number,
  payload: UpdateOrderSourceDetailDtoType
) =>
  crmDriverV1.put(`/orders/external/order-sources/${orderSourceId}`, payload);

export const getOrderStatusTaglineDetail = (payload: {
  orderStatusId: number;
  taglineId: number;
}) =>
  crmDriverV1.get(
    `/orders/external/order-status/${payload.orderStatusId}/taglines/${payload.taglineId}`
  );

export const getListOrderPayment = (payload: {
  pageNum: number;
  pageSize: number;
  orderCode?: string;
  filterStatus?: number;
  filterMethod?: number;
  filterCreatedAtFrom?: string;
  filterCreatedAtTo?: string;
}) =>
  crmDriverV1.get("orders/external/payments", {
    params: {
      ...payload,
    },
  });

export const updateOrderStatusTagline = (
  orderStatusId: number,
  taglineId: number,
  payload: UpdateOrderStatusTaglineDtoType
) =>
  crmDriverV1.put(
    `/orders/external/order-status/${orderStatusId}/taglines/${taglineId}`,
    payload
  );
export const getOrderPaymentById = (payload: { paymentId: number }) =>
  crmDriverV1.get(`/orders/external/payments/${payload.paymentId}`);

export const deleteOrderSource = (orderSourceId: number) =>
  crmDriverV1.delete(`/orders/external/order-sources/${orderSourceId}`);

export const deleteDeliveryPartner = (deliveryPartnerId: number) =>
  crmDriverV1.delete(`/orders/external/delivery-partners/${deliveryPartnerId}`);
export const deleteReturnStatus = (returnStatusId: number) =>
  crmDriverV1.delete(`/orders/external/return-order-status/${returnStatusId}`);

export const deleteOrderStatus = (orderStatusId: number) =>
  crmDriverV1.delete(`/orders/external/order-status/${orderStatusId}`);
