import crmDriverV1 from "services/crm/crm-driver-v1";
import {
  GetListDeliveryPartnerDto,
  GetListDeliveryPartnerDtoResponseDto,
  GetListDeliveryPartnerWithoutPaginationResponseDto,
} from "../dtos/list-delivery-partner.dto";

const deliveryPartnerService = {
  getListDeliveryPartner: (
    dto: GetListDeliveryPartnerDto
  ): Promise<{
    data: GetListDeliveryPartnerDtoResponseDto;
  }> => {
    const url = `/orders/external/delivery-partners`;
    return crmDriverV1.get(url, {
      params: {
        ...dto,
      },
    });
  },

  getListDeliveryPartnerWithoutPagination: (): Promise<{
    data: GetListDeliveryPartnerWithoutPaginationResponseDto;
  }> => {
    const url = `/orders/external/delivery-partners`;
    return crmDriverV1.get(url);
  },
};
export default deliveryPartnerService;
