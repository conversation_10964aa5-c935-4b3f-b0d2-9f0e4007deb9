import { useCallback, useMemo } from "react";

import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { OrderSourceModel, orderSourceOption } from "modules/order";
import { getListOrderSource } from "services/crm/order";

export interface PulldownOrderSourceFunctionalOptions {
  excludePending: boolean;
}

export const usePulldownOrderSource = ({
  excludePending = false,
}: PulldownOrderSourceFunctionalOptions) => {
  const [fetchOrderSources, fetchOrderSourcesState] = useAsync(
    useCallback(
      () =>
        getListOrderSource().then((res) =>
          OrderSourceModel.createMap(res.data.data)
        ),
      []
    ),
    {
      excludePending,
    }
  );

  const orderSources = useMemo(
    () => fetchOrderSourcesState.data || [],
    [fetchOrderSourcesState.data]
  );

  const { options: orderSourceOptions, formatOption: formatOrderSourceOption } =
    usePulldownHelper({
      dataSource: orderSources,
      optionCreator: orderSourceOption,
      valueTrans: Number,
    });

  return {
    fetchOrderSources,
    orderSourceOptions,
    formatOrderSourceOption,
  };
};
