import { <PERSON>, <PERSON>a } from "@storybook/react/types-6-0";

import { Icon } from "components/atoms/icon";

import { SideBar, Props } from ".";

export default {
  title: "Components|organisms/SideBar",
  component: SideBar,
} as Meta;

const Template: Story<Props> = ({ onToggleSideBar, menu, isCloseSideBar }) => (
  <SideBar
    onToggleSideBar={onToggleSideBar}
    isCloseSideBar={isCloseSideBar}
    menu={menu}
  />
);

export const Normal = Template.bind({});

Normal.args = {
  onToggleSideBar: () => {},
  isCloseSideBar: true,
  menu: [
    {
      id: 1,
      title: "Control Panel",
      href: "/control-panel",
      icon: <Icon iconName="remote-monitoring" />,
      subMenus: [
        {
          id: 12,
          title: "Control Panel 12",
          href: "/control-panel/1",
        },
        {
          id: 13,
          title: "Control Panel 13",
          href: "/control-panel/2",
        },
        {
          id: 14,
          title: "Control Panel sub",
          href: "/control-panel/3",
        },
      ],
    },
    {
      id: 2,
      title: "Menu",
      href: "/href",
      icon: <Icon iconName="menu" />,
    },
    {
      id: 3,
      title: "Pages",
      href: "/href",
      icon: <Icon iconName="news" />,
    },
    {
      id: 4,
      title: "Bảng điều khiển",
      href: "/href",
      icon: <Icon iconName="setting" />,
      subMenus: [
        {
          id: 41,
          title: "Bảng điều khiển 41",
          href: "/href",
        },
        {
          id: 42,
          title: "Bảng điều khiển 42",
          href: "/href",
        },
      ],
    },
    {
      id: 5,
      title: "Collection",
      href: "/href",
      icon: <Icon iconName="person-group" />,
      subMenus: [
        {
          id: 51,
          title: "Collection sub 51",
          href: "/href",
        },
        {
          id: 52,
          title: "Collection sub 52",
          href: "/href",
        },
      ],
    },
    {
      id: 6,
      title: "News",
      href: "/href",
      icon: <Icon iconName="news" />,
      subMenus: [
        {
          id: 61,
          title: "Danh sách tin tức 61",
          href: "/href",
        },
        {
          id: 62,
          title: "Danh sách tin tức 62",
          href: "/href",
        },
      ],
    },
    {
      id: 7,
      title: "Hệ thống",
      href: "/href",
      icon: <Icon iconName="browser" />,
      subMenus: [
        {
          id: 71,
          title: "Hệ thống sub 71",
          href: "/href",
        },
        {
          id: 72,
          title: "Hệ thống sub 72",
          href: "/href",
        },
      ],
    },
  ],
};
