/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  ReactNode,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";

import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { Form, Input, Modal } from "antd";
import produce from "immer";

import _ from "lodash";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { Divider } from "components/atoms/divider";
import { Heading } from "components/atoms/heading";
import { Icon } from "components/atoms/icon";
import { Numberfield } from "components/atoms/numberfield";
import { Spinner } from "components/atoms/spinner";
import { Col, Row } from "components/organisms/grid";

import { InfiniteScrollable } from "components/utils/infinitescrollable";
import { COLOR } from "constants/color";
import { formatCurrencyWithSuffix } from "helpers/currency.helper";
import { cleanEmptyString } from "helpers/object";
import { useInfinityParams } from "hooks/useInfinityParams";
import { mapFrom } from "libs/adapters/dto";
import { StoragePickerModal } from "modules/order";
import {
  productDetailDto,
  ProductDetailDtoType,
  SkuDetailDtoType,
} from "modules/product";
import {
  itemStockLocationDto,
  ItemStockLocationDtoType,
} from "modules/product/dtos/item-stock-location";
import { StorageDto } from "pages/System/Inventory/dto/storage.dto";
import { UseGetStorage } from "pages/System/Inventory/hook/useGetStorage";
import { UseGetListShopV2 } from "pages/System/ShopDetail/hooks/useGetListShop";
import { ItemStockLocationDto } from "services/crm/location/dtos/item-location-stock.dto";
import { useGetItemStockLocationBySkuId } from "services/crm/location/hooks/item-location-stock.dto";
import { getListProduct, getProductByStoreCode } from "services/crm/product";

export type ProductSkuItem = SkuDetailDtoType;

export interface SubmitItem {
  unitPrice: number;
  totalAmount: number;
  quantity: number;
  sku: ProductSkuItem;
  productId: number;
  price?: number; // Optional, used for listedPrice in some contexts
}

export interface ProductSkuPickerModalProps {
  open: boolean;
  onClose?: () => void;
  onSubmit?: (item: SubmitItem[]) => void;
}

type ModalType = "storagePicker";

interface State {
  items: {
    sku: ProductSkuItem;
    quantity: number;
    productId: number;
    storage: StorageDto;
  }[];
  modalState: {
    type?: ModalType;
    open: boolean;
  };
}

interface ListProductItemProps {
  // product: ProductDetailDtoType;
  product: ItemStockLocationDtoType;
  onRenderSkuItem: (sku: SkuDetailDtoType) => ReactNode;
}

interface ListProductSkuItemProps {
  sku: SkuDetailDtoType;
  onHandleQty?: number;
  onSelectedSku: (sku: SkuDetailDtoType) => void;
}

interface ListSelectedSkuItemProps {
  sku: ProductSkuItem;
  quantity: number;
  index: number;
  storage: StorageDto;
  onQuantityChange: (index: number, quantity: number) => void;
  onRemove: (index: number) => void;
}

const ListProductSkuItem: React.FC<ListProductSkuItemProps> = ({
  onSelectedSku,
  onHandleQty,
  sku,
}) => {
  const handleOnClickSelectButton = useCallback(() => {
    onSelectedSku(sku);
  }, [onSelectedSku]);

  return (
    <div key={sku.skuId} className="u-mt-12">
      <Row className="u-pb-12">
        <Col className="u-text-center">{sku.code}</Col>
        <Col className="u-text-center">{sku.name}</Col>
        <Col className="u-text-center">
          {formatCurrencyWithSuffix(sku.price)}
        </Col>
        <Col className="u-text-center">{onHandleQty || 0}</Col>
        <Col className="u-text-center">
          {formatCurrencyWithSuffix(sku.salePrice)}
        </Col>
        <Col className="u-text-center">
          <BaseButton
            disabled={!onHandleQty || onHandleQty <= 0}
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[600]}
            className="w-fit"
            onClick={handleOnClickSelectButton}
          >
            Chọn
          </BaseButton>
        </Col>
      </Row>
      <Divider type="dash" />
    </div>
  );
};

const ListProductItem: React.FC<ListProductItemProps> = ({
  product,
  onRenderSkuItem,
}) => {
  return (
    //   <div className="u-pl-16 u-pr-16">{product.sku?.map(onRenderSkuItem)}</div>
    <div>{onRenderSkuItem(product.sku)}</div>
  );
};

// TODO: Update when applying storage selection
const ListSelectedSkuItem: React.FC<ListSelectedSkuItemProps> = ({
  index,
  quantity,
  sku,
  storage,
  onQuantityChange,
  onRemove,
}) => {
  const handleOnQuantityInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      onQuantityChange(index, Number(event.target.value));
    },
    [onQuantityChange, index]
  );

  const handleOnClickRemoveLabel = useCallback(() => {
    onRemove(index);
  }, [onRemove, index]);

  return (
    <div key={sku.skuId} className="u-mt-16">
      <Row className="u-mb-12">
        <Col className="u-text-center">{sku.code}</Col>
        <Col className="u-text-center">
          {formatCurrencyWithSuffix(sku.salePrice || sku.price)}
        </Col>
        <Col className="u-text-center">
          {storage?.name || "Kho không xác định"}
        </Col>
        {/* <Col className="u-text-center">{storage.stockQty || 0}</Col> */}
        <Col className="u-text-center">
          <Numberfield
            style={{
              width: 100,
              height: 30,
              padding: 0,
              textAlign: "center",
            }}
            onChange={handleOnQuantityInputChange}
            value={quantity}
          />
        </Col>
        <Col className="u-text-center">
          <Icon
            iconName="circle-error-red"
            onClick={handleOnClickRemoveLabel}
          />
        </Col>
      </Row>
      <Divider type="dash" />
    </div>
  );
};

export const ProductSkuPickerModal: React.FC<ProductSkuPickerModalProps> = ({
  onClose,
  open,
  onSubmit: onSubmitProps,
}) => {
  const [state, setState] = useState<State>({
    items: [],
    modalState: {
      open: false,
      type: undefined,
    },
  });
  const [form] = Form.useForm();
  const currentItem = useRef<ProductSkuItem>();
  const { fetchStorageExe, stateStorages } = UseGetStorage();
  const { listShopExe, listShopState } = UseGetListShopV2();

  // const {
  //   loadMoreWithParams,
  //   loadMore,
  //   state: productInfinteState,
  // } = useInfinityParams<ProductDetailDtoType[]>(
  //   (params: Parameters<typeof getListProduct>[0]) =>
  //     getListProduct(params).then((res) => ({
  //       ...res,
  //       data: {
  //         ...res.data,
  //         data: mapFrom(res.data.data, productDetailDto),
  //       },
  //     })),
  //   {
  //     pageSize: 30,
  //   }
  // );

  const {
    loadMoreWithParams,
    loadMore,
    state: productInfinteState,
  } = useInfinityParams<ItemStockLocationDtoType[]>(
    // } = useInfinityParams<ProductDetailDtoType[]>(
    // getListProduct(params).then((res) => ({
    (params: Parameters<typeof getProductByStoreCode>[0]) =>
      getProductByStoreCode(params).then((res) => ({
        ...res,
        data: {
          ...res?.data,
          data: mapFrom(res?.data?.data, itemStockLocationDto),
          // data: mapFrom(res.data.data, productDetailDto),
        },
      })),
    {
      pageSize: 30,
    }
  );

  // const onSubmitSearchFilterForm = (
  //   event: React.FormEvent<HTMLFormElement>
  // ) => {
  //   event.preventDefault();
  //   const formData = new FormData(event.currentTarget);
  //   loadMoreWithParams(
  //     cleanEmptyString({
  //       filterProductName: formData.get("filterProductName"),
  //     })
  //   );
  // };
  // const onSubmitSearchFilterForm = (values: any) => {
  //   loadMoreWithParams(
  //     cleanEmptyString({
  //       filterProductName: values.filterProductName,
  //     })
  //   );
  // };

  const onSubmitSearchFilterForm = (values: any) => {
    loadMoreWithParams(
      cleanEmptyString({
        searchText: values.filterProductName,
        storeCode: values.storeCode,
      })
    );
  };

  // useEffect(() => {
  //   if (shopSentValue) {
  //     loadMoreWithParams({
  //       storeCode: shopSentValue,
  //       pageNum: 1,
  //       pageSize: 30,
  //     });
  //   }
  // }, [shopSentValue]);

  const handleOnSelectedSkuQuantityChange = useCallback(
    (skuItemIndex: number, quantity: number) => {
      if (Number.isNaN(quantity) || quantity <= 0) return;
      if (skuItemIndex >= 0) {
        setState(
          produce((draft) => {
            draft.items[skuItemIndex].quantity = quantity;
          })
        );
      }
    },
    []
  );

  const handleOnCancel = useCallback(() => {
    if (onClose) {
      onClose();
    }
    setState(
      produce((draft) => {
        draft.items = [];
      })
    );
  }, [onClose]);

  const handleOnRemoveSkuSelectedItem = useCallback((skuItemIndex: number) => {
    if (skuItemIndex >= 0) {
      setState(
        produce((draft) => {
          draft.items.splice(skuItemIndex, 1);
        })
      );
    }
  }, []);

  // const handleOnSelectedSkuItem = useCallback(
  //   (storage: ItemStockLocationDto) => {
  //     if (currentItem.current) {
  //       setState(
  //         produce((draft) => {
  //           draft.items.push({
  //             storage,
  //             quantity: 1,
  //             // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //             sku: currentItem.current as any,
  //             productId: currentItem.current.productId,
  //           });
  //         })
  //       );
  //     }
  //   },
  //   []
  // );

  const handleOnSelectedSkuItem = useCallback((storage: StorageDto) => {
    if (currentItem.current) {
      setState(
        produce((draft) => {
          draft.items.push({
            storage,
            quantity: 1,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            sku: currentItem.current as any,
            productId: currentItem.current.productId,
          });
        })
      );
    }
  }, []);

  const handleOnSubmit = useCallback(() => {
    if (onSubmitProps) {
      const submitItem = state.items.map(({ quantity, sku, productId }) => ({
        sku,
        unitPrice: sku.salePrice || sku.price,
        totalAmount: (sku.salePrice || sku.price) * quantity,
        price: sku.price, // Optional, used for listedPrice in some contexts
        quantity,
        // itemType: "SOLD",
        productId,
      }));

      onSubmitProps(submitItem);
      handleOnCancel();
    }
  }, [onSubmitProps, state.items, handleOnCancel]);

  // NOTES Ở ĐÂY //
  const handleOpenModalByType = useCallback(
    (type: ModalType) =>
      setState(
        produce((draft) => {
          draft.modalState.open = true;
          draft.modalState.type = type;
        })
      ),
    []
  );

  const handleOnCloseModal = useCallback(() => {
    console.log(currentItem.current);
    currentItem.current = undefined;
    return setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.type = undefined;
      })
    );
  }, []);

  useEffect(() => {
    if (open) {
      fetchStorageExe();
      listShopExe();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  useEffect(() => {
    if (_.size(listShopState?.data) > 0) {
      const defaultShop = listShopState.data.find((shop) => shop.isDefaultShop);
      if (defaultShop) {
        form.setFieldsValue({
          storeCode: defaultShop.storage.code,
        });
      }
      loadMoreWithParams({
        storeCode: defaultShop?.storage.code,
      });
    }
  }, [listShopState]);

  const { items } = state;

  return (
    <>
      <Modal
        open={open}
        onCancel={onClose}
        centered
        maskClosable={false}
        destroyOnHidden
        footer={null}
        width={1060}
        styles={{
          content: {
            maxWidth: 1060,
            maxHeight: "75%",
          },
        }}
      >
        <div>
          <Heading type="h2" centered>
            CHỌN SẢN PHẨM
          </Heading>
          <Form
            form={form}
            onFinish={onSubmitSearchFilterForm}
            layout="vertical"
            className="u-mt-20"
          >
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 items-end">
              <Form.Item name="filterProductName" label="Tên sản phẩm">
                <Input placeholder="Nhập tên sản phẩm" />
              </Form.Item>

              <Form.Item
                name="storeCode"
                label="Kho"
                rules={[
                  {
                    required: true,
                    message: "Vui lòng chọn kho!",
                  },
                ]}
              >
                <BaseSelect
                  placeholder="Chọn kho"
                  fieldNames={{
                    label: "name",
                    value: "code",
                  }}
                  options={stateStorages?.data}
                />
              </Form.Item>
              <Form.Item>
                <BaseButton
                  type="primary"
                  bgColor={COLOR.BLUE[500]}
                  hoverColor={COLOR.BLUE[600]}
                  onClick={() => {
                    form.submit();
                  }}
                  icon={<SearchOutlined rev={undefined} />}
                >
                  Tìm kiếm
                </BaseButton>
              </Form.Item>
            </div>
          </Form>
          <Row className="u-mt-20">
            <Col>
              <Heading type="h3" centered>
                Mã SKU
              </Heading>
            </Col>
            <Col>
              <Heading type="h3" centered>
                Tên
              </Heading>
            </Col>
            <Col>
              <Heading type="h3" centered>
                Giá NY
              </Heading>
            </Col>
            <Col>
              <Heading type="h3" centered>
                Tồn
              </Heading>
            </Col>
            <Col>
              <Heading type="h3" centered>
                Giá bán
              </Heading>
            </Col>
            <Col />
          </Row>
          <InfiniteScrollable
            className="u-overflow-x-hidden u-overflow-y-auto u-relative"
            height={300}
            width="100%"
            offset={{ bottom: 100 }}
            onTrigger={loadMore}
          >
            {productInfinteState &&
              Array.isArray(productInfinteState.data) &&
              productInfinteState.data?.map((product) => (
                <ListProductItem
                  key={product.productId}
                  product={product}
                  onRenderSkuItem={(sku) => (
                    <ListProductSkuItem
                      key={sku.skuId}
                      sku={sku}
                      onHandleQty={product.onhandQty}
                      onSelectedSku={(item) => {
                        console.log(item, "item");
                        currentItem.current = {
                          ...item,
                          productId: product.productId,
                        };
                        const findStorage = stateStorages?.data.find(
                          (itemStorage) =>
                            itemStorage.code === product.storeCode
                        );
                        handleOnSelectedSkuItem(findStorage);
                        // getItemStockLocationRefetch({
                        //   itemId: product.productId,
                        //   skuId: Number(item.skuId),
                        // });
                        // handleOpenModalByType("storagePicker");
                      }}
                    />
                  )}
                />
              ))}
            {productInfinteState.loading && <Spinner fullContainer />}
          </InfiniteScrollable>
          <section className="u-mt-20">
            <Heading type="h2">SẢN PHẨM ĐÃ CHỌN</Heading>
            <Row className="u-mt-20">
              <Col>
                <Heading type="h3" centered>
                  Mã SKU
                </Heading>
              </Col>
              <Col>
                <Heading type="h3" centered>
                  Giá
                </Heading>
              </Col>
              <Col>
                <Heading type="h3" centered>
                  Kho
                </Heading>
              </Col>
              {/* <Col>
                <Heading type="h3" centered>
                  Tồn kho
                </Heading>
              </Col> */}
              <Col>
                <Heading type="h3" centered>
                  Số lượng
                </Heading>
              </Col>
              <Col>
                <Heading type="h3" centered>
                  Xoá
                </Heading>
              </Col>
            </Row>
            <div
              style={{ height: 250 }}
              className="u-overflow-x-hidden u-overflow-y-auto u-relative"
            >
              {items.map(({ sku, quantity, storage }, index) => {
                return (
                  <ListSelectedSkuItem
                    key={sku.skuId}
                    storage={storage}
                    sku={sku}
                    index={index}
                    quantity={quantity}
                    onQuantityChange={handleOnSelectedSkuQuantityChange}
                    onRemove={handleOnRemoveSkuSelectedItem}
                  />
                );
              })}
            </div>
          </section>
        </div>
        <div className="pt-4 gap-4 flex justify-center shadow-[-4px_-4px_10px_rgba(0,0,0,0.1)]">
          <BaseButton
            type="primary"
            className="w-fit"
            bgColor={COLOR.RED[500]}
            hoverColor={COLOR.RED[700]}
            onClick={handleOnCancel}
          >
            Huỷ
          </BaseButton>
          <BaseButton
            type="primary"
            className="w-fit"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            icon={<PlusOutlined rev={undefined} />}
            onClick={handleOnSubmit}
          >
            Lưu
          </BaseButton>
        </div>
      </Modal>
      {/* <StoragePickerModal
        open={
          state.modalState.open && state.modalState.type === "storagePicker"
        }
        onClose={handleOnCloseModal}
        onSelected={handleOnSelectedSkuItem}
        itemStockLocationData={getItemStockLocationData?.data}
        loading={loading}
      /> */}
    </>
  );
};
