import React from "react";

import { ToastContainer, toast as toastInstance, Slide } from "react-toastify";

import { Icon } from "components/atoms/icon";
import { mapModifiers } from "helpers/component";

/* eslint-disable */
const NotificationIcons = {
  success: "circle-checked-green",
  warning: "triangle-warning-yellow",
  error: "circle-error-red",
} as const;

type Type = "success" | "warning" | "error";
type Modifier = "center";

export interface Props {
  modifiers?: Modifier | Modifier[];
  autoClose?: number | false;
}

export interface ToastifyMessageProps {
  message?: string;
  descripition?: React.ReactNode;
  type: Type;
  modifiers?: Modifier | Modifier[];
}

let currentToastId: React.ReactText | null;

export const toastSingleMode = (props: ToastifyMessageProps) => {
  if (currentToastId) {
    toastInstance.dismiss(currentToastId);
  }
  currentToastId = toastInstance(
    <ToastifyMessage
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
    />
  );

  return currentToastId;
};

export const toast = toastInstance;

export const ToastifyMessage: React.FC<ToastifyMessageProps> = ({
  type,
  message,
  descripition,
}) => (
  <div className="a-toastify_message">
    <Icon iconName={NotificationIcons[type]} />
    <div className="a-toastify_content">
      <div className="a-toastify_header">{message}</div>
      <div className="a-toastify_description">{descripition}</div>
    </div>
  </div>
);

export const Toastify: React.FC<React.PropsWithChildren<Props>> = ({
  children,
  modifiers,
  autoClose = 5000,
}) => (
  <div className={mapModifiers("a-toastify", modifiers)}>
    {children}
    <ToastContainer
      transition={Slide}
      position={modifiers === "center" ? "top-center" : "top-right"}
      autoClose={autoClose}
      pauseOnHover
      draggable
    />
  </div>
);
