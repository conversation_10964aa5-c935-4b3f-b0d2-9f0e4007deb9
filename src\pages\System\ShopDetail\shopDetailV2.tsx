/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from "react";
import {
  CaretLeftFilled,
  DeleteFilled,
  LeftOutlined,
  PlusOutlined,
  SaveFilled,
  SaveOutlined,
} from "@ant-design/icons";
import {
  Card,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  Row,
  Table,
  Tooltip,
} from "antd";
import { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { useLocation, useParams } from "react-router";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import BaseSelectCity from "components/atoms/base/select/shared/BaseSelectCity.share";
import BaseSelectDistrict from "components/atoms/base/select/shared/BaseSelectDistrict.share";
import { showLoading } from "components/atoms/base/Spinner";
import { Heading } from "components/atoms/heading";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";
import { EmployeeSelectedModal } from "modules/employee/shared/presentational/selectListEmployeeModal";
import { BankAccountSelectedModal } from "modules/location/shared/presentational/selectListBankAccountModal";

import { ChildrenPage } from "../types";
import { optionsShopType, ShopItemFormType } from "./constant";
import { UseCreateShop } from "./hooks/useCreateShop";
import { UseGetListShop } from "./hooks/useGetListShop";
import { UseUpdateShopDetail } from "./hooks/useUpdateDetail";
import { ShopDetailPageVM } from "./vm";

interface Employee {
  employeeId: number;
  name: string;
}

interface BankAccount {
  bankAccountId: number;
  accountNumber: string;
  owner: string;
  bankName: string;
}

type FormValueType = Partial<Omit<ShopItemFormType, "activeStatus">> & {
  // storageIds?: string[];
  startActiveDate?: Date;
  endActiveDate?: Date;
  mail?: string;
  posID: number;
  activeStatus: Number;
  printEx: number;
};

export const StatusOption = [
  {
    label: "Chưa hoạt động",
    value: 1,
  },
  {
    label: "Đang hoạt động",
    value: 2,
  },
  {
    label: "Ngừng hoạt động",
    value: 3,
  },
];

function ShopDetailV2() {
  const [form] = Form.useForm<FormValueType>();
  // const [searchParams] = useSearchParams();
  const [convertCityOption, setConvertCityOption] = useState<any>([]);
  const [convertDistrictOption, setConvertDistrictOption] = useState<any>([]);
  const [convertWardOption, setConvertWardOption] = useState<any>([]);
  const { shopId } = useParams<PageParamsType<ChildrenPage["shopDetail"]>>();
  const isAddNew = shopId === "isAddnew";
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const isView = searchParams.get("action") === "view";
  const { createShopExe } = UseCreateShop();
  const { updateShopDetailExe } = UseUpdateShopDetail();
  const { listShopState, listShopExe } = UseGetListShop();
  const cityIdValue = Form.useWatch("cityId", form);
  const {
    loading,
    shopDetailData,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    storageOptions,
    removeEmployees,
    removeBankAccounts,
    employeeSelected,
    bankAccountSelected,
    updateShopDetailState,
    handleUpdateShopDetail,
    onSaveModalEmployees,
    onSaveModalBankAccounts,
    handleUpdate,
  } = ShopDetailPageVM({ shopId: Number(shopId) });

  // const {
  //   cityOptions,
  //   districtOptions,
  //   wardOptions,
  //   setFilter: setLocationFilter,
  //   setFilters: setLocationFilters,
  //   batchUpdate: batchUpdateLocation,
  // } = useLocationSelect();

  // const pageActionType = searchParams.get("action") || "view";
  // const editMode = pageActionType === "edit";
  // const viewMode = !editMode;

  // Update location filters when shop data loads

  // useEffect(() => {
  //   const convetCity = cityOptions.map((item) => ({
  //     label: item.label,
  //     value: Number(item.value),
  //   }));
  //   const convertDistric = districtOptions.map((item) => ({
  //     label: item.label,
  //     value: Number(item.value),
  //   }));
  //   const convertWard = wardOptions.map((item) => ({
  //     label: item.label,
  //     value: Number(item.value),
  //   }));

  //   setConvertDistrictOption(convertDistric);
  //   setConvertWardOption(convertWard);
  //   setConvertCityOption(convetCity);
  // }, [cityOptions.length, districtOptions.length, wardOptions.length]);

  // useEffect(() => {
  //   if (shopDetailData) {
  //     batchUpdateLocation(() => {
  //       setLocationFilters({
  //         city_id: Number(shopDetailData?.cityId),
  //         district_id: Number(shopDetailData?.districtId),
  //         ward_id: Number(shopDetailData?.wardId),
  //       });
  //     });
  //   }
  // }, [batchUpdateLocation, setLocationFilters, shopDetailData]);

  const disableStartDate = (current: any) => {
    const endDate = form.getFieldValue("endActiveDate");
    return endDate && current && current > endDate;
  };

  const disableEndDate = (current: any) => {
    const startDate = form.getFieldValue("startActiveDate");
    return startDate && current && current < startDate;
  };

  const handleStartDateChange = (date: any) => {
    const endDate = form.getFieldValue("endActiveDate");

    if (date && endDate && date > endDate) {
      form.setFieldsValue({ endActiveDate: null });
    }
  };

  const handleEndDateChange = (date: any) => {
    const startDate = form.getFieldValue("startActiveDate");
    if (date && startDate && date < startDate) {
      form.setFieldsValue({ startActiveDate: null });
    }
  };

  const onFinish = async (values: FormValueType) => {
    await form.validateFields();

    const payload = {
      ...values,
      cityId: values.cityId,
      districtId: values.districtId,
      // wardId: values.wardId,
      type: values.type,
      startActiveDate: dayjs(values.startActiveDate).format("YYYY-MM-DD"),
      endActiveDate: dayjs(values.endActiveDate).format("YYYY-MM-DD"),
      activeStatus: values.activeStatus,
      storageId: Number(values.storageId),
      bankAccountIds: bankAccountSelected.map((item) => item.bankAccountId),
      employeeIds: employeeSelected.map((item) => item.employeeId),
      isFranchisedShop: values.isFranchisedShop || false,
      isDefaultShop: values.isDefaultShop || false,
    };
    showLoading(true);
    if (isAddNew) {
      createShopExe(payload)
        .then((res) => {
          if (res.status === 200) {
            showLoading(false);
            showNotification({
              type: "success",
              message: "Tạo mới thành công",
            });
          }
        })
        .catch((err) => {
          console.log(err, "eroooo");
          showNotification({
            type: "error",
            message: `${err?.response?.data?.errors?.[0]?.detail}`,
          });
        })
        .finally(() => {
          showLoading(false);
        });
      return;
    }
    updateShopDetailExe(Number(shopId), payload)
      .then((res) => {
        if (res.status === 200) {
          showNotification({
            type: "success",
            message: "Cập nhật thành công",
          });
        }
      })
      .catch((err) => {
        showNotification({
          type: "error",
          message: `${err?.response?.data?.errors?.[0]?.detail}`,
        });
      })
      .finally(() => {
        showLoading(false);
      });

    // handleUpdateShopDetail({
    //   ...values,
    //   cityId: values.cityId,
    //   districtId: values.districtId,
    //   wardId: values.wardId,
    //   type: values.type,
    //   storageIds: values.storageIds?.map((id) => Number(id)) || [],
    //   bankAccountIds: bankAccountSelected.map((item) => item.bankAccountId),
    //   employeeIds: employeeSelected.map((item) => item.employeeId),
    //   isFranchisedShop: values.isFranchisedShop || false,
    // });
  };

  // Employee table columns
  const employeeColumns: ColumnsType<Employee> = [
    {
      title: "Mã nhân viên",
      dataIndex: "employeeId",
      key: "employeeId",
      align: "center",
    },
    {
      title: "Tên nhân viên",
      dataIndex: "name",
      key: "name",
      align: "center",
    },
    ...(isAddNew || !isView
      ? [
          {
            title: "Thao tác",
            key: "action",
            align: "center" as const,
            render: (_: any, record: Employee) => (
              <Tooltip title="Xóa">
                <BaseButton
                  type="primary"
                  bgColor={COLOR.RED[500]}
                  hoverColor={COLOR.RED[700]}
                  icon={<DeleteFilled rev={undefined} />}
                  onClick={() => removeEmployees(record as any)} // eslint-disable-line @typescript-eslint/no-explicit-any
                />
              </Tooltip>
            ),
          },
        ]
      : []),
  ];

  // Bank account table columns
  const bankAccountColumns: ColumnsType<BankAccount> = [
    {
      title: "Số tài khoản",
      dataIndex: "accountNumber",
      key: "accountNumber",
      align: "center",
    },
    {
      title: "Chủ tài khoản",
      dataIndex: "owner",
      key: "owner",
      align: "center",
    },
    {
      title: "Ngân hàng",
      dataIndex: "bankName",
      key: "bankName",
      align: "center",
    },
    ...(isAddNew || !isView
      ? [
          {
            title: "Thao tác",
            key: "action",
            align: "center" as const,
            render: (_: any, record: BankAccount) => (
              <Tooltip title="Xóa">
                <BaseButton
                  type="primary"
                  bgColor={COLOR.RED[500]}
                  hoverColor={COLOR.RED[700]}
                  icon={<DeleteFilled rev={undefined} />}
                  onClick={() => removeBankAccounts(record as any)} // eslint-disable-line @typescript-eslint/no-explicit-any
                />
              </Tooltip>
            ),
          },
        ]
      : []),
  ];

  // Set form values when data is loaded
  useEffect(() => {
    if (shopDetailData) {
      form.setFieldsValue({
        shopCode: shopDetailData.shopCode,
        name: shopDetailData.name,
        mail: shopDetailData.mail,
        posID: shopDetailData.posID,
        startActiveDate: shopDetailData.startActiveDate
          ? dayjs(shopDetailData.startActiveDate)
          : null,
        endActiveDate: shopDetailData.endActiveDate
          ? dayjs(shopDetailData.endActiveDate)
          : null,
        printEx: shopDetailData.printEx,
        activeStatus: shopDetailData.activeStatus,
        hotline: shopDetailData.hotline,
        type: shopDetailData.type,
        address: shopDetailData.address,
        cityId: shopDetailData.cityId,
        districtId: shopDetailData.districtId,
        // wardId: shopDetailData.wardId,
        displayOrder: shopDetailData.displayOrder,
        isFranchisedShop: shopDetailData.isFranchisedShop,
        isDefaultShop: shopDetailData.isDefaultShop,
        storageId: shopDetailData.storageId?.toString(),
      });
    }
  }, [shopDetailData, form]);

  // Show loading when updating
  useEffect(() => {
    if (updateShopDetailState.loading) {
      showLoading(true);
    } else {
      showLoading(false);
    }
  }, [updateShopDetailState.loading]);

  useEffect(() => {
    listShopExe();
  }, []);

  useEffect(() => {
    let stt = Number(listShopState.length);
    stt += 1;
    form.setFieldValue("displayOrder", stt);
  }, [listShopState?.length]);

  if (loading) {
    return (
      <General>
        <div className="flex justify-center items-center h-64">
          <div>Đang tải...</div>
        </div>
      </General>
    );
  }

  return (
    <General>
      <title key="title">CHI TIẾT SHOP</title>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        // disabled={viewMode}
        disabled={!isAddNew && isView}
      >
        <Row justify="start" gutter={[8, 8]} className="p-3">
          <Col>
            <BaseButton
              type="primary"
              className="w-fit"
              onClick={navigationHelper.goBack}
              icon={<LeftOutlined />}
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[600]}
            />
          </Col>
          <Col>
            {!isView && (
              <BaseButton
                htmlType="submit"
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                disabled={updateShopDetailState.loading}
                loading={updateShopDetailState.loading}
                icon={<SaveFilled />}
                // onClick={() => form.submit()}
              >
                Lưu
              </BaseButton>
            )}
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card title="Thông tin shop">
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Form.Item
                    label="Mã shop"
                    name="shopCode"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng nhập mã shop",
                      },
                    ]}
                  >
                    <Input placeholder="Mã shop" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Tên shop"
                    name="name"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng nhập tên shop",
                      },
                    ]}
                  >
                    <Input placeholder="Tên shop" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Hotline"
                    name="hotline"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng nhập hotline",
                      },
                    ]}
                  >
                    <Input
                      type="tel"
                      className="w-full"
                      placeholder="Hotline"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Mail"
                    name="mail"
                    rules={[
                      {
                        type: "email",
                        message: "Vui lòng nhập địa chỉ email hợp lệ",
                      },
                    ]}
                  >
                    <Input type="email" placeholder="Mail" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Loại shop"
                    name="type"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn loại shop",
                      },
                    ]}
                  >
                    <BaseSelect
                      placeholder="Chọn loại shop"
                      options={optionsShopType}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Trạng thái hoạt động"
                    name="activeStatus"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn trạng thái hoạt động",
                      },
                    ]}
                  >
                    <BaseSelect
                      placeholder="Chọn trạng thái"
                      options={StatusOption}
                      fieldNames={{ label: "label", value: "value" }}
                      // disabled={viewMode}
                      // disabled={!isAddNew}
                    />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    label="Ngày hoạt động"
                    name="startActiveDate"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn ngày hoạt động",
                      },
                    ]}
                  >
                    <DatePicker
                      className="w-full"
                      disabledDate={disableStartDate}
                      onChange={handleStartDateChange}
                    />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    label="Ngày đóng"
                    name="endActiveDate"
                    // rules={[
                    //   {
                    //     required: true,
                    //     message: "Vui lòng chọn ngày kết thúc",
                    //   },
                    // ]}
                  >
                    <DatePicker
                      className="w-full"
                      disabledDate={disableEndDate}
                      onChange={handleEndDateChange}
                    />
                  </Form.Item>
                </Col>

                <Col span={24}>
                  <Form.Item
                    label="Kho"
                    name="storageId"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn kho",
                      },
                    ]}
                  >
                    <BaseSelect
                      placeholder="Chọn kho"
                      options={storageOptions}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Khu vực"
                    name="area"
                    // rules={[
                    //   {
                    //     required: true,
                    //     message: "Vui lòng chọn khu vực",
                    //   },
                    // ]}
                  >
                    <BaseSelect placeholder="Chọn khu vực" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Địa chỉ"
                    name="address"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng nhập địa chỉ",
                      },
                    ]}
                  >
                    <Input placeholder="Địa chỉ" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Tỉnh/Thành phố"
                    name="cityId"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn tỉnh/thành phố",
                      },
                    ]}
                  >
                    <BaseSelectCity
                      placeholder="Chọn tỉnh/thành phố"

                      // options={convertCityOption}
                      // onChange={(value: any) => {
                      //   setLocationFilter("city_id", value);

                      //   // Reset district and ward when city changes
                      //   form.setFieldsValue({
                      //     districtId: undefined,
                      //     wardId: undefined,
                      //   });
                      // }}
                      // disabled={!convertCityOption.length}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Phường/Xã"
                    name="districtId"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn phường/xã",
                      },
                    ]}
                  >
                    <BaseSelectDistrict
                      placeholder="Chọn phường/xã"
                      cityId={cityIdValue}

                      // disabled={!convertDistrictOption.length}
                    />
                  </Form.Item>
                </Col>
                {/* <Col span={12}>
                  <Form.Item
                    label="Phường/Xã"
                    name="wardId"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn phường/xã",
                      },
                    ]}
                  >
                    <BaseSelect
                      placeholder="Chọn phường/xã"
                      options={convertWardOption}
                      onChange={(value: any) =>
                        setLocationFilter("ward_id", value)
                      }
                      // disabled={!convertWardOption.length}
                    />
                  </Form.Item>
                </Col> */}
                <Col span={12}>
                  <Form.Item
                    label="Thứ tự hiển thị"
                    name="displayOrder"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng nhập thứ tự hiển thị",
                      },
                    ]}
                  >
                    <Input
                      type="number"
                      placeholder="Thứ tự hiển thị"
                      min={0}
                      disabled
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="printEx" label="Mẫu in">
                    <BaseSelect placeholder="Chọn mẫu in" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="isDefaultShop"
                    label="Shop mặc định"
                    valuePropName="checked"
                  >
                    <Checkbox>Shop mặc định</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="posID" label="POS ID">
                    <Input />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Shop nhượng quyền"
                    name="isFranchisedShop"
                    valuePropName="checked"
                  >
                    <Checkbox>Shop nhượng quyền</Checkbox>
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>
          <Col span={12}>
            <Card>
              <div className="mt-8">
                <div className="flex justify-between items-center mb-4">
                  <Heading>NHÂN VIÊN SHOP</Heading>
                  {!isView && (
                    <BaseButton
                      type="primary"
                      bgColor={COLOR.BLUE[500]}
                      hoverColor={COLOR.BLUE[700]}
                      icon={<PlusOutlined rev={undefined} />}
                      onClick={() => handleOpenModalByType("employeeSelected")}
                    >
                      Thêm nhân viên mới
                    </BaseButton>
                  )}
                </div>
                <Table
                  columns={employeeColumns}
                  dataSource={employeeSelected}
                  rowKey="employeeId"
                  size="small"
                  bordered
                  pagination={false}
                />
              </div>

              <div className="mt-8">
                <div className="flex justify-between items-center mb-4">
                  <Heading>TÀI KHOẢN NGÂN HÀNG</Heading>
                  {!isView && (
                    <BaseButton
                      type="primary"
                      bgColor={COLOR.BLUE[500]}
                      hoverColor={COLOR.BLUE[700]}
                      icon={<PlusOutlined rev={undefined} />}
                      onClick={() =>
                        handleOpenModalByType("bankAccountSelected")
                      }
                    >
                      Thêm tài khoản mới
                    </BaseButton>
                  )}
                </div>
                <Table
                  columns={bankAccountColumns}
                  dataSource={bankAccountSelected}
                  rowKey="bankAccountId"
                  size="small"
                  bordered
                  pagination={false}
                />
              </div>
            </Card>
          </Col>
        </Row>
      </Form>

      {/* Employee Selection Modal */}
      <EmployeeSelectedModal
        open={modalTypeIsOpen("employeeSelected")}
        employeeSelected={employeeSelected}
        onSave={(value) => onSaveModalEmployees(value)}
        onClose={handleCloseModal}
      />

      {/* Bank Account Selection Modal */}
      <BankAccountSelectedModal
        open={modalTypeIsOpen("bankAccountSelected")}
        bankAccountSelected={bankAccountSelected}
        onSave={(value) => onSaveModalBankAccounts(value)}
        onClose={handleCloseModal}
      />
    </General>
  );
}

export default ShopDetailV2;
