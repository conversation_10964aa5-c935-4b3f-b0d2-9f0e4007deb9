import React, { useEffect, useState } from "react";
import { SearchOutlined } from "@ant-design/icons";
import { Input, Typography } from "antd";
import Table, { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import _ from "lodash";
import { PartnerTabKeys } from "../enums/partnerTabs.enum";

const fakeData: PartnerSMSHistoryDataType[] = [
  {
    rowPointer: "1",
    phoneNumber: "0964077761",
    content:
      "Chuc mung thang sinh nhat chi MI. GUMAC xin gui tang chi Voucher BD525_82WG9 tri an them 10% cho 1 don hang (ap dung chung CTKM khac) HSD:31/05. LH:18006013",
    createdDate: new Date("2025-05-05T12:47:00"),
  },
  {
    rowPointer: "2",
    phoneNumber: "0964077761",
    content:
      "Chao chi MI, yeu cau doi SP cua chi GUMAC da xu ly va xac nhan. Ma don hang doi: 0004542241. Cam on chi da lua chon GUMAC. LH: 18006013",
    createdDate: new Date("2024-09-25T09:33:00"),
  },
  {
    rowPointer: "3",
    phoneNumber: "0964077761",
    content:
      "Cam on chi MI da dat hang tai GUMAC. Don hang 0004352893 cua chi da duoc xac nhan. Hotline mien phi: 18006013",
    createdDate: new Date("2024-04-19T09:59:00"),
  },
  {
    rowPointer: "4",
    phoneNumber: "0964077761",
    content:
      "GUMAC kinh chuc chi MI ngay sinh nhat vui ve, dam am ben nguoi than va gia dinh. Tran trong cam on chi MI da lua chon san pham cua GUMAC",
    createdDate: new Date("2021-05-12T09:03:00"),
  },
  {
    rowPointer: "5",
    phoneNumber: "0964077761",
    content:
      "Con 1 tuan nua la sinh nhat chi MI roi! GUMAC gui chi voucher 210505_IVB7Q ap dung -40% khi mua sam tai GUMAC co gia tri den ngay 20/05. LH:18006013",
    createdDate: new Date("2021-05-05T09:01:00"),
  },
];

interface PartnerSMSHistoryProps {
  activeKey: PartnerTabKeys;
}

interface PartnerSMSHistoryDataType {
  rowPointer: string;
  phoneNumber: string;
  content: string;
  createdDate: Date;
}

const { Text } = Typography;

export default function PartnerSMSHistory(props: PartnerSMSHistoryProps) {
  const { activeKey } = props;
  const [searchText, setSearchText] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const debouncedSearch = _.debounce((value: string) => {
    setSearchText(value);
  }, 300);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value);
  };

  const columns: ColumnsType<PartnerSMSHistoryDataType> = [
    {
      title: "Số điện thoại",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
      width: 144,
    },
    {
      title: "Nội dung",
      dataIndex: "content",
      key: "content",
      width: 432,
    },
    {
      title: "Ngày gửi",
      dataIndex: "createdDate",
      key: "createdDate",
      width: 144,
      render: (value) => {
        return <Text>{dayjs(value).format("DD/MM/YYYY HH:mm")}</Text>;
      },
    },
  ];

  useEffect(() => {
    console.log(page, pageSize, searchText);
  }, [page, pageSize, searchText]);

  useEffect(() => {
    // Fetch data based on activeKey
  }, [activeKey]);

  return (
    <div className="py-3 flex flex-col gap-3">
      <Input
        className="w-full md:w-96"
        placeholder="Tìm kiếm"
        allowClear
        onChange={handleInputChange}
        onClear={() => setSearchText("")}
        prefix={<SearchOutlined rev={undefined} />}
      />

      <Table
        rowKey={(record) => `${record.rowPointer}`}
        columns={columns}
        dataSource={fakeData}
        scroll={{
          x: "max-content",
        }}
        pagination={{
          current: page,
          pageSize,
          onChange(offset, limit) {
            setPage(offset);
            setPageSize(limit);
          },
          total: fakeData.length,
          showSizeChanger: true,
          pageSizeOptions: [5, 10, 20, 50],
          onShowSizeChange(current, size) {
            setPage(1);
            setPageSize(size);
          },
          showTotal(total, range) {
            return `Total: ${range[0]}-${range[1]} of ${total} items`;
          },
          responsive: true,
        }}
      />
    </div>
  );
}
