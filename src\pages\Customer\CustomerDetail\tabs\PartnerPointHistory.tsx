import React, { useEffect, useState } from "react";
import {
  CheckOutlined,
  CloseOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { DatePicker, Input, Table, Typography } from "antd";
import { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import _ from "lodash";
import { CustomerPoint } from "pages/Customer/Dto/customer.dto";
import { UseGetPointCustomer } from "pages/Customer/hook/useGetAllCustomer";
import { PartnerTabKeys } from "../enums/partnerTabs.enum";

interface PartnerPointHistoryProps {
  activeKey: PartnerTabKeys;
  customerId: string;
  memberPoint: number;
}

interface PartnerPointHistoryDataType extends CustomerPoint {}
// minus | plus

type FilterValueType = {
  page: number;
  pageSize: number;
  searchText: string;
  fromDate: string | null;
  toDate: string | null;
};

const { Text } = Typography;
const { RangePicker } = DatePicker;

export default function PartnerPointHistory(props: PartnerPointHistoryProps) {
  const { activeKey, customerId, memberPoint } = props;
  const [searchText, setSearchText] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const { getPointCustomerExec, getPointCustomerState } = UseGetPointCustomer({
    customerId: +customerId,
    pageNum: page,
    pageSize,
  });
  const [filterValue, setFilterValue] = useState<FilterValueType>({
    page: 1,
    pageSize: 10,
    searchText: "",
    fromDate: null,
    toDate: null,
  });

  const debouncedSearch = _.debounce((value: string) => {
    setSearchText(value);
  }, 300);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value);
  };

  const columns: ColumnsType<PartnerPointHistoryDataType> = [
    {
      title: "Ngày thành công",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 144,
      render: (value) => {
        return <Text>{dayjs(value).format("DD/MM/YYYY HH:mm")}</Text>;
      },
    },
    {
      title: "Loại",
      dataIndex: "type",
      key: "type",
      width: 144,
      align: "center",
      render: (value) => {
        if (value) {
          return (
            <Text className="text-green-600">
              <CheckOutlined rev={undefined} /> Cộng
            </Text>
          );
        }
        return (
          <Text className="text-red-600">
            <CloseOutlined rev={undefined} /> Trừ
          </Text>
        );
      },
    },
    {
      title: "Số điểm (+/-)",
      dataIndex: "point",
      key: "point",
      width: 144,
      align: "center",
    },
    {
      title: "Mã đơn hàng",
      dataIndex: "orderId",
      key: "orderId",
      width: 144,
    },
  ];

  useEffect(() => {
    setFilterValue({
      ...filterValue,
      page,
      pageSize,
      searchText,
    });
    getPointCustomerExec();
  }, [page, pageSize, searchText]);

  console.log(filterValue, "filterValue");

  useEffect(() => {
    getPointCustomerExec();
  }, [filterValue]);

  useEffect(() => {
    // Fetch data based on activeKey
  }, [activeKey]);
  return (
    <div className="py-3 flex flex-col gap-3">
      <Typography className="text-base">
        Tổng điểm hiện tại:{" "}
        <Text className="font-semibold text-xl text-red-600">
          {memberPoint}
        </Text>
      </Typography>
      <div hidden className=" flex flex-col md:flex-row items-center gap-3">
        <RangePicker
          className="w-full md:w-96"
          placeholder={["Từ ngày", "Đến ngày"]}
          onChange={(date) => {
            if (date) {
              setFilterValue({
                ...filterValue,
                fromDate: date[0] ? date[0].toISOString() : "",
                toDate: date[1] ? date[1].toISOString() : "",
              });
            } else {
              setFilterValue({
                ...filterValue,
                fromDate: null,
                toDate: null,
              });
            }
          }}
        />

        <Input
          className="w-full md:w-96"
          placeholder="Tìm kiếm"
          allowClear
          onChange={handleInputChange}
          onClear={() => setSearchText("")}
          prefix={<SearchOutlined rev={undefined} />}
        />
      </div>

      <Table
        rowKey={(record) => `${record.customerPointId} - ${record.customerId}`}
        columns={columns}
        dataSource={getPointCustomerState?.data}
        scroll={{
          x: "max-content",
        }}
        pagination={{
          current: page,
          pageSize,
          onChange(offset, limit) {
            setPage(offset);
            setPageSize(limit);
          },
          total: getPointCustomerState?.meta?.totalRecords || 0,
          showSizeChanger: true,
          pageSizeOptions: [5, 10, 20],
          onShowSizeChange(current, size) {
            setPage(1);
            setPageSize(size);
          },
          showTotal(total, range) {
            return `Total: ${range[0]}-${range[1]} of ${total} items`;
          },
          responsive: true,
        }}
      />
    </div>
  );
}
