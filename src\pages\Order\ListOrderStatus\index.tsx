import { use<PERSON><PERSON>back, useRef } from "react";

import { Col } from "react-bootstrap";
import { ValueType } from "react-select";

import imgSearchBlue from "assets/images/icons/search-blue.svg";
import { <PERSON><PERSON> } from "components/atoms/button";
import { Checkbox } from "components/atoms/checkbox";
import { ColorPicker } from "components/atoms/colorpicker";
import { Heading } from "components/atoms/heading";
import { NumberfieldHookForm } from "components/atoms/numberfield";
import { Pulldown, PulldownHookForm } from "components/atoms/pulldown";
import { Tag } from "components/atoms/tag";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { Textfield, TextfieldHookForm } from "components/atoms/textfield";
import { Toggle } from "components/atoms/toggle";

import { Formfield } from "components/molecules/formfield";
import {
  Pagination,
  PaginationReference,
} from "components/molecules/pagination";
import { Row } from "components/organisms/grid";
import { Modal } from "components/organisms/modal";
import { Section } from "components/organisms/section";
import { <PERSON>, The<PERSON>, Tr, Th, Tbody, Td } from "components/organisms/table";
import { General } from "components/pages/general";
import { Link } from "components/utils/link";
import dayjs from "helpers/dayjs";
import { FormContainer } from "helpers/form";
import PaginationSection from "pages/Common/paginationSection";
import TableManipulation from "pages/Common/tableManipulation";

import RootPageRouter from "..";
import {
  inputValidationSchema,
  CreateOrderStatusFormPayload,
} from "./constant";
import { OrderStatusListPageVm } from "./vm";

const IndexPage = () => {
  const {
    getOrderStatusListLoading,
    gotoPage,
    pageSize,
    orderStatusListData,
    orderStatusListPaginationState,
    handleChangePageSize,
    toggleSortOrderBy,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    colorSelected,
    isActiveToggle,
    createOrderStatus,
    createOrderStatusState,
    orderStatusOptionPulldown,
    onChangeColor,
    onChangeToggle,
    orderStatusLoadMore,
  } = OrderStatusListPageVm();

  const paginationRef = useRef<PaginationReference>(null);

  const handleOnChangePageSizePulldown = useCallback(
    (option: ValueType<{ label: string; value: string }, false>) => {
      if (option?.value) {
        handleChangePageSize(Number(option?.value));
        if (paginationRef.current) paginationRef.current.reset();
      }
    },
    [handleChangePageSize]
  );

  const handleSubmitCreateOrderStatus = useCallback(
    (formData: CreateOrderStatusFormPayload) => {
      createOrderStatus({
        ...formData,
        isDefault: isActiveToggle,
        displayOrder: Number(formData.displayOrder),
        color: colorSelected,
        prevOrderStatusIds: formData.prevOrderStatusIds?.map((item) =>
          Number(item.value)
        ),
      });
    },
    [createOrderStatus, isActiveToggle, colorSelected]
  );

  return (
    <General>
      <title key="title">Trạng thái đơn hàng</title>
      <Section>
        <Heading type="h1" modifiers="primary">
          TRẠNG THÁI ĐƠN HÀNG
        </Heading>

        <Section>
          <Row className="d-flex">
            <Col
              className="ml-auto"
              xs={{ span: 12, order: 2 }}
              lg={{ span: 6, order: 1 }}
            >
              <Textfield iconSrc={imgSearchBlue} placeholder="Tìm kiếm" />
            </Col>
            <Col
              className="d-flex justify-content-end u-mb-15 u-mb-lg-0"
              xs={{ span: 12, order: 1 }}
              lg={{ span: 6, order: 2 }}
            >
              <Button onClick={() => handleOpenModalByType("createStatus")}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Section>

        <Section>
          <Table
            loading={getOrderStatusListLoading}
            hasData={orderStatusListData.length > 0}
            scroll={{ x: 1500 }}
          >
            <Thead>
              <Tr>
                <Th colSpan={1} modifiers="center" stickyLeft>
                  STT
                </Th>

                <Th
                  colSpan={3}
                  isSortable
                  modifiers="center"
                  onSort={() => toggleSortOrderBy("name")}
                >
                  Tên trạng thái
                </Th>

                <Th colSpan={2} modifiers="center">
                  Màu sắc
                </Th>

                <Th colSpan={2} modifiers="center">
                  Mô tả
                </Th>

                <Th colSpan={2} modifiers="center">
                  Mặc định
                </Th>

                <Th colSpan={2} modifiers="center">
                  Tagline
                </Th>

                <Th colSpan={2} modifiers="center">
                  Thứ tự hiển thị
                </Th>

                <Th
                  colSpan={3}
                  isSortable
                  modifiers="center"
                  onSort={() => toggleSortOrderBy("updatedAt")}
                >
                  Cập nhật cuối
                </Th>

                <Th colSpan={3} modifiers="center" stickyRight>
                  Thao tác
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {orderStatusListData.map((orderStatus, index) => (
                <Tr key={orderStatus.orderStatusId}>
                  <Td colSpan={1} modifiers="center" stickyLeft>
                    {index + 1}
                  </Td>
                  <Td colSpan={3} modifiers="center">
                    {orderStatus.name}
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    <Tag color={orderStatus.color} />
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    {orderStatus.description}
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    <Checkbox defaultChecked={orderStatus.isDefault} disabled />
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    <Link
                      to={RootPageRouter.children.listOrderStatusTagline.generatePath(
                        {
                          orderStatusId: orderStatus?.orderStatusId.toString(),
                          orderStatusCode: orderStatus?.code.toString(),
                        }
                      )}
                    >
                      <span
                        style={{
                          color: "#FF1515",
                        }}
                      >
                        {(orderStatus.orderStatusTaglines || []).length} Tagline
                      </span>
                    </Link>
                  </Td>
                  <Td colSpan={2} modifiers="center">
                    {orderStatus.displayOrder}
                  </Td>
                  <Td colSpan={3} modifiers="center">
                    {dayjs(orderStatus.updatedAt).format("DD/MM/YYYY HH:mm")}
                  </Td>
                  <Td colSpan={3} modifiers="center" stickyRight>
                    <TableManipulation
                      infoAction={{
                        id: `${orderStatus.orderStatusId}info`,
                        action: () =>
                          RootPageRouter.gotoChild("orderStatusDetail", {
                            params: {
                              orderStatusCode: orderStatus.code?.toString(),
                            },
                          }),
                      }}
                      editAction={{
                        id: `${orderStatus.orderStatusId}edit`,
                        action: () =>
                          RootPageRouter.gotoChild("orderStatusDetail", {
                            params: {
                              orderStatusCode: orderStatus.code?.toString(),
                            },
                            queryString: "?action=edit",
                          }),
                      }}
                      deleteAction={{
                        id: `${orderStatus.orderStatusId}delete`,
                      }}
                    />
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Section>

        <Section>
          <PaginationSection
            appearanceOption={
              <Pulldown
                placeholder="Số lượng hiển thị"
                value={{ label: `${pageSize}`, value: `${pageSize}` }}
                options={[5, 10, 15, 25, 30].map((size) => ({
                  label: `${size}`,
                  value: `${size}`,
                }))}
                onChange={handleOnChangePageSizePulldown}
              />
            }
            paginateOption={
              orderStatusListPaginationState.totalPage && (
                <Pagination
                  modifiers="center"
                  total={orderStatusListPaginationState.totalPage}
                  pageCount={5}
                  defaultCurrentPage={1}
                  onPageChange={gotoPage}
                  ref={paginationRef}
                />
              )
            }
          />
        </Section>
      </Section>
      <Modal
        style={{ content: { maxWidth: 700 } }}
        isOpen={modalTypeIsOpen("createStatus")}
        onCloseModal={handleCloseModal}
        isClosable={false}
      >
        <Heading type="h1" centered>
          TẠO MỚI TRẠNG THÁI ĐƠN HÀNG
        </Heading>

        <Section>
          <FormContainer
            validationSchema={inputValidationSchema}
            onSubmit={handleSubmitCreateOrderStatus}
          >
            <Formfield label="Tên trạng thái" name="name">
              <TextfieldHookForm name="name" placeholder="Tên trạng thái" />
            </Formfield>

            <Formfield label="Mô tả" name="description">
              <TextareafieldHookForm name="description" placeholder="Mô tả" />
            </Formfield>

            <Formfield label="Màu sắc" name="color">
              <ColorPicker onChangeClolor={onChangeColor} />
            </Formfield>

            <Formfield label="Thứ tự hiển thị" name="displayOrder">
              <NumberfieldHookForm
                name="displayOrder"
                placeholder="Thứ tự hiển thị"
              />
            </Formfield>

            <Formfield label="Trạng thái trước" name="prevOrderStatusIds">
              <PulldownHookForm
                placeholder="Trạng thái trước"
                options={orderStatusOptionPulldown}
                isMultiSelect
                name="prevOrderStatusIds"
                triggerLoadMore={async () => orderStatusLoadMore()}
              />
            </Formfield>

            <div className="d-flex u-mt-15">
              <Toggle onChange={onChangeToggle} label="Mặc định" />
            </div>

            <div className="d-flex justify-content-end u-mt-20">
              <Button
                onClick={handleCloseModal}
                buttonType="outline"
                modifiers="secondary"
              >
                HỦY
              </Button>
              <div className="u-ml-15">
                <Button
                  type="submit"
                  disabled={createOrderStatusState.loading}
                  isLoading={createOrderStatusState.loading}
                >
                  LƯU
                </Button>
              </div>
            </div>
          </FormContainer>
        </Section>
      </Modal>
    </General>
  );
};

export default IndexPage;
