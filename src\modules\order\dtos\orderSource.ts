import { createMapper, fromSchema } from "libs/adapters/dto";
import { mergeSchema, Mixed } from "libs/domain";

import { OrderSourceSchema } from "../entities/orderSource";

export const orderSourceDto = createMapper(
  fromSchema(
    mergeSchema(OrderSourceSchema, {
      prevOrderStatus: Array(Mixed(OrderSourceSchema)),
    })
  )
);

export type OrderSourceDtoType = ReturnType<typeof orderSourceDto>;
