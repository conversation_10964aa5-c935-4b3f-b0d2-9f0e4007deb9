.o-chatbox {
	position: relative;
	height: 100%;
	padding: rem(20) 0;

	.ReactVirtualized__List {
		outline: none;
	}

	@mixin transCenter {
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	&-cell {
		padding: rem(10) 0;
	}
	&-sticky {
		position: absolute;

		&_center {
			@include transCenter();
		}

		&_bottom {
			top: auto;
			bottom: 10px;
		}

		&_top {
			top: 10px;
		}
	}

	&-scrollbottom {
		position: relative;
		width: rem(27);
		height: rem(27);
		cursor: pointer;
		background-color: $COLOR-DENIM;
		border-radius: 50%;
		box-shadow: 0px 2px 5px rgba($color: $COLOR-DENIM, $alpha: 0.7);
		transition: transform 0.3s;
		&:hover {
			transform: translateY(rem(4));
		}

		&::after {
			@include transCenter();
			position: absolute;

			width: rem(8);
			height: rem(8);
			content: " ";
			border: 0px solid $COLOR_WHITE;
			border-right-width: 2px;
			border-bottom-width: 2px;
			transform: translate(-50%, -75%) rotate(45deg);
		}
	}

	&-float {
		top: auto;
		bottom: 55px;

		&_message {
			& > *:first-child {
				max-width: rem(150);
				padding: rem(6) rem(17);
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				background-color: $COLOR_WHITE;
				border-radius: 14px;
				box-shadow: 0px 5px 7px rgba($color: $COLOR_PLATINUM, $alpha: 0.9);
			}
		}
	}

	&-spinner {
		@-webkit-keyframes sk-bouncedelay {
			0%,
			80%,
			100% {
				-webkit-transform: scale(0);
			}
			40% {
				-webkit-transform: scale(1);
			}
		}

		@keyframes sk-bouncedelay {
			0%,
			80%,
			100% {
				transform: scale(0);
			}
			40% {
				transform: scale(1);
			}
		}

		& > div {
			display: inline-block;
			width: 8px;
			height: 8px;
			margin: 0 5px;
			background-color: $COLOR-BLACK;
			background-color: $COLOR-DENIM;
			border-radius: 100%;
			animation: sk-bouncedelay 1.8s infinite ease-in-out both;
		}
	}

	&-bounce_first {
		animation-delay: -0.38s !important;
	}

	&-bounce_middle {
		animation-delay: -0.19s !important;
	}
}
