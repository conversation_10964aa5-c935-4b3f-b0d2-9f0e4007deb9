/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import {
  CreateCustomerProfileDtoType,
  CreateCustomerShippingAddressDtoType,
} from "modules/customer/";

import crmDriverV1 from "../crm-driver-v1";

type PhonePayload = {
  customerId: number;
  customerPhoneNumberId: number;
  phoneNumber: string;
  description?: string;
};

type EmailPayload = {
  customerId: number;
  customerEmailId: number;
  email: string;
  description?: string;
};

export const getCustomerShippingAddress = (customerId: number) =>
  crmDriverV1.get(
    `/customers/external/profile/${customerId}/shipping-addresses`
  );

export const getSingleCustomerShippingAddress = (payload: {
  customerId: number;
  shippingAddressId: number;
}) =>
  crmDriverV1.get(
    `/customers/external/profile/${payload.customerId}/shipping-addresses/${payload.shippingAddressId}`
  );

export const getListCustomer = (payload: {
  pageNum: number;
  pageSize: number;
  email?: string;
  phoneNumber?: string;
  name?: string;
  birthDay?: string;
}) => {
  return crmDriverV1.get(`/customers/external/profile`, {
    params: {
      ...payload,
    },
  });
};

export const getCustomerProfile = (payload: { customerId: number }) =>
  crmDriverV1.get(`/customers/external/profile/${payload.customerId}`);

export const updateCustomerMainInfo = ({
  customerId,
  name,
  gender,
  birthDay,
}: {
  customerId: number;
  name: string;
  gender: number;
  birthDay: string;
}) =>
  crmDriverV1.put(`/customers/external/profile/${customerId}`, {
    name,
    gender,
    birthDay,
  });

export const createCustomerPhoneNumber = ({
  customerId,
  phoneNumber,
  description,
}: Omit<PhonePayload, "customerPhoneNumberId">) =>
  crmDriverV1.post(`/customers/external/profile/${customerId}/phone-numbers`, {
    phoneNumber,
    description,
  });

export const deleteCustomerPhoneNumber = ({
  customerId,
  customerPhoneNumberId,
}: Omit<PhonePayload, "phoneNumber" | "description">) =>
  crmDriverV1.delete(
    `/customers/external/profile/${customerId}/phone-numbers/${customerPhoneNumberId}`
  );

export const updateCustomerPhoneNumber = ({
  customerId,
  customerPhoneNumberId,
  phoneNumber,
  description,
}: PhonePayload) =>
  crmDriverV1.put(
    `/customers/external/profile/${customerId}/phone-numbers/${customerPhoneNumberId}`,
    { phoneNumber, description }
  );

export const createCustomerEmail = ({
  customerId,
  email,
  description,
}: Omit<EmailPayload, "customerEmailId">) =>
  crmDriverV1.post(`/customers/external/profile/${customerId}/emails`, {
    email,
    description,
  });

export const deleteCustomerEmail = ({
  customerId,
  customerEmailId,
}: Omit<EmailPayload, "email" | "description">) =>
  crmDriverV1.delete(
    `/customers/external/profile/${customerId}/emails/${customerEmailId}`
  );

export const updateCustomerEmail = ({
  customerId,
  customerEmailId,
  email,
  description,
}: EmailPayload) =>
  crmDriverV1.put(
    `/customers/external/profile/${customerId}/emails/${customerEmailId}`,
    { email, description }
  );

export const createCustomerShippingAddress = async (
  customerId: number,
  payload: CreateCustomerShippingAddressDtoType
) =>
  crmDriverV1.post(
    `/customers/external/profile/${customerId}/shipping-addresses`,
    payload
  );

export const createCustomerProfile = (payload: CreateCustomerProfileDtoType) =>
  crmDriverV1.post(`/customers/external/profile`, payload);

export const updateCustomerShippingAddress = async (
  customerId: number,
  shippingAddressId: number,
  payload: CreateCustomerShippingAddressDtoType
) =>
  crmDriverV1.put(
    `/customers/external/profile/${customerId}/shipping-addresses/${shippingAddressId}`,
    payload
  );

export const CreateCustomerV2 = () => {
  const [createCustomerExe] = useAsync(
    useCallback(
      (payload: any) =>
        crmDriverV1.post(`/customers/external/profile`, payload),
      []
    )
  );
  return {
    createCustomerExe,
  };
};

export const UpdateCustomerProfileV2 = () => {
  const [updateCustomerExe] = useAsync(
    useCallback(
      (customerId: number, payload: any) =>
        crmDriverV1.put(`/customers/external/profile/${customerId}`, payload),
      []
    )
  );
  return {
    updateCustomerExe,
  };
};

export const GetCustomerProfileV2 = () => {
  const [getCustomerExe, getCustomerState] = useAsync(
    useCallback(
      (customerId: number) =>
        crmDriverV1.get(`/customers/external/profile/${customerId}`),
      []
    )
  );
  return {
    getCustomerExe,
    getCustomerState: getCustomerState.data?.data,
  };
};

export const useDeleteCustomerProfileV2 = () => {
  const [deleteCustomerExe] = useAsync(
    useCallback(
      (customerId: number) =>
        crmDriverV1.delete(`/customers/external/profile/${customerId}/delete`),
      []
    )
  );
  return {
    deleteCustomerExe,
  };
};

export const UseFindAllByFilter = () => {
  const [getProfileExe, getProfileState] = useAsync(
    useCallback(
      (value: any) =>
        crmDriverV1.post(`/customers/external/profile/findAllByFilter`, value),
      []
    )
  );
  return {
    getProfileExe,
    getProfileState: getProfileState.data?.data || [],
  };
};

export const getCustomerByFilter = (value: any) => {
  return crmDriverV1.post(`/customers/external/profile/findAllByFilter`, value);
};
