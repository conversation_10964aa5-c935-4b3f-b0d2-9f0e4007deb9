import pick from "object.pick";

import { createMapper, fromSchema } from "libs/adapters/dto";

import { SystemConfigSchema } from "../entities";

export const createPointExchangeDTO = createMapper(
  fromSchema(
    pick(SystemConfigSchema, [
      "conversionRateFromPointToMoney",
      "conversionRateFromMoneyToPoint",
    ])
  )
);
export type CreatePointExchangeDTOType = ReturnType<
  typeof createPointExchangeDTO
>;
