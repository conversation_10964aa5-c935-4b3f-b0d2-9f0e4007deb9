import { forwardRef, Ref, useImperativeHandle, useState } from "react";

import { ButtonComponent } from "@syncfusion/ej2-react-buttons";
import { DialogComponent } from "@syncfusion/ej2-react-popups";

interface DialogProps {
  id?: string;
  header?: string;
  message?: string;
  width?: string;
  height?: string;
  visible?: boolean;
  // eslint-disable-next-line
  content?: JSX.Element;
  customRef?: Ref<{ showWithMessage(message: string): void }>;
  onClickOk?: () => void;
  onClose?: () => void;
}
export const CustomDialog = forwardRef<DialogComponent, DialogProps>(
  (
    {
      id,
      header,
      message,
      width,
      visible,
      height,
      content,
      customRef,
      onClickOk,
      onClose,
    },
    dialogRef: any // eslint-disable-line
  ) => {
    const [mes, setMes] = useState<string>(null!);
    useImperativeHandle(customRef, () => ({
      showWithMessage: (mess: string) => {
        if (dialogRef && typeof dialogRef !== "function") {
          setMes(mess);
          dialogRef.current?.show();
        }
      },
    }));

    return (
      <div id={id || "dialog-target"}>
        <DialogComponent
          width={width || "320px"}
          height={height || "160px"}
          target={`#${id || "dialog-target"}`}
          ref={dialogRef}
          visible={visible || false}
          isModal
          style={{ overflow: "hidden" }}
          overlayClick={() => {
            if (dialogRef) dialogRef.current.visible = false;
          }}
          header={header || "Are you sure ?"}
          showCloseIcon
          close={onClose}
        >
          {content || (
            <div className="m-2" style={{ textAlign: "center" }}>
              <div style={{ display: "flex", justifyContent: "center" }}>
                <ButtonComponent
                  className="e-print"
                  style={{
                    backgroundColor: "#2980b9",
                    color: "#fff",
                    marginLeft: "15px",
                    marginTop: "18px",
                    width: "80px",
                    height: "30px",
                    outline: "none",
                    border: "none",
                  }}
                  content="Ok"
                  onClick={onClickOk}
                />
                <ButtonComponent
                  className="e-print"
                  style={{
                    backgroundColor: "#95a5a6",
                    color: "#fff",
                    marginLeft: "15px",
                    marginTop: "18px",
                    width: "80px",
                    height: "30px",
                    outline: "none",
                    border: "none",
                  }}
                  content="Cancel"
                  // eslint-disable-next-line
                  onClick={() => (dialogRef.current.visible = false)}
                />
              </div>
            </div>
          )}
        </DialogComponent>
      </div>
    );
  }
);
