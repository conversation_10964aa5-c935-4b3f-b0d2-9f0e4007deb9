import { useEffect, useState } from "react";
import {
  DeleteFilled,
  EditFilled,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { App, Card, Flex, Input, Modal, Tag, Typography } from "antd";
import Table, { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { showLoading } from "components/atoms/base/Spinner";
import { COLOR } from "constants/color";
import { useDeleteTicket } from "modules/ticket/hook/useCreateTicket";
import { PriorityTicketModal } from "./modal/PriorityTicketModal";
import { PriorityTicketPageVm } from "./vm";

export interface PriorityDataType {
  _id: string;
  masterDataTicketId: number;
  name: string;
  color: string;
  createdAt: Date;
  updatedAt: Date;
  displayOrder: number;
}

const { Title } = Typography;
export default function PriorityTicketPage() {
  const [isOpen, setOpen] = useState<boolean>(false);
  const [isDataModal, setDataModal] = useState<PriorityDataType | null>(null);
  const {
    loading,
    priorityTicketData,
    gotoPage,
    priorityTicketState,
    pageSize,
    handleChangePageSize,
    handleChangeInput,
    setSearchText,
    toggleSortOrderBy,
    refetch: refetchPriorityTicket,
    totalRecords,
    searchText,
  } = PriorityTicketPageVm();

  const handleRefetch = () => {
    gotoPage(1);
  };

  const { deleteTicketExe } = useDeleteTicket();

  const handleConfirmDelete = (record: PriorityDataType) => {
    Modal.confirm({
      centered: true,
      title: "Xác nhận xóa",
      content: `Bạn có chắc chắn muốn xóa mức độ "${record.name}" không?`,
      okText: "Xóa",
      okType: "primary",
      okButtonProps: {
        className: "bg-red-500 hover:!bg-red-700",
      },
      cancelText: "Hủy",
      onOk: () => {
        showLoading(true);
        deleteTicketExe(record.masterDataTicketId)
          .then(() => {
            handleRefetch();
            showNotification({
              type: "success",
              message: "Xóa thành công",
            });
          })
          .catch((err) => {
            showNotification({
              type: "error",
              message: err.message,
            });
          })
          .finally(() => {
            showLoading(false);
          });
      },
    });
  };

  const columns: ColumnsType<PriorityDataType> = [
    {
      title: "STT",
      key: "ordinalNumber",
      align: "center",
      width: 100,
      dataIndex: "ordinalNumber",
      render: (__, ___, index) =>
        (priorityTicketState.currentPage! - 1) * pageSize + index + 1,
    },
    {
      title: "Tên mức độ",
      key: "name",
      dataIndex: "name",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("name"),
      }),
    },
    {
      title: "Màu",
      key: "color",
      dataIndex: "color",
      align: "center",
      width: 144,
      render: (value, record) => {
        return <Tag className="w-12 h-4 rounded-xl" color={value} />;
      },
    },
    {
      title: "Thứ tự hiển thị",
      key: "displayOrder",
      dataIndex: "displayOrder",
      align: "center",
      width: 144,
    },
    {
      title: "Cập nhật cuối",
      key: "updatedAt",
      dataIndex: "updatedAt",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("updatedAt"),
      }),
      render: (date) => dayjs(date).format("DD/MM/YYYY HH:mm"),
    },
    {
      title: "Thao tác",
      align: "center",
      key: "action",
      width: 120,
      render: (__, record) => {
        return (
          <Flex align="center" justify="center" gap={12}>
            <BaseButton
              type="primary"
              bgColor={COLOR.GREEN[500]}
              hoverColor={COLOR.GREEN[700]}
              icon={<EditFilled rev={undefined} />}
              onClick={() => {
                setDataModal(record);
                setOpen(true);
              }}
            />
            <BaseButton
              type="primary"
              bgColor={COLOR.RED[500]}
              hoverColor={COLOR.RED[700]}
              icon={<DeleteFilled rev={undefined} />}
              onClick={() => {
                handleConfirmDelete(record);
              }}
            />
          </Flex>
        );
      },
    },
  ];

  return (
    <>
      <Card className="m-4">
        <div className="flex flex-col gap-4 p-4">
          <Title level={3}>Danh sách mức độ ưu tiên ticket</Title>

          <div className="flex flex-col gap-3 lg:flex-row lg:justify-between lg:items-center">
            <Input
              className="lg:w-96 w-full h-10"
              placeholder="Tìm kiếm"
              onChange={handleChangeInput}
              onClear={() => {
                setSearchText("");
              }}
              allowClear
              prefix={<SearchOutlined rev={undefined} />}
            />
            <BaseButton
              type="primary"
              icon={<PlusOutlined rev={undefined} />}
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              onClick={() => {
                setOpen(true);
              }}
              className="h-10"
            >
              Tạo mới
            </BaseButton>
          </div>
          <Table
            bordered
            rowKey="masterDataTicketId"
            tableLayout="auto"
            scroll={{ x: "max-content" }}
            loading={loading}
            dataSource={priorityTicketData}
            columns={columns}
            pagination={{
              current: priorityTicketState.currentPage,
              pageSize,
              onChange: gotoPage,
              pageSizeOptions: [5, 10, 20, 50],
              showSizeChanger: true,
              showQuickJumper: true,
              total: totalRecords,
              showTotal: (total) => `Tổng số ${total} bản ghi`,
              onShowSizeChange: (__, size) => handleChangePageSize(size),
            }}
          />
        </div>
      </Card>
      <PriorityTicketModal
        open={isOpen}
        setOpen={setOpen}
        refetch={handleRefetch}
        dataModal={isDataModal}
        setDataModal={setDataModal}
      />
    </>
  );
}
