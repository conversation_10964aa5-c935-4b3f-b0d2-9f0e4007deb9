import React, { createContext, useCallback, useEffect, useMemo } from "react";

import useDidMount from "helpers/react-hooks/useDidMount";
import useSocketIO, { EventNames } from "hooks/useSocketIO";
import { Source } from "libs/adapters/stores/core";
import { useEvent, useMemoCreator } from "libs/adapters/stores/react";
import {
  conversationMessageSourceCreator,
  getConversationMessageEventCreator,
  ConversationMessageSourceState,
  MessageModel,
  conversationSourceCreator,
  getAssignConversationEventCreator,
  ConversationSourceState,
  AssignConversationDtoType,
  assignConversationItemDto,
  createTempTextMessage,
  implementCreatorTypeMessage,
} from "modules/chat";
import { FacebookUploadMediaModel } from "modules/media";
import { getDetailConversationById } from "services/crm/chat";
import { uploadMediaFacebook } from "services/crm/media";

export interface IChatboxPageContext {
  selectedConversationMessageSource: Source<ConversationMessageSourceState>;
  conversationSource: Source<
    ConversationSourceState<AssignConversationDtoType>
  >;
  getConversationMessageEvent: ReturnType<
    typeof getConversationMessageEventCreator
  >;
  loadmoreConversationMessage: () => void;
  selectConversationById: ReturnType<
    typeof conversationSourceCreator
  >["selectConversationById"];
  sendConversationMessage: (
    text: string,
    files?: {
      blob: Blob;
      file: File;
      metadata?: { width?: number; height?: number };
    }[]
  ) => void;
}

export const Context = createContext<IChatboxPageContext>(
  {} as IChatboxPageContext
);

const loadmoreSlotSize = 15;
const getConversationMessageEvent = getConversationMessageEventCreator();
const getAssignConversationEvent = getAssignConversationEventCreator();

const ChatboxPageContext: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const websocketProtocol =
    window.location.protocol === "https:" ? "wss:" : "ws:";

  const { socketIoInstance, handlePushFbMessage } = useSocketIO({
    uri: `${websocketProtocol}//${process.env.REACT_APP_API_BASE_HOSTNAME}`,
  });

  const {
    conversationSource,
    conversationHasNewMessage,
    selectConversationById,
    appendListConversation,
    insertConversation,
  } = useMemoCreator(() => {
    return conversationSourceCreator<AssignConversationDtoType>();
  }, []);

  const {
    unShiftConversationListMessages,
    appendConversationMessage,
    appendMultiConversationMessage,
    conversationMessageSource: selectedConversationMessageSource,
    changeConversation,
  } = useMemoCreator(() => {
    return conversationMessageSourceCreator({
      loadmoreSlotSize,
      conversation: conversationSource.getValue().selectedConversation,
      infasInfo: {
        socketId: socketIoInstance?.id || "",
      },
    });
  }, [socketIoInstance?.id]);

  useEvent(
    getConversationMessageEvent.fulfilled,
    useCallback(
      (messages) => {
        unShiftConversationListMessages({ messages });
      },
      [unShiftConversationListMessages]
    )
  );

  useEvent(
    getAssignConversationEvent.fulfilled,
    useCallback(
      (conversations) => {
        appendListConversation({ conversations });
      },
      [appendListConversation]
    )
  );

  const fetchDetailAssignConversationById = useCallback(
    (conversationId: string) => {
      getDetailConversationById({
        conversationId,
      })
        .then((res) => assignConversationItemDto(res.data?.data))
        .then((conversation) => insertConversation({ conversation }));
    },
    [insertConversation]
  );

  useEvent(
    appendConversationMessage,
    useCallback(
      ({ message }) => {
        const listConversation = conversationSource.getValue().conversation;
        const conversationOfMessage = message.conversationId;
        if (listConversation[conversationOfMessage]) {
          conversationHasNewMessage({
            conversationId: message.conversationId,
            message: implementCreatorTypeMessage(
              listConversation[conversationOfMessage].detail,
              message
            ),
          });
        } else {
          fetchDetailAssignConversationById(conversationOfMessage);
        }
      },
      [
        conversationHasNewMessage,
        conversationSource,
        fetchDetailAssignConversationById,
      ]
    )
  );

  useEvent(
    selectConversationById,
    useCallback(
      ({ id }) => {
        const conversation = conversationSource.getValue().conversation[id];

        if (conversation) {
          changeConversation({ conversation: conversation.detail });
        }
        getConversationMessageEvent({
          conversationId: conversation.detail.conversationId,
          pageSize: loadmoreSlotSize,
        });
      },
      [conversationSource, changeConversation]
    )
  );

  useEvent(
    appendMultiConversationMessage,
    useCallback(
      ({ conversationId, messages }) => {
        const listConversation = conversationSource.getValue().conversation;
        if (listConversation[conversationId]) {
          conversationHasNewMessage({
            conversationId,
            message: messages[messages.length - 1],
          });
        } else {
          fetchDetailAssignConversationById(conversationId);
        }
      },
      [
        conversationHasNewMessage,
        conversationSource,
        fetchDetailAssignConversationById,
      ]
    )
  );

  const loadmoreConversationMessage = useCallback(() => {
    const firstMessage =
      selectedConversationMessageSource.getValue().messages?.[0];
    const { selectedConversation } = conversationSource.getValue();
    if (!firstMessage || !selectedConversation?.conversationId) return;

    getConversationMessageEvent({
      conversationId: selectedConversation?.conversationId,
      beforeTime: firstMessage.createdAt,
      pageSize: loadmoreSlotSize,
    });
  }, [selectedConversationMessageSource, conversationSource]);

  const sendConversationMessage: IChatboxPageContext["sendConversationMessage"] =
    useCallback(
      async (text, files) => {
        const { selectedConversation } = conversationSource.getValue();

        if (!socketIoInstance || !selectedConversation || !socketIoInstance?.id)
          return;
        const { conversationId } = selectedConversation;

        if (text) {
          const message = createTempTextMessage({
            senderId: "607a7585a2b68a6f9c1c3808",
            conversationId,
            text,
          });

          handlePushFbMessage({
            conversationId,
            content: {
              text,
              metadata: {
                fromSocketId: socketIoInstance.id,
                tempId: message._id,
              },
            },
          });

          appendConversationMessage({ message });
        }

        if (files && files.length > 0) {
          const messages = files.map((file) =>
            createTempTextMessage({
              senderId: "607a7585a2b68a6f9c1c3808",
              conversationId,
              files: [file],
            })
          );

          appendMultiConversationMessage({ messages, conversationId });

          const mediaIds = await Promise.allSettled(
            (files || []).map((file) =>
              uploadMediaFacebook(file.file).then(
                (res) =>
                  FacebookUploadMediaModel.createMap(res.data?.data)?.[0]
                    ?.mediaId
              )
            )
          ).then((res) =>
            res
              .map((item, index) => ({
                ...item,
                tempId: messages[index]._id,
              }))
              .filter((item) => item.status === "fulfilled")
              .map((item) => ({
                // eslint-disable-next-line no-undef
                mediaId: (item as PromiseFulfilledResult<string>).value,
                tempId: item.tempId,
              }))
          );

          mediaIds.forEach(({ mediaId, tempId }) => {
            handlePushFbMessage({
              conversationId,
              content: {
                mediaIds: [mediaId],
                metadata: {
                  fromSocketId: socketIoInstance.id,
                  tempId,
                },
              },
            });
          });
        }
      },
      [
        conversationSource,
        socketIoInstance,
        handlePushFbMessage,
        appendConversationMessage,
        appendMultiConversationMessage,
      ]
    );

  const contextStateValue = useMemo(
    () => ({
      selectedConversationMessageSource,
      loadmoreConversationMessage,
      sendConversationMessage,
      conversationSource,
      getConversationMessageEvent,
      selectConversationById,
    }),
    [
      selectedConversationMessageSource,
      loadmoreConversationMessage,
      sendConversationMessage,
      conversationSource,
      selectConversationById,
    ]
  );

  useEffect(() => {
    const handleOnReceiveMessage = (data: unknown) => {
      if (!socketIoInstance) return;
      const message = MessageModel.create(data);
      appendConversationMessage({ message });
    };

    if (socketIoInstance) {
      socketIoInstance.on(
        EventNames.RECEIVE_FB_MESSAGE,
        handleOnReceiveMessage
      );
    }

    return () => {
      if (socketIoInstance) {
        socketIoInstance.disconnect();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    socketIoInstance,
    appendConversationMessage,
    selectedConversationMessageSource,
  ]);

  useDidMount(() => {
    getAssignConversationEvent({ pageNum: 1, pageSize: 25 });
  });

  return (
    <Context.Provider value={contextStateValue}>{children}</Context.Provider>
  );
};

export default ChatboxPageContext;
