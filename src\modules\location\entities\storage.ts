import { Model, String, Number, ModelValue } from "libs/domain";

export const StorageSchema = {
  storageId: Number(),
  name: String(),
  code: String(),
  displayOrder: Number(),

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => new Date(raw),
};

export const StorageModel = new Model(StorageSchema);
export type StorageEntityType = ModelValue<typeof StorageModel>;
