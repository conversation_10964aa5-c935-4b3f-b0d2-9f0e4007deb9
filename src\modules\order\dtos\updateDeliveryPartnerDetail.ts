import pick from "object.pick";

import { cleanEmptyString } from "helpers/object";
import { createMapper, force, fromSchema } from "libs/adapters/dto";
import { mergeSchema } from "libs/domain";

import { DeliveryPartnerSchema } from "../entities/deliveryPartner";

export const updateDeliveryPartnerDetailDto = createMapper(
  fromSchema(mergeSchema(pick(DeliveryPartnerSchema, ["name", "code"]))),

  force((data) => ({
    ...cleanEmptyString(data),
  }))
);

export type UpdateDeliveryPartnerDetailDtoType = ReturnType<
  typeof updateDeliveryPartnerDetailDto
>;
