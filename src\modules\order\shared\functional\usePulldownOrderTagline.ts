import { useCallback, useMemo } from "react";

import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import {
  orderStatusTaglineOption,
  OrderStatusTaglineModel,
} from "modules/order";
import { getOrderTagline } from "services/crm/order";

export interface PulldownOrderStatusTaglineFunctionalOptions {
  excludePending: boolean;
  orderStatusId?: number;
}

export const usePulldownOrderStatusTagline = ({
  excludePending = false,
  orderStatusId,
}: PulldownOrderStatusTaglineFunctionalOptions) => {
  const [fetchOrderStatusTagline, fetchOrderStatusTaglineState] = useAsync(
    useCallback(
      async () =>
        orderStatusId &&
        getOrderTagline({ orderStatusId }).then((res) =>
          OrderStatusTaglineModel.createMap(res.data.data)
        ),
      [orderStatusId]
    ),
    {
      excludePending,
    }
  );

  const orderStatusTaglines = useMemo(
    () => fetchOrderStatusTaglineState.data || [],
    [fetchOrderStatusTaglineState.data]
  );

  const {
    options: orderStatusTaglineOptions,
    getOptionByValue,
    formatOption: formatOrderStatusTaglineOption,
  } = usePulldownHelper({
    dataSource: orderStatusTaglines,
    optionCreator: orderStatusTaglineOption,
    valueTrans: Number,
  });

  return {
    orderStatusTaglineOptions,
    orderStatusTaglines,
    fetchOrderStatusTagline,
    getOptionByValue,
    formatOrderStatusTaglineOption,
    fetchOrderStatusTaglineState,
  };
};
