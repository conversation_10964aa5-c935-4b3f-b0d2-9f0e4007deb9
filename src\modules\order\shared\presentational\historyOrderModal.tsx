/* eslint-disable react/jsx-key */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-restricted-syntax */
/* eslint-disable consistent-return */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect } from "react";
import { Modal } from "antd";
import HistoryComponent from "components/atoms/base/shared/HistoryComponent.shared";
import { useGetListEmployeeWithoutPagination } from "services/crm/employee";
import {
  useGetAllDistricts,
  useGetCities,
} from "services/crm/location/hooks/location.hooks";
import { useGetListTaglineDontByOrderStatus } from "services/crm/order/hooks/order-status.hook";
import { UseGetOrderHistory } from "services/crm/order/hooks/useGetOrderHistory";
import { OrderFormFieldLabels } from "./historyOrder.enum";
import { channelOptions, MasterData, orderTypeOptions } from "./orderform";

type HistoryOrderModalProps = {
  isOpen?: boolean;
  setOpenModal: (isOpen: boolean) => void;
  orderId: number;
  listMasterData?: MasterData;
};

export default function HistoryOrderModal({
  isOpen,
  setOpenModal,
  orderId,
  listMasterData: listData,
}: HistoryOrderModalProps) {
  const onClose = () => setOpenModal(false);
  const { employeeRefetch, employeeData } =
    useGetListEmployeeWithoutPagination();
  const {
    listTaglineDontByOrderStatusRefetch,
    listTaglineDontByOrderStatusData,
  } = useGetListTaglineDontByOrderStatus();

  const { fetchHistoryOrder, orderHistoryState } = UseGetOrderHistory(+orderId);
  const { cityRefetch, cityData } = useGetCities();
  const { allDistrictsData, allDistrictsRefetch } = useGetAllDistricts();
  useEffect(() => {
    if (orderId) {
      fetchHistoryOrder();
    }
  }, [orderId, isOpen]);

  useEffect(() => {
    if (isOpen) {
      listTaglineDontByOrderStatusRefetch();
      employeeRefetch();
      cityRefetch();
      allDistrictsRefetch();
    }
  }, [isOpen]);

  return (
    <Modal
      title="Lịch sử mua hàng"
      width={780}
      centered
      maskClosable={false}
      open={isOpen}
      onCancel={onClose}
      destroyOnHidden
      footer={null}
      className={`
        [&_.ant-modal-body]:max-h-[640px]
        [&_.ant-modal-body]:h-full
        [&_.ant-modal-body]:px-[16px]
        [&_.ant-modal-body]:overflow-y-auto
        [&_.ant-modal-body]:overflow-x-hidden
      `}
    >
      <div className="py-3">
        <HistoryComponent
          historyData={orderHistoryState?.data}
          diffKey="diffData"
          listMapping={{
            shopSent: {
              data: listData?.listShop,
              key: "shopId",
              field: "name",
            },
            orderStatusId: {
              data: listData?.orderStatus,
              key: "orderStatusId",
              field: "name",
            },
            shipping: {
              data: listData?.delivery,
              key: "deliveryPartnerId",
              field: "name",
            },
            voucherId: {
              data: listData?.promotion,
              key: "promotionId",
              field: "promotionName",
            },
            orderSourceId: {
              data: listData?.orderSource,
              key: "orderSourceId",
              field: "name",
            },
            orderType: {
              data: orderTypeOptions,
              key: "value",
              field: "label",
            },
            channel: {
              data: channelOptions,
              key: "value",
              field: "label",
            },
            assignedEmployeeId: {
              data: employeeData?.data,
              key: "employeeId",
              field: "name",
            },
            orderStatusTaglineId: {
              data: listTaglineDontByOrderStatusData?.data,
              key: "taglineId",
              field: "name",
            },
            cityId: {
              data: cityData?.data,
              key: "cityId",
              field: "name",
            },
            districtId: {
              data: allDistrictsData?.data,
              key: "districtId",
              field: "name",
            },
          }}
          listlabelMapping={OrderFormFieldLabels}
        />
      </div>
    </Modal>
  );
}
