import React, { useRef } from "react";

import { Icon } from "components/atoms/icon";
import { onTriggerCollaped } from "helpers/accordion";
import { mapModifiers } from "helpers/component";
import useDidMount from "helpers/react-hooks/useDidMount";

type Modifier = "default" | "highlight";
export interface Props {
  type?: Modifier | [];
  children?: React.ReactNode;
  title: string;
  deletable?: boolean;
  onDelete?: () => void;
}

export const Accordion: React.FC<Props> = ({
  type,
  title,
  deletable = false,
  children,
  onDelete,
}) => {
  const headerRef = useRef<HTMLDivElement | null>(null);
  const contentRef = useRef<HTMLDivElement | null>(null);

  useDidMount(() => {
    if (!headerRef.current || !contentRef.current) return undefined;
    const onTriggerEvent = () => {
      onTriggerCollaped(headerRef.current, contentRef.current);
    };
    headerRef.current.addEventListener("click", onTriggerEvent);
    return () => {
      headerRef.current?.removeEventListener("click", onTriggerEvent);
    };
  });

  return (
    <div className={mapModifiers("o-accordion", type)}>
      {deletable && (
        <div className="o-accordion_close" aria-hidden onClick={onDelete}>
          <Icon iconName="close-white" />
        </div>
      )}
      <div ref={headerRef} className="o-accordion_collapse">
        <span className="o-accordion_title">{title}</span>
        <Icon iconName={type === "default" ? "caret-down" : "plus"} />
      </div>
      <div className="o-accordion_wrapped">
        <div ref={contentRef} className="o-accordion_content">
          {children}
        </div>
      </div>
    </div>
  );
};

Accordion.defaultProps = { type: "default" };
