import useDidMount from "helpers/react-hooks/useDidMount";
import { useInfinityParams } from "hooks/useInfinityParams";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { mapFrom } from "libs/adapters/dto";
import { storageDto, StorageDtoType, storageOption } from "modules/location";
import { getListStorage } from "services/crm/system";

export const usePulldownListStorage = () => {
  const {
    loadMoreWithParams,
    loadMore,
    state: { data: storageInfinity, loading },
  } = useInfinityParams<StorageDtoType[]>(
    (params: Parameters<typeof getListStorage>[0]) =>
      getListStorage(params).then((res) => ({
        ...res,
        data: {
          ...res.data,
          data: mapFrom(res.data.data, storageDto),
        },
      })),
    {
      pageSize: 10,
    }
  );

  const { options: storageOptions } = usePulldownHelper({
    dataSource: storageInfinity || [],
    optionCreator: storageOption,
  });

  useDidMount(() => {
    loadMoreWithParams({});
  });

  return {
    loadMoreStorageWithParams: loadMoreWithParams,
    loadMoreStorage: loadMore,
    storageOptions,
    storageLoading: loading,
  };
};
