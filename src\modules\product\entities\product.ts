import {
  Array,
  ExtendSchema,
  Model,
  ModelValue,
  Number,
  String,
} from "libs/domain";

import { ColorSchema } from "./color";
import { SizeSchema } from "./size";
import { SkuSchema } from "./sku";

export const ProductSchema = {
  _id: String(),
  productId: Number(),
  code: String(),
  name: String(),
  unitName: String(),
  categoryCode: String(),
  colors: Array(ExtendSchema(ColorSchema)),
  sizes: Array(ExtendSchema(SizeSchema)),
  sku: Array(ExtendSchema(SkuSchema)),
  priceAVG: Number(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  createdAt: (raw: any) => raw && new Date(raw),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => raw && new Date(raw),
};

export const ProductModel = new Model(ProductSchema);
export type ProductEntityType = ModelValue<typeof ProductModel>;
