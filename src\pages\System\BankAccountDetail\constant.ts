import * as Yup from "yup";

import { BankAccountItem } from "./vm";

export const inputValidationSchema = Yup.object({
  bankName: Yup.string().required("Vui lòng nhập tên ngân hàng"),
  accountNumber: Yup.string().required("<PERSON>ui lòng nhập số tài khoản"),
  owner: Yup.string().required("Vui lòng nhập chủ tài khoản"),
  branchName: Yup.string().required("Vui lòng nhập chi nhánh"),
});

export type BankAccountDetailFormType = BankAccountItem;
