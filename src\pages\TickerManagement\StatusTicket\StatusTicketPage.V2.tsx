import React, { useEffect, useState } from "react";
import {
  DeleteFilled,
  EditFilled,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Card, Input, Modal, Table, Tag, Typography } from "antd";
import { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
// import timezone from "dayjs/plugin/timezone";
// import utc from "dayjs/plugin/utc";
import _ from "lodash";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { showLoading } from "components/atoms/base/Spinner";
import { COLOR } from "constants/color";
import { useDeleteStatusTicket } from "./hooks/useCreateStatusTicket";
import { useGetStatusTicket } from "./hooks/useGetStatusTicket";
import { StatusTicketModal } from "./modal/StatusTicketModal";

// dayjs.extend(utc);
// dayjs.extend(timezone);
// const localTZ = Intl.DateTimeFormat().resolvedOptions().timeZone;

export interface StatusTicketDataType {
  key: string | number;
  ordinalNumber: number;
  statusTicketId: number;
  name: string;
  color: string;
  displayOrder: number;
  updatedAt: string;
  createdAt: string;
}

const { Title } = Typography;

export default function StatusTicketPage() {
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchText, setSearchText] = useState<string>("");
  const [dataSource, setDataSource] = useState<StatusTicketDataType[]>([]);

  const [isOpenModal, setOpenModal] = useState<boolean>(false);
  const [isDataModal, setDataModal] =
    useState<StatusTicketDataType | null>(null);

  const {
    refetch: refetchStatusTicket,
    data: dataStatusTicket,
    loading: loadingStatusTicket,
  } = useGetStatusTicket({
    pageNum: page,
    pageSize,
    searchText,
  });

  const handleOpenModal = (record?: StatusTicketDataType) => {
    if (record) {
      setDataModal(record);
    } else {
      setDataModal(null);
    }
    setOpenModal(true);
  };

  const searchDebounce = _.debounce((value: string) => {
    setSearchText(value);
  }, 500);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    searchDebounce(e.target.value);
  };

  const handleChangePageSize = (size: number) => {
    setPage(1);
    setPageSize(size);
  };

  const { deleteStatusTicketExe } = useDeleteStatusTicket();

  const handleConfirmDelete = (record: StatusTicketDataType) => {
    Modal.confirm({
      centered: true,
      title: "Xác nhận xoá",
      content: `Bạn có chắc chắn muốn xoá trạng thái "${record.name}" không?`,
      okText: "Xóa",
      okType: "primary",
      okButtonProps: {
        className: "bg-red-500 hover:!bg-red-700",
      },
      cancelText: "Hủy",
      onOk: () => {
        showLoading(true);
        deleteStatusTicketExe({
          statusTicketId: record.statusTicketId,
        })
          .then(() => {
            handleRefetch();
            showNotification({
              type: "success",
              message: "Xóa thành công",
            });
          })
          .catch((err) => {
            showNotification({
              type: "error",
              message: err.message,
            });
          })
          .finally(() => {
            showLoading(false);
          });
      },
    });
  };

  const handleRefetch = () => {
    refetchStatusTicket();
  };

  const columns: ColumnsType<StatusTicketDataType> = [
    {
      title: "STT",
      dataIndex: "ordinalNumber",
      key: "ordinalNumber",
      width: 100,
      align: "center",
    },
    {
      title: "Tên trạng thái",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Màu sắc",
      dataIndex: "color",
      key: "color",
      align: "center",
      width: 144,
      render: (value) => {
        return <Tag color={value} className="w-12 h-4 rounded-xl" />;
      },
    },
    {
      title: "Thứ tự hiển thi",
      dataIndex: "displayOrder",
      key: "displayOrder",
      align: "center",
      width: 144,
    },
    {
      title: "Cập nhật cuối",
      dataIndex: "updatedAt",
      key: "updatedAt",
      render: (value) => {
        return (
          // <span>{dayjs.utc(value).tz(localTZ).format("DD/MM/YYYY HH:mm")}</span>
          <span>{dayjs(value).format("DD/MM/YYYY HH:mm")}</span>
        );
      },
    },
    {
      title: "Thao tác",
      key: "action",
      align: "center",
      width: 120,
      render: (__, record) => {
        return (
          <div className="flex items-center gap-3 justify-center">
            <BaseButton
              type="primary"
              bgColor={COLOR.GREEN[500]}
              hoverColor={COLOR.GREEN[700]}
              icon={<EditFilled rev={undefined} />}
              onClick={() => {
                handleOpenModal(record);
              }}
            />
            <BaseButton
              type="primary"
              bgColor={COLOR.RED[500]}
              hoverColor={COLOR.RED[700]}
              icon={<DeleteFilled rev={undefined} />}
              onClick={() => handleConfirmDelete(record)}
            />
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    if (dataStatusTicket) {
      const { data } = dataStatusTicket ?? {};
      const convertData = data.map((item, index: number) => {
        return {
          ...item,
          key: item.statusTicketId,
          ordinalNumber: (page - 1) * pageSize + index + 1,
        };
      });
      setDataSource(convertData);
    } else {
      setDataSource([]);
    }
  }, [dataStatusTicket]);

  useEffect(() => {
    refetchStatusTicket();
  }, [page, pageSize, searchText]);

  return (
    <>
      <Card className="m-4">
        <div className="flex flex-col gap-3">
          <Title level={3}>Danh sách trạng thái Ticket</Title>

          <div className="flex flex-col gap-3 lg:flex-row lg:justify-between lg:items-center">
            <Input
              prefix={<SearchOutlined rev={undefined} />}
              placeholder="Tìm kiếm"
              className="w-full lg:w-96 h-10"
              onChange={handleInputChange}
            />
            <BaseButton
              type="primary"
              className="h-10"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              icon={<PlusOutlined rev={undefined} />}
              onClick={() => {
                handleOpenModal();
              }}
            >
              Tạo mới
            </BaseButton>
          </div>

          <Table
            loading={loadingStatusTicket}
            tableLayout="auto"
            scroll={{ x: "max-content" }}
            bordered
            dataSource={dataSource}
            columns={columns}
            pagination={{
              current: page,
              pageSize,
              onChange: (offset, limit) => {
                setPage(offset);
                setPageSize(limit);
              },
              pageSizeOptions: [5, 10, 20, 50, 100],
              total: dataStatusTicket?.meta?.totalRecords || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} items`,
              onShowSizeChange: (__, size) => handleChangePageSize(size),
            }}
          />
        </div>
      </Card>

      <StatusTicketModal
        open={isOpenModal}
        setOpen={setOpenModal}
        dataModal={isDataModal}
        setDataModal={setDataModal}
        refetch={handleRefetch}
      />
    </>
  );
}
