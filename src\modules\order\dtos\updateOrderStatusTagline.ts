import pick from "object.pick";

import { cleanEmptyString } from "helpers/object";
import { createMapper, force, fromSchema } from "libs/adapters/dto";

import { OrderStatusTaglineSchema } from "../entities";

export const updateOrderStatusTaglineDto = createMapper(
  fromSchema(
    pick(OrderStatusTaglineSchema, [
      "name",
      "description",
      "displayOrder",
      "color",
    ])
  ),

  force((data) => ({
    ...cleanEmptyString(data),
  }))
);

export type UpdateOrderStatusTaglineDtoType = ReturnType<
  typeof updateOrderStatusTaglineDto
>;
