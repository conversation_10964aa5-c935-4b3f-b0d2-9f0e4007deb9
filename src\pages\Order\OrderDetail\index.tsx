import { useCallback, useMemo } from "react";

import { LeftOutlined, SaveOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { useSearchParams, useParams } from "react-router";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { Button } from "components/atoms/button";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import { COLOR } from "constants/color";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";
import { OrderForm } from "modules/order";

import RootPageRouter from "..";
import { ChildrenPage } from "../types";
import { OrderDetailFormType, updateOrderValidateSchema } from "./constant";
import { OrderDetailPageVm } from "./vm";

const OrderDetailPage = () => {
  const [searchUrlPath] = useSearchParams();
  const { orderId } = useParams<PageParamsType<ChildrenPage["orderDetail"]>>();

  const pageActionType = searchUrlPath.get("action") || "view";
  const formMode = pageActionType === "edit" ? "editable" : "view-only";

  const {
    orderDetail,
    loading,
    handleUpdateOrderDetail,
    handleRefetchOrderDetail,
    updateOrderDetailState,
  } = OrderDetailPageVm({ orderId: Number(orderId) });

  const handleOnSubmitUpdateOrderDetail = useCallback(
    (formData: OrderDetailFormType) => {
      const { items } = formData ?? {};
      const convertItem = items.map((item) => ({
        ...item,
        skuId: Number(item.skuId),
        productId: Number(item.productId),
        // itemType: item.itemType || "SOLD",
      }));
      // console.log(formData, "formData");
      // handleUpdateOrderDetail({
      //   ...formData,
      //   totalAmount: formData.totalAmount,
      //   shippingAddressId: Number(formData.shippingAddress?.value),
      //   orderStatusId: Number(formData.orderStatus?.value),
      //   orderStatusTaglineId: Number(formData.orderStatusTagline?.value),
      //   returnOrderStatusId: Number(formData.returnStatus?.value),
      //   returnOrderStatusTaglineId: Number(formData.returnStatusTagline?.value),
      //   assignedEmployeeId: Number(formData.assignedEmployeeId?.value),
      //   orderSourceId: Number(formData.orderSource?.value),
      // });
      handleUpdateOrderDetail({
        ...formData,
        items: convertItem,
        birthDay: formData.birthDay
          ? dayjs(formData.birthDay).toISOString()
          : null,
        // shippingAddressId: Number(formData.shippingAddressId),
        orderStatusId: Number(formData.orderStatusId),
        orderStatusTaglineId: Number(formData.orderStatusTaglineId),
        returnOrderStatusId: Number(formData.returnStatus),
        returnOrderStatusTaglineId: Number(formData.returnStatusTagline),
        assignedEmployeeId: Number(formData.assignedEmployeeId),
        orderSourceId: Number(formData.orderSourceId),
      });
    },
    [handleUpdateOrderDetail]
  );

  const handleRefetch = (idOrder: number) => {
    handleRefetchOrderDetail({ orderId: idOrder });
  };

  const handleNavigationToExchangeOrderPage = (params: {
    orderId: number;
    orderStatusId: number;
  }) => {
    if (params.orderStatusId !== 21) {
      showNotification({
        type: "error",
        message: "Đơn hàng không đủ điều kiện để đổi hàng",
      });
      return;
    }
    RootPageRouter.gotoChild("exChangeOrder", {
      params: {
        orderId: params.orderId.toString(),
      },
      queryString: "?action=exchange",
    });
  };

  const goBack = () => {
    RootPageRouter.gotoChild("listOrder");
  };

  return (
    <SpinnerContainer animating={loading}>
      {/* <General> */}
      {orderDetail && (
        <OrderForm
          validationSchema={updateOrderValidateSchema}
          onSubmit={handleOnSubmitUpdateOrderDetail}
          initialOrderDetail={orderDetail}
          handleRefetch={handleRefetch}
          navigateToExchangePage={handleNavigationToExchangeOrderPage}
          mode={formMode}
          cancelButton={
            <BaseButton
              type="primary"
              className="w-fit"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[600]}
              icon={<LeftOutlined />}
              onClick={goBack}
            >
              {/* Quay lại */}
            </BaseButton>
          }
          submitButton={
            formMode === "editable" && (
              <BaseButton
                type="primary"
                htmlType="submit"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[600]}
                className="w-fit"
                disabled={updateOrderDetailState.loading}
                icon={<SaveOutlined />}
              >
                Lưu
              </BaseButton>
            )
          }
        />
      )}
      {/* </General> */}
    </SpinnerContainer>
  );
};

export default OrderDetailPage;
