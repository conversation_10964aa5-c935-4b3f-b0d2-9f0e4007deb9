import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import listBankService from "../service/listBank.service";

export const DeleteBankAccount = () => {
  const [deleteBankAccountExe] = useAsync(
    useCallback(
      (bankAccountId: number) =>
        listBankService.deleteBankAccount(bankAccountId),
      []
    )
  );
  return {
    deleteBankAccountExe,
  };
};

export const useGetListBankWithPagination = () => {
  const [bankRefetch, bankState] = useAsync(
    useCallback(() => listBankService.getListBankWithPagination(), [])
  );
  return {
    bankRefetch,
    bankData: bankState?.data?.data,
  };
};
