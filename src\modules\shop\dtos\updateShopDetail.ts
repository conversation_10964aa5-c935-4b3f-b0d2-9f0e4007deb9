import pick from "object.pick";

import { cleanEmptyString } from "helpers/object";
import { createMapper, force, fromSchema, merge } from "libs/adapters/dto";
import { mergeSchema } from "libs/domain";

import { ShopSchema } from "../entities";

export const updateShopDto = createMapper(
  fromSchema(
    mergeSchema(
      pick(ShopSchema, [
        "name",
        "type",
        "cityId",
        "districtId",
        "wardId",
        "address",
        "employeeIds",
        "displayOrder",
        "isFranchisedShop",
        "hotline",
        "bankAccountIds",
        "storageId",
        "shopCode",
        "activeStatus",
        "mail",
        "area",
        "posID",
        // "startActiveDate",
        // "endActiveDate",
        "printEx",
      ])
    )
  ),
  merge((data) => ({
    bankAccountIds: !data.bankAccountIds?.length ? [] : data.bankAccountIds,
    employeeIds: !data.employeeIds?.length ? [] : data.employeeIds,
  })),
  force((data) => ({
    ...cleanEmptyString(data),
  }))
);

export type UpdateShopDtoType = ReturnType<typeof updateShopDto>;
