import { createMapper, fromSchema } from "libs/adapters/dto";
import { mergeSchema } from "libs/domain";

import { OrderReturnStatusTaglineSchema } from "../entities";

export const orderReturnStatusTaglineItemListDto = createMapper(
  fromSchema(mergeSchema(OrderReturnStatusTaglineSchema))
);

export type OrderReturnStatusTaglineItemListDtoType = ReturnType<
  typeof orderReturnStatusTaglineItemListDto
>;
