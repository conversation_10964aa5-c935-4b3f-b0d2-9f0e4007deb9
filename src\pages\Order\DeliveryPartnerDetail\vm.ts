import { useCallback, useEffect } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import * as navigationHelper from "helpers/navigation";
import { useAsync } from "hooks/useAsync";
import {
  updateDeliveryPartnerDetailDto,
  UpdateDeliveryPartnerDetailDtoType,
  deliveryPartnerDetailDto,
} from "modules/order";
import {
  getDeliveryPartnerDetail,
  updateDeliveryPartnerDetail,
} from "services/crm/order";

export type deliveryPartnerDetailItem = UpdateDeliveryPartnerDetailDtoType;
interface DeliveryPartnerDetailPageVmProps {
  deliveryPartnerId: number;
}

export const DeliveryPartnerDetailPageVm = ({
  deliveryPartnerId,
}: DeliveryPartnerDetailPageVmProps) => {
  const [getDeliveryPartnerDetailExec, getDeliveryPartnerDetailState] =
    useAsync(
      useCallback(
        (params: { deliveryPartnerId: number }) =>
          getDeliveryPartnerDetail({ ...params }).then((res) =>
            deliveryPartnerDetailDto(res.data.data)
          ),
        []
      )
    );

  const [updateDeliveryPartnerExec, updateDeliveryPartnerState] = useAsync(
    updateDeliveryPartnerDetail,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Cập nhật thành công" });
        navigationHelper.goBack();
      }, []),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(error?.errors?.[0]?.code);

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const deliveryPartnerDetail = getDeliveryPartnerDetailState.data;

  const handleUpdateDeliveryPartnerDetail = useCallback(
    (rawPayload: Partial<UpdateDeliveryPartnerDetailDtoType>) => {
      if (!deliveryPartnerDetail?.deliveryPartnerId) return;
      const updateDeliveryPartnerPayload = updateDeliveryPartnerDetailDto({
        ...deliveryPartnerDetail,
        ...rawPayload,
      });

      updateDeliveryPartnerExec(
        deliveryPartnerDetail.deliveryPartnerId,
        updateDeliveryPartnerPayload
      );
    },
    [updateDeliveryPartnerExec, deliveryPartnerDetail]
  );

  useEffect(() => {
    getDeliveryPartnerDetailExec({ deliveryPartnerId });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deliveryPartnerId]);

  return {
    loading: getDeliveryPartnerDetailState.loading,
    deliveryPartnerData: getDeliveryPartnerDetailState.data,
    updateDeliveryPartnerState,
    handleUpdateDeliveryPartnerDetail,
  };
};
