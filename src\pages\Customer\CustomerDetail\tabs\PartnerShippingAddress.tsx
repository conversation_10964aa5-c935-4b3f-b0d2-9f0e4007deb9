import React, { useEffect } from "react";
import { Card, Typography } from "antd";
import { CustomerAddress } from "pages/Customer/Dto/customer.dto";

interface PartnerShippingAddressProps {
  activeKey: string;
  addressList: CustomerAddress[];
}

const { Title } = Typography;

export default function PartnerShippingAddress({
  activeKey,
  addressList,
}: PartnerShippingAddressProps) {
  console.log(addressList, "addressList");
  useEffect(() => {
    // DO SOMETHING WHEN ACTIVE KEY CHANGES
    // You can fetch or update data based on the activeKey here
  }, [activeKey]);
  return <div className="py-3"><PERSON>h sách địa chỉ giao hàng</div>;
}
