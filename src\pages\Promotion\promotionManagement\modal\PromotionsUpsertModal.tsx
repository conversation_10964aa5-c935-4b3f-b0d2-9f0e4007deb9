/* eslint-disable @typescript-eslint/no-explicit-any */
import { forwardRef, useImperativeHandle, useRef, useState } from "react";
import {
  Checkbox,
  DatePicker,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
} from "antd";
import dayjs from "dayjs";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { COLOR } from "constants/color";
import { PromotionDataType } from "../PromotionManagementPage";

export interface PromotionsUpsertModalRefType {
  open: (data?: PromotionDataType) => void;
  close: () => void;
}

export interface PromotionsUpsertModalProps {
  onSubmit: (values: any) => void;
}

const PromotionUpsertModal = forwardRef<
  PromotionsUpsertModalRefType,
  PromotionsUpsertModalProps
>((props, ref) => {
  const { onSubmit } = props;
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const promotionId = useRef<number | null>(null);

  useImperativeHandle(ref, () => ({
    open: (data?: PromotionDataType) => {
      setOpen(true);
      if (data) {
        const { startDate, endDate, promotionId: id, ...restData } = data ?? {};
        promotionId.current = id;
        form.setFieldsValue({
          ...restData,
          startDate: startDate ? dayjs(startDate) : null,
          endDate: endDate ? dayjs(endDate) : null,
        });
      }
    },
    close: handleClose,
  }));

  const handleClose = () => {
    promotionId.current = null;
    form.resetFields();
    setOpen(false);
  };

  const onFinish = (values: any) => {
    const payload = {
      ...values,
    };
    if (promotionId.current) {
      payload.promotionId = promotionId.current;
    }
    onSubmit(payload);
  };

  return (
    <Modal
      title="Thông tin chương trình khuyến mãi"
      open={open}
      width={680}
      centered
      destroyOnHidden
      maskClosable={false}
      onCancel={handleClose}
      footer={
        <Flex align="center" justify="end" gap={16}>
          <BaseButton type="text" onClick={handleClose}>
            Hủy
          </BaseButton>
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={() => form.submit()}
          >
            Lưu
          </BaseButton>
        </Flex>
      }
    >
      <Form
        form={form}
        onFinish={onFinish}
        layout="vertical"
        initialValues={{
          isActive: false,
        }}
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-center">
          <Form.Item
            label="Mã khuyến mãi"
            name="promotionCode"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập mã khuyến mãi",
              },
            ]}
          >
            <Input
              placeholder="Nhập mã khuyến mãi"
              disabled={!!promotionId.current}
            />
          </Form.Item>
          <Form.Item
            label="Tên khuyến mãi"
            name="promotionName"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập tên khuyến mãi",
              },
            ]}
          >
            <Input placeholder="Nhập tên khuyến mãi" />
          </Form.Item>
          <Form.Item
            label="Ngày bắt đầu"
            name="startDate"
            rules={[
              {
                message: "Vui lòng chọn ngày bắt đầu",
                required: true,
              },
            ]}
          >
            <DatePicker
              format="DD/MM/YYYY"
              className="w-full"
              placeholder="Chọn ngày bắt đầu"
            />
          </Form.Item>
          <Form.Item
            label="Ngày kết thúc"
            name="endDate"
            rules={[
              {
                message: "Vui lòng chọn ngày kết thúc",
                required: true,
              },
            ]}
          >
            <DatePicker
              format="DD/MM/YYYY"
              className="w-full"
              placeholder="Chọn ngày kết thúc"
            />
          </Form.Item>

          <Form.Item
            label="Giá trị khuyến mãi"
            name="promotionValue"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập giá trị khuyến mãi",
              },
            ]}
          >
            <InputNumber
              className="w-full"
              placeholder="Nhập giá trị khuyến mãi"
              formatter={(value) =>
                value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",") : ""
              }
              min={0}
            />
          </Form.Item>

          <Form.Item
            label="Loại khuyến mãi"
            name="promotionType"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn loại khuyến mãi",
              },
            ]}
          >
            <BaseSelect
              placeholder="Chọn loại khuyến mãi"
              options={[
                {
                  label: "Theo phần trăm (%)",
                  value: "percent",
                },
                {
                  label: "Theo số tiền (VND)",
                  value: "amount",
                },
              ]}
            />
          </Form.Item>

          <Form.Item noStyle name="isActive" valuePropName="checked">
            <Checkbox className="w-fit">Kích hoạt</Checkbox>
          </Form.Item>
          <div className="col-span-1 lg:col-span-2">
            <Form.Item name="description" label="Mô tả">
              <Input.TextArea rows={3} placeholder="Nhập mô tả" allowClear />
            </Form.Item>
          </div>
        </div>
        {/* Form fields go here */}
      </Form>
    </Modal>
  );
});

export default PromotionUpsertModal;
