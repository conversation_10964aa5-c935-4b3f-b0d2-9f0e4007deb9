$background_activemenu: $COLOR-DENIM-3;

.o-sidebar {
	$root: &;
	$animation: cubic-bezier(0.4, 0, 0.2, 1);
	$animation2: cubic-bezier(0.645, 0.045, 0.355, 1);
	$width_wrapmenu: rem(258);
	$width_wrapmenu-closed: rem(80);

	position: relative;

	@include pc-medium {
		width: $width_wrapmenu;
	}

	&_hamburger {
		position: relative;
		width: rem(28);
		height: rem(22);
		margin: rem(16) 0;
		cursor: pointer;
		transition: opacity 0.3s $animation;

		@include pc-medium {
			width: rem(32);
		}

		&:hover {
			opacity: 0.7;
		}

		span {
			position: absolute;
			left: 0;
			display: block;
			width: 100%;
			max-width: rem(32);
			height: rem(2);
			background-color: $COLOR-CHARLESTON-GREEN;
			border-radius: rem(20);
			transition: 0.4s $animation;

			&:first-child {
				transform: translateY(#{rem(12)}) translateX(0) rotate(45deg);
			}

			&:nth-child(2) {
				top: 50%;
				opacity: 0;
				transform: translateY(-50%);
			}

			&:nth-child(3) {
				top: 100%;
				transform: translateY(-#{rem(10)}) translateX(0) rotate(-45deg);
			}
		}

		#{$root}-closed & {
			span {
				&:first-child {
					top: 0;
					transform: translateY(0) translateX(0) rotate(0deg);
				}

				&:nth-child(2) {
					top: 50%;
					opacity: 1;
					transform: translateY(0);
				}

				&:nth-child(3) {
					top: 100%;
					transform: translateY(0);
				}
			}
		}

		#{$root}_wrapmenu & {
			margin: rem(35) 0;

			@include pc-medium {
				margin: rem(55) 0;
			}

			@include sp {
				display: none;
			}
		}
	}

	&_wrapmenu {
		width: $width_wrapmenu;
		min-width: $width_wrapmenu;
		padding: 0;
		margin: 0;
		background: $COLOR-WHITE;
		transition: 0.4s $animation;

		#{$root}-closed & {
			display: inline-block;
			width: $width_wrapmenu-closed;
			min-width: $width_wrapmenu-closed;
			box-shadow: 0px 4px 4px $COLOR-BLACK-3;
		}
	}

	&:not(#{$root}-closed) {
		.rc-menu {
			#{$root}_linksubmenu {
				position: relative;
				margin-left: 20px;

				&::before {
					position: absolute;
					top: 50%;
					left: -20px;
					width: 4px;
					height: 4px;
					content: "";
					background-color: $COLOR-QUARTZ;
					transition: 0.3s ease-in-out;
					transform: translateY(-50%);
				}
			}

			&-sub {
				.rc-menu-item-divider {
					margin-right: 20px;
					margin-left: 48px;

					&:last-of-type {
						display: none;
					}
				}
			}

			&-submenu-open {
				background-color: $COLOR-TRANSPARENT;
			}

			#{$root}_submenu {
				&#{$root}_submenu-active {
					background-color: $background_activemenu;
				}
			}
		}
	}

	&_linkmenu,
	&_linksubmenu {
		font-size: rem(15);
		color: $COLOR-QUARTZ;
		transition: border-color 0.3s, background 0.3s, padding 0.15s $animation2;

		&:hover {
			text-decoration: none;
		}
	}

	&_linkmenu {
		font-weight: normal;

		#{$root}_menu-active & {
			color: $COLOR-DENIM;
		}
	}

	&_linksubmenu {
		#{$root}_submenu-active & {
			color: $COLOR-DENIM;
		}
	}

	&_wraptitle {
		display: flex;
		align-items: center;
		width: 100%;
		overflow: hidden;
		color: $COLOR-QUARTZ;
		text-overflow: ellipsis;
		white-space: nowrap;
		cursor: pointer;
		transition: border-color 0.3s, background 0.3s, padding 0.15s $animation2;
		@include u-fw-bold;
	}

	&_wrapicon {
		#{$root}-closed & {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 64px;
			transform: translateY(0);
		}

		a {
			#{$root}-closed & {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100%;
			}
		}

		> div {
			width: rem(22);
			height: rem(22);
			margin-right: 5px;

			#{$root}-closed & {
				width: rem(30);
				height: rem(31);
				margin-right: 0;
				transition: font-size 0.15s cubic-bezier(0.215, 0.61, 0.355, 1),
					margin 0.3s $animation2, color 0.3s;
			}
		}
	}

	&_menu {
		transition: border-color 0.3s $animation2, background 0.3s $animation2,
			padding 0.15s $animation2;

		#{$root}-closed & {
			margin-bottom: 2px;
		}

		&:hover {
			#{$root}_linkmenu {
				color: $COLOR-DENIM;
			}
		}

		&-active {
			background-color: $background_activemenu;
		}

		&-tooltip {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	&_submenu {
		cursor: pointer;
		transition: border-color 0.3s $animation2, background 0.3s $animation2,
			padding 0.15s $animation2;
	}

	.u-tooltip {
		display: inline-block;
		width: 100%;
	}
}

.rc-menu {
	$rootPlugin: &;
	$rootSubmenu: #{$rootPlugin}-submenu;

	padding: 0;
	border: 0;
	box-shadow: none;

	&-item {
		.o-sidebar-closed & {
			padding: 0;
		}
	}

	#{$rootSubmenu}-title {
		display: flex;
		align-items: center;
		overflow: hidden;

		.o-sidebar-closed & {
			padding: 0 !important;
		}
	}

	&-inline {
		#{$rootSubmenu}-arrow {
			right: 24px;
			color: $COLOR-ROMAN-SILVER;

			@include u-fw-regular;

			&:before {
				content: "\f105";
			}
		}
	}

	#{$rootSubmenu}-arrow {
		.o-sidebar-closed & {
			display: none;
		}
	}

	#{$rootSubmenu}-active {
		background-color: $COLOR-TRANSPARENT;

		.o-sidebar-closed & {
			background-color: $background_activemenu;
		}

		#{$rootSubmenu}-title {
			background-color: $COLOR-TRANSPARENT;
		}
	}

	&-item-divider {
		margin-right: 20px;
		margin-left: 20px;
		background-color: $COLOR-ISABELLINE;
	}

	&-item-active {
		background-color: $COLOR-TRANSPARENT;

		.o-sidebar-closed & {
			background-color: $background_activemenu;
		}

		.o-sidebar_linksubmenu {
			color: $COLOR-DENIM;
		}
	}
}

.rc-menu-submenu-popup {
	z-index: z("sidebar", "submenupopup");

	.rc-menu-sub {
		min-width: rem(260);
		border: 0;
		box-shadow: 0px 4px 4px $COLOR-BLACK-3;

		&-item-active {
			background-color: $background_activemenu;
		}
	}
}
