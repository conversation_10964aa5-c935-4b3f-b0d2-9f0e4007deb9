import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { DeleteFilled } from "@ant-design/icons";
import {
  DatePicker,
  Descriptions,
  Flex,
  Form,
  Grid,
  InputNumber,
  Modal,
  Table,
  Tag,
  Typography,
} from "antd";
import { DefaultOptionType } from "antd/es/select";
import { ColumnsType } from "antd/es/table";
import dayjs, { Dayjs } from "dayjs";
import _, { debounce } from "lodash";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { BaseSelectWithFeatures } from "components/atoms/base/select/BaseSelectWithFeatures";
import { showLoading } from "components/atoms/base/Spinner";
import { COLOR } from "constants/color";
import { formatCurrencyWithSuffix } from "helpers/currency.helper";
import useDidMount from "helpers/react-hooks/useDidMount";
import { usePulldownAssignEmployee } from "modules/employee";
import { useGetListBankWithPagination } from "pages/System/ListBankAccount/hook";
import { GetCustomerProfileV2 } from "services/crm/customer";
import { PaymentOrderDto } from "services/crm/order/dto/payment-order.dto";
import {
  useCreatePaymentOrder,
  useDeletePaymentOrder,
  useGetListPaymentOrderByOrderId,
} from "services/crm/order/hooks/payment-order";
import { getPointExchange } from "services/crm/system";
import { PointExchangeDto } from "services/crm/system/dto/point-exchange.dto";

export interface PaymentOrderModalRefType {
  open: (orderId?: number) => void;
  close: () => void;
}

export interface PaymentOrderModalProps {
  orderCode: string;
  pay: number;
  remainingAmount: number;
  paid: number;
  customerId?: number;
  handleRefetch: (idOrder: number) => void;
  disabled?: boolean;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  // onSubmit: (values: any) => void;
}

type FormPaymentOrderType = {
  paymentMethod: string;
  amount: number;
  paymentDate: Dayjs;
  employeeId: number;

  bankAccountId?: DefaultOptionType;
  applyPoint?: number;
  // applyPointValue?: number;
};

interface PaymentOrderDataType
  extends Omit<
    PaymentOrderDto,
    "createdAt" | "updatedAt" | "deletedAt" | "paymentDate"
  > {
  key: string | number;
  ordinalNumber: number;

  createdAt: Date;
  paymentDate: Date;
  updatedAt: Date | null;
  deletedAt: Date | null;
}

export const paymentMethodOptions = [
  { label: "Chuyển khoản", value: "CK" },
  { label: "Tiền mặt", value: "TM" },
  { label: "Sử dụng điểm", value: "POINT" },
];

export const PaymentOrderModal = forwardRef<
  PaymentOrderModalRefType,
  PaymentOrderModalProps
>((props, ref) => {
  const {
    orderCode,
    paid,
    pay,
    remainingAmount,
    customerId,
    disabled = false,
    handleRefetch,
  } = props;
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  const isMobile = !screens.lg && !screens.xl && !screens.xxl;

  const [formPayment] = Form.useForm<FormPaymentOrderType>();

  const [open, setOpen] = useState(false);
  const [dataSource, setDataSource] = useState<PaymentOrderDataType[]>([]);
  const [bankOptions, setBankOptions] = useState<DefaultOptionType[]>([]);
  const [pointExchangeConfig, setPointExchangeConfig] =
    useState<PointExchangeDto | null>(null);

  const orderIdRef = useRef<number | null>(null);

  const { bankRefetch, bankData } = useGetListBankWithPagination();
  const { getCustomerExe, getCustomerState } = GetCustomerProfileV2();

  const paymentMethod = Form.useWatch("paymentMethod", formPayment);
  const amountWatch = Form.useWatch("amount", formPayment);
  const applyPointWatch = Form.useWatch("applyPoint", formPayment);
  const overLimit = (amountWatch || 0) > remainingAmount;

  const { createPaymentOrderExe } = useCreatePaymentOrder();
  const { paymentOrderRefetch, paymentOrderData, paymentOrderLoading } =
    useGetListPaymentOrderByOrderId({
      orderId: orderIdRef.current,
    });
  const { deletePaymentOrderExe } = useDeletePaymentOrder();

  const {
    loadMoreEmployee,
    assignEmployeeOptions,
    loadMoreEmployeeState,
    formatAssignEmployeeOption,
  } = usePulldownAssignEmployee();

  const onAssignEmployeePulldownInputChange = useCallback(
    debounce(
      (textSearch: string) => loadMoreEmployee({ name: textSearch }),
      200
    ),
    [loadMoreEmployee]
  );

  useDidMount(() => {
    loadMoreEmployee();
  });

  useImperativeHandle(ref, () => ({
    open: (orderId?: number) => {
      if (!orderId) return;
      setOpen(true);
      orderIdRef.current = orderId;
    },
    close: handleClose,
  }));

  const handleClose = () => {
    formPayment.resetFields();
    orderIdRef.current = null;
    setDataSource([]);
    setOpen(false);
  };

  const handleConfirmDelete = (paymentOrderId: number) => {
    Modal.confirm({
      title: "Xác nhận xóa",
      content: "Bạn có chắc chắn muốn xóa thanh toán này?",
      centered: true,
      okText: "Xóa",
      cancelText: "Hủy",
      onOk: () => {
        showLoading(true);
        deletePaymentOrderExe({
          paymentOrderId,
        })
          .then(() => {
            showNotification({
              type: "success",
              message: "Xóa thanh toán thành công",
            });
            getCustomerExe(customerId);
            paymentOrderRefetch();
            handleRefetch(orderIdRef.current!);
          })
          .catch((error) => {
            const errorApi = error?.response?.data?.errors;
            if (errorApi && errorApi.length > 0) {
              errorApi.forEach((err) => {
                showNotification({
                  type: "error",
                  message: err.detail || err.title || "Lỗi không xác định",
                });
              });
            }
          })
          .finally(() => {
            showLoading(false);
          });
      },
    });
  };

  const columns: ColumnsType<PaymentOrderDataType> = [
    {
      title: "STT",
      dataIndex: "ordinalNumber",
      key: "ordinalNumber",
      width: 80,
      align: "center",
    },
    {
      title: "Phương thức thanh toán",
      dataIndex: "paymentMethod",
      key: "paymentMethod",
      width: 168,
    },
    {
      title: "Ngân hàng",
      dataIndex: "bankAccountId",
      key: "bankAccountId",
      width: 168,
      render: (bankAccountId) => {
        const bankAccount = bankOptions.find(
          (option) => option.value === bankAccountId
        );
        return (
          <Typography.Text>
            {bankAccount ? bankAccount.label : "--"}
          </Typography.Text>
        );
      },
    },
    {
      title: "Số tiền",
      dataIndex: "amount",
      key: "amount",
      width: 144,
      align: "right",
      render: (value) => {
        return formatCurrencyWithSuffix(value, "VND");
      },
    },
    {
      title: "Nhân viên",
      dataIndex: "employeeId",
      key: "employeeId",
      width: 172,
      render: (__, record) => {
        const { employee } = record ?? {};
        return <Typography.Text>{employee?.name}</Typography.Text>;
      },
    },
    {
      title: "Ngày thanh toán",
      dataIndex: "paymentDate",
      key: "paymentDate",
      width: 144,
      align: "center",
      render: (value) => {
        return dayjs(value).format("DD/MM/YYYY HH:mm");
      },
    },
    {
      title: "Điểm quy đổi",
      dataIndex: "applyPoint",
      key: "applyPoint",
      width: 144,
      align: "center",
    },
    // {
    //   title: "Số tiền quy đổi",
    //   dataIndex: "applyPointValue",
    //   hidden: true,
    //   key: "applyPointValue",
    //   width: 120,
    //   align: "center",
    //   render: (value) => {
    //     return formatCurrencyWithSuffix(value, "VND");
    //   },
    // },
    {
      title: "Thao tác",
      fixed: "right",
      align: "center",
      key: "action",
      width: 120,
      render: (__, record) => {
        const { paymentOrderId } = record ?? {};
        return (
          <Flex align="center" justify="center" gap={8}>
            <BaseButton
              disabled={disabled}
              type="primary"
              bgColor={COLOR.RED[500]}
              hoverColor={COLOR.RED[700]}
              onClick={() => {
                handleConfirmDelete(paymentOrderId);
              }}
              icon={<DeleteFilled />}
            />
          </Flex>
        );
      },
    },
  ];

  const onFinishPaymentOrderForm = async (values: FormPaymentOrderType) => {
    if (!orderIdRef.current) {
      showNotification({
        type: "error",
        message: "Không tìm thấy ID đơn hàng",
      });
      return;
    }
    // const paymentDate = dayjs(values.paymentDate);
    // const now = dayjs();
    // const combineDate = paymentDate
    //   .hour(now.hour())
    //   .minute(now.minute())
    //   .second(now.second())
    //   .millisecond(now.millisecond());

    const convertValue = {
      ...values,
      bankAccountId: Number(values.bankAccountId?.value),
      paymentDate: values.paymentDate.toISOString(),
      orderId: orderIdRef.current,
      orderCode,
    };
    showLoading(true);
    createPaymentOrderExe(convertValue)
      .then(() => {
        formPayment.resetFields();
        showNotification({
          type: "success",
          message: "Tạo mới thanh toán thành công",
        });
        paymentOrderRefetch();
        getCustomerExe(customerId);
        handleRefetch(orderIdRef.current!);
      })
      .catch((error) => {
        const errorApi = error?.response?.data?.errors;
        if (errorApi && errorApi.length > 0) {
          errorApi.forEach((err) => {
            showNotification({
              type: "error",
              message: err.detail || err.title || "Lỗi không xác định",
            });
          });
        }
      })
      .finally(() => {
        showLoading(false);
      });
  };

  useEffect(() => {
    if (!open) return;
    if (bankData?.data && _.size(bankData?.data) > 0) {
      const options = bankData?.data.map((bank) => ({
        ...bank,
        label: `${bank.bankName} - ${bank.branchName}`,
        value: bank.bankAccountId,
      }));
      setBankOptions(options);
    } else {
      setBankOptions([]);
    }
  }, [open, bankData?.data]);

  useEffect(() => {
    if (open) {
      bankRefetch();
      if (orderIdRef.current) {
        paymentOrderRefetch();
      }
    }
  }, [open]);

  useEffect(() => {
    if (customerId) {
      getCustomerExe(customerId);
    }
  }, [customerId]);

  const loadPointExchangeConfig = async () => {
    try {
      const pointExchangeCofig = await getPointExchange();

      const response = pointExchangeCofig?.data?.data;
      if (!response?.data) {
        showNotification({
          type: "error",
          message: "Không tìm thấy cấu hình quy đổi điểm",
        });
        return;
      }
      setPointExchangeConfig(response?.data);
    } catch (error) {
      console.error("Error fetching point exchange configuration:", error);
      showNotification({
        type: "error",
        message: "Không thể tải cấu hình quy đổi điểm",
      });
    }
  };

  useEffect(() => {
    if (paymentMethod === "POINT") {
      loadPointExchangeConfig();
    } else {
      setPointExchangeConfig(null);
    }
  }, [paymentMethod]);

  useEffect(() => {
    if (_.size(paymentOrderData?.data) > 0) {
      const newDataSource = paymentOrderData.data.map((item, index) => ({
        ...item,
        key: item.paymentOrderId,
        ordinalNumber: index + 1,
        paymentDate: new Date(item.paymentDate),
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt),
        deletedAt: item.deletedAt ? new Date(item.deletedAt) : null,
      }));
      setDataSource(newDataSource);
    } else {
      setDataSource([]);
    }
  }, [paymentOrderData]);

  return (
    <Modal
      title={
        <Flex align="center" gap={8}>
          <Typography.Title className="mb-0" level={5}>
            Thanh toán đơn hàng
          </Typography.Title>
          <Tag color="blue">{orderCode}</Tag>
        </Flex>
      }
      onCancel={handleClose}
      centered
      maskClosable={false}
      destroyOnHidden
      width={isMobile ? "100%" : "70vw"}
      footer={
        <Flex align="center" justify="end" gap={8}>
          <BaseButton
            disabled={disabled}
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={() => {
              formPayment.submit();
            }}
          >
            Lưu
          </BaseButton>
        </Flex>
      }
      open={open}
      // styles={{
      //   body: {
      //     maxHeight: "75vh",
      //     overflowY: "hidden",
      //   },
      // }}
    >
      <div
        className={`py-3 flex flex-col gap-3 max-h-[75vh] ${
          isMobile ? "overflow-y-auto" : ""
        }`}
      >
        <Form
          form={formPayment}
          className="[&_.ant-form-item]:!mb-[24px]"
          layout="vertical"
          name="paymentOrderForm"
          initialValues={{
            paymentDate: dayjs(),
          }}
          onFinish={onFinishPaymentOrderForm}
          onValuesChange={(changedValues, allValues) => {
            // eslint-disable-next-line @typescript-eslint/no-shadow
            const { paymentMethod, applyPoint } = allValues;

            // Nếu là POINT thì tự động tính amount = applyPoint * 1000
            if (paymentMethod === "POINT") {
              const applyPointValue = applyPoint
                ? applyPoint *
                  Number(
                    pointExchangeConfig?.conversionRateFromPointToMoney || 1000
                  )
                : 0;
              const formattedValue = parseFloat(applyPointValue.toFixed(8)); // Giữ 8 số thập phân

              // Cập nhật amount và applyPointValue cùng lúc
              formPayment.setFieldsValue({
                amount: formattedValue,
                // applyPointValue: formattedValue,
              });
            }
          }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-4 xl:grid-cols-4 gap-x-4 items-center">
            <Form.Item
              label="Phương thức thanh toán"
              name="paymentMethod"
              rules={[
                {
                  required: true,
                  message: "Vui lòng chọn phương thức thanh toán",
                },
              ]}
              help={
                paymentMethod === "POINT"
                  ? `1 điểm = ${formatCurrencyWithSuffix(
                      pointExchangeConfig?.conversionRateFromPointToMoney ||
                        1000,
                      "VND"
                    )}`
                  : undefined
              }
            >
              <BaseSelect
                placeholder="Chọn phương thức thanh toán"
                onChange={() => {
                  formPayment.setFieldsValue({
                    amount: 0,
                    applyPoint: 0,
                    bankAccountId: undefined,
                    // applyPointValue: 0,
                  });
                }}
                options={paymentMethodOptions}
              />
            </Form.Item>
            <div className="col-span-1 lg:col-span-2">
              <Form.Item
                label="Ngân hàng"
                name="bankAccountId"
                rules={
                  paymentMethod === "CK"
                    ? [
                        {
                          required: true,
                          message: "Vui lòng chọn ngân hàng",
                        },
                      ]
                    : []
                }
              >
                <BaseSelect
                  labelInValue
                  disabled={paymentMethod !== "CK"}
                  placeholder="Chọn ngân hàng"
                  options={bankOptions}
                />
              </Form.Item>
            </div>

            <Form.Item
              label="Ngày thanh toán"
              name="paymentDate"
              rules={[
                {
                  required: true,
                  message: "Vui lòng chọn ngày thanh toán",
                },
              ]}
            >
              <DatePicker
                className="w-full"
                format="DD/MM/YYYY"
                placeholder="Chọn ngày thanh toán"
              />
            </Form.Item>

            <Form.Item
              label="Nhân viên"
              name="employeeId"
              rules={[
                {
                  required: true,
                  message: "Vui lòng chọn nhân viên",
                },
              ]}
            >
              <BaseSelectWithFeatures
                triggerLoadMore={() => loadMoreEmployee()}
                loading={loadMoreEmployeeState.loading}
                showSearch
                placeholder="Chọn nhân viên"
                filterOption={false}
                onSearch={(value) => {
                  onAssignEmployeePulldownInputChange(value);
                }}
                options={assignEmployeeOptions}
              />
            </Form.Item>

            <Form.Item
              label="Điểm quy đổi"
              // label={
              //   <Typography>
              //     Điểm quy đổi
              //     {paymentMethod === "POINT" ? (
              //       <Typography.Text type="secondary" className="ml-1">
              //         (1 điểm ={" "}
              //         {formatCurrencyWithSuffix(
              //           pointExchangeConfig?.conversionRateFromPointToMoney ||
              //             0,
              //           "VND"
              //         )}
              //         )
              //       </Typography.Text>
              //     ) : undefined}
              //   </Typography>
              // }
              name="applyPoint"
              hidden={paymentMethod !== "POINT"}
              hasFeedback
              rules={[
                {
                  required: paymentMethod === "POINT",
                  message: "Vui lòng nhập số điểm",
                },
                {
                  validator: (__, value) => {
                    const memberPoints = getCustomerState?.data?.memberPoints;
                    if (value > memberPoints) {
                      return Promise.reject(
                        new Error(
                          "Số điểm sử dụng không được vượt quá số điểm hiện có"
                        )
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <InputNumber
                className="w-full"
                placeholder="Nhập số điểm"
                min={0}
              />
            </Form.Item>

            <Form.Item
              label="Số tiền"
              name="amount"
              rules={[
                {
                  required: true,
                  message: "Vui lòng nhập số tiền",
                },
                {
                  validator: (__, value) => {
                    if (value > remainingAmount) {
                      return Promise.reject(
                        new Error(
                          `Số tiền không được vượt quá số tiền còn lại (${formatCurrencyWithSuffix(
                            remainingAmount,
                            "VND"
                          )})`
                        )
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
              hasFeedback
              // validateStatus={overLimit ? "warning" : undefined}
              // help={
              //   overLimit
              //     ? `Tổng tiền vượt quá số tiền còn lại (${formatCurrencyWithSuffix(
              //         remainingAmount,
              //         "VND"
              //       )})`
              //     : undefined
              // }
            >
              <InputNumber
                disabled={paymentMethod === "POINT"}
                className="w-full"
                placeholder="Nhập số tiền"
                min={0}
                addonAfter="VND"
                formatter={(value) =>
                  `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                }
              />
            </Form.Item>

            {/* <Form.Item hidden name="applyPointValue">
              <InputNumber
                disabled
                className="w-full"
                placeholder="0"
                min={0}
              />
            </Form.Item> */}

            {/* <Form.Item
              hidden
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.applyPoint !== currentValues.applyPoint
              }
            >
              {({ getFieldValue }) => {
                const applyPoint = getFieldValue("applyPoint");
                const applyPointValue = applyPoint ? applyPoint * 1000 : 0;
                formPayment.setFieldsValue({
                  applyPointValue,
                });
                return (
                  <Form.Item label="Số tiền quy đổi" name="applyPointValue">
                    <InputNumber
                      disabled
                      className="w-full"
                      placeholder="0"
                      min={0}
                      addonAfter="VND"
                      formatter={(value) =>
                        `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                      }
                      // parser={(value) => value.replace(/₫\s?|(,*)/g, "")}
                    />
                  </Form.Item>
                );
              }}
            </Form.Item> */}
          </div>
        </Form>

        {/* <div className="my-2 border-y border-gray-200 py-2"> */}
        <Descriptions
          layout="horizontal"
          bordered
          size="small"
          column={screens.lg ? 2 : 1}
          items={[
            {
              label: "Tổng điểm hiện có",
              children: (
                <Typography.Text className="font-medium text-green-500">
                  {getCustomerState?.data?.memberPoints}
                </Typography.Text>
              ),
            },
            {
              label: "Tổng số tiền",
              children: formatCurrencyWithSuffix(pay, "VND"),
            },
            {
              label: "Số tiền đã thanh toán",
              children: formatCurrencyWithSuffix(paid, "VND"),
            },
            {
              label: "Số tiền còn lại",
              children: formatCurrencyWithSuffix(remainingAmount, "VND"),
            },
          ]}
        />
        {/* </div> */}
        <div className={` ${!isMobile ? "flex-1 overflow-y-auto scroll" : ""}`}>
          <Table
            sticky={{ offsetHeader: 0 }}
            rowKey="key"
            // bordered
            loading={paymentOrderLoading}
            size="small"
            scroll={{
              x: "max-content",
            }}
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            // summary={(data) => {
            //   const totalAmount = data.reduce(
            //     (total, item) => total + item.amount,
            //     0
            //   );
            //   return (
            //     <Table.Summary fixed>
            //       <Table.Summary.Row>
            //         <Table.Summary.Cell index={0} align="center">
            //           Tổng
            //         </Table.Summary.Cell>
            //         <Table.Summary.Cell index={1} colSpan={1} />
            //         <Table.Summary.Cell index={2} align="right">
            //           <Typography className="font-semibold">
            //             {formatCurrencyWithSuffix(totalAmount, "VND")}
            //           </Typography>
            //         </Table.Summary.Cell>
            //         <Table.Summary.Cell index={3} colSpan={2} />
            //       </Table.Summary.Row>
            //     </Table.Summary>
            //   );
            // }}
          />
        </div>
      </div>
    </Modal>
  );
});
