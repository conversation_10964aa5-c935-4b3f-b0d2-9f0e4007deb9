/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable @typescript-eslint/no-explicit-any */

import { Suspense } from "react";
import { useSso } from "@dpt/react-pack";
import { Route, Routes } from "react-router";

import { PrivateRouteWrapper } from "helpers/private-route";
import { PublicRouteWrapper } from "helpers/public-route";
import { renderPages } from "libs/react";

import * as Pages from "./pages";

function AppRoutes() {
  const { isLoading } = useSso();
  return (
    isLoading || (
      <Suspense>
        <Routes>
          <Route element={<PrivateRouteWrapper />}>
            {renderPages(
              Pages.Home,
              Pages.ChangePasswordPage,
              Pages.ChatboxPage,
              Pages.CustomerPage,
              Pages.EmployeePage,
              Pages.LeadPage,
              Pages.OrderPage,
              Pages.SystemPage,
              Pages.UserProfilePage,
              Pages.ConfigureSystem,
              Pages.TickerManagement,
              Pages.Chat,
              Pages.Promotion
            )}
          </Route>
          <Route element={<PublicRouteWrapper />}>
            {renderPages(
              Pages.ResetPasswordPage,
              Pages.ForgotPasswordPage,
              Pages.LoginPage,
              Pages.Policy,
              Pages.SsoCallback
            )}
          </Route>
          <Route path="*" element={<div>404 Not Found</div>} />
        </Routes>
      </Suspense>
    )
  );
}
export default AppRoutes;
