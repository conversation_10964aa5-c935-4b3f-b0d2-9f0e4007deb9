import { use<PERSON><PERSON><PERSON>, useEffect, use<PERSON><PERSON><PERSON> } from "react";

import dayjs from "dayjs";
import { usePara<PERSON>, useSearchParams } from "react-router";

import imgSearchBlue from "assets/images/icons/search-blue.svg";
import { Button } from "components/atoms/button";
import { Divider } from "components/atoms/divider";
import { Heading } from "components/atoms/heading";
import { PhonefieldHookForm } from "components/atoms/phonefield";
import { Pulldown } from "components/atoms/pulldown";
import { Radio } from "components/atoms/radio";
import { Text } from "components/atoms/text";
import { Textareafield } from "components/atoms/textareafield";
import { Textfield, TextfieldHookForm } from "components/atoms/textfield";
import { Calendar } from "components/molecules/calendar";
import { Formfield } from "components/molecules/formfield";
import { Pagination } from "components/molecules/pagination";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Table, Tbody, Td, Th, <PERSON><PERSON>, Tr } from "components/organisms/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON> } from "components/organisms/tabs";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import { BasePageProps } from "helpers/component";
import { FormContainer } from "helpers/form";
import * as naviationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";
import PaginationSection from "pages/Common/paginationSection";

import { ChildrenPage } from "../types";
import { validationSchema } from "./constant";
import { CustomerDetailPageVm } from "./vm";

const IndexPage: React.FC<BasePageProps> = () => {
  const [searchParams] = useSearchParams();
  const { customerId: customerIdParam } =
    useParams<PageParamsType<ChildrenPage["customerDetail"]>>();
  const customerId = Number(customerIdParam);

  const pageActionType = searchParams.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  const {
    bindSilent,
    getCheckedValueGroup,
    setDate,
    getDate,
    profile,
    mainInfo,
    phoneNumber,
    email,
  } = CustomerDetailPageVm();

  const createPhoneNumber = useCallback(
    (formData: { phoneNumberInput: string }) =>
      phoneNumber.create({
        customerId,
        phoneNumber: formData.phoneNumberInput,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [customerId, phoneNumber.create]
  );

  const createEmail = useCallback(
    (formData: { emailInput: string }) =>
      email.create({
        customerId,
        email: formData.emailInput,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [customerId, email.create]
  );

  const handleUpdatePhoneNumber = useCallback(
    (formData: { phoneNumberInput: string }, customerPhoneNumberId: number) =>
      phoneNumber.handleUpdate("Bạn có chắc muốn cập nhật số điện thoại?", {
        customerId,
        customerPhoneNumberId,
        phoneNumber: formData.phoneNumberInput,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [customerId, phoneNumber.handleUpdate]
  );

  const handleUpdateEmail = useCallback(
    (formData: { emailInput: string }, customerEmailId: number) =>
      email.handleUpdate("Bạn có chắc muốn cập nhật email?", {
        customerId,
        customerEmailId,
        email: formData.emailInput,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [customerId, email.handleUpdate]
  );

  const handleDeletePhoneNumber = useCallback(
    (customerPhoneNumberId: number) =>
      phoneNumber.handleDelete("Bạn có chắc muốn xoá số điện thoại?", {
        customerId,
        customerPhoneNumberId,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [customerId, phoneNumber.handleDelete]
  );

  const handleDeleteEmail = useCallback(
    (customerEmailId: number) =>
      email.handleDelete("Bạn có chắc muốn xoá email?", {
        customerId,
        customerEmailId,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [customerId, email.handleDelete]
  );

  const handleUpdateMainInfo = useCallback(
    (formData: { nameInput: string }) =>
      mainInfo.update({
        customerId,
        name: formData.nameInput,
        gender: Number(getCheckedValueGroup("gender")!),
        birthDay: dayjs(getDate("birthDay")).format("YYYY-MM-DD"),
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [customerId, mainInfo.update]
  );

  useEffect(() => {
    if (customerId && !Number.isNaN(customerId)) {
      profile.get({ customerId });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    customerId,
    phoneNumber.creationState.data,
    email.creationState.data,
    phoneNumber.deletionState.data,
    email.deletionState.data,
  ]);

  return (
    <SpinnerContainer animating={profile.loading}>
      <General>
        <title key="title">Chi tiết khách hàng</title>
        <Section>
          <Heading type="h1">
            KHÁCH HÀNG:
            <span style={{ color: "rgb(22, 132, 185)" }}>
              &nbsp;{profile?.data?.name}
            </span>
          </Heading>
        </Section>

        <Section>
          <Tabs>
            <TabList>
              <Tab title="Thông tin khách hàng" style={{ textAlign: "center" }}>
                Thông tin khách hàng
              </Tab>
              <Tab title="Thông tin khách hàng" style={{ textAlign: "center" }}>
                Lịch sử mua hàng
              </Tab>
              <Tab title="Thông tin khách hàng" style={{ textAlign: "center" }}>
                Lịch sử SMS
              </Tab>
              <Tab title="Thông tin khách hàng" style={{ textAlign: "center" }}>
                Lịch sử tích lũy điểm
              </Tab>
            </TabList>

            <TabPanel>
              {profile.data && (
                <>
                  <Section>
                    <Heading type="h4">
                      KHÁCH HÀNG
                      <span style={{ color: "rgb(22, 132, 185)" }}>
                        &nbsp;{profile.data.name}
                      </span>
                    </Heading>
                  </Section>

                  <Section>
                    <Row>
                      {/* TODO: Missing API */}
                      <Col lg="5">
                        <div className="d-flex justify-content-between">
                          <div className="u-mb-10 u-mb-sm-15">
                            <Text>Tổng số lần mua:</Text>
                          </div>
                          <div>
                            <Text>0/0</Text>
                          </div>
                        </div>

                        <div className="d-flex justify-content-between">
                          <div className="u-mb-10 u-mb-sm-15">
                            <Text>Tổng SP đã mua / Số tiền:</Text>
                          </div>
                          <div>
                            <Text>0/0</Text>
                          </div>
                        </div>
                        <div className="d-flex justify-content-between">
                          <div className="u-mb-10 u-mb-sm-15">
                            <Text>Tổng SP đã mua / Số tiền trong năm:</Text>
                          </div>
                          <div>
                            <Text>0/0</Text>
                          </div>
                        </div>
                      </Col>

                      <Col lg="6" className="offset-lg-1 u-mb-10 u-mb-sm-15">
                        {/* TODO: Missing API */}
                        <Formfield label="Ghi chú" name="deliverynotes">
                          <Textareafield defaultValue="Ghi chú" disabled />
                        </Formfield>
                      </Col>
                    </Row>

                    <div
                      style={{
                        marginBottom: editMode ? 30 : undefined,
                      }}
                    >
                      <Row>
                        {/* NOTE: PHONE NUMBER LIST */}
                        <Col lg="6">
                          <Formfield label="Số điện thoại" name="phoneNumber">
                            {profile.data.phoneNumbers?.map(
                              (phoneItem) =>
                                phoneItem && (
                                  <FormContainer
                                    key={phoneItem.customerPhoneNumberId}
                                    validationSchema={
                                      validationSchema.phoneNumber
                                    }
                                    onSubmit={(formData: {
                                      phoneNumberInput: string;
                                    }) =>
                                      handleUpdatePhoneNumber(
                                        formData,
                                        phoneItem.customerPhoneNumberId
                                      )
                                    }
                                  >
                                    <div className="d-flex justify-content-between u-mb-15">
                                      <div className="flex-grow-1">
                                        <PhonefieldHookForm
                                          placeholder="Số điện thoại"
                                          name="phoneNumberInput"
                                          disabled={viewMode}
                                          defaultValue={phoneItem.phoneNumber}
                                        />
                                      </div>

                                      {editMode && (
                                        <div
                                          className="d-flex align-items-center u-justify-end flex-shrink-0 u-pl-10"
                                          style={{ width: 80 }}
                                        >
                                          {/* TODO: Implement transition */}
                                          <Button
                                            type="submit"
                                            buttonType="textbutton"
                                          >
                                            Sửa
                                          </Button>

                                          <div className="u-ml-10">
                                            <Button
                                              buttonType="textbutton"
                                              onClick={() =>
                                                handleDeletePhoneNumber(
                                                  phoneItem.customerPhoneNumberId
                                                )
                                              }
                                            >
                                              Xoá
                                            </Button>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </FormContainer>
                                )
                            )}
                          </Formfield>
                          <FormContainer
                            validationSchema={validationSchema.phoneNumber}
                            onSubmit={createPhoneNumber}
                          >
                            {!phoneNumber.isOpenCreationInput && editMode && (
                              <div className="d-flex justify-content-end">
                                <Button
                                  buttonType="textbutton"
                                  onClick={phoneNumber.toggleCreationInput}
                                >
                                  <span style={{ fontSize: 14 }}>
                                    + Thêm số điện thoại
                                  </span>
                                </Button>
                              </div>
                            )}

                            {phoneNumber.isOpenCreationInput && (
                              <>
                                <div className="d-flex">
                                  <div className="flex-grow-1">
                                    <PhonefieldHookForm
                                      name="phoneNumberInput"
                                      placeholder="Số điện thoại"
                                    />
                                  </div>
                                  <div
                                    className="flex-shrink-0"
                                    style={{ width: 80 }}
                                  />
                                </div>
                                <div style={{ width: "calc(100% - 80px)" }}>
                                  <Row className="u-mt-15 u-mr-negative-5 u-ml-negative-5 justify-content-end">
                                    <Col
                                      xl={{ order: 1, span: "auto" }}
                                      xs={{ order: 2, span: "12" }}
                                      className="u-pr-5 u-pl-5 u-mb-15"
                                    >
                                      <Button
                                        modifiers="secondary"
                                        buttonType="outline"
                                        fullwidth
                                        onClick={
                                          phoneNumber.toggleCreationInput
                                        }
                                      >
                                        Huỷ
                                      </Button>
                                    </Col>
                                    <Col
                                      xl={{ order: 2, span: "auto" }}
                                      xs={{ order: 1, span: "12" }}
                                      className="u-pr-5 u-pl-5 u-mb-15"
                                    >
                                      <Button
                                        type="submit"
                                        fullwidth
                                        isLoading={
                                          phoneNumber.creationState.loading
                                        }
                                      >
                                        Thêm
                                      </Button>
                                    </Col>
                                  </Row>
                                </div>
                              </>
                            )}
                          </FormContainer>
                        </Col>

                        {/* NOTE: EMAIL LIST */}
                        <Col lg="6">
                          <Formfield label="Email" name="email">
                            {profile.data.emails?.map(
                              (emailItem) =>
                                emailItem && (
                                  <FormContainer
                                    key={emailItem.customerEmailId}
                                    validationSchema={validationSchema.email}
                                    onSubmit={(formData: {
                                      emailInput: string;
                                    }) =>
                                      handleUpdateEmail(
                                        formData,
                                        emailItem.customerEmailId
                                      )
                                    }
                                  >
                                    <div className="d-flex justify-content-between u-mb-15">
                                      <div className="flex-grow-1">
                                        <TextfieldHookForm
                                          placeholder="Email"
                                          name="emailInput"
                                          disabled={viewMode}
                                          defaultValue={emailItem.email}
                                        />
                                      </div>

                                      {editMode && (
                                        <div
                                          className="d-flex align-items-center u-justify-end flex-shrink-0 u-pl-10"
                                          style={{ width: 80 }}
                                        >
                                          <Button
                                            type="submit"
                                            buttonType="textbutton"
                                          >
                                            Sửa
                                          </Button>

                                          <div className="u-ml-10">
                                            <Button
                                              buttonType="textbutton"
                                              onClick={() =>
                                                handleDeleteEmail(
                                                  emailItem.customerEmailId
                                                )
                                              }
                                            >
                                              Xoá
                                            </Button>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </FormContainer>
                                )
                            )}
                          </Formfield>
                          <FormContainer
                            validationSchema={validationSchema.email}
                            onSubmit={createEmail}
                          >
                            {!email.isOpenCreationInput && editMode && (
                              <div className="d-flex justify-content-end">
                                <Button
                                  buttonType="textbutton"
                                  onClick={email.toggleCreationInput}
                                >
                                  <span style={{ fontSize: 14 }}>
                                    + Thêm email
                                  </span>
                                </Button>
                              </div>
                            )}

                            {email.isOpenCreationInput && (
                              <>
                                <div className="d-flex">
                                  <div className="flex-grow-1">
                                    <TextfieldHookForm
                                      name="emailInput"
                                      placeholder="Email"
                                    />
                                  </div>
                                  <div
                                    className="flex-shrink-0"
                                    style={{ width: 80 }}
                                  />
                                </div>
                                <div style={{ width: "calc(100% - 80px)" }}>
                                  <Row className="u-mt-15 u-mr-negative-5 u-ml-negative-5 justify-content-end">
                                    <Col
                                      xl={{ order: 1, span: "auto" }}
                                      xs={{ order: 2, span: "12" }}
                                      className="u-pr-5 u-pl-5 u-mb-15"
                                    >
                                      <Button
                                        modifiers="secondary"
                                        buttonType="outline"
                                        fullwidth
                                        onClick={email.toggleCreationInput}
                                      >
                                        Huỷ
                                      </Button>
                                    </Col>
                                    <Col
                                      xl={{ order: 2, span: "auto" }}
                                      xs={{ order: 1, span: "12" }}
                                      className="u-pr-5 u-pl-5 u-mb-15"
                                    >
                                      <Button
                                        type="submit"
                                        fullwidth
                                        isLoading={email.creationState.loading}
                                      >
                                        Thêm
                                      </Button>
                                    </Col>
                                  </Row>
                                </div>
                              </>
                            )}
                          </FormContainer>
                        </Col>
                      </Row>
                    </div>
                    {editMode && <Divider type="solid" />}

                    {/* MAIN INFO */}
                    <div
                      style={{
                        marginTop: editMode ? 30 : 0,
                      }}
                    >
                      <FormContainer
                        validationSchema={validationSchema.name}
                        onSubmit={handleUpdateMainInfo}
                      >
                        <Row>
                          <Col lg="6" className="u-mb-10 u-mb-sm-15">
                            <Formfield label="Họ tên" name="name">
                              <TextfieldHookForm
                                placeholder="Họ tên"
                                name="nameInput"
                                disabled={viewMode}
                                defaultValue={profile.data.name}
                              />
                            </Formfield>
                          </Col>
                          <Col lg="6" className="u-mb-10 u-mb-sm-15">
                            <Formfield label="Ngày sinh" name="dateOfBirth">
                              <Calendar
                                disabled={viewMode}
                                defaultValue={new Date(profile.data.birthDay)}
                                onDateChange={(date) =>
                                  setDate("birthDay", date)
                                }
                              />
                            </Formfield>
                          </Col>
                        </Row>

                        <Formfield label="Giới tính" name="sex" />

                        <div className="d-flex align-items-center">
                          <div className="u-pr-13">
                            <Radio
                              onChange={bindSilent("gender")}
                              name="gender"
                              value={1}
                              defaultChecked={profile.data.gender === 1}
                              disabled={viewMode}
                            >
                              Nam
                            </Radio>
                          </div>
                          <Radio
                            onChange={bindSilent("gender")}
                            name="gender"
                            value={0}
                            defaultChecked={profile.data.gender === 0}
                            disabled={viewMode}
                          >
                            Nữ
                          </Radio>
                        </div>

                        <Section>
                          <div
                            className={`d-flex u-justify-end u-pt-${
                              editMode ? "10" : "0"
                            }`}
                          >
                            <Button
                              modifiers="secondary"
                              buttonType="outline"
                              onClick={naviationHelper.goBack}
                            >
                              QUAY LẠI
                            </Button>
                            {editMode && (
                              <div className="u-ml-20">
                                <Button
                                  type="submit"
                                  isLoading={mainInfo.stateOfUpdate.loading}
                                >
                                  CẬP NHẬT
                                </Button>
                              </div>
                            )}
                          </div>
                        </Section>
                      </FormContainer>
                    </div>
                  </Section>
                </>
              )}
            </TabPanel>

            {/* TODO: IMPLEMENT API ORDER HISTORY */}
            <TabPanel>
              <Section>
                <div style={{ maxWidth: 300, marginLeft: "auto" }}>
                  <Textfield
                    name="name"
                    id="name"
                    placeholder="Placeholder"
                    iconSrc={imgSearchBlue}
                  />
                </div>
              </Section>

              <Section>
                <Table>
                  <Thead>
                    <Tr>
                      <Th colSpan={3} modifiers="center" stickyLeft>
                        Ngày tạo
                      </Th>
                      <Th colSpan={3} modifiers="center">
                        Đơn hàng
                      </Th>
                      <Th colSpan={2} modifiers="center">
                        Giao hàng
                      </Th>
                      <Th colSpan={2} modifiers="center">
                        Nhân viên
                      </Th>
                      <Th colSpan={2} modifiers="center">
                        Khách hàng
                      </Th>
                      <Th colSpan={3} modifiers="center">
                        Địa chỉ
                      </Th>
                      <Th colSpan={3} modifiers="center">
                        Ghi chú
                      </Th>
                      <Th colSpan={3} modifiers="center">
                        Sản phẩm
                      </Th>
                      <Th colSpan={3} modifiers="center" stickyRight>
                        Thành tiền (SL)
                      </Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {Array(10)
                      .fill(0)
                      .map((_i, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <Tr key={index}>
                          <Td colSpan={3} modifiers="center" stickyLeft>
                            27/4/2021
                          </Td>
                          <Td colSpan={3} modifiers="center">
                            \ [T] /
                          </Td>
                          <Td colSpan={2} modifiers="center" />
                          <Td colSpan={2} modifiers="center" />
                          <Td colSpan={2} modifiers="center" />
                          <Td colSpan={3} modifiers="center" />
                          <Td colSpan={3} modifiers="center" />
                          <Td colSpan={3} modifiers="center" />
                          <Td colSpan={3} modifiers="center" stickyRight />
                        </Tr>
                      ))}
                  </Tbody>
                </Table>
              </Section>

              <Section>
                <PaginationSection
                  appearanceOption={
                    <Pulldown
                      placeholder="Số lượng hiển thị"
                      options={[
                        { label: "5", value: "5" },
                        { label: "10", value: "10" },
                        { label: "15", value: "15" },
                        { label: "20", value: "20" },
                      ]}
                    />
                  }
                  paginateOption={
                    <Pagination
                      defaultCurrentPage={1}
                      onPageChange={() => {}}
                      pageCount={5}
                      total={100}
                    />
                  }
                />
              </Section>
            </TabPanel>

            {/* TODO: IMPLEMENT API SMS HISTORY */}
            <TabPanel>
              <Section>
                <Table>
                  <Thead>
                    <Tr>
                      <Th colSpan={2} modifiers="center" stickyLeft>
                        Ngày tạo
                      </Th>
                      <Th colSpan={7} modifiers="center">
                        Nội dung
                      </Th>
                      <Th colSpan={3} modifiers="center" stickyRight>
                        Ngày gửi
                      </Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {Array(10)
                      .fill(0)
                      .map((_i, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <Tr key={index}>
                          <Td colSpan={2} modifiers="center" stickyLeft>
                            27/4/2021
                          </Td>
                          <Td colSpan={7} modifiers="center">
                            \ [T] /
                          </Td>
                          <Td colSpan={3} modifiers="center" stickyRight />
                        </Tr>
                      ))}
                  </Tbody>
                </Table>
              </Section>
              <Section>
                <PaginationSection
                  appearanceOption={
                    <Pulldown
                      placeholder="Số lượng hiển thị"
                      options={[
                        { label: "5", value: "5" },
                        { label: "10", value: "10" },
                        { label: "15", value: "15" },
                        { label: "20", value: "20" },
                      ]}
                    />
                  }
                  paginateOption={
                    <Pagination
                      defaultCurrentPage={1}
                      onPageChange={() => {}}
                      pageCount={5}
                      total={100}
                    />
                  }
                />
              </Section>
            </TabPanel>

            {/* TODO: IMPLEMENT API POINT ACCUMULATION HISTORY */}
            <TabPanel>
              <Section>
                <Text>Điểm tích lũy: 0</Text>
              </Section>

              <Section>
                <Table>
                  <Thead>
                    <Tr>
                      <Th colSpan={2} modifiers="center" stickyLeft>
                        Ngày tạo
                      </Th>
                      <Th colSpan={7} modifiers="center">
                        Nội dung
                      </Th>
                      <Th colSpan={3} modifiers="center" stickyRight>
                        Ngày gửi
                      </Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {Array(10)
                      .fill(0)
                      .map((_i, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <Tr key={index}>
                          <Td colSpan={2} modifiers="center" stickyLeft>
                            27/4/2021
                          </Td>
                          <Td colSpan={7} modifiers="center">
                            \ [T] /
                          </Td>
                          <Td colSpan={3} modifiers="center" stickyRight />
                        </Tr>
                      ))}
                  </Tbody>
                </Table>
              </Section>

              <Section>
                <PaginationSection
                  appearanceOption={
                    <Pulldown
                      placeholder="Số lượng hiển thị"
                      options={[
                        { label: "5", value: "5" },
                        { label: "10", value: "10" },
                        { label: "15", value: "15" },
                        { label: "20", value: "20" },
                      ]}
                    />
                  }
                  paginateOption={
                    <Pagination
                      defaultCurrentPage={1}
                      onPageChange={() => {}}
                      pageCount={5}
                      total={100}
                    />
                  }
                />
              </Section>
            </TabPanel>
          </Tabs>
        </Section>
      </General>
    </SpinnerContainer>
  );
};

export default IndexPage;
