import { useCallback } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import {
  createPointExchangeDTO,
  CreatePointExchangeDTOType,
} from "modules/system";
import { createPointExchange as createPointExchangeService } from "services/crm/system";

export const ConfiguringPointExchangePageVm = () => {
  const [createPointExchangeExec, createPointExchangeState] = useAsync(
    createPointExchangeService,
    {
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
      onSuccess: useCallback(() => {
        toastSingleMode({
          type: "success",
          message: "<PERSON>ập nhật thành công",
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, []),
    }
  );

  const createPointExchange = useCallback(
    (rawPayload: Partial<CreatePointExchangeDTOType>) => {
      const pointExchangePayload = createPointExchangeDTO({
        ...rawPayload,
      });
      createPointExchangeExec(pointExchangePayload);
    },
    [createPointExchangeExec]
  );

  return {
    createPointExchange,
    createPointExchangeState,
  };
};
