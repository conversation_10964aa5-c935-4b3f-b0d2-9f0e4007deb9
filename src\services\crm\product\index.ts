import crmDriverV1 from "../crm-driver-v1";

export const getListProduct = (payload: {
  pageNum: number;
  pageSize: number;
  filterProductCode?: string;
  filterProductName?: string;
  filterColorCode?: string;
  filterColorName?: string;
  filterSizeCode?: string;
  filterSizeName?: string;
  sortPrice?: "desc" | "asc";
  sortCreatedAt?: "desc" | "asc";
  filterProductIds?: number[];
}) =>
  crmDriverV1.post("/products/external/filters", {
    params: payload,
  });

export const getProductByShop = (payload: {
  storeCode: string;
  pageNum: number;
  pageSize: number;
  searchText?: string;
}) => {
  return crmDriverV1.get(
    `/products/external/item-stock-locations/get-by-shop/${payload.storeCode}`,
    {
      params: {
        pageNum: payload.pageNum,
        pageSize: payload.pageSize,
        searchText: payload.searchText,
      },
    }
  );
};

export const getProductByStoreCode = (payload: {
  storeCode: string;
  pageNum: number;
  pageSize: number;
  searchText?: string;
}) => {
  return crmDriverV1.get(
    `/products/external/item-stock-locations/get-by-store-code/${payload.storeCode}`,
    {
      params: {
        pageNum: payload.pageNum,
        pageSize: payload.pageSize,
        searchText: payload.searchText,
      },
    }
  );
};
