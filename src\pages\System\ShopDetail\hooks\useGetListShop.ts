/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import crmDriverV1 from "services/crm/crm-driver-v1";
import { GetListShopWithoutPaginationResponseDto } from "../dto/shop.dto";

export const UseGetListShop = () => {
  const [listShopExe, listShopState] = useAsync(
    useCallback(
      () =>
        crmDriverV1.get<GetListShopWithoutPaginationResponseDto>(
          "locations/external/shops"
        ),
      []
    )
  );
  return {
    listShopExe,
    listShopState: listShopState?.data?.data?.data || [],
  };
};

export const UseGetListShopV2 = () => {
  const [listShopExe, listShopState] = useAsync(
    useCallback(
      () =>
        crmDriverV1.get<GetListShopWithoutPaginationResponseDto>(
          "locations/external/shops"
        ),
      []
    )
  );
  return {
    listShopExe,
    listShopState: listShopState?.data?.data,
  };
};
