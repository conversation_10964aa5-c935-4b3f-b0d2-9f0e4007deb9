export interface GetDistrictsDto {
  cityId: number;
}

export interface GetCitiesResponseDto {
  data: CityDto[];
}

export interface CityDto {
  cityId: number;
  name: string;
  countryId: number;
  createdAt: string;
  updatedAt: string;
}

export interface GetDistrictResponseDto {
  data: DistrictDto[];
}

export interface DistrictDto {
  districtId: number;
  name: string;
  cityId: number;
  createdAt: string;
  updatedAt: string;
}
