import { Story, Meta } from "@storybook/react/types-6-0";

import imgPencil from "assets/images/icons/pencil.svg";
import imgRecycle from "assets/images/icons/recycle.svg";
import { Link } from "components/utils/link";

import { Table, TableProps, Tbody, Td, Th, Thead, Tr } from ".";

export default {
  title: "Components|organisms/Table",
  component: Table,
} as Meta;

const Template: Story<TableProps> = ({
  scroll,
  modofiers,
  fixedHeader,
  loading,
}) => (
  <Table
    scroll={scroll}
    modofiers={modofiers}
    fixedHeader={fixedHeader}
    loading={loading}
  >
    <Thead>
      <Tr>
        <Th colSpan={1} modifiers="center" stickyLeft>
          STT
        </Th>
        <Th colSpan={6} isSortable>
          Tên
        </Th>
        <Th colSpan={3} isSortable modifiers="center">
          <PERSON><PERSON><PERSON> c<PERSON><PERSON> nh<PERSON>t gần nhất
        </Th>
        <Th colSpan={3} modifiers="center" stickyRight>
          <PERSON>hao tác
        </Th>
      </Tr>
    </Thead>
    <Tbody>
      {Array(15)
        .fill(0)
        .map((_i, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <Tr key={index}>
            <Td colSpan={1} modifiers="center" stickyLeft>
              1
            </Td>
            <Td colSpan={6}>Trang chủ</Td>
            <Td colSpan={3} modifiers="center">
              01/01/2021
            </Td>

            <Td colSpan={3} modifiers="center" stickyRight>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  alignContent: "center",
                  maxWidth: 70,
                  margin: "auto",
                }}
              >
                <Link
                  to="/"
                  style={{
                    width: 30,
                    padding: 4,
                    margin: "auto",
                    border: "solid 1px #E8E8E8",
                  }}
                >
                  <img src={imgPencil} alt="" />
                </Link>
                <Link
                  to="/"
                  style={{
                    width: 30,
                    padding: 4,
                    marginLeft: 10,
                    border: "solid 1px #E8E8E8",
                  }}
                >
                  <img src={imgRecycle} alt="" />
                </Link>
              </div>
            </Td>
          </Tr>
        ))}
    </Tbody>
  </Table>
);

export const Normal = Template.bind({});
export const FixedHeader = Template.bind({});
export const FixedRight = Template.bind({});

const EmptyTemplate: Story<TableProps> = ({
  scroll,
  modofiers,
  fixedHeader,
  loading,
  hasData,
}) => (
  <Table
    scroll={scroll}
    modofiers={modofiers}
    fixedHeader={fixedHeader}
    loading={loading}
    hasData={hasData}
  >
    <Thead>
      <Tr>
        <Th colSpan={1} modifiers="center" stickyLeft>
          STT
        </Th>
        <Th colSpan={6} isSortable>
          Tên
        </Th>
        <Th colSpan={3} isSortable modifiers="center">
          Ngày cập nhật gần nhất
        </Th>
        <Th colSpan={3} modifiers="center" stickyRight>
          Thao tác
        </Th>
      </Tr>
    </Thead>
  </Table>
);

export const EmptyData = EmptyTemplate.bind({});

Normal.args = {
  scroll: { y: 500 },
  modofiers: "borderdotted",
  fixedHeader: false,
  loading: false,
};

EmptyData.args = {
  modofiers: "borderdotted",
  fixedHeader: false,
  loading: false,
  hasData: true,
};
