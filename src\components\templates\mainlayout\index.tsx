/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/interactive-supports-focus */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";

import {
  AlignLeftOutlined,
  BellOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  HomeOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import { useSso } from "@dpt/react-pack";
import {
  ContextMenuComponent,
  TreeViewComponent,
} from "@syncfusion/ej2-react-navigations";
import { Breadcrumb, FloatButton, Layout } from "antd";
import { ItemType } from "antd/es/breadcrumb/Breadcrumb";

import { useTranslation } from "react-i18next";
import imgAvatar from "assets/images/common/menuprofile/image.png";
import { Spinner } from "components/atoms/base/Spinner";
import {
  Notification,
  TNotification,
} from "components/atoms/basecom/Notification";
import { confirm } from "contexts/dialog";
import store, { useAppDispatch, useAppSelector } from "store";
import routeSlice from "store/route";
import { systemSlice } from "store/system/systemSlice";

import { MenuSider } from "../menuSider";
import { getMessageByUser, MenuContext } from "./context";

import { LanguageSwitcher } from "./LanguageSwitcher";

import "./index.css";

export const MainLayout = ({ children }: { children: React.ReactNode }) => {
  const { i18n, t } = useTranslation();
  const { signOut } = useSso();
  const employeeInfo = useAppSelector((state) => state.auth.employeeInfo);
  const route = useAppSelector((state) => state.route.route);
  const chatRef = useRef<HTMLDivElement>(null);
  const notiRef = useRef<TNotification>(null);
  const tree = useRef<TreeViewComponent>(null);
  const [isOpenProfileMenu, setOpenProfileMenu] = useState({
    message: false,
    notification: false,
  });
  const dispatch = useAppDispatch();
  const [isFullScreen, setIsFullScreen] = useState(false);

  const { getMenuContextExe, getMenuContextState: menuContext } = MenuContext();

  // eslint-disable-next-line
  const handleLogout = useCallback(() => {
    confirm({
      title: "Đăng xuất",
      contents: "Bạn có chắc muốn thoát khỏi hệ thống?",
      okLabel: "Đồng ý",
      cancelLabel: "Đóng",
    }).then(() => signOut());
  }, [signOut]);

  const toggleClick = async () => {
    store.dispatch(systemSlice.actions.toggleMenuCollapsedAction());
  };
  const openMessage = () => {
    setOpenProfileMenu((prev) => ({ ...prev, message: true }));
  };
  const openNotification = () => {
    setOpenProfileMenu((prev) => ({ ...prev, notification: true }));
  };

  const closeMessage = () => {
    setOpenProfileMenu((prev) => ({ ...prev, message: false }));
  };
  const closeNotification = () => {
    setOpenProfileMenu((prev) => ({ ...prev, notification: false }));
  };

  // CONTEXT MENU
  const menuItem = [
    {
      text: "Open in new tab",
      iconCss: "e-icons e-bring-forward",
    },
  ];
  // eslint-disable-next-line
  const nodeTemplate = useCallback((data: any) => {
    return (
      <div>
        {!data?.nodeChild || data?.nodeChild.length === 0 ? (
          <ContextMenuComponent
            id={data.nodeId}
            items={menuItem}
            select={handleActionNode}
            target={`[data-uid='${data.nodeId}']`}
          />
        ) : (
          ""
        )}
        <span>{data.nodeText}</span>
      </div>
    );
  }, []);

  // eslint-disable-next-line
  const handleActionNode = (e: any) => {
    const data = tree.current?.getTreeData(e.element.parentNode.id);
    const routerLink = data![0].url as string;

    if (routerLink) {
      window.open(routerLink);
    }
  };

  const findParent = (nodeId: string, arr: ItemType[]) => {
    const node = menuContext.find((i: any) => i.no === nodeId);
    if (node && node.parentMenu) {
      const parent = menuContext.find((i: any) => i.no === node.parentMenu);
      arr.unshift({
        title: (
          <div className="flex items-center">
            {!parent.parentMenu ? (
              <HomeOutlined rev={undefined} className="mr-2" />
            ) : (
              ""
            )}
            <span>{parent.name}</span>
          </div>
        ),
      });
      findParent(parent.no, arr);
    } else {
      dispatch(routeSlice.actions.setRoute(arr));
    }
  };

  const handleSetRoute = (nodeId: string) => {
    if (!menuContext.some((i: any) => i.parentMenu === nodeId)) {
      const node = menuContext.find((i: any) => i.no === nodeId);
      findParent(nodeId, [
        {
          title: (
            <div className="flex items-center">
              <span>{node.name}</span>
            </div>
          ),
        },
      ]);
    }
  };

  const handleBackTopClick = useCallback(() => {
    const contentArea = document.querySelector(".ant-layout.overflow-auto");

    if (contentArea) {
      contentArea.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    } else {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  }, []);

  const useEnhancedEffect =
    typeof window !== "undefined" ? useLayoutEffect : useEffect;

  useEnhancedEffect(() => {
    document.onfullscreenchange = () =>
      setIsFullScreen(
        (document as any)[getBrowserFullscreenElementProp()] != null
      );

    return () => {
      (document as any).onfullscreenchange = undefined;
    };
  });

  function getBrowserFullscreenElementProp() {
    if (typeof (document as any).fullscreenElement !== "undefined") {
      return "fullscreenElement";
    }
    if (typeof (document as any).mozFullScreenElement !== "undefined") {
      return "mozFullScreenElement";
    }
    if (typeof (document as any).msFullscreenElement !== "undefined") {
      return "msFullscreenElement";
    }
    if (typeof (document as any).webkitFullscreenElement !== "undefined") {
      return "webkitFullscreenElement";
    }
    throw new Error("fullscreenElement is not supported by this browser");
  }

  /* View in fullscreen */
  function openFullscreen() {
    const elem = document.documentElement;

    if (elem.requestFullscreen) {
      elem.requestFullscreen();
    } else if ((elem as any).mozRequestFullScreen) {
      /* Firefox */
      (elem as any).mozRequestFullScreen();
    } else if ((elem as any).webkitRequestFullscreen) {
      /* Chrome, Safari and Opera */
      (elem as any).webkitRequestFullscreen();
    } else if ((elem as any).msRequestFullscreen) {
      /* IE/Edge */
      (elem as any).msRequestFullscreen();
    }
  }

  /* Close fullscreen */
  function closeFullscreen() {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if ((document as any).mozCancelFullScreen) {
      /* Firefox */
      (document as any).mozCancelFullScreen();
    } else if ((document as any).webkitExitFullscreen) {
      /* Chrome, Safari and Opera */
      (document as any).webkitExitFullscreen();
    } else if ((document as any).msExitFullscreen) {
      /* IE/Edge */
      (document as any).msExitFullscreen();
    }
  }

  function toggleFullScreen() {
    if (
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement
    ) {
      closeFullscreen();
    } else {
      openFullscreen();
    }
  }

  useEffect(() => {
    const onClickOutSide = (e: Event) => {
      if (
        isOpenProfileMenu.message &&
        !chatRef.current?.contains(e.target as Node)
      ) {
        closeMessage();
      }
      if (
        isOpenProfileMenu.notification &&
        !notiRef.current?.contains(e.target as Node)
      ) {
        closeNotification();
      }
    };
    window.addEventListener("click", onClickOutSide);
    return () => {
      window.removeEventListener("click", onClickOutSide);
    };
  }, [isOpenProfileMenu]);

  useEffect(() => {
    if (employeeInfo) {
      getMenuContextExe(employeeInfo?.site, employeeInfo?.userName);
      getMessageByUser(employeeInfo.userName, employeeInfo.site);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Spinner />

      <div className="control-section sidebar-tree">
        <div className="wrapper">
          <div className="col-lg-12 col-sm-12 col-md-12">
            <div className="main-header" id="header-section">
              <div className="row header-list flex justify-between items-center">
                {/* eslint-disable-next-line */}
                <div
                  className="col-3 pl-4 flex justify-start items-center"
                  id="hamburger"
                  onClick={toggleClick}
                >
                  <AlignLeftOutlined
                    className="toolbar-button"
                    rev={undefined}
                  />
                </div>
                <div className="col-9 header-style float-right support border-left flex justify-between">
                  <div className="">
                    <Breadcrumb
                      style={{ fontSize: "16px", padding: "5px 0" }}
                      items={
                        route.length
                          ? route
                          : [
                              {
                                title: (
                                  <div className="flex items-center">
                                    <HomeOutlined
                                      rev={undefined}
                                      className="mr-2"
                                    />
                                    <span>{t("home")}</span>
                                  </div>
                                ),
                              },
                            ]
                      }
                    />
                  </div>
                  <div className="header-style_profile">
                    <div
                      className="flex justify-center items-center"
                      role="button"
                    >
                      <LanguageSwitcher />
                    </div>
                    <div
                      className="header-style_profile_noti flex justify-center items-center"
                      onClick={toggleFullScreen}
                    >
                      {isFullScreen ? (
                        <FullscreenExitOutlined
                          className="toolbar-button"
                          rev={undefined}
                        />
                      ) : (
                        <FullscreenOutlined
                          className="toolbar-button"
                          rev={undefined}
                        />
                      )}
                    </div>
                    <div
                      className={`header-style_profile_noti flex justify-center items-center ${
                        isOpenProfileMenu.notification ? "active" : null
                      }`}
                      onClick={openNotification}
                      // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
                      tabIndex={0}
                    >
                      <BellOutlined
                        className="toolbar-button"
                        rev={undefined}
                      />
                      <span className="e-badge e-badge-circle e-badge-notification e-badge-overlap">
                        2
                      </span>
                    </div>
                    <div
                      className={`header-style_profile_message flex justify-center items-center ${
                        isOpenProfileMenu.message ? "active" : null
                      }`}
                      role="button"
                      tabIndex={0}
                      onClick={openMessage}
                    >
                      <MessageOutlined
                        className="toolbar-button"
                        rev={undefined}
                      />
                      <span className="e-badge e-badge-circle e-badge-notification e-badge-overlap">
                        14
                      </span>
                    </div>
                    <div className="header-style_profile_avatar flex px-2">
                      <div
                        className="flex flex-col px-2 items-end"
                        style={{ height: "100%" }}
                      >
                        <span
                          className="profile_name"
                          style={{
                            fontSize: "14px",
                            color: "#111827",
                          }}
                        >
                          {employeeInfo.name}
                        </span>
                        <span
                          style={{
                            fontSize: "12px",
                            color: "#6B7280",
                          }}
                        >
                          {employeeInfo.userName}
                        </span>
                      </div>
                      <img
                        className="rounded-full"
                        src={employeeInfo.picture || imgAvatar}
                        alt="Avatar"
                      />
                      <ul
                        className="profile_info"
                        style={{ lineHeight: "40px" }}
                      >
                        <li>Tài khoản của tôi</li>
                        <li>Đơn hàng của tôi</li>
                        <li style={{ borderTop: "1px solid #ddd" }}>
                          <div
                            role="button"
                            onClick={handleLogout}
                            tabIndex={0}
                          >
                            <span className="e-icons e-export mr-3" />
                            Đăng xuất
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {isOpenProfileMenu.notification ? (
              <Notification ref={notiRef} />
            ) : undefined}

            <Layout hasSider className="main-layout">
              <MenuSider />
              <Layout className="overflow-auto">
                {/* <General> */}
                {children}
                <FloatButton.BackTop
                  onClick={handleBackTopClick}
                  visibilityHeight={0}
                />
                {/* </General> */}
              </Layout>

              {/* <Layout className="overflow-auto ">{children}</Layout> */}
              {/* <div className="main-content" id="main-text">
                <div className="sidebar-content" style={{ overflow: "hidden" }}>
                  <General>{children}</General>
                </div>
              </div> */}
            </Layout>
            {/* <div>
              <SidebarMenu
                ref={sidebarRef}
                fontSize="12px"
                dataSource={convertMenu(menuContext)}
                onSetRoute={handleSetRoute}
              />
              <div className="main-content" id="main-text">
                <div className="sidebar-content" style={{ overflow: "hidden" }}>
                  <General>{children}</General>
                </div>
              </div>
            </div> */}
          </div>
        </div>
      </div>
    </>
  );
};
