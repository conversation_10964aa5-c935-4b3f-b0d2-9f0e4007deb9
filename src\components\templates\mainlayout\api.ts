/* eslint-disable prettier/prettier */
import PATH from "constants/paths";
import erpDriverV1 from "services/erp/erp-driver";

const crmMenu = [
  {
    nodeId: "CRM-Home",
    nodeText: "Dashboard",
    icon: "menu-dashboard",
    url: "/",
  },
  {
    nodeId: "CRM-Posts",
    nodeText: "Workspace",
    icon: "menu-workspace",
    url: PATH.WORKSPACE,
  },
];

// eslint-disable-next-line
export const convertMenu = (data: any, isNotMenu?: boolean) => {
  const lv1 = data.reduce(
    // eslint-disable-next-line
    (p: any, c: any) =>
      c.level === 1
        ? [
            ...p,
            {
              nodeId: c.no,
              nodeText: c.name,
              icon: `menu-${c.name
                .toLowerCase()
                .normalize("NFD")
                .replace(/[\u0300-\u036f]/g, "")
                .replace(/ +/g, "")}`,
              nodeChild: [],
            },
          ]
        : p,
    isNotMenu ? [] : crmMenu
  );

  const lv2 = data.reduce(
    // eslint-disable-next-line
    (p: any, c: any) =>
      c.level === 2
        ? [
            ...p,
            {
              nodeId: c.no,
              nodeText: c.name,
              url: c.formNo ? `/${c.formNo.replace("frm", "")}` : undefined,
              path: c.url,
              nodeChild: [],
              parentMenu: c.parentMenu,
            },
          ]
        : p,
    []
  );
  // eslint-disable-next-line
  const lv3 = data.reduce(
    // eslint-disable-next-line
    (p: any, c: any) =>
      c.level === 3
        ? [
            ...p,
            {
              nodeId: c.no,
              nodeText: c.name,
              url: c.formNo ? `/${c.formNo.replace("frm", "")}` : undefined,
              path: c.url,
              level: c.level,
              nodeChild: [],
              parentMenu: c.parentMenu,
            },
          ]
        : p,
    []
  );
  // eslint-disable-next-line
  const lv4 = data.reduce(
    // eslint-disable-next-line
    (p: any, c: any) =>
      c.level === 4
        ? [
            ...p,
            {
              nodeId: c.no,
              nodeText: c.name,
              url: c.formNo ? `/${c.formNo.replace("frm", "")}` : undefined,
              path: c.url,
              nodeChild: [],
              parentMenu: c.parentMenu,
            },
          ]
        : p,
    []
  );
  // eslint-disable-next-line
  const lv5 = data.reduce(
    // eslint-disable-next-line
    (p: any, c: any) =>
      c.level === 5
        ? [
            ...p,
            {
              nodeId: c.no,
              nodeText: c.name,
              url: c.formNo ? `/${c.formNo.replace("frm", "")}` : undefined,
              path: c.url,
              nodeChild: [],
              parentMenu: c.parentMenu,
            },
          ]
        : p,
    []
  );
  // eslint-disable-next-line no-restricted-syntax
  for (const i of lv5) {
    if (i.url || i.nodeChild.length !== 0) {
      // eslint-disable-next-line
      const index = lv4.findIndex((e: any) => e.nodeId === i.parentMenu);
      if (index !== -1) {
        if (i.path) {
          i.url = i.path;
        } else if (lv4[index].url) {
          i.url = `${lv4[index].url}${i.url}`;
        } else {
          i.url = `/${lv4[index].nodeText.split(" ").join("")}${i.url}`;
        }
        delete i.parentMenu;
        lv4[index].nodeChild.push(i);
      }
    }
    if (i.nodeChild.length) {
      delete i.url;
    }
  }
  // eslint-disable-next-line no-restricted-syntax
  for (const i of lv4) {
    if (i.url || i.nodeChild.length !== 0) {
      // eslint-disable-next-line
      const index = lv3.findIndex((e: any) => e.nodeId === i.parentMenu);
      if (index !== -1) {
        if (i.path) {
          i.url = i.path;
        } else if (lv3[index].url) {
          i.url = `${lv3[index].url}${i.url}`;
        } else {
          i.url = `/${lv3[index].nodeText.split(" ").join("")}${i.url}`;
        }
        delete i.parentMenu;
        lv3[index].nodeChild.push(i);
      }
    }
    if (i.nodeChild.length) {
      delete i.url;
    }
  }
  // eslint-disable-next-line no-restricted-syntax
  for (const i of lv3) {
    if (i.url || i.nodeChild.length !== 0) {
      // eslint-disable-next-line
      const index = lv2.findIndex((e: any) => e.nodeId === i.parentMenu);
      if (index !== -1) {
        if (i.path) {
          i.url = i.path;
        } else if (lv2[index].url) {
          i.url = `${lv2[index].url}${i.url}`;
        } else {
          i.url = `/${lv2[index].nodeText.split(" ").join("")}${i.url}`;
        }
        delete i.parentMenu;
        lv2[index].nodeChild.push(i);
      }
    }
    if (i.nodeChild.length) {
      delete i.url;
    }
  }
  // eslint-disable-next-line no-restricted-syntax
  for (const i of lv2) {
    if (i.url || i.nodeChild.length !== 0) {
      // eslint-disable-next-line
      const index = lv1.findIndex((e: any) => e.nodeId === i.parentMenu);
      if (index !== -1) {
        if (i.path) {
          i.url = i.path;
        } else if (lv1[index].url) {
          i.url = `${lv1[index].url}${i.url}`;
        } else {
          i.url = `/${lv1[index].nodeText.split(" ").join("")}${i.url}`;
        }
        delete i.parentMenu;
        lv1[index].nodeChild.push(i);
      }
    }
    if (i.nodeChild.length) {
      delete i.url;
    }
  }
  // eslint-disable-next-line
  return lv1.filter((i: any) => i.url || i.nodeChild.length !== 0);
};

// eslint-disable-next-line
export const convertSidebar = (data: any, isNotMenu?: boolean) => {
  const lv1 = data.reduce(
    // eslint-disable-next-line
    (p: any, c: any) =>
      c.level === 1
        ? [
            ...p,
            {
              nodeId: c.no,
              nodeText: c.name,
              icon: `menu-${c.name
                .toLowerCase()
                .normalize("NFD")
                .replace(/[\u0300-\u036f]/g, "")
                .replace(/ +/g, "")}`,
              nodeChild: [],
            },
          ]
        : p,
    isNotMenu ? [] : crmMenu
  );

  const lv2 = data.reduce(
    // eslint-disable-next-line
    (p: any, c: any) =>
      c.level === 2
        ? [
            ...p,
            {
              nodeId: c.no,
              nodeText: c.name,
              url: c.formNo ? `/${c.formNo.replace("frm", "")}` : undefined,
              nodeChild: [],
              parentMenu: c.parentMenu,
            },
          ]
        : p,
    []
  );
  // eslint-disable-next-line
  const lv3 = data.reduce(
    // eslint-disable-next-line
    (p: any, c: any) =>
      c.level === 3
        ? [
            ...p,
            {
              nodeId: c.no,
              nodeText: c.name,
              url: c.formNo ? `/${c.formNo.replace("frm", "")}` : undefined,
              level: c.level,
              nodeChild: [],
              parentMenu: c.parentMenu,
            },
          ]
        : p,
    []
  );
  // eslint-disable-next-line
  const lv4 = data.reduce(
    // eslint-disable-next-line
    (p: any, c: any) =>
      c.level === 4
        ? [
            ...p,
            {
              nodeId: c.no,
              nodeText: c.name,
              url: c.formNo ? `/${c.formNo.replace("frm", "")}` : undefined,
              nodeChild: [],
              parentMenu: c.parentMenu,
            },
          ]
        : p,
    []
  );
  // eslint-disable-next-line
  const lv5 = data.reduce(
    // eslint-disable-next-line
    (p: any, c: any) =>
      c.level === 5
        ? [
            ...p,
            {
              nodeId: c.no,
              nodeText: c.name,
              url: c.formNo ? `/${c.formNo.replace("frm", "")}` : undefined,
              nodeChild: [],
              parentMenu: c.parentMenu,
            },
          ]
        : p,
    []
  );
  // eslint-disable-next-line no-restricted-syntax
  for (const i of lv5) {
    // eslint-disable-next-line
    const index = lv4.findIndex((e: any) => e.nodeId === i.parentMenu);
    if (index !== -1) {
      delete i.parentMenu;
      lv4[index].nodeChild.push(i);
    }
  }
  // eslint-disable-next-line no-restricted-syntax
  for (const i of lv4) {
    // eslint-disable-next-line
    const index = lv3.findIndex((e: any) => e.nodeId === i.parentMenu);
    if (index !== -1) {
      delete i.parentMenu;
      lv3[index].nodeChild.push(i);
    }
  }

  // eslint-disable-next-line no-restricted-syntax
  for (const i of lv2) {
    // eslint-disable-next-line
    const index = lv1.findIndex((e: any) => e.nodeId === i.parentMenu);
    if (index !== -1) {
      if (i.url) {
        i.url = `/${lv1[index].nodeText.split(" ").join("")}${i.url}`;
      }
      delete i.parentMenu;
      lv1[index].nodeChild.push(i);
    }
  }
  // eslint-disable-next-line no-restricted-syntax
  for (const i of lv3) {
    // eslint-disable-next-line
    const index = lv2.findIndex((e: any) => e.nodeId === i.parentMenu);
    if (index !== -1) {
      if (i.url) {
        i.url = `/${lv2[index].nodeText.split(" ").join("")}${i.url}`;
      }
      delete i.parentMenu;
      lv2[index].nodeChild.push(i);
    }
  }
  return lv1;
};

export const getMenuContext = (site: string, username: string) => {
  // if (username === "admin") {
  //   return erpDriverV1.get(`/Menus/${site}/0/1/0`);
  // }
  // return erpDriverV1.get(`/Menus/ByUserSite/${site}/${username}/0/1/0`);
  return erpDriverV1.get(`/Menus/${site}/0/1/0`);
};
