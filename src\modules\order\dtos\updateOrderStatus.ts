import pick from "object.pick";

import { cleanEmptyString } from "helpers/object";
import { createMapper, force, fromSchema, merge } from "libs/adapters/dto";
import { mergeSchema } from "libs/domain";

import { OrderStatusSchema } from "..";

export const updateOrderStatusDto = createMapper(
  fromSchema(
    mergeSchema(
      pick(OrderStatusSchema, [
        "name",
        "isDefault",
        "displayOrder",
        "description",
        "prevOrderStatusIds",
        "color",
      ])
    )
  ),

  merge((data) => ({
    prevOrderStatusIds: !data.prevOrderStatusIds?.length
      ? null
      : data.prevOrderStatusIds,
    description: data.description || null,
  })),

  force((data) => ({
    ...cleanEmptyString(data),
  }))
);

export type UpdateOrderStatusDtoType = ReturnType<typeof updateOrderStatusDto>;
