/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import Button from "@material-ui/core/Button";
import ListItemIcon from "@material-ui/core/ListItemIcon";
import ListItemText from "@material-ui/core/ListItemText";
import MenuItem from "@material-ui/core/MenuItem";
import Popover from "@material-ui/core/Popover";
import Typography from "@material-ui/core/Typography";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import flagEN from "assets/images/flag/Flag_United.png";
import flagVN from "assets/images/flag/Flag_Vietnam.png";
import i18nSlice from "store/i18n";
import { useAppSelector } from "store/index";

interface ILanguage {
  id: string;
  title: string;
  flag: string;
}
const languages: ILanguage[] = [
  {
    id: "vi",
    title: "VietNam",
    flag: flagVN,
  },
  {
    id: "en",
    title: "English",
    flag: flagEN,
  },
];

export const LanguageSwitcher = () => {
  const { i18n } = useTranslation();
  const dispatch = useDispatch();
  const [menu, setMenu] = useState(null);
  const currentLanguageId = useAppSelector((state) => state.i18n.language);
  const currentLanguage = languages.find((lng) => lng.id === currentLanguageId);
  const langMenuClick = (event: any) => {
    setMenu(event.currentTarget);
  };

  const langMenuClose = () => {
    setMenu(null);
  };

  function handleLanguageChange(lng: ILanguage) {
    i18n.changeLanguage(lng.id);
    dispatch(i18nSlice.actions.changeLanguage(lng.id));
    langMenuClose();
  }

  return (
    <>
      <Button className="h-9" onClick={langMenuClick}>
        <img
          className="!w-9"
          src={currentLanguage?.flag}
          alt={currentLanguage?.id}
        />

        <Typography
          className="ml-2 !font-semibold !uppercase"
          color="textSecondary"
        >
          {currentLanguage?.id}
        </Typography>
      </Button>
      <Popover
        open={Boolean(menu)}
        anchorEl={menu}
        onClose={langMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
        classes={{
          paper: "py-2",
        }}
      >
        {languages.map((lng) => (
          <MenuItem key={lng.id} onClick={() => handleLanguageChange(lng)}>
            <ListItemIcon className="w-9 min-w-9 mr-3">
              <img className="w-9" src={lng.flag} alt={lng.title} />
            </ListItemIcon>
            <ListItemText primary={lng.title} />
          </MenuItem>
        ))}
      </Popover>
    </>
  );
};
