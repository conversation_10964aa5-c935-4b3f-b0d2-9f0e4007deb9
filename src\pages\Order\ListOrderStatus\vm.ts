import { useCallback, useEffect, useRef, useState } from "react";

import produce from "immer";

import { debounce } from "lodash";
import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import useInfinity from "hooks/useInfinity";
import { usePagination } from "hooks/usePagination";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import {
  createOrderStatusDto,
  CreateOrderStatusDtoType,
  orderStatusItemListDto,
  OrderStatusItemListDtoType,
  orderStatusOption,
} from "modules/order";
import {
  getListOrderStatus,
  createOrderStatus as createOrderStatusService,
  deleteOrderStatus,
} from "services/crm/order";

type ModalType = "createStatus";

interface State {
  modalState: {
    open: boolean;
    modalType?: ModalType;
  };
  pagination: {
    pageSize: number;
  };
  createModalState: {
    colorSelected?: string;
    isActiveToggle: boolean;
  };
  searchText: string;
}

export const OrderStatusListPageVm = () => {
  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      modalType: undefined,
    },
    pagination: {
      pageSize: 10,
    },
    createModalState: {
      colorSelected: "#000",
      isActiveToggle: false,
    },
    searchText: "",
  });
  const [searchInputValue, setSearchInputValue] = useState<string>("");
  const pageSizeRef = useRef<number>(state.pagination.pageSize);
  const isInitialMount = useRef<boolean>(true);
  const [getOrderStatusListExec, getOrderStatusListState] = useAsync(
    useCallback(
      (options: { pageNum: number; pageSize: number; searchText: string }) =>
        getListOrderStatus({
          ...options,
          ...(options.searchText && { inputSearch: options.searchText }),
        }).then((res) => ({
          data: mapFrom(res.data.data, orderStatusItemListDto),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const {
    gotoFirstPage,
    goNextPage,
    state: inifityState,
  } = useInfinity<OrderStatusItemListDtoType[]>(
    async (payload: { pageNum: number; pageSize: number }) =>
      getListOrderStatus({ ...payload }),
    {
      pageSize: 15,
    }
  );

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.modalType = type;
      })
    );
  }, []);

  const handleCloseModal = useCallback(() => {
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.modalType = undefined;
      })
    );
  }, []);

  const { gotoPage, ...orderStatusListPaginationState } = usePagination({
    pageSize: state.pagination.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getOrderStatusListExec({
        pageSize,
        pageNum: page,
        searchText: state.searchText,
      }),
  });

  const [createOrderStatusServiceExec, createOrderStatusState] = useAsync(
    createOrderStatusService,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Tạo mới thành công" });
        handleCloseModal();
        getOrderStatusListExec({
          pageSize: pageSizeRef.current,
          pageNum: 1,
          searchText: state.searchText,
        });
      }, [handleCloseModal]),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pagination.pageSize = pageSize;
      })
    );
  }, []);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) => {
      return state.modalState.open && state.modalState.modalType === type;
    },
    [state.modalState]
  );

  useEffect(() => {
    gotoPage(1);
    gotoFirstPage();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize]);

  const {
    sortedData: orderStatusListData,
    toggleSortState: toggleSortOrderBy,
  } = useSortable({
    data: getOrderStatusListState.data?.data,
    sortBy: {
      name: (data) => data.name,
      updatedAt: (data) => data.updatedAt,
    },
  });

  const { options: orderStatusOptionPulldown } = usePulldownHelper({
    dataSource: inifityState.data || [],
    optionCreator: orderStatusOption,
  });

  const onChangeColor = useCallback(
    (code: string) => {
      setState(
        produce((draft) => {
          draft.createModalState.colorSelected = code;
        })
      );
    },
    [setState]
  );
  const handleDeleteOrderStatus = useCallback((orderStatusId: number) => {
    deleteOrderStatus(orderStatusId).then((res) => {
      if (res.status === 200) {
        toastSingleMode({ type: "success", message: "Xóa thành công" });
        getOrderStatusListExec({
          pageSize: pageSizeRef.current,
          pageNum: 1,
          searchText: state.searchText,
        });
      }
    });
  }, []);

  const onChangeToggle = useCallback(
    (event) => {
      setState(
        produce((draft) => {
          draft.createModalState.isActiveToggle = event.target.checked;
        })
      );
      pageSizeRef.current = state.pagination.pageSize;
    },
    [setState]
  );

  const createOrderStatus = useCallback(
    async (rawPayload: Partial<CreateOrderStatusDtoType>) => {
      const orderPayload = createOrderStatusDto({
        ...rawPayload,
      });
      createOrderStatusServiceExec(orderPayload);
    },
    [createOrderStatusServiceExec]
  );

  const debouncedUpdateSearch = useCallback(
    debounce((searchText: string) => {
      console.log("🚀 Debounced setState with:", searchText);

      setState(
        produce((draft) => {
          draft.searchText = searchText;
        })
      );
    }, 500),
    []
  );

  const handleSearchInputChange = useCallback(
    (value: string) => {
      console.log("⌨️ Input onChange:", value);

      // Update input value ngay lập tức
      setSearchInputValue(value);

      // Debounce việc setState
      debouncedUpdateSearch(value);
    },
    [debouncedUpdateSearch]
  );

  const handleClearSearch = useCallback(() => {
    console.log("🗑️ Input cleared");

    setSearchInputValue("");
    debouncedUpdateSearch("");
  }, [debouncedUpdateSearch]);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // console.log("🔄 searchText changed, calling API:", state.searchText);

    gotoPage(1);
    getOrderStatusListExec({
      pageSize: state.pagination.pageSize,
      pageNum: 1,
      searchText: state.searchText,
    });
  }, [state.searchText]);

  useEffect(() => {
    return () => {
      debouncedUpdateSearch.cancel();
    };
  }, [debouncedUpdateSearch]);

  return {
    gotoPage,
    handleChangePageSize,
    toggleSortOrderBy,
    orderStatusListData: orderStatusListData || [],
    getOrderStatusListLoading: getOrderStatusListState.loading,
    pageSize: state.pagination.pageSize,
    orderStatusListPaginationState,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    colorSelected: state.createModalState.colorSelected,
    isActiveToggle: state.createModalState.isActiveToggle,
    createOrderStatus,
    createOrderStatusState,
    orderStatusOptionPulldown,
    onChangeColor,
    onChangeToggle,
    orderStatusLoadMore: goNextPage,
    handleDeleteOrderStatus,

    searchText: state.searchText,
    searchInputValue,
    handleSearchInputChange,
    handleClearSearch,
  };
};
