.ant-layout-sider-collapsed#menu-sider {
  flex: 0 0 80px !important;
  max-width: 80px !important;
  min-width: 80px !important;
  width: 80px !important;
}
#menu-sider .ant-menu-submenu-title {
  height: auto;
}
.ant-layout-sider-collapsed#menu-sider .ant-menu-submenu-title {
  margin-inline: unset;
  padding-inline: unset;
  display: flex;
  align-items: center;
  width: 100%;
}
.ant-layout-sider-collapsed#menu-sider .ant-menu-submenu-title .menu-icon {
  width: 100%;
  flex: 0 0 auto;
  display: flex;
  gap: 2px;
  flex-direction: column;
  padding: 2px;
}
.ant-layout-sider-collapsed#menu-sider
  .ant-menu-submenu-title
  .ant-menu-inline-collapsed-noicon {
  width: 100%;
}

.ant-layout-sider-collapsed#menu-sider .ant-menu-submenu-title .menu-icon p {
  display: block;
  width: 100%;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#menu-sider .ant-menu-submenu-title .menu-icon p {
  display: none;
}
#menu-sider .ant-menu-submenu-title img {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}
.ant-menu.ant-menu-sub.ant-menu-vertical {
  max-height: 300px;
}
