/* eslint-disable @typescript-eslint/no-explicit-any */
import { CreateShopDtoType, UpdateShopDtoType } from "modules/shop";
import crmDriverV1 from "services/crm/crm-driver-v1";

export const getShops = (payload?: { pageNum?: number; pageSize?: number }) =>
  crmDriverV1.get("/locations/external/shops", {
    params: { ...payload },
  });

export const getShopDetail = (payload: { shopId: number }) =>
  crmDriverV1.get(`/locations/external/shops/${payload.shopId}`);

// export const updateShopById = (shopId: number, payload: UpdateShopDtoType) =>
//   crmDriverV1.put(`/locations/external/shops/${shopId}`, { ...payload });
export const updateShopById = (shopId: number, payload: any) =>
  crmDriverV1.put(`/locations/external/shops/${shopId}`, { ...payload });

export const createShop = (payload: CreateShopDtoType) =>
  crmDriverV1.post("locations/external/shops", payload);

export const getListShop = () => {
  crmDriverV1.get("/locations/external/shops");
};
