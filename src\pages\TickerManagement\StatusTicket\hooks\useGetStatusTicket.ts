import { useCallback, useEffect, useMemo, useState } from "react";
import { debounce } from "lodash";
import { useAsync } from "hooks/useAsync";
import {
  GetListStatusTicketDto,
  StatusTicketDto,
} from "../dto/get-status-ticket.dto";
import statusTicketServices from "../services/status-ticket.service";

export const useGetStatusTicket = (dto: GetListStatusTicketDto) => {
  const [refetch, data] = useAsync(
    useCallback(() => statusTicketServices.getListStatusTicket(dto), [dto])
  );

  return {
    data: data?.data?.data,
    refetch,
    loading: data?.loading,
  };
};

export const useInfinityStatusTicket = ({
  pageSize = 10,
  searchText = "",
  enabled = true,
}: {
  pageSize?: number;
  searchText?: string;
  enabled?: boolean;
}) => {
  const [page, setPage] = useState(1);
  const [data, setData] = useState<StatusTicketDto[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [debouncedSearchText, setDebouncedSearchText] = useState<string>("");

  const fetchData = useCallback(async () => {
    if (!enabled || loading || !hasMore) return;

    setLoading(true);
    try {
      const response = await statusTicketServices.getListStatusTicket({
        pageNum: page,
        pageSize,
        searchText: debouncedSearchText,
      });
      const newItems = response.data?.data || [];
      const total = response?.data?.meta?.totalRecords || 0;

      setData((prev) => [...prev, ...newItems]);
      setTotalRecords(total);
      setHasMore((prev) => page * pageSize < total);
    } catch (err) {
      console.error("Failed to fetch status tickets:", err);
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, debouncedSearchText, enabled, loading, hasMore]);

  useEffect(() => {
    const handler = debounce(() => {
      const trimmed = searchText.trim();
      setDebouncedSearchText(trimmed);
    }, 400);

    handler();
    return () => {
      handler.cancel();
    };
  }, [searchText]);

  useEffect(() => {
    setData([]);
    setPage(1);
    setHasMore(true);
  }, [searchText, pageSize]);

  useEffect(() => {
    fetchData();
  }, [page, debouncedSearchText]);

  const loadMore = () => {
    if (!loading && hasMore) {
      setPage((prev) => prev + 1);
    }
  };

  return {
    data,
    loading,
    hasMore,
    totalRecords,
    loadMore,
    reset: () => {
      if (page !== 1) {
        setData([]);
      }
      setPage(1);
      setHasMore(true);
    },
  };
};
