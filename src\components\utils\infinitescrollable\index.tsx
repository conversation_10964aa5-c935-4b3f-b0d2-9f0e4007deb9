import React, { CSSProperties, useCallback, useMemo, useRef } from "react";

export type ScrollType = "top" | "left" | "right" | "bottom";
export interface InfiniteScrollableProps {
  height: CSSProperties["height"];
  width: CSSProperties["width"];
  scrollType?: ScrollType[] | ScrollType;
  offset?: { top?: number; bottom?: number; left?: number; right?: number };
  onTrigger: () => Promise<unknown>;
  className?: string;
}

interface TriggerRef {
  triggerPending: boolean;
}

export const InfiniteScrollable: React.FC<
  React.PropsWithChildren<InfiniteScrollableProps>
> = ({
  children,
  scrollType = "bottom",
  offset = { top: 0, bottom: 0, left: 0, right: 0 },
  height,
  width,
  onTrigger,
  className,
}) => {
  const triggerRef = useRef<TriggerRef>({
    triggerPending: false,
  });
  const normalizeScrollType: ScrollType[] = useMemo(() => {
    if (!Array.isArray(scrollType)) return [scrollType];
    return scrollType;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(scrollType)]);
  const handleOnTrigger = useCallback(() => {
    if (triggerRef.current.triggerPending) return;
    triggerRef.current.triggerPending = true;
    onTrigger().finally(() => {
      triggerRef.current.triggerPending = false;
    });
  }, [onTrigger]);

  const handleOnContainerScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement, UIEvent>) => {
      const { currentTarget } = event;
      if (normalizeScrollType.includes("bottom")) {
        const currentBottomScrollPosition =
          currentTarget.scrollHeight -
          (currentTarget.scrollTop + currentTarget.clientHeight);
        if (currentBottomScrollPosition <= (offset.bottom || 0)) {
          handleOnTrigger();
        }
      }

      if (normalizeScrollType.includes("top")) {
        const currentTopScrollPosition = currentTarget.scrollTop;
        if (currentTopScrollPosition <= (offset.top || 0)) {
          handleOnTrigger();
        }
      }
    },
    [handleOnTrigger, offset, normalizeScrollType]
  );

  const handleTypeOverflow = useCallback(() => {
    const overflowTemp = [];
    if (
      normalizeScrollType.includes("top") ||
      normalizeScrollType.includes("bottom")
    )
      overflowTemp.push("u-overflowY-scroll");

    if (
      normalizeScrollType.includes("left") ||
      normalizeScrollType.includes("right")
    )
      overflowTemp.push("u-overflowX-scroll");
    if (overflowTemp.length === 2) {
      overflowTemp.push("u-overflow-scroll");
    }

    return overflowTemp[overflowTemp.length - 1];
  }, [normalizeScrollType]);

  return (
    <div
      onScroll={handleOnContainerScroll}
      style={{ height, width }}
      className={`${className} ${handleTypeOverflow()}`}
    >
      {children}
    </div>
  );
};
