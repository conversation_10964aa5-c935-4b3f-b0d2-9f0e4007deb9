import { useCallback, useMemo, useEffect } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import useInfinity from "hooks/useInfinity";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { mapFrom } from "libs/adapters/dto";
import {
  orderStatusItemListDto,
  OrderStatusItemListDtoType,
  orderStatusOption,
  updateOrderStatusDto,
  UpdateOrderStatusDtoType,
} from "modules/order";
import {
  getListOrderStatus,
  getOrderStatusByCode,
  updateOrderStatus,
} from "services/crm/order";

export type orderStatusItem = UpdateOrderStatusDtoType;

interface OrderStatusDetailPage {
  orderStatusCode: string;
}

export const OrderStatusDetailPageVm = ({
  orderStatusCode,
}: OrderStatusDetailPage) => {
  const [getOrderStatusByCodeExec, getOrderStatusByCodeState] = useAsync(
    useCallback(
      (payload: { orderStatusCode: string }) =>
        getOrderStatusByCode({ ...payload }).then((res) =>
          orderStatusItemListDto(res.data.data)
        ),
      []
    )
  );

  const [updateOrderStatusExec, updateOrderStatusState] = useAsync(
    updateOrderStatus,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Cập nhật thành công" });
      }, []),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const orderStatusDetail = getOrderStatusByCodeState.data;

  const orderStatusOptions = useMemo(
    () => mapFrom(orderStatusDetail?.prevOrderStatus || [], orderStatusOption),
    [orderStatusDetail?.prevOrderStatus]
  );

  const handleUpdateOrderStatus = useCallback(
    (rawPayload: Partial<UpdateOrderStatusDtoType>) => {
      if (!orderStatusDetail?.orderStatusId) return;
      const updateOrderStatusPayload = updateOrderStatusDto({
        ...orderStatusDetail,
        ...rawPayload,
      });

      updateOrderStatusExec(
        orderStatusDetail.orderStatusId,
        updateOrderStatusPayload
      );
    },
    [orderStatusDetail, updateOrderStatusExec]
  );

  const {
    gotoFirstPage: getPrevStatus,
    goNextPage: loadMoreOrderStatus,
    state: pulldownState,
  } = useInfinity<OrderStatusItemListDtoType[]>(
    async (payload: { pageNum: number; pageSize: number }) =>
      getListOrderStatus({ ...payload }),
    {
      pageSize: 15,
    }
  );

  const { options: orderStatusOptionPulldown } = usePulldownHelper({
    dataSource: pulldownState.data || [],
    optionCreator: orderStatusOption,
  });

  const handleLoadmoreOrderStatus = async () => {
    loadMoreOrderStatus();
  };

  useEffect(() => {
    if (orderStatusCode) {
      getPrevStatus();
      getOrderStatusByCodeExec({ orderStatusCode });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderStatusCode]);

  return {
    loading: getOrderStatusByCodeState.loading,
    orderStatusDetail,
    orderStatusOptions,
    updateOrderStatusState,
    handleUpdateOrderStatus,
    orderStatusOptionPulldown,
    handleLoadmoreOrderStatus,
  };
};
