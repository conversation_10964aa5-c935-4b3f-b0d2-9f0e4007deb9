.a-buttonfilter {
	$root: &;

	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: rem(12.6) rem(10);
	overflow: hidden;
	font-family: $FONTFAMILY-ROBOTO;
	color: $COLOR-DENIM;
	cursor: pointer;
	background-color: $COLOR-WHITE;
	border: none;
	border: rem(2) solid $COLOR-DENIM;
	border-radius: rem(1);
	transition: all 0.3s ease-in-out;
	@include u-fw-bold;

	&:focus-within {
		border: rem(2) solid $COLOR-DENIM;
	}

	&:hover {
		background-color: rgba($color: $COLOR-DENIM, $alpha: 0.05);
	}

	&_title {
		font-size: rem(14);
		line-height: rem(20);
		@include u-fw-bold;
	}

	.a-icon-filter {
		background-size: rem(16);
		&:hover {
			opacity: 0.8;
		}
	}

	.a-icon-angle-right {
		background-size: rem(10);
		&:hover {
			opacity: 0.8;
		}
	}

	.a-icon-angle-right {
		transform: rotate(90deg);
		animation-name: rotate-angle-right-inactive;
		animation-duration: 0.2s;
	}

	&-expand .a-icon-angle-right {
		transform: rotate(270deg);
		animation-name: rotate-angle-right-active;
		animation-duration: 0.2s;
	}
}

@keyframes rotate-angle-right-active {
	0% {
		transform: rotate(90deg);
	}
	50% {
		transform: rotate(180deg);
	}
	100% {
		transform: rotate(270deg);
	}
}

@keyframes rotate-angle-right-inactive {
	0% {
		transform: rotate(270deg);
	}
	50% {
		transform: rotate(180deg);
	}
	100% {
		transform: rotate(90deg);
	}
}
