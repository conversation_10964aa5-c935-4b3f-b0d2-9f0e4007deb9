/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import crmDriverV1 from "services/crm/crm-driver-v1";

export const UseCreateTicket = () => {
  const [createTicketExe] = useAsync(
    useCallback(
      (data: any) =>
        crmDriverV1.post("media/external/master-data-tickets", data),
      []
    )
  );

  return {
    createTicketExe,
  };
};

export const useUpdateTicket = () => {
  const [updateTicketExe] = useAsync(
    useCallback(
      (id: number, data: any) =>
        crmDriverV1.put(`media/external/master-data-tickets/${id}`, data),
      []
    )
  );

  return {
    updateTicketExe,
  };
};

export const useDeleteTicket = () => {
  const [deleteTicketExe] = useAsync(
    useCallback(
      (id: number) =>
        crmDriverV1.delete(`media/external/master-data-tickets/${id}`),
      []
    )
  );

  return {
    deleteTicketExe,
  };
};
