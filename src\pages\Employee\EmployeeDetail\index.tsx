import React, { use<PERSON><PERSON>back, useMemo } from "react";

import { useParams, useSearchParams } from "react-router";

import { Button } from "components/atoms/button";
import { Checkbox } from "components/atoms/checkbox";
import { Heading } from "components/atoms/heading";
import { PhonefieldHookForm } from "components/atoms/phonefield";
import { PulldownHookForm } from "components/atoms/pulldown";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { Tabs, TabList, Tab, TabPanel } from "components/organisms/tabs";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import { BasePageProps } from "helpers/component";
import { FormContainer } from "helpers/form";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";
import { EmployeeUpdatePayload } from "services/crm/employee";

import { ChildrenPage } from "../types";
import {
  defaultDataPermission,
  EmployeeUpdateValidationSchema,
} from "./constant";
import { EmployeeDetailPageVm } from "./vm";

type FormData = Omit<
  EmployeeUpdatePayload,
  "employeeId" | "employeeGroupIds"
> & {
  roles: Array<{ label: string; value: string }>;
};

const IndexPage: React.FC<BasePageProps> = () => {
  const [searchUrlPath] = useSearchParams();
  const { employeeId } =
    useParams<PageParamsType<ChildrenPage["employeeDetail"]>>();

  const pageActionType = searchUrlPath.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  const {
    employeeDetail,
    loading,
    employeeGroupOptions,
    formatEmployeeGroupOptions,
    updateEmployeeExec,
    updateEmployeeState,
  } = EmployeeDetailPageVm({
    employeeId: Number(employeeId),
  });

  const onSubmitEmployeeUpdate = useCallback(
    ({ name, phoneNumber, email, roles }: FormData) => {
      const employeeGroupIds = roles.map(({ value }) => Number(value));
      updateEmployeeExec({
        employeeId: Number(employeeId),
        name,
        phoneNumber,
        email,
        employeeGroupIds,
      });
    },

    [employeeId, updateEmployeeExec]
  );

  return (
    <SpinnerContainer animating={loading}>
      <General>
        <title>Chi tiết nhân viên</title>
        <Heading type="h1" modifiers="primary">
          SỬA THÔNG TIN THÀNH VIÊN TRUY CẬP
        </Heading>
        <Tabs>
          <TabList>
            {["THÔNG TIN CHUNG", "QUYỀN TRUY CẬP"].map((title) => (
              <Tab key={title} style={{ padding: "10px 0px" }}>
                {title}
              </Tab>
            ))}
          </TabList>

          <TabPanel>
            {employeeDetail && (
              <FormContainer
                validationSchema={
                  editMode ? EmployeeUpdateValidationSchema : undefined
                }
                onSubmit={onSubmitEmployeeUpdate}
              >
                <Row className="u-mt-15">
                  <Col lg="12" className="u-mb-15">
                    <Formfield label="Vai trò truy cập" name="role">
                      <PulldownHookForm
                        name="roles"
                        id="roleId"
                        options={employeeGroupOptions}
                        isSearchable={editMode}
                        isMultiSelect
                        isDisabled={!editMode}
                        menuIsOpen={editMode ? undefined : false}
                        defaultValue={formatEmployeeGroupOptions(
                          employeeDetail.employeeGroups
                        )}
                      />
                    </Formfield>
                  </Col>

                  <Col lg="12" className="u-mb-15">
                    <Formfield label="Họ và tên" name="name">
                      <TextfieldHookForm
                        name="name"
                        id="name"
                        defaultValue={employeeDetail.name}
                        disabled={viewMode}
                      />
                    </Formfield>
                  </Col>

                  <Col lg="12" className="u-mb-15">
                    <Formfield label="Email" name="email">
                      <TextfieldHookForm
                        name="email"
                        id="email"
                        defaultValue={employeeDetail.email}
                        disabled={viewMode}
                      />
                    </Formfield>
                  </Col>

                  <Col lg="12">
                    <Formfield label="Số điện thoại" name="phoneNumber">
                      <PhonefieldHookForm
                        name="phoneNumber"
                        id="phoneNumber"
                        defaultValue={employeeDetail.phoneNumber}
                        disabled={viewMode}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="12" className="d-flex justify-content-end u-mt-20">
                    <Button
                      modifiers="secondary"
                      buttonType="outline"
                      onClick={navigationHelper.goBack}
                    >
                      QUAY LẠI
                    </Button>

                    {editMode && (
                      <div className="u-ml-20">
                        <Button
                          type="submit"
                          isLoading={updateEmployeeState.loading}
                        >
                          LƯU
                        </Button>
                      </div>
                    )}
                  </Col>
                </Row>
              </FormContainer>
            )}
          </TabPanel>

          <TabPanel>
            {defaultDataPermission.map((item) => (
              <Section key={item.role}>
                <Heading type="h2" modifiers="primary">
                  {item.role}
                </Heading>

                <Row className="u-mt-15">
                  {item.permission.map((permiss) => (
                    <Col key={permiss.name} lg="3">
                      <Checkbox checked={permiss.isActive}>
                        {permiss.name}
                      </Checkbox>
                    </Col>
                  ))}
                </Row>
              </Section>
            ))}
            <Section>
              <div className="d-flex justify-content-end">
                <Button
                  modifiers="secondary"
                  buttonType="outline"
                  onClick={navigationHelper.goBack}
                >
                  QUAY LẠI
                </Button>

                {editMode && (
                  <div className="u-ml-15">
                    <Button isLoading={updateEmployeeState.loading}>LƯU</Button>
                  </div>
                )}
              </div>
            </Section>
          </TabPanel>
        </Tabs>
      </General>
    </SpinnerContainer>
  );
};
export default IndexPage;
