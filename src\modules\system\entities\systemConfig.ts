import { Model, Number, ModelValue } from "libs/domain";

export const SystemConfigSchema = {
  conversionRateFromPointToMoney: Number(),
  conversionRateFromMoneyToPoint: Number(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => new Date(raw),
};

export const SystemConfigModel = new Model(SystemConfigSchema);

export type SystemConfigEntityType = ModelValue<typeof SystemConfigModel>;
