/* eslint-disable react/jsx-props-no-spreading */
import { useEffect, useMemo } from "react";
import { UseGetAllEmployee } from "pages/Customer/hook/useGetAllCustomer";
import { BaseSelect, BaseSelectProps } from "../BaseSelect";

export default function BaseSelectEmployee(props: BaseSelectProps) {
  const { ...rest } = props;
  const { getAllEmployeeExec, getAllEmployeeState, loading } =
    UseGetAllEmployee();

  const employeeOptions = useMemo(() => {
    return getAllEmployeeState?.data?.map((employee) => ({
      label: employee.name,
      value: employee.employeeId,
    }));
  }, [getAllEmployeeState]);

  useEffect(() => {
    getAllEmployeeExec();
  }, []);

  return <BaseSelect {...rest} options={employeeOptions} loading={loading} />;
}
