import { PaginationDTOType } from "modules/common/pagination";
import { MetaTotalRecords } from "pages/TickerManagement/StatusTicket/dto/get-status-ticket.dto";

export interface PromotionIdDto {
  promotionId: number;
}

export interface GetListPromotionDto {
  pageNum: number;
  pageSize: number;
  inputSearch?: string | null;
  fromDate?: string | null;
  toDate?: string | null;
  isActive?: boolean;
}

export interface GetListActivePromotionDto {
  pageNum: number;
  pageSize: number;
  inputSearch?: string | null;
}

export interface CreatePromotionDto {
  promotionCode: string;
  promotionName: string;
  promotionType: "percent" | "amount";
  promotionValue: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  description?: string;
}

export interface UpdatePromotionDto
  extends CreatePromotionDto,
    PromotionIdDto {}

export interface DeletePromotionDto extends PromotionIdDto {}

export interface GetListPromotionResponseDto extends MetaTotalRecords {
  data: PromotionDto[];
  links: PaginationDTOType;
}

export interface GetListPromotionWithoutPaginationResponseDto {
  data: PromotionDto[];
}

export interface PromotionDto {
  promotionId: number;
  promotionCode: string;
  promotionName: string;
  promotionType: string;
  promotionValue: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  description: string | null;
  createdBy: string;
  updatedBy: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}
