import * as Yup from "yup";

export const dummyUserPermissions = [
  {
    role: "QUẢN LÝ ĐƠN HÀNG",
    permissions: [
      {
        name: "Xem danh sách",
        isActive: false,
      },
      {
        name: "<PERSON><PERSON><PERSON>",
        isActive: false,
      },
      {
        name: "Chỉnh sửa",
        isActive: true,
      },
      {
        name: "<PERSON><PERSON><PERSON>",
        isActive: true,
      },
    ],
  },
  {
    role: "QUẢN LÝ ĐƠN HÀNG",
    permissions: [
      {
        name: "Xem danh sách",
        isActive: false,
      },
      {
        name: "Tạ<PERSON>",
        isActive: true,
      },
      {
        name: "Chỉnh sửa",
        isActive: true,
      },
      {
        name: "<PERSON><PERSON><PERSON>",
        isActive: false,
      },
    ],
  },
  {
    role: "QUẢN LÝ ĐƠN HÀNG",
    permissions: [
      {
        name: "Xem danh sách",
        isActive: false,
      },
      {
        name: "Tạo",
        isActive: false,
      },
      {
        name: "Chỉnh sửa",
        isActive: true,
      },
      {
        name: "Xóa",
        isActive: true,
      },
    ],
  },
];

export const inputValidationSchema = Yup.object({});
