import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { ItemType } from "antd/es/breadcrumb/Breadcrumb";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const initialState: { route: any[] } = {
  route: [],
};

const routeSlice = createSlice({
  name: "item",
  initialState,
  reducers: {
    setRoute: (state, action: PayloadAction<ItemType[]>) => {
      state.route = action.payload;
    },
  },
});

export default routeSlice;
