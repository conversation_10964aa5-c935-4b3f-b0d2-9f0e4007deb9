.m-filewrapper {
	$root: &;

	&_button {
		display: flex;
		align-items: center;
		width: fit-content;
		padding: rem(6) rem(15);
		margin-bottom: 0;
		cursor: pointer;
		border: 1px solid $COLOR-DENIM;

		&:hover {
			padding: rem(6) rem(15);
			background-color: $COLOR-DENIM;
			transition: all 0.25s ease-in-out;

			.a-icon {
				background-image: url("~assets/images/icons/upload-white.svg");
			}

			#{$root}_label {
				color: $COLOR-WHITE;
			}
		}
	}

	&_label {
		position: relative;
		top: rem(2);
		margin-left: rem(10);
		font-size: rem(14);
		font-weight: bold;
		line-height: rem(18);
		color: $COLOR-DENIM;
	}

	&_data {
		position: relative;
		width: fit-content;
		padding: rem(16);
		font-family: $FONTFAMILY-ARIAL;
		cursor: pointer;
		border: 1px solid $COLOR-PASTEL-GRAY;

		.a-icon-close {
			position: absolute;
			top: rem(8);
			right: rem(9);
			width: rem(10);
			height: rem(10);
		}

		&:hover {
			border-color: $COLOR-DENIM;
			.a-icon-close {
				background-image: url("~assets/images/icons/close-blue.svg");
			}
		}
	}

	&_filename {
		font-family: $FONTFAMILY-ARIAL;
		font-size: rem(14);
		font-weight: bold;
		line-height: rem(16);
		color: $COLOR-QUARTZ;
	}

	&_filetype {
		font-size: rem(14);
		line-height: rem(16);
		color: $COLOR-QUARTZ;
	}
}
