import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import orderServices from "../order.service";

export const UseGetOrderHistory = (orderId: number) => {
  const [fetchHistoryOrder, orderHistoryState] = useAsync(
    useCallback(() => {
      return orderServices.getOrderHistory(orderId);
    }, [orderId])
  );
  return {
    fetchHistoryOrder,
    orderHistoryState: orderHistoryState.data?.data,
  };
};
