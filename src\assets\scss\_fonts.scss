// @see https://stackoverflow.com/a/33687499/9918677

// FONT FAMILY ARIAL
@font-face {
  font-family: "Arial";
  font-style: normal;
  font-weight: normal;
  src: url("~assets/fonts/Arial/ArialMT.woff2") format("woff2"),
    url("~assets/fonts/Arial/ArialMT.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Arial";
  font-style: normal;
  font-weight: bold;
  src: url("~assets/fonts/Arial/Arial-BoldMT.woff2") format("woff2"),
    url("~assets/fonts/Arial/Arial-BoldMT.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Arial";
  font-style: italic;
  font-weight: bold;
  src: url("~assets/fonts/Arial/Arial-BoldItalicMT.woff2") format("woff2"),
    url("~assets/fonts/Arial/Arial-BoldItalicMT.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Arial";
  font-style: italic;
  font-weight: bold;
  font-stretch: narrower;
  src: url("~assets/fonts/Arial/ArialNarrow-BoldItalic.woff2") format("woff2"),
    url("~assets/fonts/Arial/ArialNarrow-BoldItalic.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Arial";
  font-style: italic;
  font-weight: normal;
  src: url("~assets/fonts/Arial/Arial-ItalicMT.woff2") format("woff2"),
    url("~assets/fonts/Arial/Arial-ItalicMT.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Arial";
  font-style: normal;
  font-weight: 900;
  src: url("~assets/fonts/Arial/Arial-Black.woff2") format("woff2"),
    url("~assets/fonts/Arial/Arial-Black.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Arial";
  font-style: normal;
  font-weight: bold;
  font-stretch: narrower;
  src: url("~assets/fonts/Arial/ArialNarrow-Bold.woff2") format("woff2"),
    url("~assets/fonts/Arial/ArialNarrow-Bold.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Arial";
  font-style: italic;
  font-weight: normal;
  font-stretch: narrower;
  src: url("~assets/fonts/Arial/ArialNarrow-Italic.woff2") format("woff2"),
    url("~assets/fonts/Arial/ArialNarrow-Italic.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Arial";
  font-style: normal;
  font-weight: normal;
  font-stretch: narrower;
  src: url("~assets/fonts/Arial/ArialNarrow.woff2") format("woff2"),
    url("~assets/fonts/Arial/ArialNarrow.woff") format("woff");
  font-display: swap;
}

// FONT FAMILY ROBOTO
@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: bold;
  src: url("~assets/fonts/Roboto/Roboto-Bold.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-Bold.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: italic;
  font-weight: 900;
  src: url("~assets/fonts/Roboto/Roboto-BlackItalic.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-BlackItalic.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 900;
  src: url("~assets/fonts/Roboto/Roboto-Black.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-Black.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 300;
  src: url("~assets/fonts/Roboto/Roboto-Light.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-Light.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: italic;
  font-weight: normal;
  src: url("~assets/fonts/Roboto/Roboto-Italic.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-Italic.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: italic;
  font-weight: bold;
  src: url("~assets/fonts/Roboto/Roboto-BoldItalic.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-BoldItalic.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src: url("~assets/fonts/Roboto/Roboto-Medium.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-Medium.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: italic;
  font-weight: 300;
  src: url("~assets/fonts/Roboto/Roboto-LightItalic.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-LightItalic.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: italic;
  font-weight: 500;
  src: url("~assets/fonts/Roboto/Roboto-MediumItalic.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-MediumItalic.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 100;
  src: url("~assets/fonts/Roboto/Roboto-Thin.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-Thin.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: normal;
  src: url("~assets/fonts/Roboto/Roboto-Regular.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-Regular.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Roboto";
  font-style: italic;
  font-weight: 100;
  src: url("~assets/fonts/Roboto/Roboto-ThinItalic.woff2") format("woff2"),
    url("~assets/fonts/Roboto/Roboto-ThinItalic.woff") format("woff");
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 400;
  src: url("~assets/fonts/Poppins/Poppins-Regular.ttf") format("truetype");
}

@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 500;
  src: url("~assets/fonts/Poppins/Poppins-Medium.ttf") format("truetype");
}

@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 600;
  src: url("~assets/fonts/Poppins/Poppins-SemiBold.ttf") format("truetype");
}

@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 700;
  src: url("~assets/fonts/Poppins/Poppins-Bold.ttf") format("truetype");
}
