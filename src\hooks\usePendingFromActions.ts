import { useCallback, useEffect, useState } from "react";

import { AsyncAction, Event } from "libs/adapters/stores/core";

export const usePendingFromActions = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ...events: AsyncAction<any, any>[]
) => {
  const [pendingState, setPendingState] = useState<{
    from?: Event<unknown>;
    pending: boolean;
  }>({
    from: undefined,
    pending: false,
  });

  const startPending = useCallback(
    (event: Event<unknown>) =>
      setPendingState((prevState) =>
        prevState.pending
          ? prevState
          : { ...prevState, from: event, pending: true }
      ),
    []
  );

  const stopPending = useCallback(
    (event: Event<unknown>) =>
      setPendingState((prevState) =>
        !prevState.pending || event !== prevState.from
          ? prevState
          : { ...prevState, from: event, pending: false }
      ),
    []
  );

  useEffect(() => {
    const listeners = events.map((event) => {
      const startListener = () => startPending(event.pending);
      const stopListener = () => stopPending(event.pending);
      event.pending.subscribe(startListener);
      event.fulfilled.subscribe(stopListener);
      event.rejected.subscribe(stopListener);
      return [startListener, stopListener];
    });

    return () => {
      events.forEach((event, index) => {
        const [startListener, stopListener] = listeners[index];
        event.pending.unsubscribe(startListener);
        event.fulfilled.unsubscribe(stopListener);
        event.rejected.unsubscribe(stopListener);
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, events);

  return pendingState.pending;
};
