import { CreateOrderDtoType } from "modules/order";
import { GetHistoryOrderDto } from "modules/order/dtos/historyOrders";
import { HistoryOrder } from "modules/order/entities/historyOrder";
import crmDriverV1 from "../crm-driver-v1";
import { GetOrderSourceWithoutPaginationResponseDto } from "./dto/order-source.dto";
import {
  GetDetailOrderStatusTaglineDto,
  GetDetailOrderStatusTaglineResponseDto,
  GetOrderStatusDto,
  GetOrderStatusTagLineWithoutPaginationDto,
  GetOrderStatusTagLineWithoutPaginationResponseDto,
  GetOrderStatusWithoutPaginationResponseDto,
} from "./dto/order-status.dto";
import {
  CreateExchangeOrderDto,
  CreateExchangeOrderResponseDto,
  GetAllOrderByCustomerIdDto,
  GetAllOrderByCustomerIdResponseDto,
  GetDetailOrderDto,
  GetDetailOrderResponseDto,
  UpdateOrderExchangeDto,
} from "./dto/order.dto";

const ORDER_URL = "/orders/external";
const ORDER_STATUS_URL = `${ORDER_URL}/order-status`;
const ORDER_SOURCE_URL = `${ORDER_URL}/order-sources`;

const orderServices = {
  /**
   * @Module ORDER
   */
  createOrderExchange: (
    dto: CreateExchangeOrderDto
  ): Promise<{ data: CreateExchangeOrderResponseDto }> => {
    const url = `${ORDER_URL}/create-exchange`;
    return crmDriverV1.post(url, dto);
  },

  updateOrderChange: (dto: UpdateOrderExchangeDto) => {
    const { orderId, ...restProps } = dto;
    const url = `${ORDER_URL}/update-exchange/${orderId}`;
    return crmDriverV1.put(url, restProps);
  },

  getOrderDetail: (
    dto: GetDetailOrderDto
  ): Promise<{
    data: GetDetailOrderResponseDto;
  }> => {
    const url = `${ORDER_URL}/${dto.orderId}`;
    return crmDriverV1.get(url);
  },

  getAllOrderByCustomerId: (
    dto: GetAllOrderByCustomerIdDto
  ): Promise<{ data: GetAllOrderByCustomerIdResponseDto }> => {
    const { customerId, ...restDto } = dto ?? {};
    const url = `${ORDER_URL}/getAllOrdersByCustomerId/${customerId}`;
    return crmDriverV1.post(url, restDto);
  },

  /**
   * @Module ORDER_STATUS
   */
  getOrderStatusWithoutPagination: (
    dto: GetOrderStatusDto
  ): Promise<{
    data: GetOrderStatusWithoutPaginationResponseDto;
  }> => {
    const url = ORDER_STATUS_URL;
    return crmDriverV1.get(url, {
      params: {
        searchText: dto.searchText || "",
      },
    });
  },

  getListTaglineOrderStatusWithoutPagination: (
    dto: GetOrderStatusTagLineWithoutPaginationDto
  ): Promise<{
    data: GetOrderStatusTagLineWithoutPaginationResponseDto;
  }> => {
    const url = `${ORDER_STATUS_URL}/${dto.orderStatusId}/taglines`;
    return crmDriverV1.get(url);
  },

  getOrderStatusTaglineDetail: (
    dto: GetDetailOrderStatusTaglineDto
  ): Promise<{
    data: GetDetailOrderStatusTaglineResponseDto;
  }> => {
    const url = `${ORDER_STATUS_URL}/${dto.orderStatusId}/taglines/${dto.orderStatusTaglineId}`;
    return crmDriverV1.get(url);
  },

  getListTagLineDontByOrderStatus: (): Promise<{
    data: GetOrderStatusTagLineWithoutPaginationResponseDto;
  }> => {
    const url = `${ORDER_STATUS_URL}/taglines/dont-by-order-status/list-taglines`;
    return crmDriverV1.get(url);
  },

  /**
   * @Module ORDER_SOURCE
   */

  getOrderSourceWithoutPagination: (): Promise<{
    data: GetOrderSourceWithoutPaginationResponseDto;
  }> => {
    const url = ORDER_SOURCE_URL;
    return crmDriverV1.get(url);
  },
  getOrderHistory: (orderId: number): Promise<{ data: GetHistoryOrderDto }> => {
    const url = `orders/external/history-orders/getAllHistoryByOrderId/${orderId}`;
    return crmDriverV1.get(url);
  },
};

export default orderServices;
