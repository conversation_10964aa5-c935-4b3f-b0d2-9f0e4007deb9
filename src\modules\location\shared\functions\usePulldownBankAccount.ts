import useDidMount from "helpers/react-hooks/useDidMount";
import { useInfinityParams } from "hooks/useInfinityParams";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { mapFrom } from "libs/adapters/dto";
import {
  bankDetailDto,
  BankDetailDtoType,
  bankAccountOption,
} from "modules/location";
import { getListBankAccount } from "services/crm/system";

export const usePulldownBankAccount = () => {
  const {
    loadMoreWithParams,
    loadMore,
    state: { data: bankAccountInfinity, loading },
  } = useInfinityParams<BankDetailDtoType[]>(
    (params: Parameters<typeof getListBankAccount>[0]) =>
      getListBankAccount(params).then((res) => ({
        ...res,
        data: {
          ...res.data,
          data: mapFrom(res.data.data, bankDetailDto),
        },
      })),
    {
      pageSize: 10,
    }
  );

  const { options: bankAccountOptions } = usePulldownHelper({
    dataSource: bankAccountInfinity || [],
    optionCreator: bankAccountOption,
  });

  useDidMount(() => {
    loadMoreWithParams({});
  });

  return {
    loadMoreBankAccountWithParams: loadMoreWithParams,
    loadMoreBankAccount: loadMore,
    bankAccountOptions,
    bankAccountLoading: loading,
    listBankAccounts: bankAccountInfinity,
  };
};
