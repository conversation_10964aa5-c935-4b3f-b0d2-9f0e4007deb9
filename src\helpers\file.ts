export interface FileMetadataData {
  width?: number;
  height?: number;
}

export const getFileMetaData = (blob: Blob) => {
  return new Promise<FileMetadataData>((res) => {
    if (blob.type.match(/^image\/*/)) {
      const image = new Image();

      image.onload = () => {
        res({
          width: image.naturalWidth,
          height: image.naturalHeight,
        });
      };
      image.src = URL.createObjectURL(blob);
    } else {
      res({});
    }
  });
};

export const blobFromFiles = (
  files: File[],
  onError?: (error: unknown, file: File) => void
) =>
  Promise.allSettled(
    files.map(async (file) => {
      try {
        return {
          file,
          blob: new Blob([new Uint8Array(await file.arrayBuffer())], {
            type: file.type,
          }),
        };
      } catch (error) {
        if (onError) onError(error, file);
        return null;
      }
    })
  )
    .then((res) => {
      return Promise.allSettled(
        res
          .filter((item) => item.status === "fulfilled")
          .map(async (item) => {
            if (item.status === "fulfilled") {
              return {
                ...item.value,
                metadata: await getFileMetaData(item.value!.blob),
              };
            }

            return item;
          })
      );
    })
    .then((res) =>
      res
        .filter((item) => item.status === "fulfilled")
        .map(
          (fulfilledData) =>
            // eslint-disable-next-line no-undef
            (
              fulfilledData as unknown as PromiseFulfilledResult<{
                file: File;
                blob: Blob;
                metadata: FileMetadataData;
              }>
            ).value
        )
    );
