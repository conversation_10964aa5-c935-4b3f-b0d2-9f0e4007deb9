/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/destructuring-assignment */
import React, { forwardRef } from "react";

import { Image } from "components/atoms/image";
import { mapModifiers } from "helpers/component";
import { ConnectForm, UseFormProps } from "helpers/form";

export interface Props extends React.InputHTMLAttributes<HTMLInputElement> {
  iconSrc?: string;
  errorMessage?: string;
}

export const Textfield = forwardRef<HTMLInputElement, Props>(
  ({ children, disabled, iconSrc, errorMessage, ...innerProps }, ref) => (
    <>
      <div
        className={mapModifiers(
          "a-textfield",
          disabled && "disabled",
          errorMessage && "error",
          iconSrc ? "withicon" : undefined
        )}
      >
        <input className="a-textfield_input" {...innerProps} ref={ref} />
        {iconSrc && (
          <div className="a-textfield_wrapicon">
            <Image src={iconSrc} alt="" />
          </div>
        )}
      </div>
      {errorMessage && (
        <div className="a-textfield_errormessage">{errorMessage}</div>
      )}
    </>
  )
);

interface TextfieldHookFormProps extends Props {
  name: string;
}

export const TextfieldHookForm: React.FC<TextfieldHookFormProps> = (props) => (
  <ConnectForm>
    {
      (({ register, errors }: UseFormProps) => (
        <Textfield
          ref={register}
          errorMessage={
            errors[props.name] ? errors[props.name].message : undefined
          }
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...props}
        />
      )) as any
    }
  </ConnectForm>
);
