import crmDriverV1 from "services/crm/crm-driver-v1";
import {
  CampaignDto,
  CampaignGetPage,
} from "../CampaignDetail/dto/campaign.dto";

const CampaignService = {
  createCampaign: (body: CampaignDto) => {
    const url = "promotions/external/campaigns-v2";
    return crmDriverV1.post(url, body);
  },
  updateCampaign: (id: string, body: CampaignDto) => {
    const url = `promotions/external/campaigns-v2/${id}`;
    return crmDriverV1.put(url, body);
  },
  getCampaignById: (id: string) => {
    const url = `promotions/external/campaigns-v2/byId/${id}`;
    return crmDriverV1.get(url);
  },
  deleteCampaign: (id: number) => {
    const url = `promotions/external/campaigns-v2/${id}`;
    return crmDriverV1.delete(url);
  },
  getCampaigns: async (body: CampaignDto) => {
    const url = "promotions/external/campaigns-v2";
    return crmDriverV1.get(url, { params: body });
  },

  getCampaignPage: (params: CampaignGetPage) => {
    const url = "promotions/external/campaigns-v2";
    return crmDriverV1.get(url, { params });
  },
};

export default CampaignService;
