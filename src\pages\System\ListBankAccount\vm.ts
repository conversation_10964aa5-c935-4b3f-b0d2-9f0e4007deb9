/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useState } from "react";

import produce from "immer";

import { showNotification } from "components/atoms/base/Notification";
import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { paginationDTO } from "modules/common/pagination";
import {
  CreateBankAccountDtoType,
  createBankAccountDto,
} from "modules/location/dtos";
import { BankAccountModel } from "modules/location/entities/bankAccount";
import {
  getListBankAccount,
  createBankAccount as createBankAccountService,
} from "services/crm/system";
import { DeleteBankAccount } from "./hook";

type ModalType = "createAccount";

interface State {
  modalState: {
    open: boolean;
    modalType?: ModalType;
  };
  pagination: {
    pageSize: number;
  };
}

export const ListAccountPageVm = () => {
  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      modalType: undefined,
    },
    pagination: {
      pageSize: 10,
    },
  });

  const { deleteBankAccountExe } = DeleteBankAccount();
  const [getListAccountExec, getAccountListState] = useAsync(
    useCallback(
      (pageNum: number, pageSize: number) =>
        getListBankAccount({ pageNum, pageSize }).then((res) => ({
          data: BankAccountModel.createMap(res.data.data),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const { gotoPage, ...accountListPaginationState } = usePagination({
    pageSize: state.pagination.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getListAccountExec(page, pageSize),
  });

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.modalType = type;
      })
    );
  }, []);

  const handleCloseModal = useCallback(() => {
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.modalType = undefined;
      })
    );
  }, []);

  const [createBankAccountExec, createBankAccountState] = useAsync(
    createBankAccountService,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Tạo mới thành công" });
        handleCloseModal();
        gotoPage(1);
      }, [gotoPage, handleCloseModal]),
      onFailed: useCallback((error: any) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pagination.pageSize = pageSize;
      })
    );
  }, []);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) => {
      return state.modalState.open && state.modalState.modalType === type;
    },
    [state.modalState]
  );

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize]);

  const { sortedData: accountListData, toggleSortState: toggleSortOrderBy } =
    useSortable({
      data: getAccountListState.data?.data,
      sortBy: {
        updatedAt: (data) => data.updatedAt,
      },
    });

  const createBankAccount = useCallback(
    (rawPayload: Partial<CreateBankAccountDtoType>) => {
      const bankAccountPayload = createBankAccountDto(rawPayload);
      createBankAccountExec(bankAccountPayload);
    },
    [createBankAccountExec]
  );

  const handleDeleteBankAccount = (bankAccountId: number) => {
    deleteBankAccountExe(bankAccountId)
      .then((res) => {
        if (res.status === 200) {
          showNotification({
            type: "success",
            message: "Xóa tài khoản ngân hàng thành công",
          });
          getListAccountExec(10, 1);
        }
      })
      .catch((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      });
  };
  return {
    gotoPage,
    handleChangePageSize,
    toggleSortOrderBy,
    accountListData: accountListData || [],
    getAccountListLoading: getAccountListState.loading,
    pageSize: state.pagination.pageSize,
    accountListPaginationState,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    createBankAccount,
    createBankAccountState,
    handleDeleteBankAccount,
  };
};
