import React, { PropsWithChildren } from "react";

import ReactModal from "react-modal";

import { Icon } from "components/atoms/icon";

export interface Props {
  isOpen: boolean;
  style?: ReactModal.Styles;
  isClosable?: boolean;
  shouldCloseOnEsc?: boolean;
  shouldCloseOnOverlayClick?: boolean;
  onCloseModal?: () => void;
  classname?: string;
}

export const Modal: React.FC<PropsWithChildren<Props>> = ({
  isOpen,
  isClosable = true,
  style,
  shouldCloseOnEsc = true,
  shouldCloseOnOverlayClick = true,
  children,
  onCloseModal,
  classname,
}) => {
  return (
    <div className="o-modal">
      <ReactModal
        isOpen={isOpen}
        overlayClassName="o-modal_overlay"
        onRequestClose={onCloseModal}
        portalClassName={`o-modal_portal ${classname}`}
        className="o-modal_content"
        ariaHideApp={false}
        style={style || undefined}
        shouldCloseOnEsc={shouldCloseOnEsc}
        shouldCloseOnOverlayClick={shouldCloseOnOverlayClick}
      >
        <div className="o-modal_body">
          {isClosable && (
            <div className="o-modal_close">
              <Icon iconName="close-modal" onClick={onCloseModal} />
            </div>
          )}
          {children}
        </div>
      </ReactModal>
    </div>
  );
};

// Modal.defaultProps = {
//   isClosable: true,
//   shouldCloseOnEsc: true,
//   shouldCloseOnOverlayClick: true,
// };
