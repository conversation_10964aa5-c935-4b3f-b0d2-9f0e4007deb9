import { useCallback, useState } from "react";

import produce from "immer";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useFormContainer } from "helpers/form";
import { useAsync } from "hooks/useAsync";
import { CreateLeadDTOType, CreateLeadDto } from "modules/lead/dtos";
import { createLead as createLeadService } from "services/crm/lead";

type ModalType = "collapsed";

interface StateProps {
  modalState: {
    open: boolean;
    type?: ModalType;
  };
}

export const CreateLeadChatboxPage = () => {
  const { register, reset } = useFormContainer();

  const [state, setState] = useState<StateProps>({
    modalState: {
      open: false,
      type: undefined,
    },
  });

  const [createLeadServiceExec, createLeadState] = useAsync(createLeadService, {
    onFailed: useCallback((error) => {
      const errMessage = getErrorMessageViaErrCode(
        error?.response?.data?.errors?.[0]?.code
      );
      toastSingleMode({
        type: "error",
        message: errMessage.translation.title,
        descripition: errMessage.translation.detail,
      });
    }, []),
    onSuccess: useCallback(() => {
      toastSingleMode({
        type: "success",
        message: "Tạo lead thành công",
      });
      reset();
    }, [reset]),
  });

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.type = type;
      })
    );
  }, []);

  const handleOnCloseModal = useCallback(() => {
    if (!state.modalState.open) return;
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.type = undefined;
      })
    );
  }, [state.modalState.open]);

  const modelTypeIsOpen = useCallback(
    (type: ModalType) =>
      state.modalState.open && state.modalState.type === type,
    [state.modalState.open, state.modalState.type]
  );

  const createLead = useCallback(
    (rawPayload: Partial<CreateLeadDTOType>) => {
      const orderPayload = CreateLeadDto({
        ...rawPayload,
      });

      return createLeadServiceExec(orderPayload);
    },
    [createLeadServiceExec]
  );

  return {
    createLead,
    createLeadState,
    registerFormContainer: register,
    resetFieldsForm: reset,
    handleOpenModalByType,
    handleOnCloseModal,
    modelTypeIsOpen,
  };
};
