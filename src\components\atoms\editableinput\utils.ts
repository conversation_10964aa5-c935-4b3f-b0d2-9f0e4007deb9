import { blobFromFiles as blobFromFilesHelper } from "helpers/file";

import { EditableElement } from "./types";

export const blobFromFiles = blobFromFilesHelper;

export const isFocusElement = (element: HTMLElement) =>
  document.activeElement === element;

export const shouldDisplayPlaceholder = (element: EditableElement | null) => {
  if (!element) return false;
  return !isFocusElement(element) && element.textContent?.length === 0;
};
