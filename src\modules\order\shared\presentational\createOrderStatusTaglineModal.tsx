import * as Yup from "yup";

import { Button } from "components/atoms/button";
import { ColorPicker } from "components/atoms/colorpicker";
import { Heading } from "components/atoms/heading";
import { NumberfieldHookForm } from "components/atoms/numberfield";
import { PulldownHookForm } from "components/atoms/pulldown";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Modal } from "components/organisms/modal";
import { Section } from "components/organisms/section";
import { FormContainer } from "helpers/form";
import { useAsync } from "hooks/useAsync";
import { orderStatusOption, OrderStatusItemListDtoType } from "modules/order";
import { CreateOrderStatusTaglineFormPayload } from "pages/Order/ListOrderStatusTagline/constant";

export interface CreateOrderStatusTaglineModalProps {
  open: boolean;
  inputValidationSchema: Yup.ObjectSchema<Yup.AnyObject>;
  onChangeColor: (color: string) => void;
  orderStatus?: OrderStatusItemListDtoType;
  onClose?: () => void;
  handleSubmitCreateStatusTagline: (
    formData: CreateOrderStatusTaglineFormPayload
  ) => Promise<void>;
}

export const CreateOrderStatusTaglineModal = ({
  onClose,
  open,
  inputValidationSchema,
  orderStatus,
  onChangeColor,
  handleSubmitCreateStatusTagline,
}: CreateOrderStatusTaglineModalProps) => {
  const [handleSubmit, handleSubmitState] = useAsync(
    handleSubmitCreateStatusTagline
  );

  return (
    <Modal
      style={{ content: { maxWidth: 700 } }}
      isOpen={open}
      onCloseModal={onClose}
      isClosable={false}
    >
      <Heading type="h1" centered>
        TẠO MỚI TAGLINE ĐƠN HÀNG
      </Heading>
      <Section>
        <FormContainer
          validationSchema={inputValidationSchema}
          onSubmit={handleSubmit}
        >
          <Formfield label="Tên trạng thái" name="orderStatus">
            <PulldownHookForm
              defaultValue={orderStatusOption(orderStatus)}
              name="orderStatus"
              placeholder="Trạng thái"
              isDisabled
            />
          </Formfield>
          <Formfield label="Tên tagline" name="name">
            <TextfieldHookForm name="name" placeholder="Tên tagline" />
          </Formfield>
          <Formfield label="Màu sắc" name="">
            <ColorPicker onChangeClolor={onChangeColor} />
          </Formfield>
          <Formfield label="Mô tả" name="description">
            <TextareafieldHookForm name="description" placeholder="Mô tả" />
          </Formfield>
          <Formfield label="Thứ tự hiển thị" name="displayOrder">
            <NumberfieldHookForm
              name="displayOrder"
              placeholder="Thứ tự hiển thị"
            />
          </Formfield>
          <div className="d-flex justify-content-end u-mt-20">
            <Button
              buttonType="outline"
              onClick={onClose}
              modifiers="secondary"
            >
              HỦY
            </Button>
            <div className="u-ml-15">
              <Button
                type="submit"
                isLoading={handleSubmitState.loading}
                disabled={handleSubmitState.loading}
              >
                LƯU
              </Button>
            </div>
          </div>
        </FormContainer>
      </Section>
    </Modal>
  );
};
