.m-boxmessage {
	$root: &;
	$left_size: 15;
	$avatar_size: 52;

	display: flex;

	&_avatar {
		flex: 0 0 auto;
		width: rem($avatar_size);
		height: rem($avatar_size);
		margin-right: rem(10 + ($left_size / 2));

		.a-image {
			width: 100%;
			border-radius: 50%;
			object-fit: cover;
		}

		#{$root}-contentleft & {
			margin-right: 0;
			margin-left: rem(10 + ($left_size / 2));
		}
	}

	&_content {
		position: relative;
		display: flex;
	}

	&_createdAt {
		padding: rem(3);
		margin-left: auto;
		font-size: rem(10);

		#{$root}-contentleft & {
			text-align: right;
		}
	}

	&_leaf {
		position: absolute;
		top: rem($avatar_size / 2);
		left: rem(-$left_size / 2);
		width: rem($left_size);
		height: rem($left_size);
		background-color: $COLOR_WHITE;
		transform: translateY(-50%) rotate(45deg);

		#{$root}-contentleft & {
			right: rem(-$left_size / 2);
			left: auto;
		}
	}

	&_message {
		min-width: rem(150);
		min-height: rem(50);
		padding: rem(10);
		background-color: $COLOR_WHITE;
		filter: drop-shadow(2px 3px 0px rgba(0, 0, 0, 0.09));
		border-radius: rem(8);

		#{$root}-contentleft & {
			filter: drop-shadow(-2px 3px 0px rgba(0, 0, 0, 0.09));
		}
	}

	&-contentleft {
		flex-direction: row-reverse;
	}
}
