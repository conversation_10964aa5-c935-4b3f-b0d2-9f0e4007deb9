/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useState } from "react";
import { DeleteOutlined } from "@ant-design/icons";
import {
  Modal,
  Form,
  Input,
  Row,
  Col,
  Table,
  Switch,
  AutoComplete,
  InputNumber,
  Tooltip,
} from "antd";
import { ColumnsType } from "antd/es/table";
import produce from "immer";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { Heading } from "components/atoms/heading";
import { COLOR } from "constants/color";
import debounce from "helpers/debounce";
import { useAsync } from "hooks/useAsync";
import { EmployeeEntityType, assignEmployeeOption } from "modules/employee";
import {
  useLocationSelect,
  usePulldownAssignEmployeeShop,
  usePulldownListStorage,
  BankAccountEntityType,
  bankAccountOption,
} from "modules/location";
import { CreateShopFormPayload } from "pages/System/ListShop/constant";

import { usePulldownBankAccount } from "../functions";

interface CreateShopModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (formData: CreateShopFormPayload) => Promise<void>;
}

interface State {
  listEmployeesSelected: EmployeeEntityType[];
  listBankAccountsSelected: BankAccountEntityType[];
  isActiveToggle: boolean;
}

export const CreateShopModal = ({
  open,
  onClose,
  onSubmit,
}: CreateShopModalProps) => {
  const [form] = Form.useForm();
  const [state, setState] = useState<State>({
    listEmployeesSelected: [],
    listBankAccountsSelected: [],
    isActiveToggle: false,
  });

  const [submitCreateShopExec, submitCreateShopState] = useAsync(onSubmit, {
    onSuccess: useCallback(() => {
      setState(
        produce((draft) => {
          draft.isActiveToggle = false;
          draft.listBankAccountsSelected = [];
          draft.listEmployeesSelected = [];
        })
      );
      form.resetFields();
    }, [form]),
  });

  const { loadMoreStorageWithParams, storageOptions, storageLoading } =
    usePulldownListStorage();

  const {
    listBankAccounts,
    loadMoreBankAccountWithParams,
    bankAccountOptions,
  } = usePulldownBankAccount();

  const { listEmployee, loadMoreEmployee, assignEmployeeOptions } =
    usePulldownAssignEmployeeShop();

  const {
    cityOptions,
    districtOptions,
    wardOptions,
    setFilter: setLocationFilter,
  } = useLocationSelect();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onStoragePulldownInputChange = useCallback(
    debounce((textSearch: string) => {
      loadMoreStorageWithParams(textSearch ? { name: textSearch } : {});
    }, 300),
    []
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onEmployeePulldownInputChange = useCallback(
    debounce((textSearch: string) => {
      loadMoreEmployee({ name: textSearch });
    }, 300),
    []
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onBankAccountPulldownInputChange = useCallback(
    debounce((textSearch: string) => {
      loadMoreBankAccountWithParams(textSearch ? { owner: textSearch } : {});
    }, 300),
    []
  );

  const onSelectedEmployee = useCallback(
    (value: string) => {
      if (!listEmployee) return;
      const employee = listEmployee.find(
        (item) => item.employeeId === Number(value)
      );
      if (!employee) return;
      setState(
        produce((draft) => {
          draft.listEmployeesSelected = [
            ...draft.listEmployeesSelected,
            employee,
          ];
        })
      );
    },
    [listEmployee]
  );

  const onSelectedBankAccount = useCallback(
    (value: string) => {
      if (!listBankAccounts) return;
      const bankAccount = (listBankAccounts as any[])?.find(
        (item: any) => item.bankAccountId === Number(value)
      );
      if (!bankAccount) return;
      setState(
        produce((draft) => {
          draft.listBankAccountsSelected = [
            ...draft.listBankAccountsSelected,
            bankAccount,
          ];
        })
      );
    },
    [listBankAccounts]
  );

  const onHandleUnSelectEmployee = (employeeId: number) => {
    setState(
      produce((draft) => {
        draft.listEmployeesSelected = [
          ...draft.listEmployeesSelected.filter(
            (item) => item.employeeId !== employeeId
          ),
        ];
      })
    );
  };

  const onHandleUnSelectBankAccount = (bankAccountId: number) => {
    setState(
      produce((draft) => {
        draft.listBankAccountsSelected = [
          ...draft.listBankAccountsSelected.filter(
            (item) => item.bankAccountId !== bankAccountId
          ),
        ];
      })
    );
  };

  const onRenderEmployeeOptions = useCallback(
    (employeeOptions: ReturnType<typeof assignEmployeeOption>[]) => {
      return employeeOptions.filter((item) => {
        const isSelect = state.listEmployeesSelected.find(
          (itemSelected) => itemSelected.employeeId === Number(item.value)
        );

        return !isSelect && item;
      });
    },
    [state.listEmployeesSelected]
  );

  const onRenderBankAccountOptions = useCallback(
    (bankOptions: ReturnType<typeof bankAccountOption>[]) => {
      return bankOptions.filter((item) => {
        const isSelect = state.listBankAccountsSelected.find(
          (itemSelected) => itemSelected.bankAccountId === Number(item.value)
        );

        return !isSelect && item;
      });
    },
    [state.listBankAccountsSelected]
  );

  const onChangeToggle = useCallback(
    (event) => {
      setState(
        produce((draft) => {
          draft.isActiveToggle = event.target.checked;
        })
      );
    },
    [setState]
  );

  const handleSubmitCreateShop = (formPayload: any) => {
    submitCreateShopExec({
      ...formPayload,
      bankAccounts: state.listBankAccountsSelected,
      employees: state.listEmployeesSelected,
      isFranchisedShop: formPayload.isFranchisedShop || false,
    });
  };

  // Define employee table columns
  const employeeColumns: ColumnsType<EmployeeEntityType> = [
    {
      title: "Mã nhân viên",
      dataIndex: "employeeId",
      key: "employeeId",
      align: "center",
    },
    {
      title: "Tên nhân viên",
      dataIndex: "name",
      key: "name",
      align: "center",
    },
    {
      title: "Xóa",
      key: "action",
      align: "center",
      width: 80,
      render: (_, record) => (
        <Tooltip title="Xóa">
          <BaseButton
            type="primary"
            bgColor={COLOR.RED[500]}
            hoverColor={COLOR.RED[700]}
            icon={<DeleteOutlined rev={undefined} />}
            onClick={() => onHandleUnSelectEmployee(record.employeeId)}
          />
        </Tooltip>
      ),
    },
  ];

  // Define bank account table columns
  const bankAccountColumns: ColumnsType<BankAccountEntityType> = [
    {
      title: "Số tài khoản",
      dataIndex: "accountNumber",
      key: "accountNumber",
      align: "center",
    },
    {
      title: "Chủ tài khoản",
      dataIndex: "owner",
      key: "owner",
      align: "center",
    },
    {
      title: "Ngân hàng",
      dataIndex: "bankName",
      key: "bankName",
      align: "center",
    },
    {
      title: "Xóa",
      key: "action",
      align: "center",
      width: 80,
      render: (_, record) => (
        <Tooltip title="Xóa">
          <BaseButton
            type="primary"
            bgColor={COLOR.RED[500]}
            hoverColor={COLOR.RED[700]}
            icon={<DeleteOutlined rev={undefined} />}
            onClick={() => onHandleUnSelectBankAccount(record.bankAccountId)}
          />
        </Tooltip>
      ),
    },
  ];

  return (
    <Modal
      title="TẠO MỚI SHOP"
      open={open}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmitCreateShop}
        className="space-y-4"
      >
        {/* Basic Information */}
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="Tên Shop"
              name="name"
              rules={[{ required: true, message: "Vui lòng nhập tên shop" }]}
            >
              <Input placeholder="Nhập tên Shop" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Hotline"
              name="hotline"
              rules={[{ required: true, message: "Vui lòng nhập hotline" }]}
            >
              <Input placeholder="Nhập Hotline" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Loại Shop"
              name="type"
              rules={[{ required: true, message: "Vui lòng chọn loại shop" }]}
            >
              <BaseSelect
                placeholder="Chọn loại Shop"
                options={["online", "offline"].map((type) => ({
                  label: type,
                  value: type,
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="Địa chỉ"
              name="address"
              rules={[{ required: true, message: "Vui lòng nhập địa chỉ" }]}
            >
              <Input placeholder="Nhập địa chỉ" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Tỉnh/Thành phố"
              name="cityId"
              rules={[
                { required: true, message: "Vui lòng chọn tỉnh/thành phố" },
              ]}
            >
              <BaseSelect
                placeholder="Chọn tỉnh/thành phố"
                options={cityOptions}
                onChange={(value: any) => {
                  setLocationFilter("cityId", value);
                  // Reset district and ward when city changes
                  form.setFieldsValue({
                    districtId: undefined,
                    wardId: undefined,
                  });
                }}
                disabled={!cityOptions.length}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Quận/Huyện"
              name="districtId"
              rules={[{ required: true, message: "Vui lòng chọn quận/huyện" }]}
            >
              <BaseSelect
                placeholder="Chọn quận/huyện"
                options={districtOptions}
                onChange={(value: any) => {
                  setLocationFilter("districtId", value);
                  // Reset ward when district changes
                  form.setFieldsValue({
                    wardId: undefined,
                  });
                }}
                disabled={!districtOptions.length}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Phường/Xã"
              name="wardId"
              rules={[{ required: true, message: "Vui lòng chọn phường/xã" }]}
            >
              <BaseSelect
                placeholder="Chọn phường/xã"
                options={wardOptions}
                onChange={(value: any) => setLocationFilter("wardId", value)}
                disabled={!wardOptions.length}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="Kho"
              name="storageIds"
              rules={[{ required: true, message: "Vui lòng chọn kho" }]}
            >
              <BaseSelect
                mode="multiple"
                placeholder="Chọn kho"
                options={storageOptions}
                loading={storageLoading}
                onSearch={onStoragePulldownInputChange}
                showSearch
                filterOption={false}
              />
            </Form.Item>
          </Col>
        </Row>

        {/* Employee Section */}
        <div className="mt-6">
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Nhân Viên Shop">
                <AutoComplete
                  placeholder="Chọn nhân viên"
                  options={onRenderEmployeeOptions(assignEmployeeOptions).map(
                    (option) => ({
                      value: option.value,
                      label: option.label,
                    })
                  )}
                  onSelect={onSelectedEmployee}
                  onSearch={onEmployeePulldownInputChange}
                  filterOption={false}
                />
              </Form.Item>
            </Col>
          </Row>

          <div className="mt-4">
            <Heading>NHÂN VIÊN ĐÃ CHỌN</Heading>
            <Table
              columns={employeeColumns}
              dataSource={state.listEmployeesSelected}
              rowKey="employeeId"
              size="small"
              bordered
              scroll={{ x: 600 }}
              pagination={false}
              locale={{ emptyText: "Chưa có nhân viên nào được chọn" }}
            />
          </div>
        </div>

        {/* Bank Account Section */}
        <div className="mt-6">
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Tài khoản thanh toán">
                <AutoComplete
                  placeholder="Chọn tài khoản thanh toán"
                  options={onRenderBankAccountOptions(bankAccountOptions).map(
                    (option) => ({
                      value: option.value,
                      label: option.label,
                    })
                  )}
                  onSelect={onSelectedBankAccount}
                  onSearch={onBankAccountPulldownInputChange}
                  filterOption={false}
                />
              </Form.Item>
            </Col>
          </Row>

          <div className="mt-4">
            <Heading>TÀI KHOẢN NGÂN HÀNG ĐÃ CHỌN</Heading>
            <Table
              columns={bankAccountColumns}
              dataSource={state.listBankAccountsSelected}
              rowKey="bankAccountId"
              size="small"
              bordered
              scroll={{ x: 1000 }}
              pagination={false}
              locale={{ emptyText: "Chưa có tài khoản nào được chọn" }}
            />
          </div>
        </div>

        {/* Additional Settings */}
        <div className="mt-6">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Thứ tự hiển thị"
                name="displayOrder"
                rules={[
                  { required: true, message: "Vui lòng nhập thứ tự hiển thị" },
                ]}
              >
                <InputNumber
                  placeholder="Nhập thứ tự hiển thị"
                  className="w-full"
                  min={0}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Shop nhượng quyền"
                name="isFranchisedShop"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="Có"
                  unCheckedChildren="Không"
                  onChange={onChangeToggle}
                />
              </Form.Item>
            </Col>
          </Row>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-6 border-t mt-6">
          <BaseButton type="default" onClick={onClose}>
            HỦY
          </BaseButton>
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            htmlType="submit"
            loading={submitCreateShopState.loading}
          >
            LƯU
          </BaseButton>
        </div>
      </Form>
    </Modal>
  );
};
