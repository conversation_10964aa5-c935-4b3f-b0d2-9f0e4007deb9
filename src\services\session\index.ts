const config = {
  tokenField: "GUMAC_CRM_TOKEN",
  refreshTokenField: "GUMAC_CRM_REFRESH_TOKEN",
} as const;

export interface EmployeeSessionPayload {
  accessToken: string;
  refreshToken: string;
}

export interface EmployeeSessionInfo {
  accessToken: string;
  refreshToken: string;
}

export const initialEmployeeSession = (payload: EmployeeSessionPayload) => {
  const { accessToken, refreshToken } = payload;

  localStorage.setItem(config.tokenField, accessToken);
  localStorage.setItem(config.refreshTokenField, refreshToken);
};

export const getEmployeeSessionInfo = (): EmployeeSessionInfo | false => {
  const accessToken = localStorage.getItem(config.tokenField);
  const refreshToken = localStorage.getItem(config.refreshTokenField);

  return {
    accessToken,
    refreshToken,
  };
};

export const clearEmployeeSession = () => {
  localStorage.removeItem(config.tokenField);
  localStorage.removeItem(config.refreshTokenField);
};
