import { useEffect, useRef } from "react";

const useSafeCallback = () => {
  const mountedRef = useRef(false);

  useEffect(() => {
    mountedRef.current = true;

    return () => {
      mountedRef.current = false;
    };
  }, []);

  return <P, A extends unknown[]>(callback: (...args: A) => P) =>
    (...args: A) => {
      if (!mountedRef.current) return null;

      return callback(...args);
    };
};

export default useSafeCallback;
