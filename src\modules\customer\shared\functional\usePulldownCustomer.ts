import { useCallback, useRef, useState } from "react";

import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { paginationDTO, PaginationDTOType } from "modules/common/pagination";
import { customerOption } from "modules/customer";
import {
  CustomerProfileModel,
  CustomerProfileEntityType,
} from "modules/customer/entities";
import { getListCustomer } from "services/crm/customer";

export interface CustomerFunctionalOptions {
  excludePending: boolean;
  pageSize?: number;
}

interface State {
  customers: CustomerProfileEntityType[];
  pagination?: PaginationDTOType;
}

export const usePulldownCustomer = (
  { excludePending, pageSize = 15 }: CustomerFunctionalOptions = {
    excludePending: false,
  }
) => {
  const prevFilterRef = useRef<{ name: string }>({
    name: "",
  });

  const [state, setState] = useState<State>({
    customers: [],
    pagination: undefined,
  });

  const [loadMoreCustomer, loadMoreCustomerState] = useAsync(
    useCallback((args: Parameters<typeof getListCustomer>[0]) => {
      const nextNameFilter = args.name || "";
      const payload = {
        ...args,
        name: nextNameFilter.trim() ? nextNameFilter : undefined,
      };

      return getListCustomer(payload).then(({ data }) => ({
        data: CustomerProfileModel.createMap(data.data),
        links: paginationDTO(data.links),
      }));
    }, []),
    {
      excludePending,
      onSuccess: useCallback(({ data, links }) => {
        setState((prevState) => ({
          ...prevState,
          customers: [...prevState.customers, ...data],
          pagination: links,
        }));
      }, []),
    }
  );

  const { customers } = state;

  const loadMoreCustomerExec = useCallback(
    async (args?: Pick<Parameters<typeof getListCustomer>[0], "name">) => {
      let nextPage: number | undefined;
      const { name } = prevFilterRef.current;
      const nextNameFilter = args?.name !== undefined ? args.name : name;
      if (nextNameFilter !== name) {
        nextPage = 1;
        prevFilterRef.current = {
          ...prevFilterRef.current,
          name: nextNameFilter,
        };
        setState((prevState) => ({
          ...prevState,
          customers: [],
        }));
      } else if (!state.pagination) {
        nextPage = 1;
      } else {
        const currentPageNum = state.pagination.self.pageNum;
        if (currentPageNum + 1 <= state.pagination.last.pageNum) {
          nextPage = currentPageNum + 1;
        }
      }

      if (!nextPage) return;
      loadMoreCustomer({
        pageNum: nextPage,
        pageSize,
        name: nextNameFilter,
      });
    },
    [loadMoreCustomer, state.pagination, pageSize]
  );

  const {
    options: customerOptions,
    formatOption: formatCustomerOption,
    getOptionByValue,
    getOptionDataByValue,
  } = usePulldownHelper({
    dataSource: customers,
    optionCreator: customerOption,
    valueTrans: Number,
  });

  return {
    loadMoreCustomer: loadMoreCustomerExec,
    loadMoreCustomerState,
    customers,
    customerOptions,
    formatCustomerOption,
    getOptionByValue,
    getOptionDataByValue,
  };
};
