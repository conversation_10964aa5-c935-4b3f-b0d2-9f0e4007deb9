import { useCallback, useEffect, useState } from "react";
import { DeleteFilled, SearchOutlined } from "@ant-design/icons";
import { Input, Row, Col, Table, Tooltip, Modal } from "antd";
import { ColumnsType } from "antd/es/table";
import produce from "immer";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { Heading } from "components/atoms/heading";
import { COLOR } from "constants/color";
import useDerivedStateFromProps from "helpers/react-hooks/useDerivedStateFromProps";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import { EmployeeDetailDtoType, employeeDetailDto } from "modules/employee";
import { getListEmployee } from "services/crm/employee";

interface EmployeeSelectedModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (value: Array<EmployeeDetailDtoType>) => void;
  employeeSelected: Array<EmployeeDetailDtoType>;
}

interface State {
  pageSize: number;
  selectedEmployeeModal: Array<EmployeeDetailDtoType>;
}

export const EmployeeSelectedModal = ({
  open,
  onClose,
  onSave,
  employeeSelected,
}: EmployeeSelectedModalProps) => {
  const [state, setState] = useState<State>({
    pageSize: 10,
    selectedEmployeeModal: [],
  });
  const [searchText, setSearchText] = useState("");

  const handleSelected = useCallback(
    (item: EmployeeDetailDtoType) => {
      const index = state.selectedEmployeeModal.findIndex((el) => {
        return el.employeeId === item.employeeId;
      });

      if (index === -1) {
        setState(
          produce((draft) => {
            draft.selectedEmployeeModal.push(item);
          })
        );
      }
    },
    [state.selectedEmployeeModal]
  );

  const handleRemoved = useCallback(
    (item: EmployeeDetailDtoType) => {
      const index = state.selectedEmployeeModal.findIndex((el) => {
        return el.employeeId === item.employeeId;
      });

      if (index !== -1) {
        setState(
          produce((draft) => {
            draft.selectedEmployeeModal.splice(index, 1);
          })
        );
      }
    },
    [state.selectedEmployeeModal]
  );

  useDerivedStateFromProps((_, nextOpenState) => {
    if (nextOpenState) {
      setState(
        produce((draft) => {
          draft.selectedEmployeeModal = employeeSelected || [];
        })
      );
    }
  }, open);

  const [getListEmployeeExec, getListEmployeeState] = useAsync(
    useCallback(
      (pageNum: number, pageSize: number, name?: string) =>
        getListEmployee({ pageNum, pageSize, name }).then((res) => ({
          employees: mapFrom(res.data.data, employeeDetailDto),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const listEmployee = getListEmployeeState.data?.employees;

  const { gotoPage, ...employeePaginationState } = usePagination({
    pageSize: state.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getListEmployeeExec(page, pageSize, searchText || undefined),
  });

  const isSelected = (item: EmployeeDetailDtoType) => {
    const isChecked = state.selectedEmployeeModal.find(
      (el) => el.employeeId === item.employeeId
    );
    return !!isChecked;
  };

  const handleSearch = () => {
    gotoPage(1);
  };

  useEffect(() => {
    if (open) {
      gotoPage(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pageSize, open]);

  // Define table columns for available employees
  const availableEmployeeColumns: ColumnsType<EmployeeDetailDtoType> = [
    {
      title: "Mã nhân viên",
      dataIndex: "employeeId",
      key: "employeeId",
      align: "center",
      width: 120,
    },
    {
      title: "Tên nhân viên",
      dataIndex: "name",
      key: "name",
      align: "center",
      width: 200,
    },
    {
      title: "Vai trò",
      key: "role",
      align: "center",
      width: 200,
      render: (_, record) => (
        <span>
          {record.employeeGroups
            ?.map((employeeGroup) => employeeGroup?.name)
            .join(", ")}
        </span>
      ),
    },
    {
      title: "Thao tác",
      key: "action",
      align: "center",
      width: 100,
      render: (_, record) => (
        <BaseButton
          type="primary"
          bgColor={COLOR.BLUE[500]}
          hoverColor={COLOR.BLUE[700]}
          disabled={isSelected(record)}
          onClick={() => handleSelected(record)}
        >
          {isSelected(record) ? "Đã chọn" : "Chọn"}
        </BaseButton>
      ),
    },
  ];

  // Define table columns for selected employees
  const selectedEmployeeColumns: ColumnsType<EmployeeDetailDtoType> = [
    {
      title: "Mã nhân viên",
      dataIndex: "employeeId",
      key: "employeeId",
      align: "center",
    },
    {
      title: "Tên nhân viên",
      dataIndex: "name",
      key: "name",
      align: "center",
    },
    {
      title: "Vai trò",
      key: "role",
      align: "center",
      render: (_, record) => (
        <span>
          {record.employeeGroups
            ?.map((employeeGroup) => employeeGroup?.name)
            .join(", ")}
        </span>
      ),
    },
    {
      title: "Thao tác",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Tooltip title="Xóa">
          <BaseButton
            type="primary"
            bgColor={COLOR.RED[500]}
            hoverColor={COLOR.RED[700]}
            icon={<DeleteFilled rev={undefined} />}
            onClick={() => handleRemoved(record)}
          />
        </Tooltip>
      ),
    },
  ];

  return (
    <Modal
      title="CHỌN NHÂN VIÊN SHOP"
      open={open}
      onCancel={onClose}
      footer={null}
      width={1300}
    >
      <div className="space-y-6">
        {/* Search Section */}
        <Row gutter={16}>
          <Col span={18}>
            <Input
              placeholder="Thông tin nhân viên"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              suffix={<SearchOutlined rev={undefined} />}
            />
          </Col>
          <Col span={6}>
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              onClick={handleSearch}
              className="w-full"
            >
              TÌM KIẾM
            </BaseButton>
          </Col>
        </Row>
        {/* Available Employees Table */}
        <div>
          <Heading>DANH SÁCH NHÂN VIÊN</Heading>
          <Table
            loading={getListEmployeeState.loading}
            columns={availableEmployeeColumns}
            dataSource={listEmployee || []}
            rowKey="employeeId"
            size="small"
            bordered
            scroll={{ x: 800 }}
            pagination={{
              current: employeePaginationState?.currentPage || 1,
              total: (employeePaginationState?.totalPage || 0) * state.pageSize,
              pageSize: state.pageSize,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} của ${total} mục`,
              pageSizeOptions: ["5", "10", "15", "20"],
              onChange: (page, size) => {
                gotoPage(page);
                if (size !== state.pageSize) {
                  setState(
                    produce((draft) => {
                      draft.pageSize = size;
                    })
                  );
                }
              },
              onShowSizeChange: (_, size) => {
                setState(
                  produce((draft) => {
                    draft.pageSize = size;
                  })
                );
                gotoPage(1);
              },
            }}
          />
        </div>
        {/* Selected Employees Table */}
        <div>
          <Heading>
            NHÂN VIÊN ĐÃ CHỌN ({state.selectedEmployeeModal.length})
          </Heading>
          <Table
            columns={selectedEmployeeColumns}
            dataSource={state.selectedEmployeeModal}
            rowKey="employeeId"
            size="small"
            bordered
            scroll={{ y: 250 }}
            pagination={false}
          />
        </div>

        {/* Footer Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <BaseButton type="default" onClick={onClose}>
            HỦY
          </BaseButton>
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={() => onSave(state.selectedEmployeeModal)}
          >
            LƯU
          </BaseButton>
        </div>
      </div>
    </Modal>
  );
};
