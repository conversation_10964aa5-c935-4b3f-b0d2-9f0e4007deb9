/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useCallback, useEffect } from "react";

import produce from "immer";

import { showNotification } from "components/atoms/base/Notification";
import { showLoading } from "components/atoms/base/Spinner";
import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import useDerivedStateFromProps from "helpers/react-hooks/useDerivedStateFromProps";
import { useAsync } from "hooks/useAsync";
import { EmployeeDetailDtoType } from "modules/employee";
import { usePulldownStorages, BankDetailDtoType } from "modules/location";
import {
  updateShopDto,
  UpdateShopDtoType,
  shopDetailDto,
  ShopDetailDtoType,
} from "modules/shop";
import { getShopDetail, updateShopById } from "services/crm/location/shop";

import { optionsShopType } from "./constant";
import { UseUpdateShopDetail } from "./hooks/useUpdateDetail";

type ModalType = "employeeSelected" | "bankAccountSelected";

export type ShopItem = ShopDetailDtoType;
interface State {
  employeeSelected: Array<EmployeeDetailDtoType>;
  bankAccountSelected: Array<BankDetailDtoType>;
  isFranchesed?: boolean;
  modalState: {
    open: boolean;
    modalType?: ModalType;
  };
}

export interface ShopDetailPageVmProps {
  shopId: number;
}

export const ShopDetailPageVM = ({ shopId }: ShopDetailPageVmProps) => {
  const [state, setState] = useState<State>({
    employeeSelected: [],
    bankAccountSelected: [],
    isFranchesed: false,
    modalState: {
      open: false,
      modalType: undefined,
    },
  });
  const { updateShopDetailExe } = UseUpdateShopDetail();

  const [getShopDetailExec, getShopDetailState] = useAsync(
    useCallback(
      (params: { shopId: number }) =>
        getShopDetail({ ...params }).then((res) =>
          shopDetailDto(res.data.data)
        ),
      []
    )
  );

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.modalType = type;
      })
    );
  }, []);

  const handleCloseModal = useCallback(() => {
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.modalType = undefined;
      })
    );
  }, []);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) => {
      return state.modalState.open && state.modalState.modalType === type;
    },
    [state.modalState]
  );

  const getOptionByValue = (optionValue?: string) =>
    optionValue && optionsShopType.find(({ value }) => value === optionValue);

  const selectedShopType = (optionValue?: string) =>
    getOptionByValue(optionValue) || [];

  const { storageOptions, fetchStorages, formatStorageOption } =
    usePulldownStorages({
      excludePending: true,
    });

  const onToggleFranchisedShop = useCallback(
    (event: any) => {
      setState(
        produce((draft) => {
          draft.isFranchesed = event.target.checked;
        })
      );
    },
    [setState]
  );

  const [updateShopDetailExec, updateShopDetailState] = useAsync(
    updateShopById,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Cập nhật thành công" });
      }, []),
      onFailed: useCallback((error: any) => {
        const errMessage = getErrorMessageViaErrCode(error?.errors?.[0]?.code);

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const shopItem = getShopDetailState.data;

  const handleUpdateShopDetail = useCallback(
    (rawPayload: Partial<UpdateShopDtoType>) => {
      if (!shopItem?.shopId) return;
      const updateShopItemPayload = updateShopDto({
        ...shopItem,
        ...rawPayload,
      });

      updateShopDetailExec(shopItem.shopId, updateShopItemPayload);
    },
    [updateShopDetailExec, shopItem]
  );

  useEffect(() => {
    if (shopId && !Number.isNaN(shopId)) {
      getShopDetailExec({ shopId });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shopId]);

  const removeEmployees = useCallback(
    (item: EmployeeDetailDtoType) => {
      const index = state.employeeSelected.findIndex((el) => {
        return el.employeeId === item.employeeId;
      });
      setState(
        produce((draft) => {
          draft.employeeSelected.splice(index, 1);
        })
      );
    },
    [state.employeeSelected]
  );

  const onSaveModalEmployees = useCallback(
    (value?: Array<EmployeeDetailDtoType>) => {
      setState(
        produce((draft) => {
          draft.employeeSelected = value || [];
        })
      );
      handleCloseModal();
    },
    [handleCloseModal]
  );

  const removeBankAccounts = useCallback(
    (item: BankDetailDtoType) => {
      const index = state.bankAccountSelected.findIndex((el) => {
        return el.bankAccountId === item.bankAccountId;
      });
      setState(
        produce((draft) => {
          draft.bankAccountSelected.splice(index, 1);
        })
      );
    },
    [state.bankAccountSelected]
  );

  const onSaveModalBankAccounts = useCallback(
    (value?: Array<BankDetailDtoType>) => {
      setState(
        produce((draft) => {
          draft.bankAccountSelected = value || [];
        })
      );
      handleCloseModal();
    },
    [handleCloseModal]
  );

  useDerivedStateFromProps((_, nextEmployees) => {
    setState(
      produce((draft) => {
        draft.employeeSelected = nextEmployees || [];
      })
    );
  }, getShopDetailState.data?.employees);

  useDerivedStateFromProps((_, nextBankAccounts) => {
    setState(
      produce((draft) => {
        draft.bankAccountSelected = nextBankAccounts || [];
      })
    );
  }, getShopDetailState.data?.bankAccounts);

  useDerivedStateFromProps((_, nextProps) => {
    setState(
      produce((draft) => {
        draft.isFranchesed = nextProps || false;
      })
    );
  }, getShopDetailState.data?.isFranchisedShop);

  const handleUpdate = (payload: any) => {
    updateShopDetailExe(shopId, payload).then((res) => {
      console.log(res, "aa");

      if (res.status === 200) {
        showNotification({
          type: "success",
          message: "Cập nhật thành công",
        });
        showLoading(false);
      }
    });
  };

  return {
    loading: getShopDetailState.loading,
    shopDetailData: getShopDetailState.data,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    storageOptions,
    fetchStorages,
    formatStorageOption,
    getOptionByValue,
    selectedShopType,
    onToggleFranchisedShop,
    isFranchesed: state.isFranchesed,
    removeEmployees,
    removeBankAccounts,
    employeeSelected: state.employeeSelected,
    bankAccountSelected: state.bankAccountSelected,
    updateShopDetailState,
    handleUpdateShopDetail,
    onSaveModalEmployees,
    onSaveModalBankAccounts,
    handleUpdate,
  };
};
