import { useState, useCallback, useRef } from "react";

import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import {
  Calendar,
  ExternalCalendarRegister as CalendarRegister,
} from "components/molecules/calendar";
import { Col, Row } from "components/organisms/grid";
import { Modal } from "components/organisms/modal";
import dayjs from "helpers/dayjs";

export interface FilterTimeModalProps {
  open: boolean;
  onClose?: () => void;
}

interface State {
  activeIndex: number;
  value: Date | undefined;
  valueEnd: Date | undefined;
  isRangePicker: boolean;
}

export const FilterTimeModal: React.FC<FilterTimeModalProps> = ({
  open,
  onClose,
}: FilterTimeModalProps) => {
  const [state, setState] = useState<State>({
    activeIndex: 0,
    value: new Date(),
    valueEnd: undefined,
    isRangePicker: false,
  });

  const calendarRef = useRef<CalendarRegister>();

  const handleSetState = useCallback(
    (
      activeIndex: number,
      value: Date | undefined,
      valueEnd: Date | undefined,
      isRangePicker: boolean
    ) => {
      if (state.activeIndex === activeIndex) return;
      setState({
        activeIndex,
        value,
        valueEnd,
        isRangePicker,
      });
    },
    [state.activeIndex]
  );

  return (
    <Modal
      style={{ content: { maxWidth: 800 } }}
      onCloseModal={onClose}
      isClosable
      isOpen={open}
    >
      <Heading type="h1">LỌC THEO THỜI GIAN</Heading>
      <div className="u-mb-15" />
      <Row>
        <Col sm={3}>
          <Button
            modifiers={state.activeIndex === 0 ? "primary" : "secondary"}
            buttonType="textbutton"
            type="button"
            onClick={() => handleSetState(0, new Date(), undefined, false)}
          >
            Hôm nay
          </Button>
          <div className="u-mb-15" />
          <Button
            modifiers={state.activeIndex === 1 ? "primary" : "secondary"}
            buttonType="textbutton"
            type="button"
            onClick={() =>
              handleSetState(
                1,
                new Date(Date.parse(dayjs().add(-1, "day").toString())),
                undefined,
                false
              )
            }
          >
            Hôm qua
          </Button>
          <div className="u-mb-15" />
          <Button
            modifiers={state.activeIndex === 2 ? "primary" : "secondary"}
            buttonType="textbutton"
            type="button"
            onClick={() =>
              handleSetState(
                2,
                new Date(Date.parse(dayjs().add(-7, "day").toString())),
                new Date(),
                true
              )
            }
          >
            7 ngày trước
          </Button>
          <div className="u-mb-15" />
          <Button
            modifiers={state.activeIndex === 3 ? "primary" : "secondary"}
            buttonType="textbutton"
            type="button"
            onClick={() =>
              handleSetState(
                3,
                new Date(Date.parse(dayjs().add(-30, "day").toString())),
                new Date(),
                true
              )
            }
          >
            30 ngày trước
          </Button>
          <div className="u-mb-15" />
          <Button
            modifiers={state.activeIndex === 4 ? "primary" : "secondary"}
            buttonType="textbutton"
            type="button"
            onClick={() =>
              handleSetState(
                4,
                new Date(Date.parse(dayjs().date(1).toString())),
                new Date(),
                true
              )
            }
          >
            Tháng này
          </Button>
          <div className="u-mb-15" />
          <Button
            modifiers={state.activeIndex === 5 ? "primary" : "secondary"}
            buttonType="textbutton"
            type="button"
            onClick={() =>
              handleSetState(
                5,
                new Date(
                  Date.parse(
                    dayjs().add(-1, "month").startOf("month").toString()
                  )
                ),
                new Date(
                  Date.parse(dayjs().add(-1, "month").endOf("month").toString())
                ),
                true
              )
            }
          >
            Tháng trước
          </Button>
          <div className="u-mb-15" />
          <Button
            modifiers={state.activeIndex === 6 ? "primary" : "secondary"}
            buttonType="textbutton"
            type="button"
            onClick={() =>
              handleSetState(
                6,
                new Date(Date.parse(dayjs().startOf("year").toString())),
                new Date(),
                true
              )
            }
          >
            Năm này
          </Button>
          <div className="u-mb-15" />
          <Button
            modifiers={state.activeIndex === 7 ? "primary" : "secondary"}
            buttonType="textbutton"
            type="button"
            onClick={() => {
              handleSetState(7, undefined, undefined, true);
              calendarRef.current?.reset();
            }}
          >
            Tùy chỉnh
          </Button>
        </Col>
        <Col sm={9}>
          <div style={{ maxWidth: 300 }} className="mx-auto">
            <Calendar
              value={state.value}
              isRangePicker={state.isRangePicker}
              valueEnd={state.valueEnd}
              register={calendarRef}
            />
          </div>
        </Col>
      </Row>
      <div className="d-flex justify-content-end u-mt-20">
        <div className="u-mr-15">
          <Button buttonType="outline" modifiers="secondary" onClick={onClose}>
            HUỶ
          </Button>
        </div>
        <Button>LỌC</Button>
      </div>
    </Modal>
  );
};
