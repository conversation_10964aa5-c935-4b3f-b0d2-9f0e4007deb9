.a-checkbox {
	$root: &;

	position: relative;
	display: inline-block;

	&_label {
		display: flex;
		align-items: center;
		margin: 0;
		cursor: pointer;
	}

	&_input {
		position: absolute;
		pointer-events: none;
		opacity: 0;

		&:checked + #{$root}_checkmark {
			background-color: $COLOR-DENIM;
			border: 0;
		}
	}

	&_checkmark {
		position: relative;
		flex: 0 0 auto;
		width: 18px;
		height: 18px;
		background-color: $COLOR-WHITE;
		border: rem(1) solid $COLOR-QUARTZ;
		border-radius: rem(1);

		.a-icon {
			position: absolute;
			top: 50%;
			left: 50%;
			width: rem(9.75);
			height: rem(7.5);
			transform: translate(-50%, -50%);
		}

		#{$root}_label:hover & {
			opacity: 0.7;
		}

		#{$root}-disabled & {
			opacity: 0.7;
		}
	}

	&_content {
		margin-left: rem(6);
		font-size: rem(14);
	}

	&-disabled {
		pointer-events: none;
	}
}
