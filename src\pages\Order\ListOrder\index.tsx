/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable */
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import {
  DeleteOutlined,
  EditOutlined,
  InfoCircleOutlined,
  PhoneOutlined,
  RetweetOutlined,
  UserOutlined,
} from "@ant-design/icons";
import {
  Card,
  Col,
  DatePicker,
  Flex,
  Modal,
  Pagination,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { PaginationReference } from "components/molecules/pagination";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import dayjs from "helpers/dayjs";
import { primaryPhone } from "modules/customer";
import "./index.css";

import { ColumnsType } from "antd/es/table";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import {
  FilterPickerCollapse,
  FilterPickerCollapseRef,
  FilterType,
} from "components/atoms/base/shared/FilterPickerCollapse";
import { showLoading } from "components/atoms/base/Spinner";
import { COLOR } from "constants/color";
import {
  fromDefaltOptionToData,
  mappedDataToDefaultOption,
} from "helpers/convertDataToDefaultOption.helper";
import { formatCurrencyWithSuffix } from "helpers/currency.helper";
import _ from "lodash";
import { orderTypeOptions } from "modules/order";
import {
  UseGetListShop,
  UseGetListShopV2,
} from "pages/System/ShopDetail/hooks/useGetListShop";
import { useGetListEmployeeWithoutPagination } from "services/crm/employee";
import { useGetOrderSourceWithoutPagination } from "services/crm/order/hooks/order-source.hooks";
import {
  useGetListTaglineDontByOrderStatus,
  useGetOrderStatusTaglineDetail,
  useGetOrderStatusTaglineWithoutPagination,
  useGetOrderStatusWithoutPagination,
} from "services/crm/order/hooks/order-status.hook";
import RootPageRouter from "..";
import { useGetListDeliveryPartnerWithoutPagination } from "../ListDeliveryPartner/hooks/useGetListDeliveryPartner";
import { ListOrderPageVm } from "./vm";
import { useGetCities } from "services/crm/location/hooks/location.hooks";
import {
  OrderStatusDto,
  OrderStatusTaglineDto,
} from "services/crm/order/dto/order-status.dto";
import { OrderSourceDto } from "services/crm/order/dto/order-source.dto";

const { RangePicker } = DatePicker;
const { Option } = Select;

interface OrderRecord {
  code: string;
  orderId: number;
  phoneNumber?: string;
  cityId?: number;
  city?: {
    name: string;
    cityId: number;
  };
  shopSent: number;
  shop: {
    shopId: number;
    name: string;
  };
  zipcode?: string;
  customer?: {
    name: string;
    phone?: string;
  };
  name: string;
  shippingAddress?: {
    city?: {
      city_name: string;
    };
  };
  orderNote?: string;
  items: {
    sku: { code: string };
    quantity: number;
  }[];
  totalAmount: number;
  pay: number;
  createdAt: string;
  deliveryNote?: string;
  orderStatus?: {
    name: string;
    color: string;
  };
  orderStatusTagline?: {
    name: string;
    color: string;
  };
  returnOrderStatus?: {
    name: string;
    color: string;
  };
  returnOrderStatusTagline?: {
    name: string;
    color: string;
  };
  shipping: number;
  shippingDetail?: {
    name: string;
  };

  shippingCost?: number;
  assignedEmployeeId: number;
  assignedEmployee: {
    name: string;
    employeeId: number;
  };
  orderSourceId: number;
  orderSource?: {
    name: string;
    orderSourceId: number;
  };
  orderStatusTaglineId: number;
  orderStatusId: number;
  // NEW FIELDS DATE //
  confirmDate?: string;
  confirmedBy?: string;
  deliveryDate?: string;
  returnDate?: string;
  completeDate?: string;
  cancelDate?: string;
  createdBy: string;
}

type FormFilterType = Pick<
  FilterType,
  | "inputSearch"
  | "dateChangeStatus"
  | "dateChangeShop"
  | "changeOrderDate"
  | "createdAt"
  | "orderStatusId"
  | "orderSourceId"
  | "orderStatusTaglineId"
  | "paymentMethod"
  | "deliveryMethod"
  | "employeeId"
  | "franchiseStore"
>;

const OrderDataTablePage = () => {
  const filterCollapseRef = useRef<FilterPickerCollapseRef>(null);
  // const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState<any[]>([]);
  const {
    gotoPage,
    toggleSortOrderBy,
    orderData,
    loading,
    orderPaginationState,
    gotoDetailOrderPage,
    gotoEditOrderPage,
    gotoExchangeOrderPage,
    goToDetailExchangeOrderPage,
    gotoEditExchangeOrderPage,
    pageSize,
    handleChangePageSize,
    filterState: { openForm, taglineDependencies },
    setSearchPayload,
    deleteOrderExe,
    handleRefetch,
    totalRecords,
  } = ListOrderPageVm();
  const paginationRef = useRef<PaginationReference>(null);

  const { deliveryData, deliveryRefetch } =
    useGetListDeliveryPartnerWithoutPagination();

  const { orderStatusData, orderStatusRefetch } =
    useGetOrderStatusWithoutPagination({
      searchText: "",
    });

  const { orderSourceData, orderSourceRefetch } =
    useGetOrderSourceWithoutPagination();

  const { listShopExe, listShopState } = UseGetListShopV2();
  const {
    listTaglineDontByOrderStatusRefetch,
    listTaglineDontByOrderStatusData,
  } = useGetListTaglineDontByOrderStatus();

  const { employeeRefetch, employeeData } =
    useGetListEmployeeWithoutPagination();

  const { cityRefetch, cityData } = useGetCities();

  const deliveryOption = useMemo(() => {
    if (_.size(deliveryData?.data) > 0) {
      return mappedDataToDefaultOption({
        data: deliveryData.data,
        keyValue: "deliveryPartnerId",
        keyLabel: "name",
        restItem: true,
      });
    } else {
      return [];
    }
  }, [deliveryData]);

  const orderStatusOption = useMemo(() => {
    if (_.size(orderStatusData?.data) > 0) {
      return mappedDataToDefaultOption({
        restItem: true,
        data: orderStatusData?.data,
        keyValue: "orderStatusId",
        keyLabel: "name",
      });
    } else {
      return [];
    }
  }, [orderStatusData]);

  const orderStatusTaglineOption = useMemo(() => {
    if (_.size(listTaglineDontByOrderStatusData?.data) > 0) {
      return mappedDataToDefaultOption({
        restItem: true,
        data: listTaglineDontByOrderStatusData?.data,
        keyValue: "taglineId",
        keyLabel: "name",
      });
    } else {
      return [];
    }
  }, [listTaglineDontByOrderStatusData]);

  const orderSourceOption = useMemo(() => {
    if (_.size(orderSourceData?.data) > 0) {
      return mappedDataToDefaultOption({
        data: orderSourceData?.data,
        keyValue: "orderSourceId",
        keyLabel: "name",
        restItem: true,
      });
    } else {
      return [];
    }
  }, [orderSourceData]);

  const listShopOptions = useMemo(() => {
    if (_.size(listShopState) > 0) {
      const listShops = mappedDataToDefaultOption({
        data: listShopState?.data,
        keyValue: "shopId",
        keyLabel: "name",
        restItem: true,
      });
      return listShops.filter((item) => item?.isFranchisedShop === true);
    } else {
      return [];
    }
  }, [listShopState]);

  const employeeOptions = useMemo(() => {
    if (_.size(employeeData?.data) > 0) {
      return mappedDataToDefaultOption({
        data: employeeData?.data,
        keyValue: "employeeId",
        keyLabel: "name",
        restItem: true,
      });
    } else {
      return [];
    }
  }, [employeeData]);

  useEffect(() => {
    deliveryRefetch();
    orderStatusRefetch();
    orderSourceRefetch();
    employeeRefetch();
    listShopExe();
    listTaglineDontByOrderStatusRefetch();
    cityRefetch();
  }, []);

  const handleOnChangePageSizePulldown = useCallback(
    (value: string) => {
      if (value) {
        handleChangePageSize(Number(value));
        if (paginationRef.current) paginationRef.current.reset();
      }
    },
    [handleChangePageSize]
  );

  const handleSubmitOrderSearch = useCallback(
    (values: FormFilterType) => {
      const {
        changeOrderDate,
        dateChangeShop,
        createdAt,
        dateChangeStatus,
        orderStatusTaglineId,
        orderStatusId,
        paymentMethod,
        ...restValues
      } = values;
      const { rangeDateChange, statusId } = dateChangeStatus ?? {};
      const payloadSearch = {
        ...restValues,
        paymentMethod: paymentMethod || null,
        orderStatusId: orderStatusId || null,
        orderStatusTaglineId: orderStatusTaglineId || null,
        changeStatusId: statusId || null,
        startChangeStatusDate: rangeDateChange
          ? dayjs(rangeDateChange[0]).startOf("day").toISOString()
          : null,
        endChangeStatusDate: rangeDateChange
          ? dayjs(rangeDateChange[1]).endOf("day").toISOString()
          : null,
        startChangeOrderDate: changeOrderDate
          ? dayjs(changeOrderDate[0]).startOf("day").toISOString()
          : null,
        endChangeOrderDate: changeOrderDate
          ? dayjs(changeOrderDate[1]).endOf("day").toISOString()
          : null,
        startChangeShopDate: dateChangeShop
          ? dayjs(dateChangeShop[0]).startOf("day").toISOString()
          : null,
        endChangeShopDate: dateChangeShop
          ? dayjs(dateChangeShop[1]).endOf("day").toISOString()
          : null,
        startCreatedAt: createdAt
          ? dayjs(createdAt[0]).startOf("day").toISOString()
          : null,
        endCreatedAt: createdAt
          ? dayjs(createdAt[1]).endOf("day").toISOString()
          : null,
      };

      setSearchPayload(payloadSearch);
      paginationRef.current?.reset();
    },
    [setSearchPayload]
  );

  const handleConfirmDelete = (record: OrderRecord) => {
    const { orderId } = record ?? {};
    Modal.confirm({
      centered: true,
      okText: "Xóa",
      okType: "primary",
      okButtonProps: {
        className: "!bg-red-500 hover:!bg-red-700 text-white",
      },
      cancelText: "Hủy",
      title: "Xác nhận xóa",
      content: `Bạn có chắc chắn muốn xóa đơn hàng "${record.code}" không?`,
      onOk: async () => {
        showLoading(true);
        // Call delete API here
        try {
          await deleteOrderExe({
            orderId,
          }).then(() => {
            showNotification({
              type: "success",
              message: "Xóa đơn hàng thành công",
            });
            handleRefetch();
          });
        } catch (err) {
          const errorApi = err?.response?.data?.errors || [];
          console.log(errorApi, "errorApi");
          if (errorApi) {
            errorApi.forEach((error: any) => {
              showNotification({
                type: "error",
                message: error?.title || "Xóa đơn hàng thất bại",
              });
            });
          }
        } finally {
          showLoading(false);
        }
      },
    });
  };

  function findOrderTypeValueReturnLabel(orderType: number) {
    const foundOption = orderTypeOptions.find(
      (option) => option.value === orderType
    );
    return foundOption ? foundOption.label : "";
  }

  const processData = async (orderData: any[]) => {
    // console.log("processData called with orderData:", orderData);
    const convertData = await Promise.all(
      orderData.map(async (item) => {
        try {
          const {
            orderStatusTaglineId,
            orderStatusId,
            orderSourceId,
            shipping,
            assignedEmployeeId,
            shopSent,
            cityId,
          } = item;
          let orderStatusTagline: Partial<OrderStatusTaglineDto> | null = null;
          let orderSource: Partial<OrderSourceDto> | null = null;
          let orderStatus: Partial<OrderStatusDto> | null = null;
          let assignedEmployee: any = null;
          let shippingDetail: any = null;
          let city: any = null;
          let shop: any = null;

          if (orderStatusId) {
            orderStatus = fromDefaltOptionToData({
              data: orderStatusOption,
              valueKey: "orderStatusId",
              value: orderStatusId,
            });
          }

          if (!_.isNil(orderStatusTaglineId)) {
            orderStatusTagline = fromDefaltOptionToData({
              data: orderStatusTaglineOption,
              valueKey: "taglineId",
              value: orderStatusTaglineId,
            });
          }

          if (orderSourceId) {
            orderSource = fromDefaltOptionToData({
              data: orderSourceOption,
              valueKey: "value",
              value: orderSourceId,
            });
          }

          if (assignedEmployeeId) {
            assignedEmployee = fromDefaltOptionToData({
              data: employeeOptions,
              valueKey: "value",
              value: assignedEmployeeId,
            });
          }

          if (shipping) {
            shippingDetail = fromDefaltOptionToData({
              data: deliveryOption,
              valueKey: "value",
              value: shipping,
            });
          }

          if (cityId) {
            city = fromDefaltOptionToData({
              data: cityData?.data,
              valueKey: "cityId",
              value: cityId,
            });
          }

          if (shopSent) {
            shop = fromDefaltOptionToData({
              data: listShopState?.data,
              valueKey: "shopId",
              value: Number(shopSent),
            });
          }

          return {
            ...item,
            key: item._id,
            orderStatusTagline,
            orderStatus,
            orderSource,
            assignedEmployee,
            shippingDetail,
            city,
            shop,
          };
        } catch (err) {
          console.error("❌ Error processing item:", item, err);
          return {
            ...item,
            key: item._id,
            error: true,
          };
        }
      })
    );

    // console.log(convertData, "convertData");
    setDataSource(convertData);
  };

  useEffect(() => {
    if (orderData?.length > 0) {
      processData(orderData);
    } else {
      setDataSource([]);
    }
  }, [
    orderData,
    orderSourceOption,
    orderStatusOption,
    employeeOptions,
    deliveryOption,
    listShopState,
  ]);

  // Define columns for Ant Design Table
  const columns: ColumnsType<OrderRecord> = [
    {
      title: "STT",
      key: "index",
      width: 100,
      fixed: "left",
      render: (_: any, __: any, index: number) => {
        return (orderPaginationState.currentPage - 1) * pageSize + index + 1;
      },
      align: "center",
    },
    {
      title: "Mã ĐH",
      dataIndex: "code",
      key: "code",
      sorter: true,
      fixed: "left",

      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("orderCode"),
      }),
      align: "center",
      width: 164,
      render: (code, record) => {
        const { zipcode } = record ?? {};
        return (
          <Flex vertical align="center" gap={8}>
            <Tag color={COLOR.BLUE[400]}>{code}</Tag>
            {zipcode && <Tag color={COLOR.ORANGE[400]}>{zipcode}</Tag>}
          </Flex>
        );
      },
    },
    {
      title: "Khách hàng",
      dataIndex: "customer",
      key: "customer",
      sorter: true,
      align: "center",
      width: 180,
      // fixed: "left",

      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("customerName"),
      }),
      render: (customer: any, record) => {
        const { phoneNumber, name: customerName } = record ?? {};
        return (
          <Flex vertical align="center" gap={8}>
            <Typography>
              <UserOutlined /> {customerName}
            </Typography>
            <Typography.Text>
              <PhoneOutlined /> {phoneNumber || "--"}
            </Typography.Text>
            {/* {primaryPhone(customer)} */}
          </Flex>
        );
      },
    },

    {
      title: "Tỉnh/Tp",
      dataIndex: "cityId",
      key: "city",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("city"),
      }),
      align: "center",
      width: 172,
      render: (value, record) => {
        const { city } = record ?? {};
        return <Typography>{city?.name || "--"}</Typography>;
      },
    },
    {
      title: "Chi nhánh",
      dataIndex: "shopSent",
      key: "shopSent",
      align: "center",
      width: 172,
      render: (value, record) => {
        const { shop } = record ?? {};
        return <Typography>{shop?.name || "--"}</Typography>;
      },
    },
    {
      title: "Loại đơn hàng",
      dataIndex: "orderType",
      key: "orderType",
      width: 144,
      align: "center",
      render: (orderType) => {
        return (
          <Typography className="font-medium">
            {" "}
            {findOrderTypeValueReturnLabel(orderType)}
          </Typography>
        );
      },
    },
    {
      title: "Kênh bán hàng",
      dataIndex: "channel",
      key: "channel",
      width: 120,
      align: "center",
      render: (channel: string) => {
        if (channel === "1") {
          return "Online";
        } else if (channel === "2") {
          return "Offline";
        }
        return channel.charAt(0).toUpperCase() + channel.slice(1); // If it's a string, return it directly
      },
    },

    {
      title: "Nguồn",
      key: "orderSource",
      dataIndex: "orderSource",
      align: "center",
      width: 150,
      render: (orderSource, record) => {
        return <>{orderSource?.name || "--"}</>;
      },
    },

    {
      title: "Trạng thái",
      key: "orderStatus",
      dataIndex: "orderStatus",
      align: "center",
      width: 150,
      render: (orderStatus, record) => {
        const { orderStatusTagline } = record ?? {};

        return (
          <Flex vertical align="center" gap={8}>
            {orderStatus && (
              <Tag color={orderStatus?.color}>{orderStatus?.name}</Tag>
            )}
            {orderStatusTagline && (
              <Tag color={orderStatusTagline?.color}>
                {orderStatusTagline?.name}
              </Tag>
            )}
            {/* {record?.orderStatus && (
            <Tag color={record?.orderStatus?.color}>
              {record?.orderStatus.name}
            </Tag>
          )}

          {record?.orderStatus && record?.orderStatusTagline && (
            <>
              <br />
              <br />
            </>
          )}

          {record?.orderStatusTagline && (
            <Tag color={record?.orderStatusTagline.color}>
              {record?.orderStatusTagline.name}
            </Tag>
          )} */}
          </Flex>
        );
      },
    },

    {
      title: "Phân công",
      key: "assignedEmployeeId",
      dataIndex: "assignedEmployeeId",
      align: "center",
      width: 156,
      render: (_, record) => {
        const { assignedEmployee } = record ?? {};
        return <>{assignedEmployee?.name || "--"}</>;
      },
    },
    {
      title: "Vận chuyển",
      key: "shipping",
      align: "center",
      width: 156,
      render: (_, record) => {
        const { shippingDetail } = record ?? {};
        return <Typography>{shippingDetail?.name || "--"}</Typography>;
      },
    },
    {
      title: "Ghi chú đơn hàng",
      dataIndex: "orderNote",
      key: "orderNote",
      align: "center",
      width: 156,
    },
    {
      title: "Sản phẩm",
      key: "items",
      dataIndex: "items",
      width: 180,
      render: (items) => {
        // console.log(items, "items");
        // return items
        //   ?.map((item: { sku: { code: any }; quantity: number }) => {
        //     return `${item.quantity} ${item.sku.code}`;
        //   })
        //   .join(", ");
        return (
          <div>
            {items?.map(
              (
                item: { sku: { code: any }; quantity: number },
                index: number
              ) => (
                <div key={index}>
                  {`${item.quantity} ${item.sku.code}${
                    index < items.length - 1 ? "," : ""
                  }`}
                </div>
              )
            )}
          </div>
        );
      },
    },
    {
      title: "Số lượng",
      key: "quantity",
      dataIndex: "items",
      render: (items) =>
        items?.reduce(
          (quantity: number, item: { quantity: number }) =>
            quantity + item.quantity,
          0
        ),
      align: "center",
      width: 120,
    },

    {
      title: "Tổng tiền",
      dataIndex: "pay",
      key: "pay",
      align: "center",
      width: 144,
      render: (pay: number) => {
        return (
          <Typography className="font-semibold">
            {formatCurrencyWithSuffix(pay)}
          </Typography>
        );
      },
    },

    {
      title: "Phí ship",
      dataIndex: "shippingCost",
      key: "shippingCost",
      align: "center",
      width: 120,
      render: (shipping: number) => {
        return (
          <Typography className="font-semibold">
            {shipping ? formatCurrencyWithSuffix(shipping) : "--"}
          </Typography>
        );
      },
    },
    {
      title: "Tổng tiền hàng",
      key: "totalAmount",
      dataIndex: "totalAmount",
      align: "center",
      width: 120,
      render: (totalAmount: number) => {
        return (
          <Typography className="font-semibold">
            {formatCurrencyWithSuffix(totalAmount)}
          </Typography>
        );
      },
      // render: (items) => {
      //   const totalAmount = items?.reduce(
      //     (total: number, item: { unitPrice: number; quantity: number }) =>
      //       total + item.unitPrice * item.quantity,
      //     0
      //   );
      //   return <Typography>{formatCurrencyWithSuffix(totalAmount)}</Typography>;
      // },
    },
    // {
    //   title: "Người tạo",
    //   dataIndex: "createBy",
    //   key: "createBy",
    //   align: "center",
    //   width: 144,
    //   render: (createdBy: any) => {
    //     return <Typography.Text>{createdBy}</Typography.Text>;
    //   },
    // },
    {
      title: "Ngày tạo",
      dataIndex: "createdAt",
      key: "createdAt",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("createdDate"),
      }),
      align: "center",
      width: 144,
      render: (date: string, record) => {
        const { createdBy } = record ?? {};
        return (
          <div className="flex flex-col gap-1 items-center">
            {date ? dayjs(date).format("DD/MM/YYYY") : "--"}
            {createdBy && (
              <Typography.Text className="text-base text-gray-500">
                {createdBy}
              </Typography.Text>
            )}
          </div>
        );
      },
    },
    {
      title: "Ngày xác nhận",
      dataIndex: "confirmDate",
      key: "confirmDate",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("confirmDate"),
      }),
      align: "center",
      width: 144,
      render: (date: string, record) => {
        const { confirmedBy } = record ?? {};
        return (
          <div className="flex flex-col gap-1 items-center">
            {date ? dayjs(date).format("DD/MM/YYYY") : "--"}
            {confirmedBy && (
              <Typography.Text className="text-base text-gray-500">
                {confirmedBy}
              </Typography.Text>
            )}
          </div>
        );
      },
    },

    {
      title: "Ngày chuyển",
      dataIndex: "deliveryDate",
      key: "deliveryDate",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("deliveryDate"),
      }),
      render: (date: string) =>
        date ? dayjs(date).format("DD/MM/YYYY") : "--",
      align: "center",
      width: 144,
    },

    {
      title: "Ngày Hủy",
      dataIndex: "cancelDate",
      key: "cancelDate",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("cancelDate"),
      }),
      render: (date: string) =>
        date ? dayjs(date).format("DD/MM/YYYY") : "--",
      align: "center",
      width: 144,
    },

    {
      title: "Ngày thành công",
      dataIndex: "completeDate",
      key: "completeDate",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("completeDate"),
      }),
      render: (date: string) =>
        date ? dayjs(date).format("DD/MM/YYYY") : "--",
      align: "center",
      width: 144,
    },

    {
      title: "Ngày hoàn",
      dataIndex: "returnDate",
      key: "returnDate",
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortOrderBy("returnDate"),
      }),
      render: (date: string) =>
        date ? dayjs(date).format("DD/MM/YYYY") : "--",
      align: "center",
      width: 144,
    },

    {
      title: "Ghi chú giao hàng",
      dataIndex: "deliveryNote",
      key: "deliveryNote",
      align: "center",
      width: 150,
    },

    // {
    //   title: "Trạng thái đổi trả",
    //   key: "returnOrderStatus",
    //   render: (record: any) => (
    //     <>
    //       {record?.returnOrderStatus && (
    //         <Tag color={record?.returnOrderStatus?.color}>
    //           {record?.returnOrderStatus?.name}
    //         </Tag>
    //       )}

    //       {record?.returnOrderStatus && record?.returnOrderStatusTagline && (
    //         <>
    //           <br />
    //           <br />
    //         </>
    //       )}

    //       {record?.returnOrderStatusTagline && (
    //         <Tag color={record?.returnOrderStatusTagline?.color}>
    //           {record?.returnOrderStatusTagline?.name}
    //         </Tag>
    //       )}
    //     </>
    //   ),
    //   align: "center",
    //   width: 150,
    // },

    {
      title: "Lý do hủy",
      dataIndex: "cancelReason",
      key: "cancelReason",
      align: "center",
      width: 172,
    },
    {
      title: "Thao tác",
      key: "action",
      fixed: "right",
      width: 188,
      align: "center",
      render: (record: any) => {
        const { orderStatusId, orderType } = record ?? {};
        return (
          <Space>
            <>
              <Tooltip title="Chi tiết">
                <BaseButton
                  type="primary"
                  bgColor={COLOR.BLUE[500]}
                  hoverColor={COLOR.BLUE[700]}
                  icon={<InfoCircleOutlined rev={undefined} />}
                  onClick={() => {
                    if (orderType === 2) {
                      gotoDetailOrderPage(record);
                    } else if (orderType === 1) {
                      goToDetailExchangeOrderPage(record);
                    }
                  }}
                />
              </Tooltip>

              <Tooltip title="Chỉnh sửa" trigger="hover">
                <BaseButton
                  type="primary"
                  disabled={orderStatusId === 21 || orderStatusId === 18}
                  bgColor={COLOR.GREEN[500]}
                  hoverColor={COLOR.GREEN[700]}
                  icon={<EditOutlined rev={undefined} />}
                  onClick={() => {
                    if (orderStatusId !== 21) {
                      if (orderType === 2) {
                        gotoEditOrderPage(record);
                      } else {
                        gotoEditExchangeOrderPage(record);
                      }
                    } else {
                      showNotification({
                        type: "warning",
                        message: "Đơn hàng không đủ điều kiện chỉnh sửa",
                      });
                    }
                  }}
                />
              </Tooltip>

              <Tooltip title="Đổi hàng" trigger="hover">
                <BaseButton
                  type="primary"
                  bgColor={COLOR.ORANGE[500]}
                  disabled={orderStatusId !== 21 || orderType === 1}
                  hoverColor={COLOR.ORANGE[700]}
                  icon={<RetweetOutlined rev={undefined} />}
                  onClick={() => {
                    if (orderStatusId === 21 && orderType === 2) {
                      gotoExchangeOrderPage(record);
                    } else {
                      showNotification({
                        type: "warning",
                        message: "Đơn hàng không đủ điều kiện đổi hàng",
                      });
                    }
                  }}
                />
              </Tooltip>

              <Tooltip title="Xóa" trigger="hover">
                <BaseButton
                  disabled={orderStatusId !== 15}
                  type="primary"
                  bgColor={COLOR.RED[500]}
                  hoverColor={COLOR.RED[700]}
                  icon={<DeleteOutlined rev={undefined} />}
                  onClick={() => {
                    if (orderStatusId === 15) {
                      handleConfirmDelete(record);
                    } else {
                      showNotification({
                        type: "warning",
                        message: "Đơn hàng không đủ điều kiện xóa",
                      });
                    }
                  }}
                />
              </Tooltip>
            </>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <General>
        <title key="title">Danh sách đơn hàng</title>
        <Section>
          <div className="p-4">
            <Card
              styles={{
                body: {
                  padding: 0,
                },
              }}
              title="DANH SÁCH ĐƠN HÀNG"
              // extra={
              //   <BaseButton
              //     // type="primary"
              //     // size="large"
              //     icon={<SettingOutlined rev={undefined} />}
              //     href={RootPageRouter.children.createOrder.path}
              //     style={{
              //       display: "flex",
              //       alignItems: "center",
              //       justifyContent: "center",
              //     }}
              //     className="w-fit"

              //     // bgColor={COLOR.BLUE[500]}
              //     // hoverColor={COLOR.BLUE[700]}
              //   ></BaseButton>
              // }
              // style={{ marginLeft: "2.5%", marginRight: "2.5%" }}
            >
              {/* <Collapse accordion items={itemsCollapse} /> */}

              <div className="flex flex-col gap-3">
                <div className="p-6">
                  <FilterPickerCollapse
                    storageKey="listOrderFilter"
                    onFinish={handleSubmitOrderSearch}
                    ref={filterCollapseRef}
                    defaultSelectedKeys={[
                      "orderStatusId",
                      "inputSearch",
                      "dateChangeStatus",
                      "orderSourceId",
                      "dateChangeShop",
                      "changeOrderDate",
                      "orderStatusTaglineId",
                    ]}
                    listFieldsUsed={[
                      "orderType",
                      "orderStatusId",
                      "inputSearch",
                      "dateChangeStatus",
                      "orderSourceId",
                      "dateChangeShop",
                      "changeOrderDate",
                      "orderStatusTaglineId",
                      "createdAt",
                      "paymentMethod",
                      "deliveryMethod",
                      "employeeId",
                      "franchiseStore",
                    ]}
                    customLabels={{
                      inputSearch: "Thông tin tìm kiếm",
                      employeeId: "Nhân viên bán hàng",
                    }}
                    listOptions={{
                      deliveryMethod: deliveryOption,
                      orderStatusId: orderStatusOption,
                      orderSourceId: orderSourceOption,
                      franchiseStore: listShopOptions,
                      employeeId: employeeOptions,
                      dateChangeStatus: orderStatusOption,
                      orderStatusTaglineId: orderStatusTaglineOption,
                    }}
                    buttonActions={{
                      add: () => {
                        RootPageRouter.gotoChild("createOrder");
                      },
                      update: () => {
                        showNotification({
                          type: "info",
                          message: "Feature is processing..",
                        });
                      },
                      export: () => {
                        showNotification({
                          type: "info",
                          message: "Feature is processing...",
                        });
                      },
                    }}
                  />
                </div>

                <Table
                  columns={columns as any}
                  dataSource={dataSource}
                  loading={loading}
                  size="small"
                  scroll={{ x: 3000 }}
                  pagination={false}
                  rowKey="_id"
                />

                <Row justify="space-between" align="middle" className="p-6">
                  <Col>
                    <div className="flex items-center gap-2">
                      <Typography.Text>Số lượng hiển thị:</Typography.Text>
                      <Select
                        placeholder="Số lượng hiển thị"
                        value={`${pageSize}`}
                        onChange={handleOnChangePageSizePulldown}
                        style={{ width: 150 }}
                      >
                        {[5, 10, 15, 25, 30].map((size) => (
                          <Option key={size} value={`${size}`}>
                            {size}
                          </Option>
                        ))}
                      </Select>
                    </div>
                  </Col>
                  <Col>
                    {orderPaginationState.totalPage && (
                      <Pagination
                        total={totalRecords}
                        showSizeChanger={false}
                        defaultCurrent={1}
                        current={orderPaginationState.currentPage}
                        pageSize={pageSize}
                        showQuickJumper={true}
                        onChange={gotoPage}
                        showTotal={(total, range) =>
                          `${range[0]}-${range[1]} của ${total} mục`
                        }
                      />
                    )}
                  </Col>
                </Row>
              </div>
            </Card>
          </div>
        </Section>
      </General>
    </div>
  );
};

export default OrderDataTablePage;
