import crmDriverV1 from "../crm-driver-v1";
import {
  CreatePaymentOrderDto,
  DeletePaymentOrderDto,
  GetListPaymentOrderByOrderIdDto,
  GetListPaymentOrderDto,
} from "./dto/payment-order.dto";

const PAYMENT_ORDERE_URL = "/orders/external/payment-orders";

const paymentOrderServices = {
  create: (dto: CreatePaymentOrderDto) => {
    const url = `${PAYMENT_ORDERE_URL}/create-payment-order`;
    return crmDriverV1.post(url, dto);
  },

  getListPaymentOrder: (
    dto: GetListPaymentOrderDto
  ): Promise<{ data: GetListPaymentOrderByOrderIdDto }> => {
    const url = `${PAYMENT_ORDERE_URL}/payment-order-by-orderId/${dto.orderId}`;
    return crmDriverV1.get(url);
  },

  delete: (dto: DeletePaymentOrderDto) => {
    const url = `${PAYMENT_ORDERE_URL}/delete-payment-order/${dto.paymentOrderId}`;
    return crmDriverV1.delete(url);
  },
};

export default paymentOrderServices;
