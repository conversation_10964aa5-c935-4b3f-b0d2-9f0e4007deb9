import React from "react";

import { Image } from "components/atoms/image";
import { mapModifiers } from "helpers/component";
import dayjs from "helpers/dayjs";

export interface Props {
  contentLeft?: boolean;
  src: string;
  children?: React.ReactNode;
  createdAt?: Date;
}

export const BoxTextMessage: React.FC<Props> = ({
  contentLeft,
  src,
  children,
  createdAt,
}) => (
  <div className={mapModifiers("m-boxmessage", contentLeft && "contentleft")}>
    <div className="m-boxmessage_avatar">
      <Image src={src} alt="avatar" aspectRatio="1x1" />
    </div>
    <div>
      <div className="m-boxmessage_createdAt">
        {dayjs(createdAt).format("DD/MM/YYYY HH:mm")}
      </div>
      <div className="m-boxmessage_content">
        <span className="m-boxmessage_leaf" />
        <div className="m-boxmessage_message">{children}</div>
      </div>
    </div>
  </div>
);
