import crmDriverV1 from "../crm-driver-v1";
import {
  GetListItemLocationStockBySkuIdResponseDto,
  GetListItemLocationStockBySkyIdDto,
} from "./dtos/item-location-stock.dto";
import {
  GetCitiesResponseDto,
  GetDistrictResponseDto,
  GetDistrictsDto,
} from "./dtos/location.dto";

const LOCATION_URL_EXTERNAL = "/locations/external";
const LOCATION_STOCK_URL = "/locations/external/item-stock-locations";

const locationService = {
  /**
   * @Module LOCATION
   */
  getCities: (): Promise<{ data: GetCitiesResponseDto }> => {
    const url = `${LOCATION_URL_EXTERNAL}/cities/getAllCity`;
    return crmDriverV1.get(url);
  },
  getDistricts: (
    dto: GetDistrictsDto
  ): Promise<{ data: GetDistrictResponseDto }> => {
    const url = `${LOCATION_URL_EXTERNAL}/districts?cityId=${dto.cityId}`;
    return crmDriverV1.get(url);
  },
  getWards: (districtId) => {
    const url = `${LOCATION_URL_EXTERNAL}/wards?districtId=${districtId}`;
    return crmDriverV1.get(url);
  },
  getAllDistricts: (): Promise<{ data: GetDistrictResponseDto }> => {
    const url = `${LOCATION_URL_EXTERNAL}/districts/getAllDistricts`;
    return crmDriverV1.get(url);
  },
  /**
   * @Module ITEM_LOCATION_STOCK
   */

  getListLocationStock: (
    dto: GetListItemLocationStockBySkyIdDto
  ): Promise<{
    data: GetListItemLocationStockBySkuIdResponseDto;
  }> => {
    const url = `${LOCATION_STOCK_URL}/getItemStockLocationBySkuId`;
    return crmDriverV1.post(url, dto);
  },
};

export default locationService;
