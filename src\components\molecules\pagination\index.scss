.m-pagination {
	$root: &;

	&_wrapper {
		display: flex;
		font-family: $FONTFAMILY-ROBOTO;
		font-size: rem(14);
		color: $COLOR-QUARTZ;
		list-style-type: none;

		&-center {
			justify-content: center;
		}

		li {
			min-width: rem(30);
			height: rem(30);
			line-height: rem(30);
			text-align: center;
			cursor: pointer;
			background-color: $COLOR-WHITE;
			border: solid 1px $COLOR-PLATINUM-4;
			transition: opacity 0.3s ease-in-out;

			&:not(:last-child) {
				margin-right: rem(5);
			}

			&:hover {
				opacity: 0.7;
			}

			&#{$root}_actived {
				color: $COLOR-WHITE;
				cursor: unset;
				background-color: $COLOR-DENIM;
				border: 0;
			}
		}
	}

	&_first,
	&_previous,
	&_next,
	&_last {
		position: relative;
		overflow: hidden;
		border: solid 1px $COLOR-PLATINUM-4;
		transition: all 0.3s ease-in-out;

		.a-icon {
			width: rem(6);
			margin-bottom: 3px;
		}

		&:hover {
			opacity: 0.7;
		}

		&-disabled {
			pointer-events: none;
			.a-icon {
				opacity: 0.5;
			}
		}
	}
}
