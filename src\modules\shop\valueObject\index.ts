import { emptyIfNull } from "helpers/string";

import { ShopEntityType } from "../entities";

export const shopOption = (shop?: ShopEntityType) => ({
  label: emptyIfNull(shop?.name),
  value: emptyIfNull(shop?.shopId?.toString()),
});

export const shopTypeOption = (shopType?: ShopEntityType) => ({
  label: emptyIfNull(shopType?.type?.toString()),
  value: emptyIfNull(shopType?.type?.toString()),
});
