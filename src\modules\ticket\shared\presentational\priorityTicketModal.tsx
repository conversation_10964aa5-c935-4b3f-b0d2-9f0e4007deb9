/* eslint-disable @typescript-eslint/no-explicit-any */
import { ColorPicker, Form, Input, InputNumber, Modal, Space } from "antd";
import { Color } from "antd/es/color-picker";
import { Col } from "react-bootstrap";
import { BaseButton } from "components/atoms/base/button/BaseButton";
// import { Modal } from "components/organisms/modal";
import { COLOR } from "constants/color";
import { UseCreateTicket } from "modules/ticket/hook/useCreateTicket";

interface PriorityTicketModalProps {
  open: boolean;
  onClose: () => void;
}

export const PriorityTicketModal = ({
  open,
  onClose,
}: PriorityTicketModalProps) => {
  const [form] = Form.useForm();
  const { createTicketExe } = UseCreateTicket();
  const handleSubmit = (values: any) => {
    const color =
      typeof values?.color === "string"
        ? values.color
        : (values.color as unknown as Color).toRgbString();

    const payload = {
      ...values,
      color,
    };
    console.log(payload, "payloadddd");
    createTicketExe(payload);
  };
  return (
    <Modal
      title="TẠO MỚI MỨC ĐỘ ƯU TIÊN TICKET"
      open={open}
      onCancel={onClose}
      centered
      footer={
        <Space align="center">
          <BaseButton
            type="primary"
            bgColor={COLOR.GRAY[500]}
            hoverColor={COLOR.GRAY[600]}
            onClick={onClose}
          >
            HUỶ
          </BaseButton>
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={() => form.submit()}
          >
            LƯU
          </BaseButton>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ color: "#000" }}
        onFinish={handleSubmit}
      >
        <Col>
          <Form.Item label="Tên mức độ ưu tiên" name="name">
            <Input placeholder="Nhập tên mức độ ưu tiên" />
          </Form.Item>
        </Col>
        <Col>
          <Form.Item label="Màu sắc" name="color">
            <ColorPicker />
          </Form.Item>
        </Col>
        <Col>
          <Form.Item label="Thứ tự hiển thị" name="displayOrder">
            <InputNumber
              placeholder="Nhập thứ tự hiển thị"
              className="w-full"
            />
          </Form.Item>
        </Col>
      </Form>
    </Modal>
  );
};

/* <Section>
        <Section>
          <FormContainer validationSchema={null}>
            <Formfield label="Tên mức độ ưu tiên" name="">
              <TextfieldHookForm
                name=""
                placeholder="Nhập tên mức độ ưu tiên"
              />
            </Formfield>
            <Formfield label="Màu sắc" name="color">
              <ColorPicker />
            </Formfield>
            <Formfield label="Thứ tự hiển thị" name="">
              <NumberfieldHookForm name="" placeholder="Nhập thứ tự hiển thị" />
            </Formfield>
            <div className="d-flex justify-content-end u-mt-20">
              <div className="u-mr-15">
                <Button
                  buttonType="outline"
                  modifiers="secondary"
                  onClick={onClose}
                >
                  HUỶ
                </Button>
              </div>
              <Button type="submit">LƯU</Button>
            </div>
          </FormContainer>
        </Section>
      </Section> */
