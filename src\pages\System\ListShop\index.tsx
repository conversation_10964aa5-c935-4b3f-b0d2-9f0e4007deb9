/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { on } from "events";
import { useCallback, useRef, useState } from "react";
import {
  DeleteFilled,
  EditFilled,
  InfoCircleFilled,
  SearchOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import {
  Col,
  Input,
  Row,
  Tooltip,
  Modal,
  Form,
  Select,
  Checkbox,
  Tag,
  Collapse,
  Card,
  Pagination,
} from "antd";
import Table, { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";

import { use } from "i18next";
import { pad } from "lodash";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import BaseSelectCity from "components/atoms/base/select/shared/BaseSelectCity.share";
import { Heading } from "components/atoms/heading";
import { PaginationReference } from "components/molecules/pagination";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";

import RootPageRouter from "../index";
import { StorageDto } from "./dto/storage.dto";
import { FilterShopPayload, ShopPageVm } from "./vm";

interface Shop {
  shopId: number;
  name: string;
  hotline: string;
  address: string;
  cityId: number;
  // storages?: Array<{ name: string }>;
  storage: StorageDto;
  isFranchisedShop: boolean;
  isDefaultShop: boolean;
  displayOrder: number;
  updatedAt: string | Date;
}
const { Option } = Select;
const IndexPage = () => {
  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const {
    loading,
    handleChangePageSize,
    shopPaginationState,
    pageSize,
    listShop,
    gotoPage,
    handleOpenModalByType,
    toggleSortShopBy,
    handleCloseModal,
    modalTypeIsOpen,
    createShop,
    setFilterSearchPayload,
    storageOptions,
    cityOptions,
    filterState,
    gotoShopPage,
    handleDeleteShop,
    handleFilterSearchPayload,
    cityData,
  } = ShopPageVm();

  const getCityName = (cityId: number) => {
    return (
      cityData?.data?.find((v) => +v.cityId === cityId)?.name || "Chưa cập nhật"
    );
  };

  const handleConfirmDelete = (record: Shop) => {
    Modal.confirm({
      title: "Xác nhận xóa",
      content: `Bạn có chắc chắn muốn xóa shop "${record.name}"?`,
      okText: "Xóa",
      cancelText: "Hủy",
      onOk: () => {
        handleDeleteShop(record.shopId);
      },
    });
  };

  // Define table columns
  const columns: ColumnsType<Shop> = [
    {
      title: "STT",
      key: "no.",
      render: (_, __, index) => index + 1,
      fixed: "left",
      align: "center",
      width: 60,
    },
    {
      title: "Mã shop",
      dataIndex: "shopCode",
      key: "shopCode",
      align: "center",
      width: 180,
    },
    {
      title: "Tên shop",
      sorter: true,
      dataIndex: "name",
      key: "name",
      align: "center",
      width: 200,
      onHeaderCell: () => ({
        onClick: () => toggleSortShopBy("name"),
      }),
    },
    {
      title: "Thông tin liên hệ",
      key: "contact",
      align: "center",
      width: 250,
      render: (_, record) => (
        <div>
          <div>{record.hotline}</div>
          <div className="text-gray-500 text-sm">{record.address}</div>
        </div>
      ),
    },
    {
      title: "Tỉnh",
      key: "city",
      align: "center",
      width: 240,
      render: (_, record) => getCityName(record.cityId),
    },
    {
      title: "Kho",
      key: "storage",
      align: "center",
      width: 200,
      render: (storage, record) => <span>{record?.storage?.name}</span>,
    },
    {
      title: "Nhượng quyền",
      sorter: true,
      key: "franchise",
      align: "center",
      width: 150,
      render: (_, record) => (
        <Tag color={record.isFranchisedShop ? "#FF6568" : "#0BBA99"}>
          {record.isFranchisedShop ? "Nhượng quyền" : "GUMAC"}
        </Tag>
      ),
      onHeaderCell: () => ({
        onClick: () => toggleSortShopBy("franchise"),
      }),
    },
    {
      title: "Kho mặc định",
      dataIndex: "isDefaultShop",
      key: "isDefaultShop",
      align: "center",
      width: 200,
      render: (_, record) => {
        return (
          <Input
            // value={record.isDefaultShop ? "Có" : "Không"}
            type="checkbox"
            readOnly
            checked={record.isDefaultShop}
            style={{ textAlign: "center" }}
          />
        );
      },
    },
    {
      title: "Thứ tự hiển thị",
      sorter: true,
      dataIndex: "displayOrder",
      key: "displayOrder",
      align: "center",
      width: 120,
      onHeaderCell: () => ({
        onClick: () => toggleSortShopBy("displayOrder"),
      }),
    },
    {
      title: "Trạng thái hoạt động",
      dataIndex: "activeStatus",
      key: "activeStatus",
      align: "center",
      width: 150,
      render: (value) => (
        <Tag
          color={value === 2 ? "#0BBA99" : value === 1 ? "#e0aa14" : "#FF6568"}
        >
          {value === 1
            ? "Chưa hoạt dộng"
            : value === 2
            ? "Đang hoạt động"
            : "Ngừng hoạt động"}
        </Tag>
      ),
    },
    {
      title: "Ngày hoạt động",
      dataIndex: "startActiveDate",
      key: "startActiveDate",
      align: "center",
      width: 150,
      render: (value) => (
        <div className="flex justify-center">
          <span>{dayjs(value).format("DD/MM/YYYY")}</span>
        </div>
      ),
    },
    {
      title: "Cập nhật cuối",
      dataIndex: "updatedAt",
      align: "center",
      key: "updatedAt",
      sorter: true,
      width: 150,
      render: (value) => (
        <div className="flex justify-center">
          <span>{dayjs(value).format("DD/MM/YYYY")}</span>
        </div>
      ),
      onHeaderCell: () => ({
        onClick: () => toggleSortShopBy("updatedAt"),
      }),
    },
    {
      title: "Thao tác",
      align: "center",
      key: "action",
      fixed: "right",
      width: 150,
      render: (_, record) => {
        const { shopId } = record ?? {};
        return (
          <div className="flex justify-center gap-3">
            <Tooltip title="Xem chi tiết">
              <BaseButton
                type="primary"
                bgColor={COLOR.BLUE[500]}
                hoverColor={COLOR.BLUE[700]}
                icon={<InfoCircleFilled rev={undefined} />}
                onClick={() =>
                  // RootPageRouter.gotoChild("shopDetail", {
                  //   params: { shopId: shopId?.toString() },
                  // })
                  gotoShopPage({ shopId, action: "view" })
                }
              />
            </Tooltip>
            <Tooltip title="Chỉnh sửa">
              <BaseButton
                type="primary"
                bgColor={COLOR.GREEN[500]}
                hoverColor={COLOR.GREEN[700]}
                icon={<EditFilled rev={undefined} />}
                onClick={() => {
                  // RootPageRouter.gotoChild("shopDetail", {
                  //   params: { shopId: shopId?.toString() },
                  //   queryString: "?action=edit",
                  // });
                  gotoShopPage({ shopId });
                }}
              />
            </Tooltip>
            <Tooltip title="Xóa">
              <BaseButton
                type="primary"
                bgColor={COLOR.RED[500]}
                hoverColor={COLOR.RED[700]}
                icon={<DeleteFilled rev={undefined} />}
                onClick={() => {
                  // TODO: Implement delete functionality
                  // handleDeleteShop(shopId);
                  handleConfirmDelete(record);
                }}
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  // const onFinish = (values: Record<string, unknown>) => {
  //   // Convert simple form values to CreateShopFormPayload format
  //   const formPayload = {
  //     name: values.name as string,
  //     hotline: values.hotline as string,
  //     address: values.address as string,
  //     displayOrder: (values.displayOrder as number) || 1,
  //     isFranchisedShop: (values.isFranchisedShop as boolean) || false,
  //     city: { label: "", value: values.cityId as string },
  //     district: { label: "", value: "" },
  //     ward: { label: "", value: "" },
  //     type: { label: "", value: "" },
  //     storages: [],
  //     bankAccounts: [],
  //     employees: [],
  //   };
  //   createShop(formPayload as FilterShopPayload & any);
  //   form.resetFields();
  // };

  const onFilterFinish = (values: FilterShopPayload) => {
    const payload = {
      ...values,
      name: values.name?.trim() ? values.name.trim() : undefined,
    };

    // setFilterSearchPayload(payload);
    handleFilterSearchPayload(payload);
  };

  const handleResetFilter = () => {
    handleFilterSearchPayload({});
    filterForm.resetFields();

    // setFilterSearchPayload({
    //   name: undefined,
    //   cityId: undefined,
    //   storageId: undefined,
    //   isFranchisedShop: undefined,
    // });
  };
  const paginationRef = useRef<PaginationReference>(null);
  const handleOnChangePageSizePulldown = useCallback(
    (value: string) => {
      if (value) {
        handleChangePageSize(Number(value));
        if (paginationRef.current) paginationRef.current.reset();
      }
    },
    [handleChangePageSize]
  );
  return (
    <General>
      <title key="title">Danh sách shop</title>
      <Section>
        <div className="p-4">
          <Card
            styles={{
              body: {
                padding: 0,
              },
            }}
            title="DANH SÁCH SHOP"
            style={{ marginLeft: "1.5%", marginRight: "1.5%" }}
          >
            <div className="flex flex-col gap-3">
              <div className="p-6">
                <div className="mb-6">
                  <Row gutter={[16, 16]} className="flex justify-end">
                    <Col
                      xs={{ span: 24, order: 1 }}
                      lg={{ span: 6, order: 2, offset: 6 }}
                    >
                      <div className="flex gap-3">
                        <BaseButton
                          type="default"
                          icon={<FilterOutlined rev={undefined} />}
                          onClick={() => setIsFilterOpen(!isFilterOpen)}
                          className="flex-1"
                        >
                          Bộ lọc
                        </BaseButton>
                        <BaseButton
                          type="primary"
                          bgColor={COLOR.BLUE[500]}
                          hoverColor={COLOR.BLUE[700]}
                          onClick={() =>
                            //  handleOpenModalByType("createShop")
                            gotoShopPage({ shopId: "isAddnew" })
                          }
                          className="flex-1"
                        >
                          Tạo mới
                        </BaseButton>
                      </div>
                    </Col>
                  </Row>
                </div>
                <Collapse
                  activeKey={isFilterOpen ? ["filter"] : []}
                  onChange={() => setIsFilterOpen(!isFilterOpen)}
                  ghost
                >
                  <Collapse.Panel header="Bộ lọc tìm kiếm" key="filter">
                    <Form
                      form={filterForm}
                      layout="vertical"
                      onFinish={onFilterFinish}
                      initialValues={filterState}
                    >
                      <Row gutter={16}>
                        <Col xs={24} sm={12} md={6}>
                          <Form.Item label="Tên Shop" name="name">
                            <Input placeholder="Nhập tên Shop" />
                          </Form.Item>
                        </Col>
                        <Col xs={24} sm={12} md={6}>
                          <Form.Item label="Tỉnh" name="cityId">
                            {/* <Select
                              placeholder="Chọn tỉnh"
                              allowClear
                              options={cityOptions}
                            /> */}
                            <BaseSelectCity placeholder="Chọn tỉnh" />
                          </Form.Item>
                        </Col>
                        <Col xs={24} sm={12} md={6}>
                          <Form.Item label="Kho" name="storageId">
                            <Select
                              placeholder="Chọn kho"
                              allowClear
                              options={storageOptions}
                            />
                          </Form.Item>
                        </Col>
                        <Col xs={24} sm={12} md={6}>
                          <Form.Item
                            label="Nhượng quyền"
                            name="isFranchisedShop"
                            valuePropName="checked"
                          >
                            <Checkbox>Nhượng quyền</Checkbox>
                          </Form.Item>
                        </Col>
                      </Row>
                      <div className="flex justify-center gap-3 mt-4">
                        <BaseButton
                          type="default"
                          onClick={() => setIsFilterOpen(false)}
                        >
                          Đóng
                        </BaseButton>
                        <BaseButton
                          htmlType="submit"
                          type="primary"
                          bgColor={COLOR.BLUE[500]}
                          hoverColor={COLOR.BLUE[700]}
                          loading={loading}
                        >
                          Tìm kiếm
                        </BaseButton>
                        <BaseButton
                          type="default"
                          onClick={handleResetFilter}
                          loading={loading}
                        >
                          Đặt lại
                        </BaseButton>
                      </div>
                    </Form>
                  </Collapse.Panel>
                </Collapse>
              </div>
              <Table
                loading={loading}
                scroll={{ x: 1500 }}
                size="small"
                bordered
                columns={columns}
                pagination={false}
                dataSource={listShop}
                rowKey="shopId"
                // pagination={{
                //   current: shopPaginationState?.currentPage || 1,
                //   total: (shopPaginationState?.totalPage || 0) * pageSize,
                //   pageSize,
                //   showSizeChanger: true,
                //   showQuickJumper: true,
                //   showTotal: (total, range) =>
                //     `${range[0]}-${range[1]} của ${total} mục`,
                //   pageSizeOptions: ["5", "10", "15", "25", "30", "50", "100"],
                //   onChange: (page, size) => {
                //     gotoPage(page);
                //     if (size !== pageSize) {
                //       handleChangePageSize(size);
                //     }
                //   },
                //   onShowSizeChange: (_, size) => {
                //     handleChangePageSize(size);
                //     gotoPage(1); // Reset to first page when changing page size
                //   },
                // }}
              />
              <Row justify="space-between" align="middle" className="p-6">
                <Col>
                  <Select
                    placeholder="Chọn số lượng hiển thị"
                    value={`${pageSize}`}
                    onChange={handleOnChangePageSizePulldown}
                    style={{ width: 150 }}
                  >
                    {[5, 10, 15, 25, 30].map((size) => (
                      <Option key={size} value={`${size}`}>
                        {size}/pages
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col>
                  {shopPaginationState?.totalPage > 0 && (
                    <Pagination
                      total={shopPaginationState?.totalPage * 10}
                      showSizeChanger={false}
                      defaultCurrent={1}
                      onChange={gotoPage}
                    />
                  )}
                </Col>
              </Row>
            </div>
          </Card>
        </div>
      </Section>

      {/* <Modal
        title="TẠO MỚI SHOP"
        open={modalTypeIsOpen("createShop")}
        onCancel={handleCloseModal}
        footer={null}
        width={700}
      >
        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Form.Item
            label="Tên shop"
            name="name"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập tên shop",
              },
            ]}
          >
            <Input placeholder="Nhập tên shop" />
          </Form.Item>

          <Form.Item
            label="Hotline"
            name="hotline"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập hotline",
              },
            ]}
          >
            <Input placeholder="Nhập hotline" />
          </Form.Item>

          <Form.Item
            label="Địa chỉ"
            name="address"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập địa chỉ",
              },
            ]}
          >
            <Input placeholder="Nhập địa chỉ" />
          </Form.Item>

          <Form.Item
            label="Tỉnh"
            name="cityId"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn tỉnh",
              },
            ]}
          >
            <Select placeholder="Chọn tỉnh" options={cityOptions} />
          </Form.Item>

          <Form.Item
            label="Thứ tự hiển thị"
            name="displayOrder"
            rules={[
              {
                required: true,
                message: "Vui lòng nhập thứ tự hiển thị",
              },
            ]}
          >
            <Input type="number" placeholder="Nhập thứ tự hiển thị" />
          </Form.Item>

          <Form.Item
            label="Nhượng quyền"
            name="isFranchisedShop"
            valuePropName="checked"
          >
            <Checkbox>Nhượng quyền</Checkbox>
          </Form.Item>

          <div className="flex justify-end gap-3 mt-6">
            <BaseButton type="default" onClick={handleCloseModal}>
              Hủy
            </BaseButton>
            <BaseButton
              htmlType="submit"
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
            >
              Lưu
            </BaseButton>
          </div>
        </Form>
      </Modal> */}
    </General>
  );
};

export default IndexPage;
