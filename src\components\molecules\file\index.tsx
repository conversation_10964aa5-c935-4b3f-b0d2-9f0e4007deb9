import React, { useCallback } from "react";

import { Checkbox } from "components/atoms/checkbox";
import { Image } from "components/atoms/image";
import { mapModifiers } from "helpers/component";

type Modifiers = "floatcheckbox";

export interface Props {
  modifiers?: Modifiers | Modifiers[];
  src: string;
  alt: string;
  name: string;
  onSelect?: (selected: boolean) => void;
}

export const File: React.FC<Props> = ({
  modifiers,
  src,
  alt,
  name,
  onSelect,
}) => {
  const onChange = useCallback(
    (e) => onSelect && onSelect(e.target.checked),
    [onSelect]
  );
  return (
    <div className={mapModifiers("m-file", modifiers)}>
      <Checkbox onChange={onChange} />
      <div className="m-file_picture">
        <Image aspectRatio="file" src={src} alt={alt} />
      </div>
      <div className="m-file_content">{name}</div>
    </div>
  );
};
