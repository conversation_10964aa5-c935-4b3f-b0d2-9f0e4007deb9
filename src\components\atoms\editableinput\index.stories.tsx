import { Story, Meta } from "@storybook/react/types-6-0";

import { useEditableInput } from "./hook";
import { Props } from "./types";
import { EditableInput } from ".";
// This default export determines where your story goes in the story list
export default {
  title: "Components|atoms/EditableInput",
  component: EditableInput,
} as Meta;

const Template: Story<Props> = ({
  placeholder,
  defaultValue,
  placeholderStyle,
}) => {
  const { reset, register } = useEditableInput();
  return (
    <div style={{ padding: 20, backgroundColor: "#eeeeee" }}>
      <EditableInput
        register={register}
        placeholder={placeholder}
        defaultValue={defaultValue}
        placeholderStyle={placeholderStyle}
      />
      <button type="button" onClick={reset}>
        Reset
      </button>
    </div>
  );
};

export const Normal = Template.bind({});

Normal.args = {
  placeholder: "Nhập nội dung tin nhắn",
  placeholderStyle: {},
  defaultValue: `Default
	Value
	`,
};
