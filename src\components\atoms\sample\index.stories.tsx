import { Story, Meta } from "@storybook/react/types-6-0";

import { Sam<PERSON>, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|atoms/Sample",
  component: Sample,
} as Meta;

const Template: Story<Props> = ({ modifiers, children }) => (
  <Sample modifiers={modifiers}>{children}</Sample>
);

export const Normal = Template.bind({});

Normal.args = {
  children: "Sample",
};
