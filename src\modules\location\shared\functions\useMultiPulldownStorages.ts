import { useCallback, useEffect, useMemo } from "react";

import { useAsync } from "hooks/useAsync";
import { mapFrom } from "libs/adapters/dto";
import { StorageModel } from "modules/location";
import { StorageEntityType } from "modules/location/entities";
import { storageOption } from "modules/location/valueObject";
import { getListStorage } from "services/crm/system";

export interface PulldownStoragesFunctionalOptions {
  excludePending: boolean;
}

export const usePulldownStorages = ({
  excludePending = false,
}: PulldownStoragesFunctionalOptions) => {
  const [fetchStorages, fetchStoragesState] = useAsync(
    useCallback(
      () =>
        getListStorage({}).then((res) => StorageModel.createMap(res.data.data)),
      []
    ),
    {
      excludePending,
    }
  );

  const storageItems = useMemo(
    () => fetchStoragesState.data || [],
    [fetchStoragesState.data]
  );

  const storageOptions = useMemo(
    () => mapFrom(storageItems || [], storageOption),
    [storageItems]
  );

  const getOptionByValue = useCallback(
    (storageId?: number) =>
      storageId &&
      storageOptions.find(({ value }) => Number(value) === storageId),
    [storageOptions]
  );

  const formatStorageOption = useCallback(
    (payload?: StorageEntityType[]) => mapFrom(payload || [], storageOption),
    []
  );

  useEffect(() => {
    fetchStorages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    storageOptions,
    storageItems,
    fetchStorages,
    getOptionByValue,
    formatStorageOption,
  };
};
