html {
  font-size: $FONT-SIZE-HTML;
  word-break: break-word;
}

body {
  margin: 0;
  font-family: $FONTFAMILY-BASIC;
  color: $COLOR-BLACK;
  background-color: #f9fafa;
}

// Default a-text style for p tags
p {
  font-size: rem($FONT-SIZE-HTML-VALUE);

  &:last-child {
    margin-bottom: 0;
  }
}

// Default a-textlink style for a tags
a {
  color: $COLOR-DENIM;
  text-decoration: none;

  &:hover {
    color: inherit;
  }
}

img {
  max-width: 100%;
}

button,
select {
  font-family: inherit;

  &:focus {
    border: none;
    outline: none;
    appearance: none;
  }
}

[role="button"],
button {
  cursor: pointer;
}

ul,
ol {
  padding: 0;
  margin: 0;
}

b,
strong {
  @include u-fw-bold;
}

// Set the background-color of the body Iframe to transparent
.sb-show-main {
  background-color: $COLOR-TRANSPARENT;
}

.flex {
  display: flex;
}

.u-relative {
  position: relative;
}

$offsets: (top, bottom, left, right);
$breakpoints: (
  sm: "(min-width: 576px)",
  md: "(min-width: 768px)",
  lg: "(min-width: 992px)",
  xl: "(min-width: 1200px)",
);

@each $offset in $offsets {
  @for $spacing from -20 through 20 {
    @if $spacing >= 0 {
      .u-m#{str-slice($offset, 0, 1)}-#{$spacing} {
        margin-#{$offset}: #{$spacing}px;
      }
      .u-p#{str-slice($offset, 0, 1)}-#{$spacing} {
        padding-#{$offset}: #{$spacing}px;
      }
    } @else {
      .u-m#{str-slice($offset, 0, 1)}-negative#{$spacing} {
        margin-#{$offset}: #{$spacing}px;
      }
    }
  }
}

@each $name, $value in $breakpoints {
  @each $offset in $offsets {
    @for $spacing from -20 through 20 {
      @if $spacing >= 0 {
        .u-m#{str-slice($offset, 0, 1)}-#{$name}-#{$spacing} {
          @media #{$value} {
            margin-#{$offset}: #{$spacing}px;
          }
        }

        .u-p#{str-slice($offset, 0, 1)}-#{$name}-#{$spacing} {
          @media #{$value} {
            padding-#{$offset}: #{$spacing}px;
          }
        }
      } @else {
        .u-m#{str-slice($offset, 0, 1)}-negative-#{$name}#{$spacing} {
          @media #{$value} {
            margin-#{$offset}: #{$spacing}px;
          }
        }
      }
    }
  }
}

.u-h-0 {
  height: 0;
}

.u-overflow-scroll {
  overflow: scroll;
}

.u-overflowX-scroll {
  overflow-x: scroll;
  overflow-y: hidden;
}

.u-overflowY-scroll {
  overflow-x: hidden;
  overflow-y: scroll;
}

.u-overflow-hidden {
  overflow-x: hidden;
  overflow-y: hidden;
}

.u-overflow-x-hidden {
  overflow-x: hidden;
}

.u-overflow-auto {
  overflow-x: auto;
  overflow-y: auto;
}

.u-overflow-y-auto {
  overflow-y: auto;
}

.u-transition {
  transition-timing-function: linear;
  transition-duration: 100ms;
  transition-property: all;
}

.u-text-italic {
  font-style: italic;
}

.u-text-bold {
  font-weight: bold;
}

.u-text-underline {
  text-decoration: underline;
}

.u-text-center {
  text-align: center;
}
