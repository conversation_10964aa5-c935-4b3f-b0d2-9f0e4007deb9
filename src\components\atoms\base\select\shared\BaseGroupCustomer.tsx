/* eslint-disable react/jsx-props-no-spreading */
import { useEffect, useMemo } from "react";
import { UseGetGroupCustomers } from "pages/Customer/hook/useGetGroupCustomer";
import { BaseSelect, BaseSelectProps } from "../BaseSelect";

export default function BaseSelectGroupCustomer(props: BaseSelectProps) {
  const { ...rest } = props;
  const { getGroupCustomersExec, getGroupCustomersState, loading } =
    UseGetGroupCustomers();

  const groupCustomerOptions = useMemo(() => {
    return getGroupCustomersState?.data?.map((group) => ({
      label: group.groupCustomerName,
      value: group.groupCustomerId,
    }));
  }, [getGroupCustomersState]);

  useEffect(() => {
    getGroupCustomersExec({});
  }, []);

  return (
    <BaseSelect {...rest} options={groupCustomerOptions} loading={loading} />
  );
}
