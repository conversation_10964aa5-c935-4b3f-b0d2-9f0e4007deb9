/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ClockCircleOutlined,
  CloseCircleOutlined,
  FolderOpenOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { Space, Timeline, Typography } from "antd";

import type { TimelineItemProps } from "antd";

interface HistoryEntry {
  date: string | Date;
  time: string | Date;
  user: string;
  changes: string[];
  changeType: string;
}

interface GroupedHistoryEntry {
  date: string;
  entries: Array<{
    user: string;
    action: string[];
    time: string;
    icon: string;
  }>;
}

// interface HistoryComponentProps {
//   historyData?: T[];
//   listMasterData?: ListMasterData;
//   fieldLabels?: typeof OrderFormFieldLabels;
// }

type MappingItem<
  DataItem extends Record<string, any>,
  Key extends keyof DataItem = keyof DataItem,
  Field extends keyof DataItem = keyof DataItem
> = {
  data: DataItem[];
  key: Key;
  field: Field;
};

type ListMapping<
  T extends Record<string, any>,
  <PERSON><PERSON><PERSON> extends Record<string, any>,
  <PERSON><PERSON><PERSON><PERSON> extends keyof T | false | undefined = false
> = <PERSON>ff<PERSON><PERSON> extends keyof T
  ? T[Diff<PERSON><PERSON>] extends Record<string, any>
    ? {
        [P in keyof T[DiffKey]]?: MappingItem<MasterList>;
      }
    : never
  : {
      [P in keyof T]?: MappingItem<MasterList>;
    };

interface HistoryComponentProps<
  T extends Record<string, any>,
  MasterList extends Record<string, any>,
  DiffKey extends keyof T | false | undefined = false,
  LabelMapping extends Record<string, string> = Record<string, string>
> {
  historyData: T[];
  listMapping: ListMapping<T, MasterList, DiffKey>;
  diffKey?: DiffKey;
  listlabelMapping?: LabelMapping;
}

export default function HistoryComponent<
  T extends Record<string, any>,
  Data extends Record<string, any>,
  DiffKey extends keyof T | false | undefined = false
>({
  historyData,
  listMapping,
  diffKey,
  listlabelMapping,
}: HistoryComponentProps<T, Data, DiffKey>) {
  const handleMappingData = (
    key: string,
    value: string | number,
    masterData: Record<string, MappingItem<Data>>
  ): string => {
    if (!value && value !== 0) return "";

    const mapping = masterData?.[key];
    if (mapping && mapping.data) {
      const find = mapping.data.find(
        (item) =>
          item[mapping.key] === value || item[mapping.key] === Number(value)
      );

      if (find) {
        const labelKey = mapping.field || "name"; // fallback if mapping.field không có
        return find[labelKey] ?? value.toString();
      }
    }

    return value.toString();
  };

  const formatDate = (dateInput: Date | string): string => {
    const date = dateInput instanceof Date ? dateInput : new Date(dateInput);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const formatTime = (dateInput: Date | string): string => {
    const date = dateInput instanceof Date ? dateInput : new Date(dateInput);
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    return `${hours}:${minutes}:${seconds}`;
  };

  const handleConvert = (dataItem: T[]): HistoryEntry[] => {
    if (dataItem?.length > 0) {
      const result = dataItem.map((item: T) => {
        const date = item.createdAt;
        const time = item.createdAt;
        const userBy = item.changedByName;
        const { changeType } = item;
        const changes: string[] = [];

        const dataDiff = item.diffData as {
          [key: string]: (string | number)[];
        };

        // Check if diffData exists and is not null/undefined
        if (!dataDiff || typeof dataDiff !== "object") {
          return {
            date,
            time,
            user: userBy,
            changes: changeType === "VIEW" ? ["Mở đơn hàng"] : ["Tạo đơn hàng"],
            changeType,
          };
        }

        Object.entries(dataDiff).forEach(([key, value]) => {
          if (Array.isArray(value)) {
            const label =
              listlabelMapping[key as keyof typeof listlabelMapping] || key;
            const oldValue = handleMappingData(key, value[0], listMapping);
            const newValue = handleMappingData(key, value[1], listMapping);
            if (value.length === 2) {
              changes.push(`Thay đổi ${label}: ${oldValue} => ${newValue}`);
            } else if (value.length === 1) {
              changes.push(`Thêm ${label}: ${oldValue}`);
            } else if (value.length === 3 && value[2] === 0) {
              changes.push(`Xoá ${label}: ${oldValue}`);
            }
          }
        });

        return {
          date,
          time,
          user: userBy,
          changes,
          changeType,
        };
      });
      return result;
    }
    return [];
  };

  const convertToHistoryData = (
    rawData: HistoryEntry[]
  ): GroupedHistoryEntry[] => {
    const historyDataResult: GroupedHistoryEntry[] = [];

    // Filter null and valid elements
    const validData = rawData.filter((item) => item !== null);

    validData.forEach((item) => {
      const { date, time, user, changes } = item;

      // Format date and time
      const formattedDate = formatDate(date);
      const formattedTime = formatTime(time);

      // Find group by date
      let dateGroup = historyDataResult.find(
        (group) => group.date === formattedDate
      );

      if (!dateGroup) {
        dateGroup = { date: formattedDate, entries: [] };
        historyDataResult.push(dateGroup);
      }

      // Add each action to entries
      dateGroup.entries.push({
        user,
        action: changes,
        time: formattedTime,
        icon: "info",
      });
    });

    return historyDataResult;
  };

  const getIcon = (type: string) => {
    switch (type) {
      case "success":
        return <FolderOpenOutlined style={{ color: "green", fontSize: 16 }} />;
      case "info":
        return (
          <InfoCircleOutlined style={{ color: "#1890ff", fontSize: 16 }} />
        );
      case "error":
        return <CloseCircleOutlined style={{ color: "red" }} />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  // Convert and process data
  const convertedData = historyData ? handleConvert(historyData) : [];
  const dataSource = convertToHistoryData(convertedData);

  const timelineItems: TimelineItemProps[] = [];

  dataSource?.forEach((group) => {
    // Date label
    timelineItems.push({
      dot: (
        <span
          className="bg-[#F5222D] text-white px-3 py-1 rounded font-medium text-sm"
          style={{
            display: "inline-block",
            minWidth: 100,
            textAlign: "center",
          }}
        >
          {group.date}
        </span>
      ),
      children: null,
      className: `
        !pt-0
        !pb-2
        [&_.ant-timeline-item-head]:!static
        [&_.ant-timeline-item-head]:!transform-none
        [&_.ant-timeline-item-tail]:!hidden
      `,
    });

    group.entries.forEach((entry, entryIndex) => {
      timelineItems.push({
        dot: getIcon(entry.icon),
        children: (
          <Space direction="vertical" size={4}>
            <Typography.Text strong>{entry.user}</Typography.Text>
            {entry.action.map((line: string) => (
              <Typography.Text key={line}>{line}</Typography.Text>
            ))}
            <Typography.Text type="secondary" style={{ fontSize: 12 }}>
              {entry.time}
            </Typography.Text>
          </Space>
        ),
      });
    });
  });

  return (
    <Timeline
      items={timelineItems}
      className={`
        [&_.ant-timeline-item]:!pt-0
        [&_.ant-timeline-item-tail]:!top-4
        [&_.ant-timeline-item-head]:!text-start
      `}
    />
  );
}
