import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Pulldown } from "components/atoms/pulldown";
import { Text } from "components/atoms/text";
import { Modal } from "components/organisms/modal";

export interface FilterTagModalProps {
  open: boolean;
  onClose?: () => void;
  pulldownOptions: Array<{ label: string; value: string }>;
}

export const FilterTagModal: React.FC<FilterTagModalProps> = ({
  open,
  onClose,
  pulldownOptions,
}: FilterTagModalProps) => {
  return (
    <Modal
      style={{ content: { maxWidth: 800 } }}
      onCloseModal={onClose}
      isClosable
      isOpen={open}
    >
      <Heading type="h1">LỌC THEO TAG</Heading>
      <div className="u-mb-20" />
      <Text>Chọn tag</Text>
      <div style={{ marginBottom: 60 }} className="u-mt-10">
        <Pulldown maxMenuHeight={130} options={pulldownOptions} isMultiSelect />
      </div>
      <div className="d-flex justify-content-end u-mt-20">
        <div className="u-mr-15">
          <Button buttonType="outline" modifiers="secondary" onClick={onClose}>
            HUỶ
          </Button>
        </div>
        <Button>LỌC</Button>
      </div>
    </Modal>
  );
};
