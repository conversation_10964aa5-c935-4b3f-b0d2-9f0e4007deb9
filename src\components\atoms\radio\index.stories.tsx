import { Story, <PERSON>a } from "@storybook/react/types-6-0";

import { Radio, Props } from ".";

export default {
  title: "Components|atoms/Radio",
  component: Radio,
} as Meta;

const Template: Story<Props> = ({ disabled, checked, onChange, children }) => {
  return (
    <Radio checked={checked} disabled={disabled} onChange={onChange}>
      {children}
    </Radio>
  );
};

export const Normal = Template.bind({});

Normal.args = {
  checked: false,
  children: <span>Công ty</span>,
  disabled: false,
};
