import pick from "object.pick";

import { createMapper, fromSchema, merge } from "libs/adapters/dto";

import { GroupEmployeeSchema } from "../entities";

export const createEmployeeGroupDto = createMapper(
  fromSchema(pick(GroupEmployeeSchema, ["name"])),
  merge((data) => ({
    name: data.name.trim(),
  }))
);

export type CreateEmployeeGroupDtoType = ReturnType<
  typeof createEmployeeGroupDto
>;
