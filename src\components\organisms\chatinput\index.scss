.o-chatinput {
	$root: &;
	height: rem(230);
	background-color: $COLOR_WHITE;

	&_utils {
		display: flex;
		align-items: center;
		height: rem(50);
	}

	&_lefticons {
		display: flex;
		margin-right: rem(25);
		margin-left: auto;

		& > * {
			cursor: pointer;

			&:not(:first-child) {
				margin-left: rem(5);
			}
		}
	}

	&_preview {
		display: flex;
		width: 50%;
		margin-bottom: rem(5);
		margin-left: rem(25);
		overflow-x: auto;

		& > #{$root}_previewwrapper:not(:first-child) {
			margin-left: rem(5);
		}
	}

	&_removeicon {
		width: rem(20);
		height: rem(20);
		background-color: $COLOR_WHITE;
		border-radius: 50%;
		& .a-icon-close {
			transform: translate(-2px, -2px) scale(0.35);
		}
	}

	&_previewwrapper {
		position: relative;
		width: fit-content;
		margin: 0 auto;
		overflow: hidden;
		cursor: pointer;
		border-radius: rem(5);

		&:hover::after {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			content: " ";
			background: rgba($color: $COLOR_WHITE, $alpha: 0.3);
		}

		& #{$root}_removeicon {
			position: absolute;
			top: 4px;
			right: 4px;
			z-index: 1;
			visibility: hidden;
			transition: visibility 0.1s ease-in-out;
		}

		&:hover #{$root}_removeicon {
			visibility: visible;
		}
	}

	&_previewitem {
		width: rem(75);
		min-width: rem(75);
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	&_filepreview {
		width: rem(50);
		height: rem(50);
		@include aspectRatio(1, 1);
	}
}
