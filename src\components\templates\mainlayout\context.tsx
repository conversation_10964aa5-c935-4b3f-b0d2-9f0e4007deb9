import { useCallback } from "react";

import { useAsync } from "hooks/useAsync";
import crmDriverV2 from "services/nest-crm/crm-driver-v2";

import { getMenuContext } from "./api";

export const MenuContext = () => {
  const [getMenuContextExe, getMenuContextState] = useAsync(
    useCallback(
      (site: string, username: string) => getMenuContext(site, username),
      []
    )
  );
  return {
    getMenuContextExe,
    getMenuContextState: getMenuContextState.data?.data || [],
  };
};

export const getMessageByConversation = (conversationId: number) =>
  crmDriverV2.get(
    `/conversation/get-message-by-conversation/${conversationId}`
  );

export const getMessageByUser = (username: string, site: string) =>
  crmDriverV2.get(`/conversation/get-message-by-user/${username}/${site}`);
