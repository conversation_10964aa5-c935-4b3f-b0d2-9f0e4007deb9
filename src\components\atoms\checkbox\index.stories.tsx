import { action } from "@storybook/addon-actions";
import { Story, Meta } from "@storybook/react/types-6-0";

import { Checkbox, Props } from ".";

export default {
  title: "Components|atoms/Checkbox",
  component: Checkbox,
} as Meta;

const Template: Story<Props> = ({ disabled, checked, onChange, children }) => (
  <Checkbox disabled={disabled} checked={checked} onChange={onChange}>
    {children}
  </Checkbox>
);

export const Normal = Template.bind({});

Normal.args = {
  disabled: false,
  checked: false,
  onChange: action("Change"),
  children: <span style={{ fontWeight: 700 }}>Công ty</span>,
};
