import * as Yup from "yup";

export const validationSchemaOfCreation = Yup.object({
  accessRole: Yup.array()
    .min(1, "Vai trò truy cập là bắt buộc")
    .of(Yup.object().required()),
  name: Yup.string().required("Họ và tên là bắt buộc"),
  phoneNumber: Yup.string().required("Số điện thoại là bắt buộc"),
  email: Yup.string()
    .required("Email là bắt buộc")
    .email("<PERSON><PERSON> lòng nhập email hợp lệ"),
  password: Yup.string()
    .required("Mật khẩu là bắt buộc")
    .min(8, "Mật khẩu vui lòng sử dụng 8 ký tự trở lên"),
  cPassword: Yup.string()
    .required("Nhập lại mật khẩu là bắt buộc")
    .oneOf([Yup.ref("password"), ""], "<PERSON>ật khẩu không khớp"),
});
