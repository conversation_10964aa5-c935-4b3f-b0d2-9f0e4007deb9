/* eslint-disable @typescript-eslint/no-explicit-any */
import { memo } from "react";

import _ from "lodash";
import Chart from "react-apexcharts";

function Widget1(props: any) {
  const data = {
    conversion: { value: 492, ofTarget: 13 },
    series: [
      {
        name: "Review",
        data: [10, 50, 30, 90],
      },
    ],
    options: {
      chart: {
        height: "100%",
        type: "area",
        id: "basic-bar",
        sparkline: { enabled: true },
      },
      colors: ["#252f3e"],
      fill: { type: "solid", opacity: "0.7" },
      xaxis: {
        categories: [1991, 1992, 1993, 1994],
      },
    },
  };
  return (
    <div className="CustomerReview">
      <Chart options={data.options as any} series={data.series} type="area" />
    </div>
  );
}

export default memo(Widget1);
