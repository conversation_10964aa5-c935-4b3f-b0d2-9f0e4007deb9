import pick from "object.pick";

import { createMapper, fromSchema, merge } from "libs/adapters/dto";
import { String, Number, mergeSchema } from "libs/domain";

import { LeadItemSchema } from "../entities";

export const CreateLeadDto = createMapper(
  fromSchema(
    mergeSchema(
      pick(LeadItemSchema, [
        "phone",
        "conversationId",
        "customerName",
        "clientInchargeName",
        "clientInchargeTitle",
        "clientInchargeDOB",
        "clientInchargeGender",
        "clientInchargePhone",
        "clientInchargeEmail",
        "clientReviewName",
        "clientReviewTitle",
        "clientReviewDOB",
        "clientReviewGender",
        "clientReviewPhone",
        "clientReviewEmail",
      ]),
      {
        note: String(),
        assignedEmployeeId: Number(),
      }
    )
  ),
  merge((data) => ({
    note: data.note || undefined,
    conversationId: data.conversationId || undefined,
    customerName: data.customerName || undefined,
    clientInchargeName:
      data.clientInchargeName !== "" ? data.clientInchargeName : undefined,
    clientInchargeTitle:
      data.clientInchargeTitle !== "" ? data.clientInchargeTitle : undefined,
    clientInchargeDOB:
      data.clientInchargeDOB !== "" ? data.clientInchargeDOB : undefined,
    clientInchargeGender:
      data.clientInchargeGender !== "" ? data.clientInchargeGender : undefined,
    clientInchargePhone:
      data.clientInchargePhone !== "" ? data.clientInchargePhone : undefined,
    clientInchargeEmail:
      data.clientInchargeEmail !== "" ? data.clientInchargeEmail : undefined,
    clientReviewName:
      data.clientReviewName !== "" ? data.clientReviewName : undefined,
    clientReviewTitle:
      data.clientReviewTitle !== "" ? data.clientReviewTitle : undefined,
    clientReviewDOB:
      data.clientReviewDOB !== "" ? data.clientReviewDOB : undefined,
    clientReviewGender:
      data.clientReviewGender !== "" ? data.clientReviewGender : undefined,
    clientReviewPhone:
      data.clientReviewPhone !== "" ? data.clientReviewPhone : undefined,
    clientReviewEmail:
      data.clientReviewEmail !== "" ? data.clientReviewEmail : undefined,
  }))
);
export type CreateLeadDTOType = ReturnType<typeof CreateLeadDto>;
