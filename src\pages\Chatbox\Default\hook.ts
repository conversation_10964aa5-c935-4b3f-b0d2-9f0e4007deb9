/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useContext } from "react";

import { useSelector } from "libs/adapters/stores/react";

import { Context } from "./context";

export const useChatboxPageContext = () => useContext(Context);

export const useSelectedConversation = () => {
  const { conversationSource } = useChatboxPageContext();

  const selectedConversation = useSelector(
    conversationSource,
    useCallback(
      ({ selectedConversation: selectedConversationSource }: any) =>
        selectedConversationSource,
      []
    )
  );

  return selectedConversation;
};

export const useSelectedMessageConversation = () => {
  const { selectedConversationMessageSource } = useChatboxPageContext();
  const activeConversationMessages = useSelector(
    selectedConversationMessageSource,
    useCallback(({ messages }: any) => messages, [])
  );

  const hasLoadMore = useSelector(
    selectedConversationMessageSource,
    useCallback(({ hasMore }: any) => hasMore, [])
  );

  return {
    activeConversationMessages,
    hasLoadMore,
  };
};

export const useRecentConversation = () => {
  const { conversationSource } = useChatboxPageContext();
  const recentConversationStack = useSelector(
    conversationSource,
    ({ recentStack }) => recentStack
  );

  return {
    recentConversationStack,
  };
};
