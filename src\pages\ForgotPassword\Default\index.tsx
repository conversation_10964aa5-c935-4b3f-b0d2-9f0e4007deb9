/* eslint-disable no-useless-escape */
import React, { useCallback } from "react";

import * as Yup from "yup";

import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Text } from "components/atoms/text";
import { TextfieldHookForm } from "components/atoms/textfield";
import { toastSingleMode } from "components/atoms/toastify";
import { Formfield } from "components/molecules/formfield";
import { Modal } from "components/organisms/modal";
import { UnAuth } from "components/pages/unauth";
import { BasePageProps } from "helpers/component";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { FormContainer } from "helpers/form";
import { useAsync } from "hooks/useAsync";
import * as PAGES from "pages/pages";
import {
  employeeForgotPassword,
  ForgetPasswordResponse,
} from "services/crm/employee";

interface IForgetPasswordForm extends Omit<ForgetPasswordResponse, ""> {}

const validationSchema = Yup.object({
  email: Yup.string()
    .required("Email là bắt buộc")
    .test(
      "email",
      "Email hoặc số điện thoại không hợp lệ",
      (value?: unknown) => {
        if (typeof value !== "string") return false;
        if (!Number.isNaN(Number(value))) return true;
        const emailRegex =
          /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        const isValidEmail = emailRegex.test(value);
        if (!isValidEmail) {
          return false;
        }
        return true;
      }
    ),
});

const IndexPage: React.FC<BasePageProps> = () => {
  const [employeeForgotPasswordExec, employeeForgotPasswordState] = useAsync(
    employeeForgotPassword,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({
          type: "success",
          message: "Vui lòng kiểm tra email để nhận liên kết làm mới mật khẩu!",
        });
      }, []),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const onSubmitForgotPassword = useCallback(
    (formData: IForgetPasswordForm, reset: () => void) => {
      employeeForgotPasswordExec({
        email: formData.email,
      });
      reset();
    },
    [employeeForgotPasswordExec]
  );

  return (
    <UnAuth>
      <title>Quên mật khẩu</title>
      <Modal
        isOpen
        isClosable={false}
        shouldCloseOnEsc={false}
        shouldCloseOnOverlayClick={false}
        style={{
          content: {
            maxWidth: 620,
          },
        }}
      >
        <FormContainer
          validationSchema={validationSchema}
          onSubmit={onSubmitForgotPassword}
        >
          <Heading type="h1" centered>
            YÊU CẦU ĐỔI MẬT KHẨU
          </Heading>
          <Text centered>
            Vui lòng nhập email đã sử dụng đăng ký tài khoản để lấy lại mật khẩu
          </Text>
          <Formfield label="Email" name="email">
            <TextfieldHookForm name="email" id="email" />
          </Formfield>
          <div className="u-mt-15 u-mb-15">
            <Button
              type="submit"
              fullwidth
              isLoading={employeeForgotPasswordState.loading}
            >
              GỬI YÊU CẦU
            </Button>
          </div>
          <Button
            href={PAGES.LoginPage.path}
            buttonType="textbutton"
            modifiers="secondary"
            fullwidth
          >
            Đăng nhập
          </Button>
        </FormContainer>
      </Modal>
    </UnAuth>
  );
};

export default IndexPage;
