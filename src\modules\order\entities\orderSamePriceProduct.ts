import { Model, String, Enum, Array, ExtendSchema } from "libs/domain";

import { OrderGroupSamePriceItemSchema } from "./orderGroupSamePriceItem";

export const OrderSamePriceProductSchema = {
  code: String(),
  promotionType: Enum({ values: ["useVoucher", "defaultCampaign"] }),
  groups: Array(ExtendSchema(OrderGroupSamePriceItemSchema)),
};

export const OrderSamePriceProductModel = new Model(
  OrderSamePriceProductSchema
);
