import { useCallback, useMemo } from "react";

import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { orderReturnStatusOption, OrderReturnStatusModel } from "modules/order";
import { getOrderReturnStatus } from "services/crm/order";

export interface PulldownOrderReturnStatusFunctionalOptions {
  excludePending: boolean;
}

export const usePulldownOrderReturnStatus = ({
  excludePending,
}: PulldownOrderReturnStatusFunctionalOptions) => {
  const [fetchOrderReturnStatus, fetchOrderReturnStatusState] = useAsync(
    useCallback(
      async () =>
        getOrderReturnStatus().then((res) =>
          OrderReturnStatusModel.createMap(res.data.data)
        ),
      []
    ),
    {
      excludePending,
    }
  );

  const orderReturnStatuses = useMemo(
    () => fetchOrderReturnStatusState.data || [],
    [fetchOrderReturnStatusState.data]
  );

  const {
    options: orderReturnStatusOptions,
    getOptionByValue,
    formatOption: formatOrderReturnStatusOption,
  } = usePulldownHelper({
    dataSource: orderReturnStatuses,
    optionCreator: orderReturnStatusOption,
    valueTrans: Number,
  });

  return {
    fetchOrderReturnStatus,
    fetchOrderReturnStatusState,
    orderReturnStatusOptions,
    getOptionByValue,
    formatOrderReturnStatusOption,
  };
};
