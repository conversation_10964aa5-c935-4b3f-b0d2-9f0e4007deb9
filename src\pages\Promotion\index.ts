import { lazy } from "react";

import PATHS from "constants/paths";
import { createAppPage } from "libs/react";

import { ChildrenPage } from "./types";

export default createAppPage<{}, ChildrenPage>({
  name: "OrderManagement",
  path: PATHS.PROMOTIONS,
  childrenPages: {
    promotionManagement: createAppPage({
      name: "PromotionManagement",
      path: "/ChuongTrinhKhuyenMai",
      page: () =>
        lazy(() => import("./promotionManagement/PromotionManagementPage")),
    }),
    promotionDetail: createAppPage({
      name: "PromotionDetail",
      path: "/ChuongTrinhKhuyenMai/:promotionId",
      page: () => lazy(() => import("./PromotionDetail/PromotionDetailPage")),
    }),
    campaignManagement: createAppPage({
      name: "CampaignManagement",
      path: "/QuanLyChienDich",
      page: () => lazy(() => import("./CampaignManagement/campaignManagement")),
    }),
    campaignDetail: createAppPage({
      name: "CampaignDetail",
      path: "/QuanLyChienDich/tao-moi",
      page: () => lazy(() => import("./CampaignDetail/campaignDetail")),
    }),
    campaignDetailV2: createAppPage({
      name: "CampaignDetail",
      path: "/QuanLyChienDich/chi-tiet/:campaignId",
      page: () => lazy(() => import("./CampaignDetail/campaignDetail")),
    }),
  },
});
