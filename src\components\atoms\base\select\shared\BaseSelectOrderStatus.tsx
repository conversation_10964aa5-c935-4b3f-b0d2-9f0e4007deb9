/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useMemo } from "react";
import _ from "lodash";
import { mappedDataToDefaultOption } from "helpers/convertDataToDefaultOption.helper";
import { useGetOrderStatusWithoutPagination } from "services/crm/order/hooks/order-status.hook";
import { BaseSelect, BaseSelectProps } from "../BaseSelect";

/**
 * BaseSelectOrderStatus component to select order status.
 * It fetches order statuses and maps them to options for the BaseSelect component.
 *
 * @param {BaseSelectOrderStatusProps} props - The properties for the component.
 * @returns {JSX.Element} The rendered BaseSelect component with order status options.
 *
 * @description to check CURRENT_DATA_IN_SQL_2025_07_22
 * @data
 * -------------------------------------
 * | Order Status ID | Name            |
 * -------------------------------------
 * | 15              | Chưa x<PERSON>c nh<PERSON>   |
 * | 2               | Đã x<PERSON>c nhận     |
 * | 5               | Chuyển hàng     |
 * | 16              | Chuyển hoàn     |
 * | 21              | Thành công      |
 * | 18              | Đã hủy          |
 * -------------------------------------
 */

export type BaseSelectOrderStatusProps = {
  currentValue?: number;
} & BaseSelectProps;

export default function BaseSelectOrderStatus(
  props: BaseSelectOrderStatusProps
) {
  const { currentValue, ...rest } = props;

  const { orderStatusData, loading, orderStatusRefetch } =
    useGetOrderStatusWithoutPagination({
      searchText: "",
    });

  const orderStatusOptions = useMemo(() => {
    if (_.size(orderStatusData?.data) > 0) {
      const options = mappedDataToDefaultOption({
        data: orderStatusData?.data,
        keyLabel: "name",
        keyValue: "orderStatusId",
        restItem: true,
      });
      if (currentValue && currentValue !== 15) {
        if (currentValue === 2) {
          return options.filter((item) => item.value !== 15);
        }
        if (currentValue === 5 || currentValue === 16) {
          return options.filter(
            (item) => item.value !== 15 && item.value !== 2
          );
        }
        if (currentValue === 21) {
          return options.filter((item) => item.value === 21);
        }
        if (currentValue === 18) {
          return options.filter((item) => item.value !== 21);
        }
      }
      return options;
    }
    return [];
  }, [orderStatusData, currentValue]);

  useEffect(() => {
    orderStatusRefetch();
  }, []);

  return (
    <BaseSelect
      disabled={currentValue === 21 || currentValue === 18}
      loading={loading}
      options={orderStatusOptions}
      {...rest}
    />
  );
}
