const path = require('path');

module.exports = {
  "stories": [
    "../src/**/*.stories.mdx",
    "../src/**/*.stories.@(js|jsx|ts|tsx)"
  ],
  "addons": [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/preset-create-react-app",
    '@whitespace/storybook-addon-html/register',
  ],
  webpackFinal: (config) => {
    config.resolve.modules.push(path.resolve(__dirname, "../src"));
    return config;
  },
}

