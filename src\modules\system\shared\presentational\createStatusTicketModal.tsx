import * as Yup from "yup";

import { Button } from "components/atoms/button";
import { ColorPicker } from "components/atoms/colorpicker";
import { Heading } from "components/atoms/heading";
import { NumberfieldHookForm } from "components/atoms/numberfield";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Modal } from "components/organisms/modal";
import { Section } from "components/organisms/section";
import { FormContainer } from "helpers/form";

interface CreateStatusTicketModalProps {
  open: boolean;
  onClose: () => void;
  CreateStatusTicketValidation: Yup.ObjectSchema<Yup.AnyObject>;
}

export const CreateStatusTicketModal = ({
  open,
  onClose,
  CreateStatusTicketValidation,
}: CreateStatusTicketModalProps) => {
  return (
    <Modal
      style={{ content: { maxWidth: 700 } }}
      isOpen={open}
      onCloseModal={onClose}
    >
      <Section>
        <Heading centered type="h1">
          TẠO MỚI TRẠNG THÁI TICKET
        </Heading>
        <Section>
          <FormContainer validationSchema={CreateStatusTicketValidation}>
            <Formfield label="Tên trạng thái" name="statusName">
              <TextfieldHookForm name="statusName" />
            </Formfield>
            <Formfield label="Màu sắc" name="statusColor">
              <ColorPicker />
            </Formfield>
            <Formfield label="Thứ tự hiển thị" name="displayOrder">
              <NumberfieldHookForm name="displayOrder" />
            </Formfield>
            <div className="d-flex justify-content-end u-mt-20">
              <Button
                buttonType="outline"
                modifiers="secondary"
                onClick={onClose}
              >
                HỦY
              </Button>
              <div className="u-ml-15">
                <Button>LƯU</Button>
              </div>
            </div>
          </FormContainer>
        </Section>
      </Section>
    </Modal>
  );
};
