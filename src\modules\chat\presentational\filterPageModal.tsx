import { useRef } from "react";

import { Button } from "components/atoms/button";
import { Heading } from "components/atoms/heading";
import { Text } from "components/atoms/text";
import { File } from "components/molecules/file";
import { Col, Row } from "components/organisms/grid";
import { Modal } from "components/organisms/modal";

export interface PageType {
  pageName: string;
  pageThumbnail: string;
}

export interface FilterPageModalProps {
  open: boolean;
  onClose?: () => void;
  listPage: Array<PageType>;
}

export const FilterPageModal: React.FC<FilterPageModalProps> = ({
  open,
  onClose,
  listPage,
}: FilterPageModalProps) => {
  const selectedNumbers = useRef<Number>(0);

  return (
    <Modal
      style={{ content: { maxWidth: 800 } }}
      onCloseModal={onClose}
      isClosable
      isOpen={open}
    >
      <Heading type="h1">LỌC THEO PAGE</Heading>
      <Text>Chọn page</Text>
      <div
        style={{
          height: "30vh",
          overflowY: "auto",
          paddingRight: 15,
          marginBottom: 15,
        }}
      >
        <Row>
          {listPage.length > 0 &&
            listPage.map((page, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <Col key={index} md={3} sm={4}>
                <File
                  modifiers="floatcheckbox"
                  name={page.pageName}
                  src={page.pageThumbnail}
                  alt={page.pageName}
                />
              </Col>
            ))}
        </Row>
      </div>
      <Text>{`Đã chọn ${selectedNumbers.current}/${
        listPage?.length || 0
      }`}</Text>
      <div className="d-flex justify-content-end u-mt-20">
        <div className="u-mr-15">
          <Button buttonType="outline" modifiers="secondary" onClick={onClose}>
            HUỶ
          </Button>
        </div>
        <Button>LỌC</Button>
      </div>
    </Modal>
  );
};
