import { useCallback, useMemo } from "react";

import { useAsync } from "hooks/useAsync";
import { usePulldownHelper } from "hooks/usePulldownHelper";
import { DeliveryPartnerModel, deliveryPartnerOption } from "modules/order";
import { getListDeliveryPartner } from "services/crm/order";

export interface PulldownDeliveryPartnerFunctionalOptions {
  excludePending: boolean;
}

export const usePulldownDeliveryPartner = ({
  excludePending = false,
}: PulldownDeliveryPartnerFunctionalOptions) => {
  const [fetchDeliveryPartners, fetchDeliveryPartnersState] = useAsync(
    useCallback(
      () =>
        getListDeliveryPartner().then((res) =>
          DeliveryPartnerModel.createMap(res.data.data)
        ),
      []
    ),
    {
      excludePending,
    }
  );

  const deliveryPartners = useMemo(
    () => fetchDeliveryPartnersState.data || [],
    [fetchDeliveryPartnersState.data]
  );

  const { options: deliveryPartnerOptions } = usePulldownHelper({
    dataSource: deliveryPartners,
    optionCreator: deliveryPartnerOption,
    valueTrans: Number,
  });

  return {
    fetchDeliveryPartners,
    deliveryPartnerOptions,
  };
};
