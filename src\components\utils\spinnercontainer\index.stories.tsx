import { <PERSON>, Meta } from "@storybook/react/types-6-0";

import { SpinnerContainer, Props } from ".";

export default {
  title: "Components|utils/SpinnerContainer",
  component: SpinnerContainer,
} as Meta;

const Template: Story<Props> = () => (
  <SpinnerContainer animating>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
    <div>AAAAA</div>
  </SpinnerContainer>
);

export const Normal = Template.bind({});
