.a-pulldown {
	&_errormessage {
		margin-top: rem(5);
		margin-left: rem(10);
		font-size: rem(13);
		color: $COLOR-CINNABAR;
		@include u-fw-light;
	}

	// Override `MenuList`
	[class$="-MenuList"] {
		&:last-child {
			&::after {
				position: absolute;
				left: 0;
				width: 100%;
				content: "";
				background-image: url("~assets/images/icons/loading-blue.svg");
				background-repeat: no-repeat;
				background-position: center;
				background-size: 20px;
			}
		}
	}

	// Override `menu`
	[class$="-menu"] {
		z-index: z("pulldown", "menu");
	}
}
