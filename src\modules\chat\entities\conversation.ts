/* eslint-disable @typescript-eslint/no-explicit-any */
import omit from "object.omit";

import logoDummy from "assets/images/dummy/logo.png";
import { Enum, String, Model, ModelValue } from "libs/domain";
import { EmployeeSchema } from "modules/employee";

import { ClientSchema } from "./client";
import { MessageModel, MessageSchema } from "./message";

export const ConversationSchema = {
  conversationId: String(),
  latestMessage: omit(MessageSchema, ["conversationId"]),
  client: ClientSchema,
  type: Enum({ values: ["message", "comment"] }),
  assignedEmployee: EmployeeSchema,
  createdAt: (rawData: any) => rawData && new Date(rawData),
  updatedAt: (rawData: any) => rawData && new Date(rawData),
};

export type MessageCreatorType = "customer" | "employee";

export const ConversationModel = new Model(ConversationSchema);
export type ConversationEntityType = ModelValue<typeof ConversationModel>;

export const messageCreatorType = (
  conversation?: Partial<ModelValue<typeof ConversationModel>>,
  messageSender?: Partial<ModelValue<typeof MessageModel>["sender"]>
): MessageCreatorType =>
  messageSender?.id === conversation?.client?.id ? "customer" : "employee";

export const messageCreatorAvatar = (
  conversation?: Partial<ModelValue<typeof ConversationModel>>,
  messageSender?: Partial<ModelValue<typeof MessageModel>["sender"]>
) =>
  messageSender?.id === conversation?.client?.id
    ? conversation?.client?.profilePic
    : logoDummy;
