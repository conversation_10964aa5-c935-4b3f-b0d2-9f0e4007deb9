import pick from "object.pick";

import { cleanEmptyString } from "helpers/object";
import { createMapper, force, fromSchema } from "libs/adapters/dto";
import { mergeSchema } from "libs/domain";

import { GroupEmployeeSchema } from "../entities";

export const updateEmployeeGroupDto = createMapper(
  fromSchema(mergeSchema(pick(GroupEmployeeSchema, ["name"]))),
  force((data) => ({
    ...cleanEmptyString(data),
  }))
);

export type UpdateEmployeeGroupDtoType = ReturnType<
  typeof updateEmployeeGroupDto
>;
