import { useEffect, useMemo, useState } from "react";
import {
  ColorPicker,
  Form,
  Input,
  InputNumber,
  Switch,
  Typography,
} from "antd";
import { Color } from "antd/es/color-picker";
import { useSearchParams, useParams } from "react-router";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelectWithFeatures } from "components/atoms/base/select/BaseSelectWithFeatures";
import { showLoading } from "components/atoms/base/Spinner";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";
import { ChildrenPage } from "../types";
import { OrderStatusFormType } from "./constant";
import { OrderStatusDetailPageVm } from "./vm";

type FormValueType = Partial<OrderStatusFormType>;

export default function OrderStatusDetailV2() {
  const [colorValue, setColorValue] = useState<string>(
    "rgba(0, 0, 0, 0)" // Default color value
  );
  const [form] = Form.useForm<FormValueType>();
  const [searchParams] = useSearchParams();

  const { orderStatusCode } =
    useParams<PageParamsType<ChildrenPage["orderStatusDetail"]>>();

  const {
    loading,
    orderStatusDetail,
    orderStatusOptions,
    updateOrderStatusState,
    handleUpdateOrderStatus,
    orderStatusOptionPulldown,
    handleLoadmoreOrderStatus,
  } = OrderStatusDetailPageVm({
    orderStatusCode,
  });

  const pageActionType = searchParams.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  const onFinish = (formData: FormValueType) => {
    form.validateFields();
    const color =
      typeof formData?.color === "string"
        ? formData.color
        : (formData.color as unknown as Color).toRgbString();
    handleUpdateOrderStatus({
      ...formData,
      color,
      displayOrder: Number(formData.displayOrder),
      prevOrderStatusIds: formData.prevOrderStatus?.map((item) =>
        Number(item.value)
      ),
    });
  };

  const handleChangeColor = (value: Color, hex: string) => {
    const rgbColorValue = value.toRgbString();
    setColorValue(rgbColorValue);
    // form.setFieldsValue({
    //   color: rgbColorValue,
    // });
  };

  useEffect(() => {
    if (orderStatusDetail) {
      const { name, color, displayOrder, description, isDefault } =
        orderStatusDetail || {};
      setColorValue(color || "rgba(0, 0, 0, 0)"); // Set initial color value
      form.setFieldsValue({
        prevOrderStatus: orderStatusOptions.map((item) => {
          return {
            value: item.value,
          };
        }),
        name,
        color,
        displayOrder,
        description,
        isDefault,
      });
    }
  }, [orderStatusDetail]);

  useEffect(() => {
    showLoading(loading);
  }, [loading]);

  return (
    <General>
      <title key="title">Sửa thông tin trạng thái đơn hàng</title>
      <div className="max-w-4xl mx-auto h-full p-4 flex flex-col gap-4">
        <Typography.Title level={3} className="!text-blue-500 font-semibold">
          {editMode ? "SỬA" : viewMode ? "XEM" : null} THÔNG TIN TRẠNG THÁI ĐƠN
          HÀNG
        </Typography.Title>
        {!loading && (
          <Form
            layout="vertical"
            form={form}
            onFinish={onFinish}
            labelAlign="left"
            labelCol={{ span: 8 }}
            wrapperCol={{
              span: 24,
            }}
            // disabled={viewMode}
          >
            <div className="grid grid-cols-1 lg:grid-cols-1 gap-4">
              <Form.Item
                label="Tên trạng thái"
                name="name"
                rules={[
                  {
                    required: true,
                    message: "Vui lòng nhập tên trạng thái",
                  },
                ]}
              >
                <Input placeholder="Tên trạng thái" disabled={viewMode} />
              </Form.Item>

              <Form.Item hidden label="Trạng thái trước" name="prevOrderStatus">
                <BaseSelectWithFeatures
                  placeholder="Trạng thái trước"
                  mode="multiple"
                  options={orderStatusOptionPulldown}
                  // tagRender={(props) => {
                  //   const { label, value, closable, onClose } = props;
                  //   return (
                  //     <Tag
                  //       closable={closable}
                  //       key={`tag-id-${value}`}
                  //       style={{
                  //         borderRadius: "10px",
                  //       }}
                  //       onClose={onClose}
                  //     >
                  //       {label}
                  //     </Tag>
                  //   );
                  // }}
                  disabled={viewMode}
                  triggerLoadMore={handleLoadmoreOrderStatus}
                />
              </Form.Item>

              <Form.Item label="Thứ tự hiển thị" name="displayOrder">
                <InputNumber
                  controls
                  placeholder="Thứ tự hiển thị"
                  className="w-full"
                  disabled={viewMode}
                />
              </Form.Item>

              <Form.Item label="Mô tả" name="description">
                <Input.TextArea
                  placeholder="Mô tả"
                  rows={5}
                  disabled={viewMode}
                />
              </Form.Item>

              <Form.Item
                label="Mặc định"
                labelAlign="left"
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                name="isDefault"
                valuePropName="checked"
              >
                <Switch disabled={viewMode} />
              </Form.Item>

              <Form.Item label="Màu sắc" name="color">
                <ColorPicker
                  format="rgb"
                  onChange={handleChangeColor}
                  disabled={viewMode}
                />
              </Form.Item>

              <div className="flex justify-end gap-4">
                <BaseButton
                  className="h-10"
                  type="text"
                  bgColor={COLOR.GRAY[700]}
                  hoverColor={COLOR.GRAY[700]}
                  onClick={navigationHelper.goBack}
                >
                  Quay lại
                </BaseButton>
                {editMode && (
                  <BaseButton
                    type="primary"
                    htmlType="submit"
                    className="h-10"
                    bgColor={COLOR.BLUE[500]}
                    hoverColor={COLOR.BLUE[700]}
                    loading={updateOrderStatusState.loading}
                    disabled={updateOrderStatusState.loading}
                  >
                    Lưu
                  </BaseButton>
                )}
              </div>
            </div>
          </Form>
        )}
      </div>
    </General>
  );
}
