import * as Yup from "yup";

import { EmployeeEntityType } from "modules/employee";
import {
  cityOption,
  districtOption,
  wardOption,
  storageOption,
  BankAccountEntityType,
} from "modules/location";
import { CreateShopDtoType } from "modules/shop";

export const validationSchemaOfCreation = Yup.object({
  name: Yup.string().required("Vui lòng nhập tên shop"),
  hotline: Yup.string().required("Vui lòng nhập Hotline"),
  type: Yup.object().required("Vui lòng chọn loại shop"),
  address: Yup.string().required("Vui lòng nhập địa chỉ shop"),
  city: Yup.object().nullable().required("Vui lòng chọn Tỉnh/Thành phố"),
  district: Yup.object().nullable().required("Vui lòng chọn Quận/Huyện"),
  ward: Yup.object().nullable().required("<PERSON>ui lòng chọn Phường/Xã"),
  storages: Yup.array().required("Vui lòng chọn kho"),
});

export type CreateShopFormPayload = Pick<
  CreateShopDtoType,
  "name" | "hotline" | "address" | "displayOrder" | "isFranchisedShop"
> & {
  city: ReturnType<typeof cityOption>;
  district: ReturnType<typeof districtOption>;
  ward: ReturnType<typeof wardOption>;
  type: { label: string; value: string };
  storages: ReturnType<typeof storageOption>[];
  bankAccounts: BankAccountEntityType[];
  employees: EmployeeEntityType[];
};
