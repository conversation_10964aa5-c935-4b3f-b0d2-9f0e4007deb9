import {
  UpdateBankAccountDtoType,
  UpdateStorageDtoType,
} from "modules/location";
import {
  CreatePointExchangeDTOType,
  SystemConfigEntityType,
} from "modules/system";
import erpDriverV1 from "services/erp/erp-driver";

import crmDriverV1 from "../crm-driver-v1";
import {
  PointExchangeResponseDto,
  SystemConfigPointExchangeResponseDto,
} from "./dto/point-exchange.dto";

export interface ShopFilterPayload {
  pageNum: number;
  pageSize: number;
  name?: string;
  type?: string;
  isFranchisedShop?: boolean;
  cityId?: number;
  storageId?: number;
}

export const getListStorage = (payload: {
  pageNum?: number;
  pageSize?: number;
  name?: string;
}) =>
  crmDriverV1.get("/locations/external/storages", { params: { ...payload } });

export const getStorageById = (payload: { storageId: number }) =>
  crmDriverV1.get(`/locations/external/storages/${payload.storageId}`);

export const updateStorageById = (
  storageId: number,
  payload: UpdateStorageDtoType
) => crmDriverV1.put(`/locations/external/storages/${storageId}`, payload);

export type StoragePayload = {
  name: string;
  displayOrder: number;
  code: string;
};

export const createStorage = (payload: StoragePayload) =>
  crmDriverV1.post("/locations/external/storages", { ...payload });

export const getListBankAccount = (payload: {
  pageNum: number;
  pageSize: number;
  owner?: string;
  branchName?: string;
  accountNumber?: string;
}) =>
  crmDriverV1.get(`/locations/external/bank-accounts`, {
    params: { ...payload },
  });

export const getBankAccountById = (payload: { bankAccountId: number }) =>
  crmDriverV1.get(`/locations/external/bank-accounts/${payload.bankAccountId}`);

export const updateBankAccountById = (
  bankAccountId: number,
  payload: UpdateBankAccountDtoType
) =>
  crmDriverV1.put(
    `/locations/external/bank-accounts/${bankAccountId}`,
    payload
  );

export const createBankAccount = (payload: {
  bankName: string;
  branchName: string;
  owner: string;
  accountNumber: string;
}) => crmDriverV1.post("/locations/external/bank-accounts", payload);

export const getShops = (payload?: ShopFilterPayload) =>
  crmDriverV1.get("/locations/external/shops", {
    params: { ...payload },
  });

export const getShopDetail = (payload: { shopId: number }) =>
  crmDriverV1.get(`/locations/external/shops/${payload.shopId}`);

export const getCities = (payload: { site: string }) =>
  erpDriverV1.get(`/Address/listallcity/${payload.site}`);

export const getDistricts = (payload: { site: string; cityId: number }) =>
  erpDriverV1.get(`/Address/listdistrict/${payload.site}/${payload.cityId}`);

export const getWards = (payload: { site: string; districtId: number }) =>
  erpDriverV1.get(`/Address/listward/${payload.site}/${payload.districtId}`);

export const createPointExchange = (payload: CreatePointExchangeDTOType) =>
  crmDriverV1.post("/common/external/system-configurations/point-exchange", {
    ...payload,
  });

export const getPointExchange = () => {
  return crmDriverV1.get<{
    data: SystemConfigPointExchangeResponseDto;
  }>("/common/external/system-configurations/point-exchange");
};
