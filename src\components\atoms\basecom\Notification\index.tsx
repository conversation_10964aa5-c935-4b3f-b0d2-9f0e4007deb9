import { forwardRef, SyntheticEvent, useEffect, useState } from "react";

import {
  FileTextOutlined,
  FolderOutlined,
  SearchOutlined,
  TeamOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Divider, Empty, Input, Tag } from "antd";

import style from "./index.module.scss";

export type TNotification = {
  test: string;
} & HTMLDivElement;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Props = {};

const navigate = ["Quan trọng", "Khách hàng", "Lead", "Ticket", "Tất cả"];

export const Notification = forwardRef<TNotification, Props>((prop, ref) => {
  /**
   * Screen 1: Quan trọng
   * Screen 2: Khách hàng
   * Screen 3: Lead
   * Screen 4: Ticket
   * Screen 5: Tất cả
   */
  const [screen, setScreen] = useState(5);
  const handleChangeScreen = (e: SyntheticEvent<HTMLDivElement>) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setScreen(+(e.target as any).id);
  };
  useEffect(() => {}, []);
  const [selectedNotification, setSelectedNotification] =
    useState<Notification | null>(() => null);
  const [selectedTag, setSelectedTag] = useState<number | null>(null);
  const [showDetailContent, setShowDetailContent] = useState<boolean>(false);

  interface Notification {
    id: number;
    title: string;
    icon: JSX.Element;
    note: string;
    // detail: string[];
    read: boolean;
  }
  const detailsConfig: Record<number, string[]> = {
    1: ["Khách hàng A"],
    2: ["Công việc 1"],
    3: ["Tất cả A"],
  };
  const detailsConfigs: Record<number, string[]> = {
    1: ["Khách hàng B"],
    2: ["Công việc 2"],
    3: ["Tất cả B"],
  };
  const notifications: Notification[] = [
    {
      id: 1,
      title: "Khách hàng",
      icon: <TeamOutlined rev={undefined} />,
      note: "Demo khach hang",
      read: false,
      // detail: ["Khách hàng A", "Khách hàng B", "Khách hàng C"],
    },
    {
      id: 2,
      title: "Công việc",
      icon: <FileTextOutlined rev={undefined} />,
      note: "Demo cong viec",
      read: false,

      // detail: ["Công việc 1", "Công việc 2", "Công việc 3"],
    },
    {
      id: 3,
      title: "Tất cả",
      icon: <FolderOutlined rev={undefined} />,
      note: "Demo tat ca",
      read: false,

      // detail: ["Tất cả A", "Tất cả B", "Tất cả C"],
    },
  ];
  const handleTagClick = (notification: Notification) => {
    setSelectedNotification(notification);
    setSelectedTag(notification.id); // Cập nhật selectedTag khi tag được chọn
    setSelectedNotification({ ...notification, read: true }); // Đánh dấu là đã đọc khi click vào thông báo
    setShowDetailContent(true);
  };
  const handleDetailContentClick = () => {
    setSelectedNotification(
      selectedNotification ? { ...selectedNotification, read: true } : null
    ); // Đánh dấu là đã đọc khi click vào detailContent
  };
  let detailContent = <Empty description="No Data Available" />;

  if (showDetailContent && selectedNotification) {
    const details = detailsConfig[selectedNotification.id] || [];
    const detail = detailsConfigs[selectedNotification.id] || [];
    if (details.length > 0 || detail.length > 0) {
      detailContent = (
        <div className="">
          <div className={style.notification}>
            <div className={style.avatar}>
              <span className={style.userIcon}>
                <UserOutlined rev={undefined} />
              </span>
            </div>
            <div className={style.text}>
              <div className={style.note}>
                <span className={style.name}>
                  {selectedNotification.title} :
                </span>
                {details.join(", ")}
                <br />
                <span className={style.note}>{selectedNotification.note}</span>
              </div>
            </div>
          </div>
          <div className={style.notification}>
            <div className={style.avatar}>
              <span className={style.userIcon}>
                <UserOutlined rev={undefined} />
              </span>
            </div>
            <div className={style.text}>
              <div className={style.note}>
                <span className={style.name}>
                  {selectedNotification.title} :
                </span>
                {detail.join(", ")}
                <br />
                <span className={style.note}>{selectedNotification.note}</span>
              </div>
            </div>
          </div>
        </div>
      );
    }
  }

  return (
    <div className={style.content} ref={ref}>
      <p className="font-bold text-2xl	mt-[15px] ml-[15px]">Thông báo</p>
      <Divider style={{ margin: "2px" }} />
      {/* <div className=""> */}
      <Input
        className=" h-12 mt-4"
        placeholder="Tìm kiếm thông báo"
        prefix={<SearchOutlined rev={undefined} />}
      />
      <div className="">
        <div className="flex items-center mt-4 ml-2">
          {notifications.map((notification) => (
            <Tag
              key={notification.id}
              className={`flex items-center h-8 px-3 py-1 text-center text-base cursor-pointer
                  ${
                    selectedTag === notification.id
                      ? "bg-blue-500 hover:bg-blue-600 focus:bg-blue-600 text-white"
                      : "bg-gray-200 hover:bg-gray-300 focus:bg-gray-300"
                  } `}
              onClick={() => handleTagClick(notification)}
            >
              {notification.icon}
              <span className="ml-2">{notification.title}</span>
            </Tag>
          ))}
        </div>
        <div>
          {selectedNotification && showDetailContent ? (
            // eslint-disable-next-line jsx-a11y/no-static-element-interactions
            <div
              className={`mt-4 ${
                selectedNotification.read ? "bg-gray-200" : "bg-blue-500"
              }`}
              onClick={handleDetailContentClick}
            >
              {detailContent}
            </div>
          ) : (
            <Empty
              className="p-24"
              // image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={false}
              imageStyle={{
                height: 300,
              }}
            />
          )}
        </div>
      </div>
      {/* </div> */}
      {/*
      <div className={style.notification}>
        <div className={style.avatar}>Add image here</div>
        <div className={style.text}>
          <div className={style.note}>
            <span className={style.name}>Lê Hoàng Khang</span> đã bình luận trên
            lệnh Purchase Price List chứng từ PRL20000
          </div>
          <div className={style.time}>3 giờ trước</div>
        </div>
      </div>
      <Skeleton
      avatar={{ style: { width: "50px", height: "50px" } }}
      active
      loading
        paragraph={{ rows: 2 }}
        style={{ padding: "10px" }}
        title={false}
        />
        <Skeleton
        avatar={{ style: { width: "50px", height: "50px" } }}
        active
        loading
        paragraph={{ rows: 2 }}
        style={{ padding: "10px" }}
        title={false}
        />
        <Skeleton
        avatar={{ style: { width: "50px", height: "50px" } }}
        active
        loading
        paragraph={{ rows: 2 }}
        style={{ padding: "10px" }}
        title={false}
      />
      <Skeleton
      avatar={{ style: { width: "50px", height: "50px" } }}
      active
      loading
      paragraph={{ rows: 2 }}
      style={{ padding: "10px" }}
      title={false}
      /> */}
    </div>
  );
});
