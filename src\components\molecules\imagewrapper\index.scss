.m-imagewrapper {
	$root: &;

	position: relative;
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
	border: solid 1px $COLOR-PASTEL-GRAY;
	transition: all 0.3s ease-in-out, transform 0s;
	@include aspectRatio(135, 158);

	&:hover {
		border: solid 1px $COLOR-DENIM;
	}

	&_input {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: z("imagewrapper", "input");
		width: 100%;
		height: 100%;
		cursor: pointer;
		background-color: $COLOR-PLATINUM-5;
		opacity: 0;
	}

	&_wrapicon {
		position: absolute;
		top: 50%;
		left: 50%;
		width: rem(24);
		height: rem(24);
	}

	&_close {
		.a-icon {
			position: absolute;
			top: rem(8);
			right: rem(8);
			z-index: z("imagewrapper", "close");
			width: rem(10);
			height: rem(10);
			visibility: hidden;
			opacity: 0;
			transition: all 0.2s ease-in-out;
		}
	}

	&-uploaded {
		&:after {
			opacity: 0;
		}
		.a-icon-close {
			visibility: visible;
			opacity: 1;
		}

		&:hover {
			.a-icon-close {
				visibility: hidden;
				opacity: 0;
			}

			.a-icon-close-blue {
				visibility: visible;
				opacity: 1;
			}
		}
	}
}
