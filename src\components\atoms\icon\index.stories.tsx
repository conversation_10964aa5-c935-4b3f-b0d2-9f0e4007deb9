import React from "react";

import { action } from "@storybook/addon-actions";
import { Story, Meta } from "@storybook/react/types-6-0";

import { Icon, Props } from ".";

export default {
  title: "Components|atoms/Icon",
  component: Icon,
} as Meta;

const Template: Story<Props> = ({
  iconName,
  clickable,
  modifiers,
  style,
  onClick,
}) => <Icon iconName={iconName} />;

export const Normal = Template.bind({});

Normal.args = {
  iconName: "slidebar",
  onClick: action("Click"),
};
