.a-button {
	$root: &;
	$animation: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: rem(14) rem(60);
	overflow: hidden;
	font-family: $FONTFAMILY-ROBOTO;
	font-size: rem(16);
	line-height: rem(20);
	border: 1px solid $COLOR-TRANSPARENT;
	border-radius: rem(1);
	outline: none;
	transition: all $animation;
	appearance: none;
	@include u-fw-bold;

	&:hover,
	&:focus,
	&:focus-within {
		text-decoration: none;
	}

	.a-icon {
		position: absolute;
		top: 50%;
		left: 22px;
		pointer-events: none;
		transform: translateY(-50%);
	}

	&-primary {
		color: $COLOR-WHITE;
		background-color: $COLOR-DENIM;
		border-color: $COLOR-TRANSPARENT;

		&:hover,
		&:focus,
		&:focus-within {
			color: $COLOR-WHITE;
			background-color: $COLOR-DENIM;
			border: 1px solid $COLOR-TRANSPARENT;
		}
	}

	&-secondary {
		color: $COLOR-DENIM;
		background-color: $COLOR-WHITE-SMOKE;
		border-color: $COLOR-WHITE-SMOKE;

		&:hover,
		&:focus,
		&:focus-within {
			color: $COLOR-WHITE;
			background-color: $COLOR-LIGHT-GRAY;
			border: 1px solid $COLOR-TRANSPARENT;
			border-width: 1px;
		}
	}

	&-outline {
		&#{$root}-primary {
			color: $COLOR-DENIM;
			background-color: $COLOR-TRANSPARENT;
			border-color: $COLOR-DENIM;

			&:hover,
			&:focus,
			&:focus-within {
				color: $COLOR-WHITE;
				background-color: $COLOR-DENIM;
			}
		}

		&#{$root}-secondary {
			color: $COLOR-QUARTZ;
			background-color: $COLOR-TRANSPARENT;
			border-color: $COLOR-QUARTZ;

			&:hover,
			&:focus {
				color: $COLOR-WHITE;
				background-color: $COLOR-QUARTZ;
			}
		}
	}

	&-textbutton {
		min-width: inherit;
		padding: 0;
		color: $COLOR-DENIM;
		text-decoration: none;
		background-color: $COLOR-TRANSPARENT;
		border: 0;
		border-color: $COLOR-TRANSPARENT;

		span {
			border-bottom: 1px solid $COLOR-TRANSPARENT;
		}

		&:hover,
		&:focus {
			text-decoration: underline;
			background-color: $COLOR-TRANSPARENT;
			border: 0;
		}

		&#{$root}-primary {
			color: $COLOR-DENIM;
		}
		&#{$root}-secondary {
			color: $COLOR-QUARTZ;
		}
	}

	&-disabled {
		color: $COLOR-WHITE;
		pointer-events: none;
		background-color: $COLOR-PLATINUM-4;

		&#{$root}-textbutton,
		&#{$root}-outline {
			color: $COLOR-PLATINUM-4;
			pointer-events: none;
			background-color: $COLOR-TRANSPARENT;
		}

		&#{$root}-textbutton {
			border-color: $COLOR-TRANSPARENT;
		}

		&#{$root}-outline {
			border-color: $COLOR-PLATINUM-4;
		}
	}

	&-fullwidth {
		width: 100%;
	}

	&-medium {
		padding: rem(10) rem(50);

		.a-icon {
			left: 17px;
		}
	}
}

a[class*="a-button"] {
	display: inline-flex;
}
