import { useCallback, useState } from "react";

import { sort as fastSort } from "fast-sort";

import useDerivedStateFromProps from "helpers/react-hooks/useDerivedStateFromProps";

type SupportSortType = "asc" | "desc";

interface SortableState<
  S,
  SortBy extends { [key: string]: (state: S) => unknown },
  K extends keyof SortBy
> {
  data?: S;
  sortBy: [K, SupportSortType] | [];
}

export const useSortable = <
  S extends unknown[],
  SortBy extends { [key: string]: (state: S[number]) => unknown },
  K extends keyof SortBy
>(props: {
  data?: S;
  sortBy: SortBy;
}) => {
  const [state, setState] = useState<SortableState<S, SortBy, K>>({
    data: props.data,
    sortBy: [],
  });

  useDerivedStateFromProps((_, nextData) => {
    if (nextData) {
      setState((prevState) => ({
        ...prevState,
        data: nextData,
      }));
    }
  }, props.data);

  const toggleSortState = useCallback(
    (sortBy: K) => {
      const [currentSortBy, currentSortType] = state.sortBy;
      const dataSet = state.data;
      if (!dataSet) return;
      let nextSortType: SupportSortType = currentSortType || "asc";
      if (currentSortBy === sortBy) {
        if (currentSortType === "asc") {
          nextSortType = "desc";
        } else {
          nextSortType = "asc";
        }
      }

      const sorted = fastSort(dataSet)[nextSortType](props.sortBy[sortBy]) as S;

      setState((prevState) => ({
        ...prevState,
        data: sorted,
        sortBy: [sortBy, nextSortType],
      }));
    },
    [props.sortBy, state.data, state.sortBy]
  );

  return {
    toggleSortState,
    sortedData: state.data,
  };
};
