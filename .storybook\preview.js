import { withHTML } from '@whitespace/storybook-addon-html/react';
import StorybookReactRouter from '../src/helpers/storybook-router';

if (typeof jest === "undefined") {
  // NOTES: Resolve storybook static file after built
  require('!style-loader!css-loader!sass-loader!../src/index.scss');
}

// NOTE: <PERSON><PERSON> generates className
// @see https://github.com/wwayne/react-tooltip/issues/595#issuecomment-638438372
if (process.env.NODE_ENV === 'test' && typeof jest !== 'undefined') {
  jest.mock('react-tooltip/node_modules/uuid', () => ({ v4: () => '00000000-0000-0000-0000-000000000000' }));
}

localStorage.removeItem('storybook-layout');

export const parameters = {
  actions: { argTypesRegex: "^on[A-Z].*" },
  viewport: {
    viewports: {
      smallmobile: {
        name: 'Mobile',
        styles: {
          width: '375px',
          height: '576px',
        },
      },
      tablet: {
        name: 'Tablet',
        styles: {
          width: '577px',
          height: '1199px',
        },
      },
    },
  },
};

export const decorators = [
  StorybookReactRouter,
  withHTML({
    prettier: {
      tabWidth: 2,
      singleQuote: true,
      bracketSpacing: true,
      htmlWhitespaceSensitivity: 'ignore',
    },
  })
]
