import { useEffect, useCallback } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { bankDetailDto, BankDetailDtoType } from "modules/location/dtos";
import {
  updateBankAccountDto,
  UpdateBankAccountDtoType,
} from "modules/location/dtos/updateBankAccountDetail";
import { getBankAccountById, updateBankAccountById } from "services/crm/system";

interface BankDetailPageVmProps {
  bankAccountId: number;
}

export type BankAccountItem = BankDetailDtoType;

export const BankDetailPageVm = ({ bankAccountId }: BankDetailPageVmProps) => {
  const [getBankAccountByIdExec, getBankAccountByIdState] = useAsync(
    useCallback(
      (params: { bankAccountId: number }) =>
        getBankAccountById({ ...params }).then((res) =>
          bankDetailDto(res.data.data)
        ),
      []
    )
  );

  const [updateBankAccountDetailExec, updateBankAccountDetailState] = useAsync(
    updateBankAccountById,
    {
      onSuccess: useCallback(() => {
        toastSingleMode({ type: "success", message: "Cập nhật thành công" });
      }, []),
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(error?.errors?.[0]?.code);

        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
    }
  );

  const bankAccountDetail = getBankAccountByIdState.data;

  const handleUpdateBankAccountDetail = useCallback(
    (rawPayload: Partial<UpdateBankAccountDtoType>) => {
      if (!bankAccountDetail?.bankAccountId) return;
      const updateBankAccountPayload = updateBankAccountDto({
        ...bankAccountDetail,
        ...rawPayload,
      });

      updateBankAccountDetailExec(
        bankAccountDetail.bankAccountId,
        updateBankAccountPayload
      );
    },
    [updateBankAccountDetailExec, bankAccountDetail]
  );

  useEffect(() => {
    getBankAccountByIdExec({ bankAccountId });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bankAccountId]);

  return {
    loading: getBankAccountByIdState.loading,
    bankAccountData: getBankAccountByIdState.data,
    updateBankAccountDetailState,
    handleUpdateBankAccountDetail,
  };
};
