import { useCallback, useRef } from "react";

import { Register } from "./types";

export const useEditableInput = () => {
  const registerRef = useRef<Register>();

  const register = useCallback((registerValue: Register) => {
    registerRef.current = registerValue;
  }, []);

  const getValue = useCallback(() => {
    const getter = registerRef.current?.getter;
    if (getter) {
      const { value } = getter();
      return value;
    }
    return null;
  }, []);

  const setValue = useCallback((value: string) => {
    const setter = registerRef.current?.setter;
    if (setter) {
      const { setValue: originSetter } = setter();
      originSetter(value);
    }
  }, []);

  const reset = useCallback(() => {
    const setter = registerRef.current?.setter;
    if (setter) {
      const { setValue: originSetter } = setter();
      originSetter("");
    }
  }, []);

  return {
    register,
    getValue,
    reset,
    setValue,
  };
};
