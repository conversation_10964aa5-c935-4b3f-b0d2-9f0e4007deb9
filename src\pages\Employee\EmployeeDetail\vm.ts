import { useCallback, useEffect } from "react";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import useDidMount from "helpers/react-hooks/useDidMount";
import { useAsync } from "hooks/useAsync";
import { employeeDetailDto } from "modules/employee";
import { usePulldownEmployeeGroup } from "modules/employee/shared";
import { getEmployeeDetail, updateEmployee } from "services/crm/employee";

export interface EmployeeDetailPageVmProps {
  employeeId: number;
}

export const EmployeeDetailPageVm = ({
  employeeId,
}: EmployeeDetailPageVmProps) => {
  const [getEmployeeDetailExec, getEmployeeDetailState] = useAsync(
    useCallback(
      (options: { employeeId: number }) =>
        getEmployeeDetail({ ...options }).then((res) =>
          employeeDetailDto(res.data.data)
        ),
      []
    )
  );

  const {
    employeeGroupOptions,
    fetchEmployeeGroups,
    formatEmployeeGroupOptions,
  } = usePulldownEmployeeGroup({
    excludePending: true,
  });

  const [updateEmployeeExec, updateEmployeeState] = useAsync(updateEmployee, {
    onFailed: useCallback((error) => {
      const errMessage = getErrorMessageViaErrCode(
        error?.response?.data?.errors?.[0]?.code
      );
      toastSingleMode({
        type: "error",
        message: errMessage.translation.title,
        descripition: errMessage.translation.detail,
      });
    }, []),
    onSuccess: useCallback(() => {
      toastSingleMode({
        type: "success",
        message: "Cập nhật thành công",
      });
    }, []),
  });

  useEffect(() => {
    if (employeeId && !Number.isNaN(employeeId)) {
      getEmployeeDetailExec({ employeeId });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [employeeId]);

  useDidMount(() => {
    fetchEmployeeGroups();
  });

  return {
    loading: getEmployeeDetailState.loading,
    employeeDetail: getEmployeeDetailState.data,
    employeeGroupOptions,
    formatEmployeeGroupOptions,
    updateEmployeeExec,
    updateEmployeeState,
  };
};
