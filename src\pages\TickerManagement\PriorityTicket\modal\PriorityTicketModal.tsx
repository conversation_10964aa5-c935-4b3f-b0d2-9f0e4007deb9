/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect } from "react";
import { ColorPicker, Form, Input, InputNumber, Modal, Space } from "antd";
import { Color } from "antd/es/color-picker";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { showLoading } from "components/atoms/base/Spinner";
// import { Modal } from "components/organisms/modal";
import { COLOR } from "constants/color";
import {
  UseCreateTicket,
  useUpdateTicket,
} from "modules/ticket/hook/useCreateTicket";
import { PriorityDataType } from "../PriorityTicketPage";

interface PriorityTicketModalProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  refetch: () => void;
  dataModal: PriorityDataType | null;
  setDataModal: React.Dispatch<React.SetStateAction<PriorityDataType | null>>;
}

export const PriorityTicketModal = ({
  open,
  dataModal,
  setOpen,
  setDataModal,
  refetch,
}: PriorityTicketModalProps) => {
  const [form] = Form.useForm();
  const { createTicketExe } = UseCreateTicket();
  const { updateTicketExe } = useUpdateTicket();
  const handleSubmit = (values: any) => {
    const color =
      typeof values?.color === "string"
        ? values.color
        : (values.color as unknown as Color).toRgbString();

    const payload = {
      ...values,
      color,
    };
    showLoading(true);
    if (dataModal?.masterDataTicketId) {
      updateTicketExe(dataModal.masterDataTicketId, payload)
        .then(() => {
          showNotification({
            type: "success",
            message: "Cập nhật thành công",
          });
          refetch();
          onClose();
        })
        .catch((err) => {
          const errors = err?.response?.data?.errors;
          if (errors && errors.length > 0) {
            errors.forEach((e: any) => {
              showNotification({
                type: "error",
                message: e.title,
              });
            });
            return;
          }
          showNotification({
            type: "error",
            message: errors.title,
          });
        })
        .finally(() => {
          showLoading(false);
        });
      return;
    }
    createTicketExe(payload)
      .then((res) => {
        console.log(res, "res");
        showNotification({
          type: "success",
          message: "Tạo thành công",
        });
        refetch();
        onClose();
      })
      .catch((err) => {
        const errors = err?.response?.data?.errors;
        if (errors && errors.length > 0) {
          errors.forEach((e: any) => {
            showNotification({
              type: "error",
              message: e.title,
            });
          });
          return;
        }
        showNotification({
          type: "error",
          message: errors.title,
        });
      })
      .finally(() => {
        showLoading(false);
      });
  };

  const onClose = () => {
    form.resetFields();
    setDataModal(null);
    setOpen(false);
  };

  useEffect(() => {
    if (dataModal) {
      const { createdAt, updatedAt, ...restProps } = dataModal;
      form.setFieldsValue(restProps);
    }
  }, [dataModal]);

  return (
    <Modal
      title={
        dataModal?.masterDataTicketId
          ? "Chỉnh sửa mức độ ưu tiên"
          : "Tạo mới mức độ ưu tiên"
      }
      open={open}
      onCancel={onClose}
      centered
      footer={
        <Space align="center">
          <BaseButton
            type="primary"
            bgColor={COLOR.GRAY[500]}
            hoverColor={COLOR.GRAY[600]}
            onClick={onClose}
          >
            Hủy
          </BaseButton>
          <BaseButton
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[700]}
            onClick={() => form.submit()}
          >
            Lưu
          </BaseButton>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ color: "#000" }}
        onFinish={handleSubmit}
      >
        <Form.Item
          rules={[
            {
              required: true,
              message: "Vui lòng nhập tên mức độ",
            },
          ]}
          label="Tên mức độ ưu tiên"
          name="name"
        >
          <Input placeholder="Nhập tên mức độ ưu tiên" />
        </Form.Item>
        <Form.Item label="Màu sắc" name="color">
          <ColorPicker />
        </Form.Item>
        <Form.Item
          rules={[
            {
              required: true,
              message: "Vui lòng nhập thứ tự hiển thị",
            },
          ]}
          label="Thứ tự hiển thị"
          name="displayOrder"
        >
          <InputNumber placeholder="Nhập thứ tự hiển thị" className="w-full" />
        </Form.Item>
      </Form>
    </Modal>
  );
};
