/* eslint-disable @typescript-eslint/no-explicit-any */
import { InboxOutlined } from "@ant-design/icons";
import { Upload } from "antd";
import { UploadChangeParam, UploadFile } from "antd/es/upload/interface";

type Props<T = any> = {
  onChange?: (info: UploadChangeParam<UploadFile<T>>) => void;
};

/**
 * Component wrapped from Ant Design Upload.Dragger
 */
export const UploadDragger = ({ onChange }: Props) => {
  return (
    <Upload.Dragger onChange={onChange} beforeUpload={() => false}>
      <p className="ant-upload-drag-icon">
        <InboxOutlined rev={undefined} />
      </p>
      <p className="ant-upload-text">
        Click or drag file to this area to upload
      </p>
    </Upload.Dragger>
  );
};
