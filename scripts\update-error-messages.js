const fs = require('fs');

const axios = require('axios');

axios(
  'http://gumac-crm-api-dev.3forcom.net/docs/v1/error-codes/resources',
).then((res) => {
  const ErrorMessageProps = `interface ErrorMessageProps {\n\t[key: string]: {
      original: { title: string; detail: string };
      translation: { title: string; detail: string };
    };\n}`;
  let contents = `// @see http://gumac-crm-api-dev.3forcom.net/docs/v1/error-codes/resources\n${ErrorMessageProps}\n\nexport const ERROR_MESSAGES: ErrorMessageProps = {`;
  // eslint-disable-next-line no-restricted-syntax
  for (const [index, item] of res.data.entries()) {
    contents += `\n\t/* ==== ${item.name.toUpperCase()} START ==== */`;
    // eslint-disable-next-line no-restricted-syntax
    for (const errorMsg of item.items) {
      contents += `\n\t"${errorMsg.code}": { original: { title: "${
        errorMsg.title || ''
      }", detail: "${
        errorMsg.detail || ''
      }" }, translation: { title: '', detail: '' } },`;
    }
    contents += `\n\t/* ==== ${item.name.toUpperCase()} END ==== */${
      index !== res.data.length - 1 ? '\n' : ''
    }`;
  }
  contents += '\n};';
  fs.writeFileSync('src/constants/errorMessages.ts', contents, {
    encoding: 'utf-8',
  });
});
