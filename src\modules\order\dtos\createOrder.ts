import omit from "object.omit";

import { cleanEmptyString } from "helpers/object";
import { createMapper, force, fromSchema } from "libs/adapters/dto";
import { Array, Enum, mergeSchema, Mixed, Number, String } from "libs/domain";

import { OrderItemSchema } from "../entities";

export const createOrderDto = createMapper(
  fromSchema(
    mergeSchema(
      omit(OrderItemSchema, [
        "_id",
        "code",
        "orderId",
        "payment",
        // "personalInfo",
        "discountProducts",
        "createdAt",
        "voucherCodes",
        "paymentMethod",
        "requestSupport",
      ]),
      {
        // orderStatusId: Number(),

        items: Array(
          Mixed({
            skuId: Number(),
            listedPrice: Number(),
            discountAmount: Number(),
            discountPercent: Number(),
            quantity: Number(),
            productId: Number(),
            unitPrice: Number(),
            totalAmount: Number(),
            // itemType: Enum({
            //   values: ["SOLD", "RETURNED", "EXCHANGED"],
            // }),
          })
        ),
      }
    )
  ),
  force((data) => ({
    ...cleanEmptyString(data),
    assignedEmployeeId: data.assignedEmployeeId || null,
  }))
);

export type CreateOrderDtoType = ReturnType<typeof createOrderDto>;
