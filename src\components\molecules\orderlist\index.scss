.m-orderlist {
	$root: &;

	&_item {
		display: flex;
		align-items: center;

		& + & {
			margin-top: rem(15);
		}
	}

	&_title {
		.a-icon {
			flex: 0 0 auto;
			width: rem(12);
			height: rem(12);
		}
	}

	&_wrapcode {
		display: flex;
	}

	&_code {
		margin-left: rem(10);
		font-size: rem(11);
		line-height: rem(14);
		color: $COLOR-DENIM;
		@include u-fw-regular;
	}

	&_phone {
		display: flex;
		align-items: center;
		margin-top: rem(5);
		font-size: rem(12);
		line-height: rem(16);

		a {
			margin-left: rem(10);
			color: $COLOR-QUARTZ;
			@include u-fw-regular;
		}
	}

	&_content {
		display: flex;
		flex: 0 0 auto;
		flex-direction: column;
		align-items: flex-end;

		span {
			font-size: rem(12);
			line-height: rem(16);
			color: $COLOR-QUARTZ;
			@include u-fw-regular;
		}
	}

	&_date {
		margin-top: rem(5);
	}

	&_status {
		display: flex;
		flex: 0 0 auto;
		margin: 0 rem(10);
		background-color: $COLOR-BLACK;
		border-radius: rem(13);

		span {
			padding: rem(4) rem(10);
			font-family: $FONTFAMILY-ROBOTO;
			font-size: rem(10);
			line-height: rem(12);
			color: $COLOR-WHITE;
		}
	}
}
