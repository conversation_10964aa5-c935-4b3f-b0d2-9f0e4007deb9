import pick from "object.pick";

import { createMapper, fromSchema } from "libs/adapters/dto";
import { Array, mergeSchema, Number } from "libs/domain";

import { ShopSchema } from "../entities";

export const createShopDto = createMapper(
  fromSchema(
    mergeSchema(
      pick(ShopSchema, [
        "name",
        "hotline",
        "type",
        "address",
        "cityId",
        "districtId",
        "wardId",
        "displayOrder",
        "isFranchisedShop",
      ]),
      {
        employeeIds: Array(Number()),
        storageIds: Array(Number()),
        bankAccountIds: Array(Number()),
      }
    )
  )
);

export type CreateShopDtoType = ReturnType<typeof createShopDto>;
