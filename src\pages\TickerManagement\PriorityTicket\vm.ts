import { useCallback, useEffect, useMemo, useState } from "react";

import produce from "immer";
import _ from "lodash";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import { priorityTicketDto } from "modules/ticket/dtos/masterDataPriorityTicket.dto";
import { getListMasterDataTicket } from "modules/ticket/hook/useGetListMasterDataTicket";

type ModalType = "priorityTicket";

interface State {
  pageSize: number;
  modalState: {
    open: boolean;
    modalType?: ModalType;
  };
}

export const PriorityTicketPageVm = () => {
  const [state, setState] = useState<State>({
    pageSize: 10,
    modalState: {
      open: false,
      modalType: undefined,
    },
  });

  const [searchText, setSearchText] = useState<string>("");

  const handleChangeInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSetSearchText(e.target.value);
  };

  const debouncedSetSearchText = useMemo(
    () => _.debounce((value: string) => setSearchText(value), 500),
    []
  );

  const [getDataTableMasterDataTicketExec, getDataTableMasterDataTicketState] =
    useAsync(
      useCallback(
        (options: { pageNum: number; pageSize: number; searchText?: string }) =>
          getListMasterDataTicket({ ...options }).then((res) => ({
            data: mapFrom(res.data.data, priorityTicketDto),
            pagination: paginationDTO(res.data.links),
            total: res.data.meta.totalRecords,
          })),
        []
      )
    );

  const { gotoPage, ...priorityTicketState } = usePagination({
    pageSize: state.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getDataTableMasterDataTicketExec({ pageSize, pageNum: page, searchText }),
  });

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      (draft) => ({ ...draft, pageSize })
      // produce((draft) => {
      //   draft.pageSize = pageSize;
      // })
    );
  }, []);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) => {
      return state.modalState.open && state.modalState.modalType === type;
    },
    [state.modalState]
  );

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.modalType = type;
      })
    );
  }, []);

  const handleCloseModal = useCallback(
    () =>
      setState(
        produce((draft) => {
          draft.modalState.open = false;
          draft.modalState.modalType = undefined;
        })
      ),
    []
  );

  const { sortedData: priorityTicketData, toggleSortState: toggleSortOrderBy } =
    useSortable({
      data: getDataTableMasterDataTicketState.data?.data,
      sortBy: {
        name: (data) => data.name,
        updatedAt: (data) => data.updatedAt,
      },
    });

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pageSize, searchText]);

  return {
    priorityTicketState,
    gotoPage,
    priorityTicketData: priorityTicketData || [],
    loading: getDataTableMasterDataTicketState.loading,
    totalRecords: getDataTableMasterDataTicketState.data?.total || 0,
    refetch: getDataTableMasterDataTicketExec,
    handleChangePageSize,
    toggleSortOrderBy,
    pageSize: state.pageSize,
    handleOpenModalByType,
    handleChangeInput,
    setSearchText,
    handleCloseModal,
    modalTypeIsOpen,
    pageState: state,
    searchText,
  };
};
