import React from "react";

import { Text } from "components/atoms/text";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";

interface Props {
  appearanceOption?: React.ReactNode;
  paginateOption?: React.ReactNode;
}

const IndexSection: React.FC<Props> = ({
  appearanceOption,
  paginateOption,
}) => (
  <Section>
    <Row>
      {appearanceOption && (
        <Col
          md="12"
          lg="6"
          className="d-flex u-mb-16 u-mb-lg-0 align-items-center justify-content-start"
        >
          <div className="u-mr-10">
            <Text>Hi<PERSON>n thị</Text>
          </div>
          <div style={{ width: "100%", maxWidth: 200 }}>{appearanceOption}</div>
        </Col>
      )}
      <Col
        md="12"
        lg={appearanceOption ? "6" : "12"}
        className="d-flex align-items-center justify-content-end"
      >
        {paginateOption}
      </Col>
    </Row>
  </Section>
);

export default IndexSection;
