import { useCallback } from "react";

import { showNotification } from "components/atoms/base/Notification";
import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { createOrderDto, CreateOrderDtoType } from "modules/order";
import { createOrder as createOrderService } from "services/crm/order";

import RootPageRouter from "..";

export const CreateOrderPageVm = () => {
  const [createOrderServiceExec, createOrderState] = useAsync(
    createOrderService,
    {
      onSuccess: useCallback(() => {
        // toastSingleMode({ type: "success", message: "Tạo mới thành công" });
        showNotification({
          type: "success",
          message: "Tạo mới thành công",
        });
        RootPageRouter.children.listOrder.goto();
      }, []),
      onFailed: useCallback((error) => {
        const errorApi = error?.response?.data?.errors?.[0];
        if (errorApi) {
          showNotification({
            type: "error",
            message: errorApi.detail || errorApi.title,
          });
        }
        // const errMessage = getErrorMessageViaErrCode(
        //   error?.response?.data?.errors?.[0]?.code
        // );
        // toastSingleMode({
        //   type: "error",
        //   message: errMessage.translation.title,
        //   descripition: errMessage.translation.detail,
        // });
      }, []),
    }
  );

  const createOrder = useCallback(
    (rawPayload: Partial<CreateOrderDtoType>) => {
      const orderPayload = createOrderDto({
        ...rawPayload,
        // TODO: update late
        // paymentMethod: 0,
      });
      createOrderServiceExec(orderPayload);
    },
    [createOrderServiceExec]
  );

  return {
    createOrder,
    createOrderState,
  };
};
