import React, { useCallback, useEffect } from "react";
import { DeleteOutlined, FilterOutlined } from "@ant-design/icons";
import {
  Card,
  Col,
  Collapse,
  Form,
  Input,
  Modal,
  Row,
  Space,
  Tooltip,
} from "antd";
import Table, { ColumnType } from "antd/es/table";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showNotification } from "components/atoms/base/Notification";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import RootPageRouter from "..";
import {
  UseDeleteGroupCustomer,
  UseGetGroupCustomers,
} from "../hook/useGetGroupCustomer";

interface groupCustomer {
  groupCustomerId: number;
  groupCustomerCode: string;
  groupCustomerName: string;
  createdAt;
  updatedAt: string | Date;
}

export default function GroupCustomer() {
  const [form] = Form.useForm();
  const { getGroupCustomersExec, getGroupCustomersState } =
    UseGetGroupCustomers();
  const { deleteGroupCustomerExec } = UseDeleteGroupCustomer();
  const gotoGroupCustomerDetail = useCallback(
    ({
      groupCustomerId,
      action,
    }: {
      groupCustomerId: number | string;
      action?: string;
    }) => {
      if (action) {
        RootPageRouter.gotoChild("groupCustomerDetail", {
          params: { groupCustomerId: groupCustomerId.toString() },
          queryString: action ? `?action=${action}` : undefined,
        });
      } else {
        RootPageRouter.gotoChild("groupCustomerDetail", {
          params: { groupCustomerId: groupCustomerId.toString() },
        });
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  // eslint-disable-next-line react-hooks/exhaustive-deps

  const handeleDeleteCustomerGroup = (groupCustomerId: number) => {
    // Implement delete logic here
    Modal.confirm({
      title: "Xác nhận xóa",
      content: "Bạn có chắc chắn muốn xóa nhóm khách hàng này?",
      okText: "Xóa",
      cancelText: "Hủy",
      onOk: () => {
        deleteGroupCustomerExec(groupCustomerId)
          .then((res) => {
            if (res.status === 200) {
              showNotification({
                type: "success",
                message: "Xóa nhóm khách hàng thành công",
              });
              getGroupCustomersExec({});
            }
          })
          .catch((error) => {
            console.error("Error deleting group customer:", error);
            showNotification({
              type: "error",
              message:
                error?.response?.data?.errors?.[0]?.message ||
                "Xóa nhóm khách hàng thất bại",
            });
          });
      },
    });
  };

  const columnTable: ColumnType<groupCustomer>[] = [
    {
      title: "Mã nhóm khách hàng",
      dataIndex: "groupCustomerCode",
      key: "groupCustomerCode",
      width: 180,
      align: "center",
    },
    {
      title: "Tên nhóm khách hàng",
      dataIndex: "groupCustomerName",
      key: "groupCustomerName",
      width: 200,
      align: "center",
    },
    {
      title: "Thao tác",
      key: "action",
      width: 150,
      align: "center",
      render: (_, record) => (
        <Space size={8}>
          <Tooltip title="Chi tiết">
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              onClick={() =>
                gotoGroupCustomerDetail({
                  groupCustomerId: record.groupCustomerId,
                  action: "view",
                })
              }
            >
              Chi tiết
            </BaseButton>
          </Tooltip>
          <Tooltip title="Chỉnh sửa">
            <BaseButton
              type="default"
              onClick={() =>
                gotoGroupCustomerDetail({
                  groupCustomerId: record.groupCustomerId,
                  action: "edit",
                })
              }
            >
              Chỉnh sửa
            </BaseButton>
          </Tooltip>
          <Tooltip title="Xóa">
            <BaseButton
              type="primary"
              bgColor={COLOR.RED[500]}
              hoverColor={COLOR.RED[700]}
              icon={<DeleteOutlined />}
              style={{ padding: "4px 8px", fontSize: 12 }}
              onClick={() => handeleDeleteCustomerGroup(record.groupCustomerId)}
            >
              Xóa
            </BaseButton>
          </Tooltip>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    getGroupCustomersExec({});
  }, []);

  return (
    <General>
      <title key="title">Nhóm khách hàng</title>
      <Section>
        <div className="p-4">
          <Card
            styles={{
              body: {
                padding: 0,
              },
            }}
            title="DANH SÁCH NHÓM KHÁCH HÀNG"
            style={{ marginLeft: "1.5%", marginRight: "1.5%" }}
          >
            <div className="flex flex-col gap-3">
              <div className="p-6">
                <div className="mb-6">
                  <Row gutter={[16, 16]} className="flex justify-end">
                    <Col
                      xs={{ span: 24, order: 1 }}
                      lg={{ span: 6, order: 2, offset: 6 }}
                    >
                      <div className="flex gap-3">
                        <BaseButton
                          type="default"
                          icon={<FilterOutlined rev={undefined} />}
                          // onClick={() => setIsFilterOpen(!isFilterOpen)}
                          className="flex-1"
                        >
                          Bộ lọc
                        </BaseButton>
                        <BaseButton
                          type="primary"
                          bgColor={COLOR.BLUE[500]}
                          hoverColor={COLOR.BLUE[700]}
                          onClick={() =>
                            gotoGroupCustomerDetail({
                              groupCustomerId: "isAddNew",
                            })
                          }
                          className="flex-1"
                        >
                          Tạo mới
                        </BaseButton>
                      </div>
                    </Col>
                  </Row>
                </div>
                {/* <Collapse
                  // activeKey={isFilterOpen ? ["filter"] : []}
                  // onChange={() => setIsFilterOpen(!isFilterOpen)}
                  ghost
                >
                  <Collapse.Panel header="Bộ lọc tìm kiếm" key="filter">
                    <Form
                      form={form}
                      layout="vertical"
                      // onFinish={onFilterFinish}
                      // initialValues={filterState}
                    >
                      <Row gutter={16}>
                        <Col xs={24} sm={12} md={6}>
                          <Form.Item
                            label="Tên nhóm khách hàng"
                            name="groupCustomerName"
                          >
                            <Input placeholder="Nhập tên nhóm khách hàng" />
                          </Form.Item>
                        </Col>
                      </Row>
                      <div className="flex justify-center gap-3 mt-4">
                        <BaseButton
                          type="default"
                          // onClick={() => setIsFilterOpen(false)}
                        >
                          Đóng
                        </BaseButton>
                        <BaseButton
                          htmlType="submit"
                          type="primary"
                          bgColor={COLOR.BLUE[500]}
                          hoverColor={COLOR.BLUE[700]}
                          // loading={Loading}
                        >
                          Tìm kiếm
                        </BaseButton>
                        <BaseButton
                          type="default"
                          // onClick={handleResetFilter}
                          // loading={loading}
                        >
                          Đặt lại
                        </BaseButton>
                      </div>
                    </Form>
                  </Collapse.Panel>
                </Collapse> */}
              </div>
              <Table
                // loading={loading}
                scroll={{ x: 1500 }}
                size="small"
                bordered
                columns={columnTable}
                pagination={false}
                dataSource={getGroupCustomersState?.data}
                rowKey="groupCustomerId"
              />
              <Row justify="space-between" align="middle" className="p-6">
                {/* <Col>
                  <Select
                    placeholder="Chọn số lượng hiển thị"
                    value={`${pageSize}`}
                    onChange={handleOnChangePageSizePulldown}
                    style={{ width: 150 }}
                  >
                    {[5, 10, 15, 25, 30].map((size) => (
                      <Option key={size} value={`${size}`}>
                        {size}/pages
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col>
                  {shopPaginationState?.totalPage > 0 && (
                    <Pagination
                      total={shopPaginationState?.totalPage * 10}
                      showSizeChanger={false}
                      defaultCurrent={1}
                      onChange={gotoPage}
                    />
                  )}
                </Col> */}
              </Row>
            </div>
          </Card>
        </div>
      </Section>
    </General>
  );
}
