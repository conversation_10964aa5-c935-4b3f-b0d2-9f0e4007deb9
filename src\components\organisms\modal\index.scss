.o-modal {
	$root: &;

	&_overlay {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: z("modal", "base");
		content: "";
		background: rgba($COLOR-EERIE-BLACK, 0.5);
	}

	&_close {
		position: absolute;
		top: 15px;
		right: 15px;
		z-index: z("modal", "close");
		display: inline-flex;
		cursor: pointer;

		.a-icon {
			width: 15px;
			height: 15px;
		}
	}

	&_content {
		position: absolute;
		top: 50%;
		right: 40px;
		left: 40px;
		width: auto;
		height: max-content;
		max-height: calc(100% - 80px);
		padding: rem(30) rem(40);
		margin: 0 auto;
		overflow: hidden auto;
		background-color: $COLOR-WHITE;
		border-radius: 2px;
		outline: none;
		transform: translateY(-50%);
		appearance: none;

		@include sp {
			right: 20px;
			left: 20px;
			left: 15px;
			max-height: calc(100% - 40px);
		}

		&-centered {
			top: 50%;
			transform: translateY(-50%);
		}
	}
}
