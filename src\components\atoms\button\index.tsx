import React from "react";

import { Link } from "components/utils/link";
import { mapModifiers } from "helpers/component";

import { Icon, IconName } from "../icon";

type ButtonSizes = "medium";
type ButtonType = "textbutton" | "outline";
type Modifiers = "primary" | "secondary";

export interface Props {
  modifiers?: Modifiers | Modifiers[];
  buttonSize?: ButtonSizes;
  iconName?: IconName;
  type?: "submit" | "button" | "reset";
  href?: string;
  target?: string;
  buttonType?: ButtonType;
  disabled?: boolean;
  fullwidth?: boolean;
  isLoading?: boolean;
  onClick?: (e?: React.MouseEvent<HTMLElement>) => void;
}

export const Button: React.FC<React.PropsWithChildren<Props>> = ({
  type,
  href,
  target,
  buttonType,
  modifiers,
  disabled,
  fullwidth,
  isLoading,
  children,
  onClick,
  buttonSize,
  iconName,
}) =>
  href ? (
    <Link
      className={mapModifiers(
        "a-button",
        buttonType,
        modifiers,
        disabled && "disabled",
        fullwidth && "fullwidth",
        buttonSize
      )}
      to={href}
      target={target}
      onClick={onClick}
    >
      {children}
    </Link>
  ) : (
    <button
      // eslint-disable-next-line react/button-has-type
      type={type}
      className={mapModifiers(
        "a-button",
        buttonType,
        modifiers,
        disabled && "disabled",
        fullwidth && "fullwidth",
        buttonSize
      )}
      onClick={onClick}
    >
      <div className="a-button_title">
        {(iconName || isLoading) && (
          <Icon
            modifiers="tiny"
            iconName={(isLoading ? "loading" : iconName) as IconName}
          />
        )}
        {children}
      </div>
    </button>
  );

Button.defaultProps = {
  type: "button",
  modifiers: "primary",
};
