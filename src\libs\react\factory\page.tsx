/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  ComponentType,
  LazyExoticComponent,
  PropsWithChildren,
} from "react";

import {
  generatePath,
  Route,
  RouteProps as RouteComponentProps,
} from "react-router";

import * as navigationHelper from "helpers/navigation";

// const AppPages: Record<string, AppPage> = {};

type PageProps = RouteComponentProps;

type ParamsPageType = Record<string, string | number | boolean | undefined>;

export type WrapperProps = PropsWithChildren<{ path: string }>;

export type PageParamsType<Page extends AppPage<any, any>> =
  Page extends AppPage<infer T> ? T : unknown;

export interface AppPageConfig<ChildrenPage> {
  name: string;
  page?: () =>
    | LazyExoticComponent<ComponentType<PageProps>>
    | React.ComponentType<PageProps>;
  path: string;
  fullPath?: string;
  childrenPages?: ChildrenPage;
}

interface NavigationEventOptions {
  method?: "push" | "replace" | "newPage";
  state?: unknown;
  queryString?: string;
}

export class AppPage<
  Params extends ParamsPageType = {},
  ChildrenPage extends { [key: string]: AppPage<any, any> } = {}
> {
  public config: AppPageConfig<ChildrenPage>;

  public childrenPages: ChildrenPage;

  constructor(config: AppPageConfig<ChildrenPage>) {
    this.config = config;
    this.childrenPages = Object.entries(config.childrenPages || {}).reduce(
      (childrenPages, [key, child]) => {
        childrenPages[key as keyof ChildrenPage] = child.clone({
          fullPath: `${config.path}${child.config.path}`,
        }) as any;
        return childrenPages;
      },
      {} as ChildrenPage
    );
  }

  goto(
    param?: Params,
    { method = "push", state, queryString }: NavigationEventOptions = {}
  ) {
    const { path, fullPath } = this.config;
    console.log(path, "path - fullPath", fullPath);
    const pagePath = `${generatePath(fullPath || path, param)}${
      queryString || ""
    }`;

    if (!pagePath) return;

    if (method === "push") navigationHelper.push(pagePath, state);
    if (method === "replace") navigationHelper.replace(pagePath, state);
    if (method === "newPage") navigationHelper.newPage(pagePath);
  }

  gotoChild<Key extends keyof ChildrenPage>(
    childrenName: Key,
    options?: {
      params?: Parameters<ChildrenPage[Key]["goto"]>[0];
      queryString?: string;
    } & NavigationEventOptions
  ) {
    const children = this.childrenPages[childrenName];

    if (children) {
      children.goto(options?.params, {
        method: options?.method,
        queryString: options?.queryString,
        state: options?.state,
      });
    }
  }

  clone(overwriteConfig: Partial<AppPageConfig<ChildrenPage>>) {
    return new AppPage({
      ...this.config,
      ...(overwriteConfig || {}),
    });
  }

  get path() {
    return this.config.path;
  }

  get children() {
    return this.childrenPages;
  }

  generatePath(params?: Params, options?: { queryString: string }) {
    return `${generatePath(this.config.fullPath || this.config.path, params)}${
      options?.queryString || ""
    }`;
  }

  render() {
    const { page, path } = this.config;
    let component:
      | React.LazyExoticComponent<
          React.ComponentType<React.PropsWithChildren<PageProps>>
        >
      | React.ComponentType<React.PropsWithChildren<PageProps>>;

    if (page) {
      component = page();
    }

    const routeComponent = (
      <Route
        key={path}
        path={path.replace(/^\//, "")} // Ensure path does not start with a slash
        element={
          component ? React.createElement(component, { key: path }) : undefined
        }
      >
        {renderPages(...Object.values(this.childrenPages))}
      </Route>
    );

    return routeComponent;
  }
}

export const createAppPage = <
  Params extends ParamsPageType,
  ChildrenPage extends { [key: string]: AppPage<any, any> } = {}
>(
  config: AppPageConfig<ChildrenPage>
): AppPage<Params, ChildrenPage> => {
  return new AppPage(config) as AppPage<Params, ChildrenPage>;
};

export const renderPages = (...pages: AppPage[]) =>
  pages.map((page) => page.render());
