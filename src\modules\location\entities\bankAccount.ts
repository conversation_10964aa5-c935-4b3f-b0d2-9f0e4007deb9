import { Model, String, Number, ModelValue } from "libs/domain";

export const BankAccountSchema = {
  bankAccountId: Number(),
  bankName: String(),
  branchName: String(),
  shopId: Number(),
  owner: String(),
  accountNumber: String(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  createdAt: (raw: any) => new Date(raw),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => new Date(raw),
};

export const BankAccountModel = new Model(BankAccountSchema);
export type BankAccountEntityType = ModelValue<typeof BankAccountModel>;
