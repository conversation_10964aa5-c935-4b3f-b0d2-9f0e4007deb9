import React, { ReactNode, useCallback, useEffect } from "react";
import { SearchOutlined } from "@ant-design/icons";
import { Divider, Empty, Form, Input, Typography } from "antd";
import { Col, Row } from "react-bootstrap";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { Spinner } from "components/atoms/spinner";
import { InfiniteScrollable } from "components/utils/infinitescrollable";
import { COLOR } from "constants/color";
import { formatCurrencyWithSuffix } from "helpers/currency.helper";
import { cleanEmptyString } from "helpers/object";
import { useInfinityParams } from "hooks/useInfinityParams";
import { mapFrom } from "libs/adapters/dto";
import { SkuDetailDtoType, SubmitItem } from "modules/product";
import {
  itemStockLocationDto,
  ItemStockLocationDtoType,
} from "modules/product/dtos/item-stock-location";
import { UseGetStorage } from "pages/System/Inventory/hook/useGetStorage";
import { UseGetListShopV2 } from "pages/System/ShopDetail/hooks/useGetListShop";
import { getProductByShop } from "services/crm/product";

interface ListProductItemProps {
  product: ItemStockLocationDtoType;
  // product: ProductDetailDtoType;
  onRenderSkuItem: (sku: SkuDetailDtoType) => ReactNode;
}

interface ListProductSkuItemProps {
  sku: SkuDetailDtoType;
  onHandleQty?: number;
  disabledSelectedButton: boolean;

  onSelectedSku: (sku: SkuDetailDtoType) => void;
}

export interface ProductSkuPickerModalProps {
  shopSentValue: number;
  disabledSelectedButton: boolean;
  onSubmit?: (item: SubmitItem[]) => void;
}

const ListProductSkuItem: React.FC<ListProductSkuItemProps> = ({
  onSelectedSku,
  onHandleQty,
  sku,
  disabledSelectedButton,
}) => {
  const handleOnClickSelectButton = useCallback(() => {
    onSelectedSku(sku);
  }, [onSelectedSku]);
  return (
    <div key={sku.skuId} className="u-mt-12">
      <Row className="">
        <Col className="u-text-center">{sku.code}</Col>
        <Col className="u-text-center">{sku.name}</Col>
        <Col className="u-text-center">
          {formatCurrencyWithSuffix(sku.price)}
        </Col>
        <Col className="u-text-center">
          {formatCurrencyWithSuffix(sku.salePrice)}
        </Col>
        <Col className="u-text-center">{onHandleQty}</Col>
        <Col className="u-text-center">
          <BaseButton
            disabled={
              !onHandleQty || onHandleQty <= 0 || disabledSelectedButton
            }
            type="primary"
            bgColor={COLOR.BLUE[500]}
            hoverColor={COLOR.BLUE[600]}
            className="w-fit"
            onClick={handleOnClickSelectButton}
          >
            Chọn
          </BaseButton>
        </Col>
      </Row>
      <Divider className="my-2" />
    </div>
  );
};

const ListItemStockLocation: React.FC<ListProductItemProps> = ({
  product,
  onRenderSkuItem,
}) => {
  // return <div>{product.sku?.map(onRenderSkuItem)}</div>;
  return <div>{onRenderSkuItem(product.sku)}</div>;
};

export default function ListProduct({
  shopSentValue,
  disabledSelectedButton,
  onSubmit: onSubmitProps,
}: ProductSkuPickerModalProps) {
  const [form] = Form.useForm();
  // const storeCode = Form.useWatch("storeCode", form);

  const {
    loadMoreWithParams,
    loadMore,
    state: productInfinteState,
  } = useInfinityParams<ItemStockLocationDtoType[]>(
    // } = useInfinityParams<ProductDetailDtoType[]>(
    // getListProduct(params).then((res) => ({
    (params: Parameters<typeof getProductByShop>[0]) =>
      getProductByShop(params).then((res) => ({
        ...res,
        data: {
          ...res?.data,
          data: mapFrom(res?.data?.data, itemStockLocationDto),
          // data: mapFrom(res.data.data, productDetailDto),
        },
      })),
    {
      pageSize: 30,
    }
  );
  const { listShopExe, listShopState } = UseGetListShopV2();
  const { fetchStorageExe, stateStorages } = UseGetStorage();

  const handleLoadMore = useCallback(async () => {
    if (productInfinteState.loading) return; // Đang load thì bỏ qua
    await loadMore(); // loadMore trả về Promise
  }, [productInfinteState.loading, loadMore]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onSubmitSearchFilterForm = (values: any) => {
    loadMoreWithParams(
      cleanEmptyString({
        searchText: values.filterProductName,
        storeCode: shopSentValue,
      })
    );
  };

  useEffect(() => {
    if (shopSentValue) {
      loadMoreWithParams({
        storeCode: shopSentValue,
        pageNum: 1,
        pageSize: 30,
      });
    }
  }, [shopSentValue]);

  useEffect(() => {
    fetchStorageExe();
    listShopExe();
  }, []);

  // useEffect(() => {
  //   if (_.size(listShopState?.data) > 0) {
  //     const isDefaultShop = listShopState?.data.find(
  //       (shop) => shop.isDefaultShop
  //     );
  //     const { storage } = isDefaultShop || {};
  //     form.setFieldsValue({
  //       storeCode: storage?.code,
  //     });
  //   }
  // }, [listShopState]);

  return (
    <div className="border-b border-[#f0f0f0]">
      <Form
        className="px-3 mb-2"
        form={form}
        onFinish={onSubmitSearchFilterForm}
        layout="vertical"
      >
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 items-end">
          <Form.Item
            name="filterProductName"
            className="mb-0"
            label="Tên sản phẩm"
          >
            <Input
              onPressEnter={(e) => {
                e.preventDefault();
                form.submit();
              }}
              placeholder="Nhập tên sản phẩm"
            />
          </Form.Item>

          {/* <Form.Item
            name="storeCode"
            className="mb-0"
            label="Kho"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn kho",
              },
            ]}
          >
            <BaseSelect
              placeholder="Chọn kho"
              options={stateStorages?.data}
              fieldNames={{
                label: "name",
                value: "code",
              }}
            />
          </Form.Item> */}
          <div className="w-max justify-self-start">
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[600]}
              onClick={() => {
                form.submit();
              }}
              icon={<SearchOutlined rev={undefined} />}
            >
              Tìm kiếm
            </BaseButton>
          </div>
        </div>
      </Form>
      <Row className="mt-2 py-2 border-y border-[#f0f0f0] bg-[#fafafa]">
        <Col className="relative before:absolute before:top-1/2 before:-translate-y-1/2 before:h-full before:w-[1px] before:bg-[#f0f0f0] before:transition-colors before:content-[''] before:end-0">
          <Typography.Title className="text-center mb-0 !text-[14px]" level={5}>
            Mã SKU
          </Typography.Title>
        </Col>
        <Col className="relative before:absolute before:top-1/2 before:-translate-y-1/2 before:h-full before:w-[1px] before:bg-[#f0f0f0] before:transition-colors before:content-[''] before:end-0">
          <Typography.Title className="text-center mb-0 !text-[14px]" level={5}>
            Tên
          </Typography.Title>
        </Col>
        <Col className="relative before:absolute before:top-1/2 before:-translate-y-1/2 before:h-full before:w-[1px] before:bg-[#f0f0f0] before:transition-colors before:content-[''] before:end-0">
          <Typography.Title className="text-center mb-0 !text-[14px]" level={5}>
            Giá NY
          </Typography.Title>
        </Col>
        <Col className="relative before:absolute before:top-1/2 before:-translate-y-1/2 before:h-full before:w-[1px] before:bg-[#f0f0f0] before:transition-colors before:content-[''] before:end-0">
          <Typography.Title className="text-center mb-0 !text-[14px]" level={5}>
            Giá bán
          </Typography.Title>
        </Col>
        <Col className="relative before:absolute before:top-1/2 before:-translate-y-1/2 before:h-full before:w-[1px] before:bg-[#f0f0f0] before:transition-colors before:content-[''] before:end-0">
          <Typography.Title className="text-center mb-0 !text-[14px]" level={5}>
            Số lượng tồn
          </Typography.Title>
        </Col>

        <Col>
          <Typography.Title className="text-center mb-0 !text-[14px]" level={5}>
            Thao tác
          </Typography.Title>
        </Col>
      </Row>
      {productInfinteState?.data?.length > 0 ? (
        <InfiniteScrollable
          className="u-overflow-x-hidden u-overflow-y-auto u-relative"
          height={300}
          width="100%"
          offset={{ bottom: 100 }}
          onTrigger={handleLoadMore}
        >
          {productInfinteState &&
            Array.isArray(productInfinteState.data) &&
            productInfinteState.data?.map((product) => (
              <ListItemStockLocation
                key={product.productId}
                product={product}
                onRenderSkuItem={(sku) => (
                  <ListProductSkuItem
                    key={sku.skuId}
                    sku={sku}
                    disabledSelectedButton={disabledSelectedButton}
                    onHandleQty={product.onhandQty}
                    onSelectedSku={(item) => {
                      const itemPicked = {
                        sku,
                        unitPrice: sku.salePrice || sku.price,
                        totalAmount: sku.salePrice || sku.price * 1,
                        price: sku.price, // Optional, used for listedPrice in some contexts
                        quantity: 1,
                        // itemType: "SOLD",s
                        productId: product.productId,
                      };
                      onSubmitProps?.([itemPicked]);
                    }}
                  />
                )}
              />
            ))}
          {productInfinteState.loading && <Spinner fullContainer />}
        </InfiniteScrollable>
      ) : (
        <Empty
          className="m-0 h-[300px] flex flex-col items-center justify-center"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}
    </div>
  );
}
