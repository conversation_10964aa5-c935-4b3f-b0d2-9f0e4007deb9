import React, { useEffect, useLayoutEffect, useRef, useState } from "react";

import { Spinner } from "components/atoms/spinner";

const maxWidth = 300;
const minWidth = 150;
const maxHeight = 400;
const minHeight = 200;

export const ImageMessage: React.FC<{
  src?: string;
  alt?: string;
  width?: number;
  height?: number;
}> = ({ src, width, height, alt }) => {
  const wrapperElementRef = useRef<HTMLDivElement>(null);
  const [state, setState] = useState({
    src,
    status: "initial",
  });

  useEffect(() => {
    let hasNewSrc = false;
    if (src) {
      const image = new Image();
      image.onload = () => {
        if (hasNewSrc) return;
        setState((prevState) => ({
          ...prevState,
          src: image.src,
          status: "ready",
        }));
      };

      image.src = src;
    }

    return () => {
      hasNewSrc = true;
    };
  }, [src]);

  useLayoutEffect(() => {
    if (!width || !height || !wrapperElementRef.current) return;
    if (state.status !== "loading") {
      const dimensionalType = width > height ? "horizontal" : "vertical";
      let wrapperHeight: number = height;
      let wrapperWidth: number = width;
      if (dimensionalType === "horizontal") {
        if (wrapperWidth > maxWidth) {
          wrapperWidth = maxWidth;
        }

        if (wrapperWidth < minWidth) {
          wrapperWidth = minWidth;
        }
        wrapperHeight = height * (wrapperWidth / width);
      }

      if (dimensionalType === "vertical") {
        if (wrapperHeight > maxHeight) {
          wrapperHeight = maxHeight;
        }

        if (wrapperHeight < minHeight) {
          wrapperHeight = minHeight;
        }
        wrapperWidth = width * (wrapperHeight / height);
      }
      Object.assign(wrapperElementRef.current.style, {
        width: `${wrapperWidth}px`,
        height: `${wrapperHeight}px`,
      });
    }
  }, [state.status, state.src, width, height]);

  return (
    <div ref={wrapperElementRef}>
      {state.status === "ready" ? (
        <img src={src} alt={alt} />
      ) : (
        <Spinner hasContainer />
      )}
    </div>
  );
};
