import { useCallback, useMemo } from "react";

import { Button, Select, SelectProps, Tooltip, Typography } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { removeAccents } from "helpers/remove-accents";

export type BaseSelectProps = {
  showSelectAll?: boolean;
} & SelectProps;

export const BaseSelect = (props: BaseSelectProps) => {
  const {
    showSelectAll,
    mode,
    onChange,
    options = [],
    value,
    fieldNames,
    ...rest
  } = props;
  const multipleOptions = useMemo(() => {
    let opt: BaseSelectProps = {};
    if (mode) {
      opt = {
        maxTagCount: "responsive",
        maxTagPlaceholder: (v) => {
          const displayed = v.slice(0, 10);
          return (
            <Tooltip
              title={
                <>
                  {displayed.map(({ label }, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <span key={`organization-${index}`}>
                      {label}
                      <br />
                    </span>
                  ))}
                  {v.length > 10 && <span>...</span>}
                </>
              }
            >
              <Typography>
                + {v.length - 10 > 0 ? `10...` : v.length}
              </Typography>
            </Tooltip>
          );
        },
      };
    }
    return opt;
  }, [mode]);

  const handleSelectAll = useCallback(() => {
    onChange?.(
      options.map((option) => {
        return option[fieldNames?.value || "value"];
      }),
      options
    );
  }, [fieldNames, onChange, options]);

  const handleUnselectAll = useCallback(() => {
    onChange?.([], options);
  }, [onChange, options]);

  const handleFilterOption: BaseSelectProps["filterOption"] = useCallback(
    (inputValue: string, opts: DefaultOptionType | undefined) => {
      let v = opts?.[fieldNames?.label || "label"];
      v = v ? String(v) : "";
      return removeAccents(v)
        .toLowerCase()
        .includes(removeAccents(inputValue).toLowerCase());
    },
    [fieldNames]
  );

  return (
    <Select
      allowClear
      showSearch
      mode={mode}
      onChange={onChange}
      value={value}
      filterOption={handleFilterOption}
      fieldNames={fieldNames}
      options={options}
      popupRender={(menu) => (
        <>
          {showSelectAll && mode === "multiple" && (
            <div style={{ padding: 8 }}>
              {!value?.length ? (
                <Button type="link" onClick={handleSelectAll}>
                  Chọn tất cả
                </Button>
              ) : (
                <Button type="link" onClick={handleUnselectAll}>
                  Bỏ chọn tất cả
                </Button>
              )}
            </div>
          )}
          {menu}
        </>
      )}
      {...multipleOptions}
      {...rest}
    />
  );
};
