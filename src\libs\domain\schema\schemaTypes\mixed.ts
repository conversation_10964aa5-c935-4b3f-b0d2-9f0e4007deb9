/* eslint-disable @typescript-eslint/no-explicit-any */
import { Schema, schemaParser } from "../schema";
import { IObject, SchemaReturnValue, SchemaType } from "../types";

export interface MixedTypeOptions {
  safeMode?: boolean;
  optional?: boolean;
  defaultValue?: number;
}

/**
 * A function will return a `SchemaTypeParser<R>`
 * @function Mixed
 * @param `first` - Object
 * @param `second` - Options
 * @returnsType `Function`
 * @return A `Function` returns a `schemaParser` object
 */
export function Mixed<O extends IObject>(
  mixedSchema: SchemaType<O>,
  options?: MixedTypeOptions
) {
  const schema = new Schema(mixedSchema);

  return (
    value: any,
    extraConfig?: any
  ):
    | {
        [key in keyof SchemaType<O>]: SchemaReturnValue<SchemaType<O>[key]>;
      }
    | undefined => {
    const mergeConfig = {
      safeMode: true,
      ...extraConfig,
      ...options,
    };

    // eslint-disable-next-line no-underscore-dangle
    let _value = value;
    if (!_value) {
      if (mergeConfig?.safeMode && !mergeConfig?.optional) {
        if ("defaultValue" in mergeConfig) return mergeConfig.defaultValue;
        _value = {};
      } else {
        return undefined;
      }
    }

    return schemaParser(schema, _value, extraConfig);
  };
}
