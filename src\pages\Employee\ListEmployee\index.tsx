/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect } from "react";
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  Modal,
  Pagination,
  Row,
  Select,
  Space,
  Table,
  Tooltip,
  Typography,
} from "antd";

import dayjs from "dayjs";

import useDidMount from "helpers/react-hooks/useDidMount";
import { EmployeeCreationPayload } from "services/crm/employee";

import { ListEmployeePageVm } from "./vm";
import type { ColumnsType } from "antd/es/table";

const { Title } = Typography;

type EmployeeCreationForm = Omit<
  EmployeeCreationPayload,
  "employeeGroupIds"
> & {
  accessRole: Array<{ label: string; value: string }>;
};

const ListEmployeePage = () => {
  const {
    gotoPage,
    loading,
    toggleSortEmployeeBy,
    listEmployee,
    employeePaginationState,
    pageState: { modalState },
    handleOpenModalByType,
    handleOnCloseModal,
    fetchEmployeeGroups,
    employeeGroupOptions,
    createEmployeeExec,
    createEmployeeState,
    gotoDetailEmployeePage,
    gotoEditEmployeePage,
    pageSize,
    handleChangePageSize,
  } = ListEmployeePageVm();

  useDidMount(() => {
    fetchEmployeeGroups();
  });

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageSize]);

  const handleChangePulldownPageSize = useCallback(
    (value: number) => {
      handleChangePageSize(value);
    },
    [handleChangePageSize]
  );

  const onSubmitEmployeeCreation = useCallback(
    ({
      accessRole,
      name,
      phoneNumber,
      email,
      password,
    }: EmployeeCreationForm) => {
      const employeeGroupIds = accessRole.map((value) => Number(value));
      createEmployeeExec({
        employeeGroupIds,
        name,
        phoneNumber,
        email,
        password,
      }).then((res) => {
        console.log(res, "vooooooo");
      });
    },
    [createEmployeeExec]
  );

  // Define table columns
  const columns: ColumnsType<any> = [
    {
      title: "STT",
      key: "index",
      width: 60,
      align: "center",
      fixed: "left",
      render: (_, __, index) => index + 1,
    },
    {
      title: "Họ và tên",
      dataIndex: "name",
      key: "name",
      width: 200,
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortEmployeeBy("name"),
      }),
    },
    {
      title: "Vai trò",
      key: "role",
      width: 200,
      render: (record) =>
        record.employeeGroups
          ?.map((employeeGroup: any) => employeeGroup?.name)
          .join(", "),
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      width: 200,
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortEmployeeBy("email"),
      }),
    },
    {
      title: "Số điện thoại",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
      width: 150,
    },
    {
      title: "Cập nhật cuối",
      dataIndex: "updatedAt",
      key: "updatedAt",
      width: 150,
      sorter: true,
      onHeaderCell: () => ({
        onClick: () => toggleSortEmployeeBy("updatedAt"),
      }),
      render: (date) => dayjs(date).format("DD/MM/YYYY"),
    },
    {
      title: "Shop",
      key: "shop",
      width: 200,
      render: () => "Số 199 Thủ Khoa Huân", // TODO: missing api
    },
    {
      title: "Thao tác",
      key: "action",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Tooltip title="Xem chi tiết">
            <Button
              type="primary"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => gotoDetailEmployeePage(record.employeeId)}
            />
          </Tooltip>
          <Tooltip title="Chỉnh sửa">
            <Button
              type="default"
              icon={<EditOutlined />}
              size="small"
              onClick={() => gotoEditEmployeePage(record.employeeId)}
            />
          </Tooltip>
          <Tooltip title="Xóa">
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              size="small"
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: "24px" }}>
      <title>Danh sách nhân viên</title>

      <Card>
        <Title level={2} style={{ marginBottom: "24px" }}>
          NHÂN VIÊN TRUY CẬP HỆ THỐNG
        </Title>

        {/* Search and Action Bar */}
        <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
          <Col xs={24} lg={12}>
            <Input
              placeholder="Tìm kiếm nhân viên..."
              prefix={<SearchOutlined />}
              allowClear
            />
          </Col>
          <Col xs={24} lg={12} style={{ textAlign: "right" }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleOpenModalByType("employeeCreation")}
            >
              Tạo mới
            </Button>
          </Col>
        </Row>

        {/* Table */}
        <Table
          columns={columns}
          dataSource={listEmployee}
          rowKey="employeeId"
          loading={loading}
          scroll={{ x: 1500 }}
          size="small"
          bordered
          pagination={false}
        />

        {/* Pagination */}
        <Row
          justify="space-between"
          align="middle"
          style={{ marginTop: "16px" }}
        >
          <Col>
            <Space>
              <span>Số lượng hiển thị:</span>
              <Select
                value={pageSize}
                onChange={handleChangePulldownPageSize}
                style={{ width: 80 }}
                options={[
                  { label: "5", value: 5 },
                  { label: "10", value: 10 },
                  { label: "15", value: 15 },
                  { label: "20", value: 20 },
                ]}
              />
            </Space>
          </Col>
          <Col>
            {employeePaginationState.totalPage && (
              <Pagination
                current={employeePaginationState.currentPage || 1}
                total={employeePaginationState.totalPage * pageSize}
                pageSize={pageSize}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) =>
                  `${range[0]}-${range[1]} của ${total} mục`
                }
                onChange={gotoPage}
              />
            )}
          </Col>
        </Row>
      </Card>
      <Modal
        title="TẠO MỚI THÀNH VIÊN TRUY CẬP HỆ THỐNG"
        open={modalState.open && modalState.type === "employeeCreation"}
        onCancel={handleOnCloseModal}
        footer={null}
        width={700}
        centered
      >
        <Form
          layout="vertical"
          onFinish={onSubmitEmployeeCreation}
          style={{ marginTop: "24px" }}
        >
          <Form.Item
            label="Vai trò truy cập"
            name="accessRole"
            rules={[
              { required: true, message: "Vui lòng chọn vai trò truy cập" },
            ]}
          >
            <Select
              // mode="multiple"
              placeholder="Chọn vai trò truy cập"
              options={employeeGroupOptions}
            />
          </Form.Item>

          <Form.Item
            label="Họ và tên"
            name="name"
            rules={[{ required: true, message: "Vui lòng nhập họ và tên" }]}
          >
            <Input placeholder="Nhập họ và tên" />
          </Form.Item>

          <Form.Item
            label="Số điện thoại"
            name="phoneNumber"
            rules={[{ required: true, message: "Vui lòng nhập số điện thoại" }]}
          >
            <Input placeholder="Nhập số điện thoại" />
          </Form.Item>

          <Form.Item
            label="Email"
            name="email"
            rules={[
              { required: true, message: "Vui lòng nhập email" },
              { type: "email", message: "Email không hợp lệ" },
            ]}
          >
            <Input placeholder="Nhập email" />
          </Form.Item>

          <Form.Item
            label="Mật khẩu"
            name="password"
            rules={[{ required: true, message: "Vui lòng nhập mật khẩu" }]}
          >
            <Input.Password placeholder="Nhập mật khẩu" />
          </Form.Item>

          <Form.Item
            label="Nhập lại mật khẩu"
            name="cPassword"
            dependencies={["password"]}
            rules={[
              { required: true, message: "Vui lòng nhập lại mật khẩu" },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error("Mật khẩu không khớp!"));
                },
              }),
            ]}
          >
            <Input.Password placeholder="Nhập lại mật khẩu" />
          </Form.Item>

          <Form.Item style={{ textAlign: "right", marginTop: "24px" }}>
            <Space>
              <Button onClick={handleOnCloseModal}>HUỶ</Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createEmployeeState.loading}
              >
                LƯU
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ListEmployeePage;
