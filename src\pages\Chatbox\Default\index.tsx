import React, { useCallback, useState } from "react";

import dummyImage from "assets/images/dummy/logo.png";
import { Icon } from "components/atoms/icon";
import { General } from "components/pages/general";
import { mapModifiers } from "helpers/component";
import {
  FilterPageModal,
  FilterTagModal,
  FilterEmployeeModal,
  FilterTimeModal,
} from "modules/chat";

import ChatboxPageContext from "./context";
import ConversationDetail from "./ConversationDetail";
import MessageBox from "./Messagebox";
import RecentConversation from "./RecentConversation";
import { ChatPageVm } from "./vm";

const ChatboxPage = () => {
  const { handleOpenModalByType, handleCloseModal, modalTypeIsOpen } =
    ChatPageVm();

  const [isCloseTabConversationDetail, setOpenTabConversationDetail] =
    useState(false);

  const handleToggleTabNavigation = useCallback(() => {
    setOpenTabConversationDetail(!isCloseTabConversationDetail);
  }, [isCloseTabConversationDetail]);

  return (
    <General>
      <title>Chat</title>
      <ChatboxPageContext>
        <div
          className={mapModifiers(
            "page-chatbox",
            isCloseTabConversationDetail && "tabconversationdetail-closed"
          )}
        >
          <div className="page-chatbox_filterbar">
            <button
              type="button"
              onClick={() => handleOpenModalByType("filterPage")}
            >
              <Icon iconName="facebook" />
            </button>
            <button
              type="button"
              onClick={() => handleOpenModalByType("filterTag")}
            >
              <Icon iconName="tag" />
            </button>
            <button type="button">
              <Icon iconName="comment" />
            </button>
            <button type="button">
              <Icon iconName="inbox" />
            </button>
            <button type="button">
              <Icon iconName="unseen" />
            </button>
            <button type="button">
              <Icon iconName="seen" />
            </button>
            <button type="button">
              <Icon iconName="reply" />
            </button>
            <button type="button">
              <Icon iconName="unreply" />
            </button>
            <button type="button">
              <Icon iconName="has-phone-number" />
            </button>
            <button type="button">
              <Icon iconName="not-has-phone-number" />
            </button>
            <button type="button">
              <Icon
                iconName="calendar-black"
                onClick={() => handleOpenModalByType("filterTime")}
              />
            </button>
            <button type="button">
              <Icon
                iconName="employee-black"
                onClick={() => handleOpenModalByType("filterEmployee")}
              />
            </button>
            <button type="button">
              <Icon iconName="group" />
            </button>
            <button type="button">
              <Icon iconName="settings-black" />
            </button>
            <button type="button">
              <Icon iconName="report" />
            </button>
          </div>
          <div className="page-chatbox_recent">
            <RecentConversation />
          </div>
          <div className="page-chatbox_messagebox">
            <MessageBox />
          </div>

          <div
            className="page-chatbox_tabavigation"
            aria-hidden
            onClick={handleToggleTabNavigation}
          >
            <div
              className="page-chatbox_wraptabavigation"
              aria-hidden
              onClick={handleToggleTabNavigation}
            >
              <span />
              <span />
              <span />
            </div>
          </div>
          <div className="page-chatbox_conversationdetail">
            <div className="page-chatbox_tabconversationdetail">
              <div className="page-chatbox_boxtab">
                <div className="page-chatbox_wraptabconversationdetail">
                  <ConversationDetail />
                </div>
              </div>
            </div>
          </div>
        </div>
      </ChatboxPageContext>
      <FilterPageModal
        open={modalTypeIsOpen("filterPage")}
        onClose={handleCloseModal}
        listPage={Array(15).fill({
          pageName: "Page Gumac",
          pageThumbnail: dummyImage,
        })}
      />
      <FilterTagModal
        open={modalTypeIsOpen("filterTag")}
        onClose={handleCloseModal}
        pulldownOptions={Array(15)
          .fill({
            label: "Page Gumac",
            value: "Page Gumac",
          })
          .map((item, index) => ({
            label: `${item.label}-${index}`,
            value: `${item.value}-${index}`,
          }))}
      />
      <FilterEmployeeModal
        open={modalTypeIsOpen("filterEmployee")}
        onClose={handleCloseModal}
        listEmployee={Array(15).fill({
          name: "Nguyễn Văn A",
          code: "NV01",
        })}
      />
      <FilterTimeModal
        open={modalTypeIsOpen("filterTime")}
        onClose={handleCloseModal}
      />
    </General>
  );
};

export default ChatboxPage;
