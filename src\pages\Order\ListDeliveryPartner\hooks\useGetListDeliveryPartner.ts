import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import deliveryPartnerService from "../services/delivery-partner.service";

export const useGetListDeliveryPartnerWithoutPagination = () => {
  const [deliveryRefetch, deliveryData] = useAsync(
    useCallback(() => {
      return deliveryPartnerService.getListDeliveryPartnerWithoutPagination();
    }, [])
  );
  return {
    deliveryRefetch,
    deliveryData: deliveryData.data?.data,
  };
};
