import { lazy } from "react";

import paths from "constants/paths";
import { createAppPage } from "libs/react";

import { ChildrenPage } from "./types";

export default createAppPage<{}, ChildrenPage>({
  name: "StatusTicket",
  path: paths.TICKET_MANAGEMENT,
  childrenPages: {
    statusTicket: createAppPage({
      name: "StatusTicket",
      path: "/danh-sach-trang-thai-ticket",
      page: () => lazy(() => import("./StatusTicket/StatusTicketPage.V2")),
    }),
    statusTicketDetails: createAppPage({
      name: "StatusTicket",
      path: "/danh-sach-trang-thai-ticket/chi-tiet-trang-thai-ticket/:ticketId",
      page: () => lazy(() => import("./StatusTicketDetails")),
    }),
    listTicket: createAppPage({
      name: "ListTicket",
      path: "/danh-sach-ticket",
      page: () => lazy(() => import("./ListTicket/ListTicketPageV2")),
    }),
    priorityTicket: createAppPage({
      name: "PriorityTicket",
      path: "/danh-sach-muc-do-uu-tien",
      page: () => lazy(() => import("./PriorityTicket/PriorityTicketPage")),
    }),
    ticketDetail: createAppPage({
      name: "TicketDetail",
      path: "/danh-sach-ticket/chi-tiet-ticket/:ticketId",
      page: () => lazy(() => import("./TicketDetail")),
    }),
  },
});
