import React from "react";

import { Icon } from "components/atoms/icon";
import { messageTypeByExtension } from "modules/chat";

import { getFilePreviewSrc } from "./utils";
import { FileStateType } from ".";

const PreviewItem: React.FC<{
  file: FileStateType;
  onRemove: () => void;
}> = ({ file, onRemove }) => {
  const fileType = messageTypeByExtension(file.file.type);
  return (
    <div className="o-chatinput_previewitem">
      <div className="o-chatinput_previewwrapper">
        {["image", "file"].includes(fileType) && (
          <img
            alt={file.file.name}
            className="o-chatinput_filepreview"
            src={getFilePreviewSrc(file)}
          />
        )}
        {fileType === "video" && (
          // eslint-disable-next-line jsx-a11y/media-has-caption
          <video controls onCanPlay={() => false} height={50} width={50}>
            <source src={getFilePreviewSrc(file)} />
          </video>
        )}
        <div aria-hidden className="o-chatinput_removeicon" onClick={onRemove}>
          <Icon iconName="close" />
        </div>
      </div>
      <span>{file.file.name}</span>
    </div>
  );
};

export default PreviewItem;
