/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-array-constructor */
/* eslint-disable no-array-constructor */
import { fromSchema } from "libs/adapters/dto";
import { Array, Number, String, Boolean, Model, ModelValue } from "libs/domain";
import { EmployeeSchema } from "modules/employee";
import {
  BankAccountSchema,
  CitySchema,
  DistrictSchema,
  StorageSchema,
  WardSchema,
} from "modules/location";

export const ShopSchema = {
  shopId: Number(),
  name: String(),
  displayOrder: Number(),
  type: String(),
  hotline: String(),
  cityId: Number(),
  districtId: Number(),
  wardId: Number(),
  address: String(),
  employeeIds: Array(Number(), { defaultValue: [] }),
  // storageIds: Array(Number(), { defaultValue: [] }),
  storageId: Number() || String(), // Allow both number and string for storageId
  bankAccountIds: Array(Number(), { defaultValue: [] }),
  isFranchisedShop: Boolean(),
  employees: Array(fromSchema(EmployeeSchema)),
  storages: Array(fromSchema(StorageSchema)),
  bankAccounts: Array(fromSchema(BankAccountSchema)),
  city: fromSchema(CitySchema),
  district: fromSchema(DistrictSchema),
  ward: fromSchema(WardSchema),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  createdAt: (raw: any) => new Date(raw),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => new Date(raw),
  shopCode: String(),
  activeStatus: Number(),
  mail: String(),
  area: Number(),
  posID: Number(),
  printEx: Number(),
  startActiveDate: (raw: string) => (raw ? new Date(raw) : null),
  endActiveDate: (raw: string) => (raw ? new Date(raw) : null),
  isDefaultShop: Boolean(),
  storage: fromSchema(StorageSchema),
};

export const ShopModel = new Model(ShopSchema);
export type ShopEntityType = ModelValue<typeof ShopModel>;
