/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";

import {
  Input,
  DatePicker,
  Divider,
  InputNumber,
  Form,
  Table,
  Row,
  Col,
} from "antd";
import { ColumnsType } from "antd/es/table";
import { BaseButton } from "components/atoms/base/button/BaseButton";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import { BasePageProps } from "helpers/component";

// Define interface for table data
interface OrderData {
  key: string;
  orderCode: string;
  customer: string;
  province: string;
  status: string;
  note: string;
  product: string;
  quantity: number;
  totalPrice: number;
  createdDate: string;
  updatedDate: string;
  actions?: React.ReactNode;
}

const IndexPage: React.FC<BasePageProps> = () => {
  const [form] = Form.useForm();

  const handleFinish = (values: any) => {
    console.log("Form values:", values);
  };

  const handleReset = () => {
    form.resetFields();
  };

  // Define columns with proper TypeScript typing and colSpan handling
  const columns: ColumnsType<OrderData> = [
    {
      title: "Mã ĐH",
      dataIndex: "orderCode",
      key: "orderCode",
      align: "center",
      fixed: "left",
    },
    {
      title: "Khách hàng",
      dataIndex: "customer",
      key: "customer",
      align: "center",
    },
    {
      title: "Tỉnh",
      dataIndex: "province",
      key: "province",
      align: "center",
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      align: "center",
    },
    {
      title: "Ghi chú",
      dataIndex: "note",
      key: "note",
      align: "center",
      onCell: () => ({ colSpan: 3 }),
      render: (text) => <span>{text}</span>,
    },
    {
      title: "Sản phẩm",
      dataIndex: "product",
      key: "product",
      align: "center",
      onCell: () => ({ colSpan: 2 }),
      render: (text) => <span>{text}</span>,
    },
    {
      title: "SL",
      dataIndex: "quantity",
      key: "quantity",
      align: "center",
    },
    {
      title: "Thành tiền",
      dataIndex: "totalPrice",
      key: "totalPrice",
      align: "center",
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdDate",
      key: "createdDate",
      align: "center",
    },
    {
      title: "Cập nhật",
      dataIndex: "updatedDate",
      key: "updatedDate",
      align: "center",
    },
    {
      title: "Thao tác",
      dataIndex: "actions",
      key: "actions",
      align: "center",
    },
  ];

  return (
    <General>
      <title key="title">Quản lý đơn hàng</title>
      <div className="h-full p-4 flex flex-col gap-4">
        <Form form={form} onFinish={handleFinish} layout="vertical">
          <Row gutter={[16, 16]} className="u-mb-lg-15">
            <Col xl={6} lg={6} className="u-mb-10 u-mb-sm-20">
              <Form.Item label="Tên" name="name">
                <Input placeholder="Nhập tên" />
              </Form.Item>
            </Col>

            <Col xl={6} lg={6} className="u-mb-10 u-mb-sm-20">
              <Form.Item label="Status" name="status">
                <BaseSelect
                  placeholder="Chọn status"
                  fieldNames={{ label: "label", value: "value" }}
                />
              </Form.Item>
            </Col>

            <Col xl={6} lg={6} className="u-mb-10 u-mb-sm-20">
              <Form.Item label="Title 01" name="title">
                <Input placeholder="Nhập title" />
              </Form.Item>
            </Col>

            <Col xl={6} lg={6} className="u-mb-10 u-mb-sm-20">
              <Row gutter={[16, 0]}>
                <Col span={12}>
                  <Form.Item label="Độ tuổi" name="ageFrom">
                    <InputNumber
                      placeholder="Nhập số"
                      style={{ width: "100%" }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Độ tuổi" name="ageTo">
                    <InputNumber
                      placeholder="Nhập số"
                      style={{ width: "100%" }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Col>

            <Col xl={6} lg={6} className="u-mb-10 u-mb-sm-20">
              <Form.Item label="Title 01" name="title1">
                <Input placeholder="Nhập title" />
              </Form.Item>
            </Col>

            <Col xl={6} lg={6} className="u-mb-10 u-mb-sm-20">
              <Form.Item label="Title 01" name="title2">
                <Input placeholder="Nhập title" />
              </Form.Item>
            </Col>

            <Col xl={6} lg={6} className="u-mb-10 u-mb-sm-20">
              <Form.Item label="Title 01" name="title3">
                <Input placeholder="Nhập title" />
              </Form.Item>
            </Col>
          </Row>

          <div className="u-pt-20 u-pb-20">
            <Divider dashed />
          </div>

          <Form.Item label="Thời gian cập nhật" name="date">
            <div style={{ display: "flex", alignItems: "center" }}>
              <DatePicker style={{ marginRight: "12px" }} />
              -
              <DatePicker style={{ marginLeft: "12px" }} />
            </div>
          </Form.Item>

          <div className="u-pt-20 u-pb-20">
            <Divider dashed />
          </div>

          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              marginBottom: "20px",
            }}
          >
            <div style={{ paddingRight: "10px" }}>
              <BaseButton
                type="default"
                onClick={handleReset}
                style={{
                  borderColor: COLOR.BLUE[500],
                  color: COLOR.BLUE[500],
                  height: "40px",
                  minWidth: "200px",
                  fontWeight: 500,
                  backgroundColor: "white",
                }}
                className="hover:!bg-blue-700 hover:!text-white"
              >
                THIẾT LẬP LẠI
              </BaseButton>
            </div>
            <div style={{ paddingLeft: "10px" }}>
              <BaseButton
                type="primary"
                htmlType="submit"
                style={{
                  backgroundColor: COLOR.BLUE[500],
                  borderColor: COLOR.BLUE[500],
                  height: "40px",
                  minWidth: "200px",
                  fontWeight: 500,
                }}
                className="hover:!bg-blue-700 hover:!text-white"
              >
                ÁP DỤNG
              </BaseButton>
            </div>
          </div>
        </Form>

        <Table
          columns={columns}
          dataSource={[]}
          bordered
          pagination={false}
          scroll={{ x: true }}
          className="bordered-table"
          rowKey="key"
        />
      </div>
    </General>
  );
};

export default IndexPage;
