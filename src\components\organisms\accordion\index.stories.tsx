import { Story, Meta } from "@storybook/react/types-6-0";

import { Accordion, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|organisms/Accordion",
  component: Accordion,
} as Meta;

const Template: Story<Props> = ({ type, title, children }) => (
  <Accordion title={title} type={type}>
    {children}
  </Accordion>
);

export const Normal = Template.bind({});

Normal.args = {
  title: "Text Text",
  // type: "default",
  children: (
    <>
      <div>Tâm</div>
      <div><PERSON><PERSON><PERSON></div>
      <div><PERSON>h</div>
      <div><PERSON><PERSON><PERSON><PERSON></div>
      <div>My</div>
    </>
  ),
};
