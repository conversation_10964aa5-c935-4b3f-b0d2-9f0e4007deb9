import pick from "object.pick";

import { createMapper, fromSchema, merge } from "libs/adapters/dto";

import { OrderReturnStatusTaglineSchema } from "..";

export const createReturnOrderStatusTaglineDto = createMapper(
  fromSchema(
    pick(OrderReturnStatusTaglineSchema, [
      "returnOrderStatusId",
      "description",
      "name",
      "displayOrder",
      "color",
    ])
  ),
  merge((data) => ({
    description: data.description || undefined,
  }))
);

export type createReturnOrderStatusTaglineDtoType = ReturnType<
  typeof createReturnOrderStatusTaglineDto
>;
