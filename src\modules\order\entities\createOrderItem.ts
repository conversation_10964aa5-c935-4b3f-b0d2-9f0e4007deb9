import { Enum, Model, ModelValue, Number, String } from "libs/domain";

export const CreateOrderItemSchema = {
  _id: String(),
  skuId: String(),
  quantity: Number(),
  orderItemId: Number(),
  unitPrice: Number(),
  productId: Number(),
  totalAmount: Number(),
  listedPrice: Number(),
  // itemType: Enum({
  //   values: ["SOLD", "RETURNED", "EXCHANGED"],
  // }),

  discountPercent: Number({ defaultValue: 0 }),
  discountAmount: Number({ defaultValue: 0 }),
};

export const CreateOrderItemModel = new Model(CreateOrderItemSchema);
export type CreateOrderItemEntity = ModelValue<typeof CreateOrderItemModel>;
