import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import { GetPointCustomerDto } from "../Dto/customer.dto";
import customerService from "../service/customer.service";
import groupCustomerService from "../service/groupCustomer.service";

export const UseGetAllCustomers = () => {
  const [getAllCustomers, customersState] = useAsync(
    useCallback((params) => customerService.getAllCustomers(params), [])
  );
  return {
    getAllCustomers,
    customersState: customersState?.data?.data || [],
  };
};

export const UseGetAllEmployee = () => {
  const [getAllEmployeeExec, getAllEmployeeState] = useAsync(
    useCallback(() => groupCustomerService.getAllEmployee(), [])
  );
  return {
    getAllEmployeeExec,
    getAllEmployeeState: getAllEmployeeState?.data?.data || [],
    loading: getAllEmployeeState.loading,
  };
};

export const UseGetPointCustomer = (dto: GetPointCustomerDto) => {
  const [getPointCustomerExec, getPointCustomerState] = useAsync(
    useCallback(() => customerService.getPointCustomer(dto), [dto])
  );
  return {
    getPointCustomerExec,
    getPointCustomerState: getPointCustomerState?.data?.data,
    loading: getPointCustomerState.loading,
  };
};
