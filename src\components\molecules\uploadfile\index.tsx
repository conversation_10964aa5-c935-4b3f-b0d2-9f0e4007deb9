import React from "react";

import { Button } from "components/atoms/button";
import { Icon } from "components/atoms/icon";
import useUploadFile from "components/hooks/useUploadFile";
import { mapModifiers } from "helpers/component";

export interface Props {
  onChange?: (file: File) => void;
}

export const Uploadfile: React.FC<Props> = ({ onChange }) => {
  const { refInput, fileUpload, handleClearFile, onChangeFileUpload } =
    useUploadFile({
      onChange,
      extensions: ["jpg", "jpeg", "png", "pdf"],
      fileSize: 1,
    });
  return (
    <div
      className={mapModifiers("m-uploadfile", !!fileUpload && "uploaded")}
      style={{
        backgroundImage: fileUpload
          ? `url(${URL.createObjectURL(fileUpload)})`
          : "none",
      }}
    >
      {fileUpload ? (
        <>
          <div className="m-uploadfile_close">
            <Icon iconName="close-blue" onClick={handleClearFile} />
          </div>
          {fileUpload.type === "application/pdf" && (
            <div>
              <div className="m-uploadfile_fileicon">
                <Icon iconName="pdf-file" />
              </div>
              <span className="m-uploadfile_filename">{fileUpload.name}</span>
            </div>
          )}
        </>
      ) : (
        <div className="m-uploadfile_wrap">
          <input
            className="m-uploadfile_input"
            type="file"
            ref={refInput}
            onChange={onChangeFileUpload}
          />
          <div className="m-uploadfile_wrapicon">
            <Icon iconName="upload-file" />
          </div>
          <Button modifiers="primary">Tải lên</Button>
          <span className="m-uploadfile_title">Hoặc thả tập tin</span>
        </div>
      )}
    </div>
  );
};
