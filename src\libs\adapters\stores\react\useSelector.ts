import { useEffect, useRef } from "react";

import { Source } from "../core";
import { useForceUpdate } from "./useForceUpdate";

export type Selector<S, SS> = (source: S) => SS;

export type ReturnTypeSelector<SS, SSS, Transform> = Transform extends undefined
  ? SS
  : SSS;

export const useSelector = <S, SS, SSS = SS>(
  source: Source<S>,
  selector: Selector<S, SS>,
  transfrom: Selector<SS, SSS> = (selectorState: SS) =>
    selectorState as unknown as SSS
) => {
  const [, forceUpdate] = useForceUpdate();
  const selectorRef = useRef<SS>(
    (() => {
      const sourceValue = source.getValue();
      return selector(sourceValue);
    })()
  );

  useEffect(() => {
    const unsubscribe = source.watch((state) => {
      const nextSelectorValue = selector(state);
      if (!Object.is(selectorRef.current, nextSelectorValue)) {
        selectorRef.current = nextSelectorValue;
        forceUpdate();
      }
    });

    return () => {
      unsubscribe();
    };
  }, [selector, forceUpdate, source]);

  return transfrom(selectorRef.current);
};
