import React from "react";

import { mapModifiers } from "helpers/component";

type Modifiers = "uppercase";
export interface Props {
  centered?: true;
  children?: React.ReactNode;
  modifiers?: Modifiers | Modifiers[];
}

export const Text: React.FC<Props> = ({ centered, modifiers, children }) => (
  <p className={mapModifiers("a-text", centered && "centered", modifiers)}>
    {children}
  </p>
);
