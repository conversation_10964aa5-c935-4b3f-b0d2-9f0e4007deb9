import { useState, useCallback } from "react";

import produce from "immer";

type ModalType = "createStatusTicket";

interface State {
  modalState: {
    open: boolean;
    type?: ModalType;
  };
}

export const StatusTicketPageVm = () => {
  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      type: undefined,
    },
  });

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.type = type;
      })
    );
  }, []);

  const handleOnCloseModal = useCallback(() => {
    if (!state.modalState.open) return;
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.type = undefined;
      })
    );
  }, [state.modalState.open]);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) =>
      state.modalState.open && state.modalState.type === type,
    [state.modalState.open, state.modalState.type]
  );

  return {
    handleOpenModalByType,
    handleOnCloseModal,
    modalTypeIsOpen,
  };
};
