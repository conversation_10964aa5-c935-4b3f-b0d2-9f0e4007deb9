import { useCallback, useEffect, useState } from "react";

import { de } from "date-fns/locale";
import produce from "immer";

import { toastSingleMode } from "components/atoms/toastify";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import { useAsync } from "hooks/useAsync";
import { usePagination } from "hooks/usePagination";
import { useSortable } from "hooks/useSortable";
import { mapFrom } from "libs/adapters/dto";
import { paginationDTO } from "modules/common/pagination";
import {
  createDeliveryPartnerDto,
  CreateDeliveryPartnerDtoType,
  deliveryPartnerListItemDto,
} from "modules/order";
import {
  createDeliveryPartner as createDeliveryPartnerService,
  deleteDeliveryPartner,
  getListDeliveryPartner,
} from "services/crm/order";

type ModalType = "shippingPartner";

interface State {
  modalState: {
    open: boolean;
    modalType?: ModalType;
  };
  pagination: {
    pageSize: number;
  };
}

export const DeliveryPartnerListPageVm = () => {
  const [state, setState] = useState<State>({
    modalState: {
      open: false,
      modalType: undefined,
    },
    pagination: {
      pageSize: 10,
    },
  });

  const handleOnCloseModal = useCallback(
    () =>
      setState(
        produce((draft) => {
          draft.modalState.open = false;
          draft.modalState.modalType = undefined;
        })
      ),
    []
  );

  const [getDeliveryPartnerListExec, getDeliveryPartnerListState] = useAsync(
    useCallback(
      (options: { pageNum: number; pageSize: number }) =>
        getListDeliveryPartner({ ...options }).then((res) => ({
          data: mapFrom(res.data.data, deliveryPartnerListItemDto),
          pagination: paginationDTO(res.data.links),
        })),
      []
    )
  );

  const { gotoPage, ...deliveryPartnerListPaginationState } = usePagination({
    pageSize: state.pagination.pageSize,
    actionOnPageChange: ({ page, pageSize }) =>
      getDeliveryPartnerListExec({ pageSize, pageNum: page }),
  });

  const handleChangePageSize = useCallback((pageSize: number) => {
    setState(
      produce((draft) => {
        draft.pagination.pageSize = pageSize;
      })
    );
  }, []);

  useEffect(() => {
    gotoPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.pagination.pageSize]);

  const {
    sortedData: deliveryPartnerListData,
    toggleSortState: toggleSortOrderBy,
  } = useSortable({
    data: getDeliveryPartnerListState.data?.data,
    sortBy: {
      updatedAt: (data) => data.updatedAt,
    },
  });

  const handleOpenModalByType = useCallback((type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.modalType = type;
      })
    );
  }, []);

  const handleCloseModal = useCallback(() => {
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.modalType = undefined;
      })
    );
  }, []);

  const modalTypeIsOpen = useCallback(
    (type: ModalType) => {
      return state.modalState.open && state.modalState.modalType === type;
    },
    [state.modalState]
  );

  const [createDeliveryPartnerExec, createDeliveryPartnerState] = useAsync(
    createDeliveryPartnerService,
    {
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
      onSuccess: useCallback(() => {
        handleOnCloseModal();
        toastSingleMode({
          type: "success",
          message: "Tạo mới thành công",
        });
        getDeliveryPartnerListExec({
          pageSize: state.pagination.pageSize,
          pageNum: 1,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, []),
    }
  );

  const createDeliveryPartner = useCallback(
    (rawPayload: Partial<CreateDeliveryPartnerDtoType>): Promise<unknown> => {
      const deliveryPartnerPayload = createDeliveryPartnerDto({
        ...rawPayload,
      });
      return createDeliveryPartnerExec(deliveryPartnerPayload);
    },
    [createDeliveryPartnerExec]
  );

  const handleDeleteDeliveryPartner = useCallback(
    (deliveryPartnerId: number) => {
      deleteDeliveryPartner(deliveryPartnerId).then((res) => {
        if (res.status === 200) {
          // Refresh the list after deletion
          toastSingleMode({
            type: "success",
            message: "Xóa đơn vị vận chuyển thành công",
          });
          getDeliveryPartnerListExec({
            pageSize: state.pagination.pageSize,
            pageNum: 1,
          });
        }
      });
    },
    []
  );

  return {
    gotoPage,
    handleChangePageSize,
    toggleSortOrderBy,
    deliveryPartnerListData: deliveryPartnerListData || [],
    deliveryPartnerListLoading: getDeliveryPartnerListState.loading,
    pageSize: state.pagination.pageSize,
    deliveryPartnerListPaginationState,
    handleOpenModalByType,
    handleCloseModal,
    modalTypeIsOpen,
    createDeliveryPartner,
    handleDeleteDeliveryPartner,
    createDeliveryPartnerState,
  };
};
