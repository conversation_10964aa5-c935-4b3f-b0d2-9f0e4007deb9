export const collapseSection = (element: HTMLElement) => {
  const sectionHeight = element.scrollHeight;

  const elementTransition = "all 0.25s cubic-bezier(0.2, 0, 0.4, 1)";
  element.style.transition = "";

  requestAnimationFrame(() => {
    element.style.height = `${sectionHeight}px`;
    element.style.transition = elementTransition;

    requestAnimationFrame(() => {
      element.style.height = `${0}px`;
    });
  });
};

export const expandSection = (element: HTMLElement) => {
  // have the element transition to the height of its inner content
  element.style.height = `auto`;

  function transitionendEvent() {
    // remove this event listener so it only gets triggered once
    element.removeEventListener("transitionend", transitionendEvent);
  }

  // when the next css transition finishes (which should be the one we just triggered)
  element.addEventListener("transitionend", transitionendEvent);
};

export const onTriggerCollaped = (
  collapElm: HTMLElement | null,
  contentElm: HTMLElement | null,
  cb?: (isCollapsed: boolean) => void
) => {
  const wrapped = document.querySelector<HTMLElement>(".o-accordion");
  const isCollapsed = contentElm?.getAttribute("data-collapsed") === "true";
  collapElm?.classList[isCollapsed ? "remove" : "add"]("active");
  wrapped?.classList[isCollapsed ? "remove" : "add"]("active");
  if (contentElm) {
    contentElm.style.paddingTop = isCollapsed ? "0" : "22px";
    contentElm.style.paddingBottom = isCollapsed ? "0" : "22px";
    contentElm.style.borderWidth = isCollapsed ? "0" : "1px";
    contentElm.setAttribute("data-collapsed", isCollapsed ? "false" : "true");
    expandSection(contentElm);
    if (!isCollapsed) expandSection(contentElm);
    else collapseSection(contentElm);
  }
  if (cb) {
    cb(!isCollapsed);
  }
};
