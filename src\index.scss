@import "~bootstrap/dist/css/bootstrap-reboot.min.css";
@import "~bootstrap/dist/css/bootstrap-grid.min.css";
@import "~rc-menu/assets/index.css";
@import "~react-datepicker/dist/react-datepicker.css";
@import "~react-tabs/style/react-tabs.css";
@import "./assets/scss/_fonts.scss";
@import "./assets/scss/_variables.scss";
@import "./assets/scss/_mixins.scss";
@import "./assets/scss/_zIndex.scss";
@import "./assets/scss/_functions.scss";
@import "./assets/scss/_globals.scss";

/* Components */
@import "components/atoms/autocomplete/index.scss";
@import "components/atoms/button/index.scss";
@import "components/atoms/buttonfilter/index.scss";
@import "components/atoms/checkbox/index.scss";
@import "components/atoms/colorpicker/index.scss";
@import "components/atoms/divider/index.scss";
@import "components/atoms/editableinput/index.scss";
@import "components/atoms/heading/index.scss";
@import "components/atoms/icon/index.scss";
@import "components/atoms/image/index.scss";
@import "components/atoms/numberfield/index.scss";
@import "components/atoms/phonefield/index.scss";
@import "components/atoms/pulldown/index.scss";
@import "components/atoms/radio/index.scss";
@import "components/atoms/sample/index.scss";
@import "components/atoms/selectedchoice/index.scss";
@import "components/atoms/spinner/index.scss";
@import "components/atoms/tag/index.scss";
@import "components/atoms/text/index.scss";
@import "components/atoms/textareafield/index.scss";
@import "components/atoms/textfield/index.scss";
@import "components/atoms/toastify/index.scss";
@import "components/atoms/toggle/index.scss";
@import "components/molecules/boxmessage/index.scss";
@import "components/molecules/calendar/index.scss";
@import "components/molecules/file/index.scss";
@import "components/molecules/filewrapper/index.scss";
@import "components/molecules/folder/index.scss";
@import "components/molecules/formfield/index.scss";
@import "components/molecules/historynote/index.scss";
@import "components/molecules/imagewrapper/index.scss";
@import "components/molecules/message/index.scss";
@import "components/molecules/orderlist/index.scss";
@import "components/molecules/pagination/index.scss";
@import "components/molecules/recentconversation/index.scss";
@import "components/molecules/shippingaddress/index.scss";
@import "components/molecules/uploadfile/index.scss";
@import "components/organisms/accordion/index.scss";
@import "components/organisms/chatbox/index.scss";
@import "components/organisms/chatinput/index.scss";
@import "components/organisms/grid/index.scss";
@import "components/organisms/modal/index.scss";
@import "components/organisms/recentconversation/index.scss";
@import "components/organisms/section/index.scss";
@import "components/organisms/sidebar/index.scss";
@import "components/organisms/table/index.scss";
@import "components/organisms/tabs/index.scss";
@import "components/pages/general/index.scss";
@import "components/pages/unauth/index.scss";
@import "components/templates/mainlayout/index.scss";
@import "components/utils/spinnercontainer/index.scss";
@import "components/utils/tooltip/index.scss";
/* Components */

/* Pages */
@import "pages/Chatbox/Default/index.scss";
/* Pages */

.e-btn.e-blue,
.e-css.e-btn.e-blue {
  background-color: $COLOR-DPT-BLUE;
  border: none;
  color: #fff;
  &:hover {
    background-color: #115885;
    box-shadow: none;
    color: #fff;
  }
  &:focus {
    background-color: #115885;
    color: #fff;
    box-shadow: 0 0 0 4px rgba($COLOR-DPT-BLUE, 0.5);
  }
  &:active,
  .e-active {
    background-color: $COLOR-DPT-BLUE;
    color: #fff;
  }
  &:disabled {
    background-color: lightgray;
    color: rgba(0, 0, 0, 0.75);
  }
  & .e-ripple-element {
    background-color: transparent;
  }
}
/* Tree Grid Syncfusion */
.e-control.e-treegrid.e-lib.e-keyboard {
  overflow: hidden;
}
/* Dialog Syncfusion */
.e-dlg-container.e-dlg-center-center {
  position: fixed !important;
}
.e-dlg-content div:has(> div.d-flex) {
  height: 100%;
  overflow: auto;
}
.e-dlg-content:has(div > div.d-flex) {
  padding-bottom: 0;
}
/* Accordion Synfusion */
.e-acrdn-header:focus {
  box-shadow: none !important;
}

/* Button Syncfusion */
.e-control .e-btn {
  box-shadow: none !important;
  border: none;
}

/* Input Syncfusion */
.e-input-group:not(.e-disabled):active:not(.e-success):not(.e-warning):not(.e-error),
.e-input-group.e-control-wrapper:not(.e-disabled):active:not(.e-success):not(.e-warning):not(.e-error) {
  box-shadow: none;
}
.e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error),
.e-input-group.e-control-wrapper.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) {
  box-shadow: none;
}
label {
  margin-bottom: 3px;
}
.e-groupcaption {
  color: blue !important;
}
.e-upload-custom .e-icons {
  display: none !important;
}
.e-upload-custom .e-file-container {
  margin: 0 12px !important;
}

.pane {
  margin: 0.5em !important;
  padding: 0.75em;
  border: 1px solid #e5e7eb;
  border-radius: 0.5em;
  background-color: white;
}

.form_base {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f9fafb;
}

.ant-tabs-nav {
  margin: 0 !important;
}

@import "@syncfusion/ej2-base/styles/bootstrap5.css";
@import "@syncfusion/ej2-icons/styles/bootstrap5.css";
@import "@syncfusion/ej2-dropdowns/styles/bootstrap5.css";
@import "@syncfusion/ej2-inputs/styles/bootstrap5.css";
@import "@syncfusion/ej2-lists/styles/bootstrap5.css";
@import "@syncfusion/ej2-popups/styles/bootstrap5.css";
@import "@syncfusion/ej2-calendars/styles/bootstrap5.css";
@import "@syncfusion/ej2-notifications/styles/bootstrap5.css";
@import "@syncfusion/ej2-grids/styles/bootstrap5.css";
@import "@syncfusion/ej2-splitbuttons/styles/bootstrap5.css";
@import "@syncfusion/ej2-navigations/styles/bootstrap5.css";
@import "@syncfusion/ej2-buttons/styles/bootstrap5.css";
@import "@syncfusion/ej2-richtexteditor/styles/bootstrap5.css";
@import "@syncfusion/ej2-react-spreadsheet/styles/material.css";

@import "@syncfusion/ej2-react-navigations/styles/bootstrap5.css";
@import "@syncfusion/ej2-react-grids/styles/bootstrap5.css";
@import "@syncfusion/ej2-react-buttons/styles/bootstrap5.css";
@import "@syncfusion/ej2-react-inputs/styles/bootstrap5.css";
@import "@syncfusion/ej2-react-layouts/styles/bootstrap5.css";
@import "@syncfusion/ej2-react-dropdowns/styles/bootstrap5.css";
@import "@syncfusion/ej2-react-calendars/styles/bootstrap5.css";
@import "@syncfusion/ej2-react-splitbuttons/styles/bootstrap5.css";
@import "@syncfusion/ej2-react-treegrid/styles/bootstrap5.css";
@import "@syncfusion/ej2-kanban/styles/bootstrap5.css";
@import "@syncfusion/ej2-react-schedule/styles/bootstrap5.css";

/* Syncfuson */

/* Fix Bootstrap CSS */
.row {
  margin-left: 0;
  margin-right: 0;
}
button {
  border: none;
}

/* hide scrollbar but allow scrolling */
.ant-modal-body {
  -ms-overflow-style: none; /* for Internet Explorer, Edge */
  scrollbar-width: none; /* for Firefox */
  overflow-y: scroll;
}
.ant-modal-body::-webkit-scrollbar {
  display: none; /* for Chrome, Safari, and Opera */
}

/* Ant Design */
.dpt-collapse-header .ant-collapse-content-box {
  padding: 0 1em 1em 1em !important;
}
.react-flow__handle:not(.connectionindicator) {
  display: none;
}
