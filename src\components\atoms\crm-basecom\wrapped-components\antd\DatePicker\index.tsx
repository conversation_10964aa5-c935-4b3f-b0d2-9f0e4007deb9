/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/jsx-props-no-spreading */
import { DatePicker, DatePickerProps } from "antd";
import dayjs, { Dayjs } from "dayjs";

// Định nghĩa kiểu Props rõ ràng để tránh xung đột
type BaseDatePickerProps = {
  value?: Date | string;
  onChange?: (value?: Date | string) => void;
} & Omit<DatePickerProps, "value" | "onChange">;

/**
 * Component DatePicker được bọc để sử dụng với Ant Design Form
 * Sử dụng kiểu Date object thuần của JavaScript (không phải dayjs như trong antd)
 */
export const BaseDatePicker = (props: any) => {
  const { value, onChange, ...restProps } = props;

  // Xử lý sự kiện thay đổi ngày
  const handleOnChange = (date: Dayjs | null) => {
    if (onChange) {
      if (date) {
        // Chuyển đổi dayjs object thành Date object
        onChange(date.toDate());
      } else {
        // Trường hợp người dùng xóa ngày
        onChange(undefined);
      }
    }
  };

  // Chuyển đổi value từ Date/string thành dayjs object
  const dayjsValue = value ? dayjs(value) : undefined;

  return (
    <DatePicker
      {...restProps} // Chỉ spread các props không bị xung đột
      value={dayjsValue}
      onChange={handleOnChange}
    />
  );
};
