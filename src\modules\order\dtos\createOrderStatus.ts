import pick from "object.pick";

import { createMapper, fromSchema, merge } from "libs/adapters/dto";

import { OrderStatusSchema } from "../entities";

export const createOrderStatusDto = createMapper(
  fromSchema(
    pick(OrderStatusSchema, [
      "name",
      "isDefault",
      "displayOrder",
      "description",
      "prevOrderStatusIds",
      "color",
    ])
  ),
  merge((data) => ({
    name: data.name.trim(),
    description: data.description || undefined,
    prevOrderStatusIds: !data.prevOrderStatusIds?.length
      ? null
      : data.prevOrderStatusIds,
  }))
);
export type CreateOrderStatusDtoType = ReturnType<typeof createOrderStatusDto>;
