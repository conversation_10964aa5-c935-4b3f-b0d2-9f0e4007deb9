import {
  Category,
  ChartComponent,
  ColumnSeries,
  DataLabel,
  Inject,
  Legend,
  LineSeries,
  SeriesCollectionDirective,
  SeriesDirective,
  Tooltip,
} from "@syncfusion/ej2-react-charts";

/* eslint-disable */
export default function MultipleChart({ id, title, multipleData }: any) {
	return (
		<div style={{ width: "100%", height: "100%", padding: "0 2px" }}>
			<ChartComponent
				id={id}
				primaryXAxis={{ valueType: "Category" }}
				primaryYAxis={{ valueType: "Category" }}
				title={title}
				style={{ width: "100%", height: "100%", padding: 0 }}
				palettes={["#00b894", "#0984e3", "#95afc0"]}
			>
				<Inject
					services={[
						ColumnSeries,
						Legend,
						Tooltip,
						DataLabel,
						LineSeries,
						Category,
					]}
				/>
				<SeriesCollectionDirective>
					<SeriesDirective
						dataSource={multipleData}
						xName="country"
						yName="gold"
						type="Column"
					></SeriesDirective>
					<SeriesDirective
						dataSource={multipleData}
						xName="country"
						yName="silver"
						type="Column"
					></SeriesDirective>
					<SeriesDirective
						dataSource={multipleData}
						xName="country"
						yName="bronze"
						type="Column"
					></SeriesDirective>
				</SeriesCollectionDirective>
			</ChartComponent>
		</div>
	);
}
