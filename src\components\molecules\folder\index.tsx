import React, { useState, useRef, useEffect, useCallback } from "react";

import { Icon } from "components/atoms/icon";
import { Text } from "components/atoms/text";
import useDidMount from "helpers/react-hooks/useDidMount";

export interface Props {
  name: string;
  totalFile: number;
  onClick?: () => void;
  onRename?: (name: string) => void;
}
export const Folder: React.FC<Props> = ({
  name,
  totalFile,
  onClick,
  onRename,
}) => {
  const [isContentEditable, setContentEditable] = useState<boolean>(false);
  const prevName = useRef<string>("");
  const folderRef = useRef<HTMLSpanElement | null>(null);

  const handleRename = useCallback(() => {
    const folderTarget = folderRef.current as HTMLSpanElement;
    if (folderTarget) {
      folderTarget.innerHTML = folderTarget.innerHTML.replace(
        /(&nbsp;|\s)*$/,
        ""
      );
    }
    setContentEditable(false);
  }, []);

  useDidMount(() => {
    const folderTarget = folderRef.current as HTMLSpanElement;
    if (!folderTarget) return undefined;

    const handleBlur = (event: KeyboardEvent) => {
      if (event.key === "Enter") folderTarget.blur();
    };

    folderTarget.addEventListener("keydown", handleBlur);

    return () => folderTarget.removeEventListener("keydown", handleBlur);
  });

  useEffect(() => {
    const folderTarget = folderRef.current as HTMLSpanElement;
    if (!folderTarget) return;

    if (isContentEditable) {
      prevName.current = folderTarget.innerHTML;
      folderTarget.focus();

      const range = document.createRange();
      range.selectNodeContents(folderTarget);
      range.collapse(false);

      const selection = window.getSelection();
      selection?.removeAllRanges();
      selection?.addRange(range);
    } else if (folderTarget.innerHTML === "") {
      folderTarget.innerHTML = prevName.current;
    } else if (folderTarget.innerHTML !== prevName.current && onRename) {
      onRename(folderTarget.innerHTML);
      prevName.current = folderTarget.innerHTML;
    }
  }, [isContentEditable, onRename]);

  return (
    <div aria-hidden className="m-folder" onClick={onClick}>
      <div className="m-folder_iconfolder">
        <Icon iconName="folder" />
      </div>
      <div className="m-folder_wrapname">
        <span
          ref={folderRef}
          contentEditable={isContentEditable}
          className="m-folder_name"
          onBlur={handleRename}
        >
          {name}
        </span>
        <div
          aria-hidden
          className="m-folder_editicon"
          onClick={() => !isContentEditable && setContentEditable(true)}
        >
          <Icon iconName="pen" />
        </div>
      </div>
      <div className="m-folder_totalfile">
        <Text centered>{`${totalFile} mục`}</Text>
      </div>
    </div>
  );
};
