import { createMapper, fromSchema } from "libs/adapters/dto";
import { Array, mergeSchema } from "libs/domain";

import { ProductSchema } from "../entities";
import { skuDetailDto } from "./skuDetail";

export const productDetailDto = createMapper(
  fromSchema(
    mergeSchema(ProductSchema, {
      sku: Array(skuDetailDto),
    })
  )
);

export type ProductDetailDtoType = ReturnType<typeof productDetailDto>;
