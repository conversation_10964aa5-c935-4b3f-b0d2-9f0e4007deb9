/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable */
import { BaseButton } from "components/atoms/base/button/BaseButton";
import {
  ChangeEvent,
  FocusEvent,
  ReactNode,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import {
  CheckOutlined,
  DeleteOutlined,
  PercentageOutlined,
  PlusOutlined,
  RetweetOutlined,
  RightOutlined,
} from "@ant-design/icons";
import {
  Col as AntCol,
  Radio as AntRadio,
  Row as AntRow,
  Card,
  Checkbox,
  Collapse,
  DatePicker,
  Divider,
  Flex,
  Form,
  Grid,
  Input,
  InputNumber,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import produce from "immer";
import { ValueType } from "react-select";

import { showNotification } from "components/atoms/base/Notification";
import { BaseSelect } from "components/atoms/base/select/BaseSelect";
import { BaseSelectWithFeatures } from "components/atoms/base/select/BaseSelectWithFeatures";
import BaseSelectCity from "components/atoms/base/select/shared/BaseSelectCity.share";
import BaseSelectDistrict from "components/atoms/base/select/shared/BaseSelectDistrict.share";
import BaseSelectOrderStatus from "components/atoms/base/select/shared/BaseSelectOrderStatus";
import { showLoading } from "components/atoms/base/Spinner";
import { ExternalRegister } from "components/atoms/pulldown";
import { useRadioProvider } from "components/atoms/radio";
import { toastSingleMode } from "components/atoms/toastify";
import { Section } from "components/organisms/section";
import { COLOR } from "constants/color";
import dayjs from "dayjs";
import { motion } from "framer-motion";
import {
  fromDefaltOptionToData,
  mappedDataToDefaultOption,
} from "helpers/convertDataToDefaultOption.helper";
import { formatCurrencyWithSuffix } from "helpers/currency.helper";
import debounce from "helpers/debounce";
import { getErrorMessageViaErrCode } from "helpers/error-messages";
import useDerivedStateFromProps from "helpers/react-hooks/useDerivedStateFromProps";
import useDidMount from "helpers/react-hooks/useDidMount";
import { useAsync } from "hooks/useAsync";
import _, { set } from "lodash";
import {
  createCustomerShippingAddressDto,
  CustomerPickerByPhone,
  CustomerProfileEntityType,
  CustomerShippingAddressModal,
  displayFullAddress,
  primaryEmail,
  shippingAddressDto,
  ShippingAddressDtoType,
  SubmitShippingAddressType,
  updateCustomerShippingAddressDto,
  usePulldownShippingAddress,
} from "modules/customer";
import { usePulldownAssignEmployee } from "modules/employee";
import {
  calcTotalAmount,
  MasterData,
  OrderDetailDtoType,
  orderItemDetailDto,
  usePulldownOrderReturnStatus,
  usePulldownOrderSource,
  usePulldownOrderStatus,
  usePulldownOrderStatusTagline,
  usePulldownReturnStatusTagline,
} from "modules/order";
import { ProductSkuPickerModal, SubmitItem } from "modules/product";
import {
  CustomerUpserModal,
  CustomerUpserModalRef,
  FormCustomerUpserType,
} from "pages/Customer/modal/CustomerUpsertModal";
import { useGetListDeliveryPartnerWithoutPagination } from "pages/Order/ListDeliveryPartner/hooks/useGetListDeliveryPartner";
import { PromotionDto } from "pages/Promotion/promotionManagement/dtos/promotion.dto";
import { useGetListPromotionWithoutPagination } from "pages/Promotion/promotionManagement/hooks/useGetListPromotion";
import { UseGetListShop } from "pages/System/ShopDetail/hooks/useGetListShop";
import { useParams } from "react-router";
import {
  createCustomerShippingAddress,
  CreateCustomerV2,
  getSingleCustomerShippingAddress,
  updateCustomerShippingAddress,
} from "services/crm/customer";
import { UseGetOrderHistory } from "services/crm/order/hooks/useGetOrderHistory";
import "./button.css";
import {
  PaymentOrderModal,
  PaymentOrderModalRefType,
} from "./PaymentOrderModal";
import ListProduct from "./ListProduct";
import HistoryOrderModal from "./historyOrderModal";

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface UncontrolledProperties {
  shippingCost: number;
  applicableFee: number;
  discountValue: number;
  promotionValue: number;
}

type OrderItem = OrderDetailDtoType & {
  orderIdRef: number;
  codeRef: string;
};
type ShippingAddress = ShippingAddressDtoType;

type CustomerItem = CustomerProfileEntityType;

type CustomerUpsertType = Omit<Partial<FormCustomerUpserType>, "birthDay"> & {
  customerId?: number;
  birthDay?: string;
};

type ModalType =
  | "productSkuPicker"
  | "customerPickerByPhone"
  | "customerCreateShippingModal"
  | "customerUpdateShippingModal";

interface State {
  order?: OrderItem;
  modalState: {
    open: boolean;
    type?: ModalType;
  };
  controlledProperties: {
    selectedOrderStatusId?: number;
    selectedOrderStatusTaglineId?: number;
    selectedOrderReturnStatusId?: number;
    selectedOrderReturnStatusTaglineId?: number;
    items: OrderItem["items"];
    totalAmount: number;
    customer?: CustomerItem;
    pay?: number;
    remainingAmount?: number;
    paid?: number;
  };
  customerShippingAddress?: ShippingAddress;
}

export const orderTypeOptions = [
  {
    label: "Đơn hàng đổi",
    value: 1,
  },
  {
    label: "Đơn hàng bán",
    value: 2,
  },
  {
    label: "Đơn hàng trả",
    value: 3,
  },
];

export interface OrderFormProps<DataForm> {
  onSubmit: (formData: DataForm & { totalAmount: number }) => void;
  mode?: "view-only" | "editable" | "create" | "exchange";
  initialOrderDetail?: OrderItem;
  cancelButton?: ReactNode;
  submitButton?: ReactNode;
  handleRefetch?: (idOrder: number) => void;
  navigateToExchangePage?: (params: {
    orderId: number;
    orderStatusId: number;
  }) => void;
  navigateLink?: string;
}

export const OrderFormExchange = <DataForm,>({
  onSubmit,
  mode = "view-only",
  initialOrderDetail,
  cancelButton,
  submitButton,
  handleRefetch,
  navigateToExchangePage,
  navigateLink,
}: OrderFormProps<DataForm>) => {
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  const isMobile = !screens.md && !screens.lg && !screens.xl && !screens.xxl;

  const [form] = Form.useForm();
  const customerUpsertModalRef = useRef<CustomerUpserModalRef>(null);
  const paymentOrderModalRef = useRef<PaymentOrderModalRefType>(null);
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
  const editMode = mode === "editable";
  const createMode = mode === "create";
  const viewMode = mode === "view-only";
  const [discountType, setDiscountType] = useState<"percent" | "VND">("VND");
  const [discountState, setDiscountState] = useState<number>(0);
  const discountTypeRef = useRef<string>("VND");
  const discountValueRef = useRef<number>(0);
  const cityIdValue = Form.useWatch("cityId", form);
  const discountValue = Form.useWatch("discountValue", form);
  const promotionValue = Form.useWatch("promotionValue", form);
  const shopSentValue = Form.useWatch<number>("shopSent", form);
  const firstRenderApiRef = useRef<boolean>(true);
  const orderIdParams = useParams<{ orderId: string }>().orderId;

  const { listShopExe, listShopState } = UseGetListShop();

  const [listMasterData, setListMasterData] = useState<MasterData>({
    delivery: [],
    orderStatus: [],
    listShop: [],
    orderSource: [],
    promotion: [],
    employee: [],
  });

  const { deliveryData, deliveryRefetch } =
    useGetListDeliveryPartnerWithoutPagination();
  const deliveryOption = useMemo(() => {
    if (_.size(deliveryData?.data) > 0) {
      return mappedDataToDefaultOption({
        restItem: true,
        data: deliveryData.data,
        keyValue: "deliveryPartnerId",
        keyLabel: "name",
      });
    } else {
      return [];
    }
  }, [deliveryData]);

  const [promotionSelected, setPromotionSelected] =
    useState<PromotionDto | null>(null);

  const { promotionRefetch, promotionData } =
    useGetListPromotionWithoutPagination();

  const promotionOptions = useMemo(() => {
    if (promotionData?.data) {
      return mappedDataToDefaultOption({
        restItem: true,
        data: promotionData?.data,
        keyValue: "promotionId",
        keyLabel: "promotionName",
        customLabel: (item) => `${item.promotionCode} - ${item.promotionName}`,
      });
    } else {
      return [];
    }
  }, [promotionData]);

  useEffect(() => {
    let value = 0;
    if (promotionSelected) {
      // console.log(promotionSelected, "promotionSelected");
      const totalAmount = state.controlledProperties.totalAmount;
      const { promotionType, promotionValue } = promotionSelected;
      if (promotionType === "percent") {
        value = (totalAmount * promotionValue) / 100 || 0;
        form.setFieldValue("promotionValue", value);
      } else if (promotionType === "amount") {
        value = promotionValue || 0;
        form.setFieldValue("promotionValue", value);
      }
    } else {
      form.setFieldValue("promotionValue", value);
    }
    recalculatePaymentFields({
      promotionValue: value,
    });
  }, [promotionSelected]);

  const uncontrolledPropertiesRef = useRef<UncontrolledProperties>({
    shippingCost: 0,
    applicableFee: 0,
    discountValue: 0,
    promotionValue: 0,
  });

  const shippingAddressSelectRef = useRef<ExternalRegister>();
  const [state, setState] = useState<State>({
    order: undefined,
    modalState: {
      open: false,
      type: undefined,
    },
    controlledProperties: {
      selectedOrderStatusId: undefined,
      selectedOrderStatusTaglineId: undefined,
      selectedOrderReturnStatusId: undefined,
      selectedOrderReturnStatusTaglineId: undefined,
      items: [],
      totalAmount: 0,
      customer: initialOrderDetail?.customer,
      pay: initialOrderDetail?.pay || 0,
      remainingAmount: initialOrderDetail?.remainingAmount || 0,
      paid: initialOrderDetail?.paid || 0,
    },
    customerShippingAddress: initialOrderDetail?.shippingAddress,
  });

  const [
    getSingleCustomerShippingAddressExec,
    getSingleCustomerShippingAddressState,
  ] = useAsync(
    useCallback(
      (payload: { customerId: number; shippingAddressId: number }) =>
        getSingleCustomerShippingAddress({ ...payload }).then((res) => {
          return shippingAddressDto(res.data.data);
        }),
      []
    ),
    {
      onSuccess: useCallback((res) => {
        setState(
          produce((draft) => {
            draft.customerShippingAddress = res;
          })
        );
      }, []),
    }
  );

  const handleChangeShippingAddress = useCallback(
    (option: ValueType<{ label: string; value: string }, false>) => {
      // CHỈ CHECK KHI CÓ ShippingAddressId lưu dưới db (không rõ)
      // if (firstRenderRef.current && !createMode) {
      //   firstRenderRef.current = false;
      //   return;
      // }

      // if (firstSelectedRef.current) {
      //   firstSelectedRef.current = false;
      // }
      getSingleCustomerShippingAddressExec({
        customerId: Number(state.controlledProperties.customer?.customerId),
        shippingAddressId: Number(option?.value),
      });
    },
    [
      createMode,
      getSingleCustomerShippingAddressExec,
      state.controlledProperties.customer?.customerId,
    ]
  );

  const updateDiscountTypeDisplay = useCallback(
    (newType: "percent" | "VND") => {
      setDiscountType(newType);
      discountTypeRef.current = newType;
    },
    []
  );

  const handleChangeDiscountType = useCallback(() => {
    const newDiscountType = discountType === "VND" ? "percent" : "VND";
    setDiscountType(newDiscountType);
    discountTypeRef.current = newDiscountType;
    setDiscountState(0);
    form.setFieldsValue({
      discount: 0,
      discountValue: 0,
    });
  }, [discountType, form]);

  const recalculateDiscountValue = useCallback(
    (value: number) => {
      // const totalValue = form.getFieldValue("totalValue");
      // const discount = form.getFieldValue("discount");
      const totalAmount = Math.abs(state.controlledProperties.totalAmount);
      let discountValue = 0;

      if (_.isNil(value) || value < 0) {
        setDiscountState(0);
        form.setFieldValue("discountValue", 0);
      } else {
        setDiscountState(value);
        if (discountTypeRef.current.includes("VND")) {
          // Nếu là giảm giá theo số tiền cố định

          if (value >= totalAmount) {
            form.setFieldValue("discountValue", discountValue);
          } else {
            discountValue = value;
            form.setFieldValue("discountValue", discountValue);
          }
        } else if (discountTypeRef.current.includes("percent")) {
          // Nếu là giảm giá theo phần trăm

          if (value > 100) {
            form.setFieldValue("discountValue", discountValue);
          } else {
            discountValue = totalAmount ? (totalAmount * value) / 100 : 0;
            form.setFieldValue("discountValue", discountValue);
          }
        }
      }

      recalculatePaymentFields({
        discountValue,
      });
    },
    [discountType, form, discountState, state]
  );

  const handleOnCloseModal = useCallback(() => {
    if (!state.modalState.open) return;
    setState(
      produce((draft) => {
        draft.modalState.open = false;
        draft.modalState.type = undefined;
      })
    );
  }, [state.modalState]);

  const {
    fetchCustomerShippingAddress,
    shippingAddressOptions: customerShippingAddressOptions,
    formatShippingAddressOption,
    fetchCustomerShippingAddressLoading,
  } = usePulldownShippingAddress({
    excludePending: true,
  });

  const {
    orderStatusesOptions,
    isDefaultOrderStatus,
    fetchOrderStatus,
    getOptionByValue: getOrderStatusOptionById,
  } = usePulldownOrderStatus({
    excludePending: true,
  });

  const {
    fetchOrderStatusTagline,
    orderStatusTaglineOptions,
    orderStatusTaglines,
    getOptionByValue: getTaglineOptionById,
  } = usePulldownOrderStatusTagline({
    excludePending: true,
    orderStatusId: state.controlledProperties.selectedOrderStatusId,
  });

  const {
    fetchOrderReturnStatus,
    orderReturnStatusOptions,
    getOptionByValue: getReturnStatusOptionById,
  } = usePulldownOrderReturnStatus({
    excludePending: true,
  });

  const {
    fetchReturnOrderStatusTagline,
    orderReturnStatusTaglineOptions,
    orderReturnStatusTaglines,
    getOptionByValue: getReturnStatusTaglineOptionById,
  } = usePulldownReturnStatusTagline({
    excludePending: true,
    orderReturnStatusId: state.controlledProperties.selectedOrderReturnStatusId,
  });

  const {
    loadMoreEmployee,
    assignEmployeeOptions,
    loadMoreEmployeeState,
    formatAssignEmployeeOption,
  } = usePulldownAssignEmployee();

  const selectedStatusTagline =
    getTaglineOptionById(
      state.controlledProperties.selectedOrderStatusTaglineId
    ) || null;

  const selectedOrderStatus =
    getOrderStatusOptionById(
      state.controlledProperties.selectedOrderStatusId
    ) || null;

  const selectedReturnStatus =
    getReturnStatusOptionById(
      state.controlledProperties.selectedOrderReturnStatusId
    ) || null;

  const selectedReturnStatusTagline =
    getReturnStatusTaglineOptionById(
      state.controlledProperties.selectedOrderReturnStatusTaglineId
    ) || null;

  const requestReCalcTotalAmount = useCallback(
    (payload?: { items?: []; discountValue?: number }) => {
      const items = payload?.items || state.controlledProperties.items || [];
      const nextTotalAmount = calcTotalAmount({
        items,
      });

      setState((prevState) => ({
        ...prevState,
        controlledProperties: {
          ...prevState.controlledProperties,
          totalAmount: nextTotalAmount,
          items: payload?.items ?? prevState.controlledProperties.items,
        },
      }));
    },
    [state.controlledProperties.items]
  );

  const initialOrderState = useCallback((order: OrderItem) => {
    uncontrolledPropertiesRef.current = {
      shippingCost: order.shippingCost ?? 0,
      applicableFee: order.applicableFee ?? 0,
      discountValue: order.discountValue ?? 0,
      promotionValue: order.promotionValue ?? 0,
    };
    setState(
      produce((draft) => {
        // console.log(order, "orderorderorderorder");
        draft.controlledProperties = {
          selectedOrderStatusId: order.orderStatusId,
          selectedOrderStatusTaglineId: order.orderStatusTaglineId,
          selectedOrderReturnStatusId: order.returnOrderStatusId,
          selectedOrderReturnStatusTaglineId: order.returnOrderStatusTaglineId,
          paid: order.paid || 0,
          remainingAmount: order.remainingAmount || 0,
          pay: order.pay || 0,
          items:
            order.items.map((item) => ({
              ...item,
              discountAmount: item.discountAmount || 0,
              discountPercent: item.discountPercent || 0,
            })) ?? [],
          totalAmount: calcTotalAmount({
            items:
              order.items.map((item) => ({
                ...item,
                discountAmount: item.discountAmount || 0,
                discountPercent: item.discountPercent || 0,
              })) ?? [],
          }),
          customer: !_.isNil(order?.customer?.customerId)
            ? order?.customer
            : {
                ...order.customer,
                customerId: order?.customerId,
              },
        };
      })
    );
  }, []);

  const setOrderProperties = useCallback(
    (properties: Partial<State["controlledProperties"]>) => {
      setState(
        produce((draft) => {
          draft.controlledProperties = Object.assign(
            draft.controlledProperties,
            properties
          );
        })
      );
    },
    []
  );

  const resetShippingAddress = useCallback(() => {
    setState(
      produce((draft) => {
        draft.customerShippingAddress = undefined;
      })
    );
  }, []);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onAssignEmployeePulldownInputChange = useCallback(
    debounce(
      (textSearch: string) => loadMoreEmployee({ name: textSearch }),
      200
    ),
    [loadMoreEmployee]
  );

  const modalTypeIsOpen = useCallback(
    (type: ModalType) =>
      state.modalState.open && state.modalState.type === type,
    [state.modalState]
  );

  const handleOnSubmitProductSkuPickerForm = useCallback(
    (skuItems: SubmitItem[]) => {
      setState(
        produce((draft) => {
          if (!draft.controlledProperties.items) {
            draft.controlledProperties.items = [];
          }
          skuItems.forEach((skuItemFromPicker) => {
            const skuItemExisted = draft.controlledProperties.items!.find(
              (skuItem: { skuId: string }) =>
                skuItem.skuId === skuItemFromPicker.sku.skuId
            );
            if (skuItemExisted) {
              skuItemExisted.quantity += skuItemFromPicker.quantity;
              skuItemExisted.totalAmount += skuItemFromPicker.totalAmount;
            } else {
              draft.controlledProperties.items?.push(
                orderItemDetailDto({
                  ...skuItemFromPicker.sku,
                  sku: skuItemFromPicker.sku,
                  quantity: skuItemFromPicker.quantity,
                  unitPrice: skuItemFromPicker.unitPrice,
                  totalAmount: skuItemFromPicker.totalAmount,
                  listedPrice: skuItemFromPicker.price,
                  // NEW //
                  discountPercent: 0,
                  discountAmount: 0,
                })
              );
            }
          });
        })
      );
    },
    []
  );

  const handleRemoveItemByIndex = useCallback((index: number) => {
    setState(
      produce((draft) => {
        const nextItems = [...(draft.controlledProperties.items || [])];
        nextItems.splice(index, 1);
        draft.controlledProperties.items = nextItems;
      })
    );
  }, []);

  const handleUpdateItemByIndex = useCallback(
    (
      index: number,
      field: "quantity" | "discountPercent" | "discountAmount",
      value: number,
      isExchange?: boolean
    ) => {
      if (isExchange) {
        if (value >= 0) return;
        setState(
          produce((draft) => {
            const item = draft.controlledProperties.items?.[index];
            if (!item) return;

            if (field === "discountPercent") {
              item[field] = parseFloat(value.toFixed(8));
            } else {
              item[field] = value;
            }

            switch (field) {
              case "quantity":
                if (value >= 0) {
                  item.quantity = -1;
                }
                // ❌ Không tính lại discountAmount ở đây (theo yêu cầu)
                break;
            }

            // ✅ Tính lại tổng tiền cuối cùng
            const totalDiscount = (item.discountAmount || 0) * item.quantity;
            item.totalAmount = item.unitPrice * item.quantity - totalDiscount;

            if (item.totalAmount > 0) {
              item.totalAmount = 0;
            }
          })
        );
      } else {
        if (value < 0) return;

        setState(
          produce((draft) => {
            const item = draft.controlledProperties.items?.[index];
            if (!item) return;

            if (field === "discountPercent") {
              item[field] = parseFloat(value.toFixed(8));
            } else {
              item[field] = value;
            }

            switch (field) {
              case "quantity":
                if (value < 1) {
                  item.quantity = 1;
                }
                // ❌ Không tính lại discountAmount ở đây (theo yêu cầu)
                break;

              case "discountPercent":
                if (value > 100) {
                  item.discountPercent = parseFloat((100).toFixed(8));
                }
                // ✅ Tính tổng discountAmount từ discountPercent
                const totalPrice = item.unitPrice * item.quantity;
                const totalDiscount = (totalPrice * item.discountPercent) / 100;

                // ✅ Gán lại discountAmount theo từng sản phẩm
                item.discountAmount = totalDiscount / item.quantity;
                break;

              case "discountAmount":
                const maxPerItemDiscount = item.unitPrice;
                if (value > maxPerItemDiscount) {
                  item.discountAmount = maxPerItemDiscount;
                }

                const totalDiscountAmount = item.discountAmount * item.quantity;
                const totalPriceBeforeDiscount = item.unitPrice * item.quantity;

                item.discountPercent =
                  totalPriceBeforeDiscount > 0
                    ? parseFloat(
                        (
                          (totalDiscountAmount / totalPriceBeforeDiscount) *
                          100
                        ).toFixed(8)
                      )
                    : 0;

                // Đảm bảo không vượt quá 100%
                if (item.discountPercent > 100) {
                  item.discountPercent = parseFloat((100).toFixed(8));
                  item.discountAmount = item.unitPrice; // tối đa giảm bằng đơn giá
                }
                break;
            }

            // ✅ Tính lại tổng tiền cuối cùng
            const totalDiscount = (item.discountAmount || 0) * item.quantity;

            item.totalAmount = item.unitPrice * item.quantity - totalDiscount;
            if (item.totalAmount < 0) {
              item.totalAmount = 0;
            }
          })
        );
      }
    },
    []
  );

  const handleOnSubmit = useCallback(
    (formData) => {
      if (!state.controlledProperties.items?.length) {
        showNotification({
          type: "error",
          message: "Vui lòng chọn ít nhất một sản phẩm",
        });

        return;
      }

      // Format tất cả discountPercent về 8 chữ số thập phân trước khi gửi
      const formattedItems = state.controlledProperties.items.map((item) => ({
        ...item,
        discountPercent: parseFloat(item.discountPercent.toFixed(8)),
      }));

      const {
        orderSourceId,
        shipping,
        assignedEmployeeId,
        orderStatusTaglineId,
        isShopSent,
        shopSent,
        shopSentNote,
      } = formData;

      onSubmit({
        ...formData,
        // isShopSent,
        // shopSent: isShopSent ? shopSent : null,
        // shopSentNote: isShopSent ? shopSentNote : null,
        orderSourceId: Number(orderSourceId),
        shipping: shipping,
        assignedEmployeeId: Number(assignedEmployeeId),
        orderStatusTaglineId: Number(orderStatusTaglineId),
        // personalInfo,
        discount: discountState,
        discountType: discountTypeRef.current,
        customer: state.controlledProperties.customer,
        customerId: state.controlledProperties.customer?.customerId,
        totalAmount: state.controlledProperties.totalAmount,
        items: formattedItems,
      });
    },
    [
      onSubmit,
      state.controlledProperties.totalAmount,
      state.controlledProperties.customer,
      state.controlledProperties.items,
      discountState,
    ]
  );

  const handleOnFocusCustomerPhoneInput = useCallback(
    (event: FocusEvent<HTMLInputElement>) => {
      setState(
        produce((draft) => {
          draft.modalState.type = "customerPickerByPhone";
          draft.modalState.open = true;
        })
      );
      event.currentTarget.blur();
    },
    []
  );

  const handleOpenModalByType = (type: ModalType) => {
    setState(
      produce((draft) => {
        draft.modalState.open = true;
        draft.modalState.type = type;
      })
    );
  };

  const { fetchOrderSources, formatOrderSourceOption, orderSourceOptions } =
    usePulldownOrderSource({
      excludePending: true,
    });

  useDerivedStateFromProps((_, nextOrderItem) => {
    if (nextOrderItem) {
      initialOrderState(nextOrderItem);
    }
  }, initialOrderDetail);

  useDerivedStateFromProps((_, nextOrderStatusTagline) => {
    if (nextOrderStatusTagline) {
      const shouldShowDefault = nextOrderStatusTagline.find(
        ({ taglineId }) =>
          taglineId === initialOrderDetail?.orderStatusTaglineId
      );
      setOrderProperties({
        selectedOrderStatusTaglineId: shouldShowDefault?.taglineId,
      });
    }
  }, orderStatusTaglines);

  useDerivedStateFromProps((_, nextOrderReturnStatusTaglines) => {
    if (nextOrderReturnStatusTaglines) {
      const shouldShowDefault = nextOrderReturnStatusTaglines.find(
        ({ returnOrderStatusTaglineId }) =>
          returnOrderStatusTaglineId ===
          initialOrderDetail?.returnOrderStatusTaglineId
      );
      setOrderProperties({
        selectedOrderReturnStatusTaglineId:
          shouldShowDefault?.returnOrderStatusTaglineId,
      });
    }
  }, orderReturnStatusTaglines);

  useDerivedStateFromProps(() => {
    requestReCalcTotalAmount();
  }, state.controlledProperties.items);

  useDerivedStateFromProps((prevCustomer) => {
    if (!prevCustomer) return;
    if (shippingAddressSelectRef.current) {
      shippingAddressSelectRef.current.reset();
    }
  }, state.controlledProperties.customer);

  useEffect(() => {
    if (state.controlledProperties.customer) {
      fetchCustomerShippingAddress(
        state.controlledProperties.customer.customerId
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.controlledProperties.customer]);

  useLayoutEffect(() => {
    fetchOrderStatusTagline();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.controlledProperties.selectedOrderStatusId]);

  useLayoutEffect(() => {
    fetchReturnOrderStatusTagline();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.controlledProperties.selectedOrderReturnStatusId]);

  useEffect(() => {
    const customerId = initialOrderDetail?.customerId;
    if (customerId) {
      fetchCustomerShippingAddress(customerId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialOrderDetail?.customer?.customerId]);

  useDidMount(() => {
    const loadData = async () => {
      try {
        const [
          orderStatusData,
          orderSourcesData,
          listShop,
          deliveryData,
          promotionData,
          // orderReturnStatusData,
        ] = await Promise.all([
          fetchOrderStatus(),
          fetchOrderSources(),
          listShopExe(),
          deliveryRefetch(),
          promotionRefetch(),
          // fetchOrderReturnStatus(),
          loadMoreEmployee(),
        ]);
        setListMasterData({
          orderStatus: orderStatusData,
          orderSource: orderSourcesData,
          listShop: listShop?.data?.data,
          delivery: deliveryData?.data?.data,
          promotion: promotionData?.data?.data,
        });
      } catch (error) {
        console.error("Error loading initial data:", error);
        toastSingleMode({
          type: "error",
          message: "Lỗi khi tải dữ liệu ban đầu",
          descripition: "Vui lòng thử lại sau.",
        });
      }
    };
    loadData();
    // fetchOrderStatus();
    // fetchOrderReturnStatus();
    // fetchOrderSources();
    // loadMoreEmployee();
    // listShopExe();
    // deliveryRefetch();
    // promotionRefetch();
  });

  const { bindSilent } = useRadioProvider<{
    gender: "GENDER_MALE" | "GENDER_FEMALE";
  }>({
    defaultValue: {
      gender: "GENDER_MALE",
    },
  });

  const handleSubmitShippingAddressByMode = useCallback(
    async (formData: SubmitShippingAddressType) => {
      if (!state.controlledProperties.customer) return;
      const { customerId } = state.controlledProperties.customer;
      if (modalTypeIsOpen("customerCreateShippingModal")) {
        await createCustomerShippingAddress(
          customerId,
          createCustomerShippingAddressDto(formData)
        );
      }

      if (
        modalTypeIsOpen("customerUpdateShippingModal") &&
        state.customerShippingAddress
      ) {
        await updateCustomerShippingAddress(
          customerId,
          state.customerShippingAddress.shippingAddressId,
          updateCustomerShippingAddressDto(formData)
        );
      }
    },
    [
      modalTypeIsOpen,
      state.controlledProperties.customer,
      state.customerShippingAddress,
    ]
  );

  const [submitCustomerShippingAddressExec] = useAsync(
    handleSubmitShippingAddressByMode,
    {
      excludePending: true,
      onFailed: useCallback((error) => {
        const errMessage = getErrorMessageViaErrCode(
          error?.response?.data?.errors?.[0]?.code
        );
        toastSingleMode({
          type: "error",
          message: errMessage.translation.title,
          descripition: errMessage.translation.detail,
        });
      }, []),
      onSuccess: useCallback(() => {
        if (!state.controlledProperties.customer) return;
        const create = modalTypeIsOpen("customerCreateShippingModal");
        toastSingleMode({
          type: "success",
          message: `${create ? "Tạo" : "Cập nhật"} địa chỉ mới thành công`,
        });

        const { customerId } = state.controlledProperties.customer;

        handleOnCloseModal();
        fetchCustomerShippingAddress(customerId);
        if (!create && state.customerShippingAddress) {
          getSingleCustomerShippingAddressExec({
            customerId,
            shippingAddressId: state.customerShippingAddress.shippingAddressId,
          });
        }
      }, [
        fetchCustomerShippingAddress,
        getSingleCustomerShippingAddressExec,
        handleOnCloseModal,
        modalTypeIsOpen,
        state.controlledProperties.customer,
        state.customerShippingAddress,
      ]),
    }
  );

  // Define columns for product table
  const productColumns = [
    {
      title: "STT",
      key: "index",
      width: 60,
      render: (_: any, __: any, index: number) => index + 1,
      align: "center" as const,
    },
    {
      title: "Tên sản phẩm",
      dataIndex: ["sku", "name"],
      key: "name",
      width: 200,
    },
    {
      title: "Màu sắc",
      dataIndex: ["sku", "color", "name"],
      key: "color",
      align: "center" as const,
      width: 100,
    },

    {
      title: "Size",
      dataIndex: ["sku", "size", "name"],
      key: "size",
      align: "center" as const,
      width: 80,
    },

    {
      title: "Số lượng",
      dataIndex: "quantity",
      key: "quantity",
      align: "center" as const,
      width: 100,
      render: (quantity: number, record: any, index: number) => {
        const { isExchange, minQuantity, maxQuantity } = record ?? {};
        return (
          <InputNumber
            disabled={viewMode}
            // disabled={viewMode || initialOrderDetail?.orderStatusId !== 15}
            min={isExchange ? minQuantity : 1}
            max={isExchange ? maxQuantity : undefined}
            value={quantity}
            onChange={(value) =>
              handleUpdateItemByIndex(
                index,
                "quantity",
                Number(value),
                isExchange
              )
            }
            style={{ width: 70 }}
          />
        );
      },
    },
    {
      title: "Giá bán",
      dataIndex: ["sku", "salePrice"],
      key: "salePrice",
      align: "center" as const,
      width: 100,
      render: (salePrice: number) => `${formatCurrencyWithSuffix(salePrice)}`,
    },

    {
      title: "Giảm giá (%)",
      key: "discountPercent",
      dataIndex: "discountPercent",
      align: "center" as const,
      width: 120,
      render: (discountPercent: number, record: any, index: number) => {
        const { isExchange } = record ?? {};
        return (
          <InputNumber
            disabled={viewMode || isExchange}
            // disabled={
            //   viewMode || isExchange || initialOrderDetail?.orderStatusId !== 15
            // }
            className="w-full"
            min={isExchange ? undefined : 0}
            max={isExchange ? undefined : 100}
            precision={2} // Vẫn giữ precision cao để xử lý chính xác
            value={discountPercent || 0}
            formatter={(value) => {
              if (!value) return "";
              const numberValue = Number(value);
              return isNaN(numberValue) ? "" : numberValue.toFixed(2); // Hiển thị 2 số thập phân
            }}
            parser={(value) => {
              if (!value) return 0;
              const parsed = parseFloat(value.replace(/[^\d.-]/g, ""));
              return isNaN(parsed) ? 0 : parsed;
            }}
            onChange={(value) =>
              handleUpdateItemByIndex(
                index,
                "discountPercent",
                Number(value),
                isExchange
              )
            }
          />
        );
      },
    },
    {
      title: "Giảm giá (VND)",
      key: "discountAmount",
      dataIndex: "discountAmount",
      align: "center" as const,
      width: 120,
      render: (discountAmount: number, record: any, index: number) => {
        const { isExchange } = record ?? {};
        return (
          <InputNumber
            disabled={viewMode || isExchange}
            // disabled={
            //   viewMode || isExchange || initialOrderDetail?.orderStatusId !== 15
            // }
            min={isExchange ? undefined : 0}
            className="w-full"
            value={discountAmount || 0}
            formatter={(value) =>
              value !== undefined && value !== null
                ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                : ""
            }
            // parser={(value) => {
            //   if (!value) return 0;
            //   const parsed = parseFloat(value.replace(/[^\d.-]/g, ""));
            //   return isNaN(parsed) ? 0 : parsed;
            // }}
            onChange={(value) =>
              handleUpdateItemByIndex(
                index,
                "discountAmount",
                Number(value),
                isExchange
              )
            }
          />
        );
      },
    },

    {
      title: "Thành tiền",
      dataIndex: "totalAmount",
      key: "totalAmount",
      align: "center" as const,
      width: 120,
      render: (totalAmount: number) =>
        `${formatCurrencyWithSuffix(totalAmount)}`,
    },
    ...(!viewMode &&
    (initialOrderDetail
      ? initialOrderDetail?.orderStatusId === 15 ||
        initialOrderDetail?.orderStatusId === 2
      : true)
      ? [
          {
            title: "Thao tác",
            key: "action",
            align: "center" as const,
            width: 100,
            render: (_: any, __: any, index: number) => (
              <BaseButton
                type="text"
                danger
                icon={<DeleteOutlined rev={undefined} />}
                onClick={() => handleRemoveItemByIndex(index)}
              />
            ),
          },
        ]
      : []),
  ];
  const handleOpenHistoryOrder = useCallback(() => {
    setIsOpenModal(true);
  }, [isOpenModal]);

  const { createCustomerExe } = CreateCustomerV2();

  const onFinishCustomerUpsert = (values: CustomerUpsertType) => {
    const { customerId, ...restProps } = values ?? {};

    showLoading(true);
    if (customerId) {
      const payloadUpdate = {
        ...values,
      };
      // console.log("Customer Upserted:", values);
    } else {
      const payloadInsert = {
        ...restProps,
      };
      createCustomerExe(payloadInsert)
        .then(async (res) => {
          if (res.status === 200) {
            showNotification({
              type: "success",
              message: "Tạo mới khách hàng thành công",
            });
            const { address, ...restProps } = res?.data?.data ?? {};
            setState(
              produce((draft) => {
                draft.controlledProperties.customer = restProps;
              })
            );
            customerUpsertModalRef.current?.close();
          }
        })
        .catch((err) => {
          const apiError = err?.response?.data?.errors;
          // console.log(apiError, "apiError");
          if (apiError) {
            apiError.forEach((item: any) => {
              showNotification({
                type: "error",
                message: item?.detail || item?.title,
              });
            });
          }
        })
        .finally(() => {
          showLoading(false);
        });
    }
  };

  useEffect(() => {
    if (initialOrderDetail) {
      const {
        items,
        createdAt,
        customer,
        discountProducts,
        assignedEmployeeId,
        payment,
        orderSourceId,
        // personalInfo,
        shippingAddress,
        voucherCodes,
        orderStatusId,
        orderType,
        persionalInfo,
        discount,
        discountType,
        discountValue,
        shipping,
        orderStatusTaglineId,
        customerHeight,
        customerWeight,
        customerWaist,
        birthDay,
        name,
        email,
        // OPTIONS
        assignedEmployee,
        orderSource,
        orderStatus,
        shippingDetail,
        cityId,
        districtId,
        wardId,
        voucherId,
        shopSent,
        phoneNumber: phoneNumberFromOrder,
        ...rest
      } = initialOrderDetail;
      const {
        phoneNumber,
        phoneNumber2,
        phoneNumbers,
        name: customerName,
      } = customer ?? {};
      const isPrimaryEmail = primaryEmail(customer) || "";
      const { shippingAddressId } = shippingAddress ?? {};

      if (discountType) {
        const newDiscountType = discountType.includes("percent")
          ? "percent"
          : "VND";
        updateDiscountTypeDisplay(newDiscountType);
      }
      if (discount) {
        setDiscountState(discount);
      }

      discountValueRef.current = discountValue;
      if (voucherId && promotionData?.data) {
        const promotion = fromDefaltOptionToData({
          data: promotionData?.data,
          value: voucherId,
          valueKey: "promotionId",
        });
        setPromotionSelected(promotion as PromotionDto);
      }
      form.setFieldsValue({
        ...rest,
        voucherId,
        createdAt: createdAt ? dayjs(createdAt) : null,
        orderType,
        phoneNumber: phoneNumberFromOrder || phoneNumber2 || phoneNumber || "",
        shipping,
        shopSent: shopSent ? Number(shopSent) : null,
        email: email || isPrimaryEmail,
        // shippingAddressId: String(shippingAddressId),
        orderStatusId,
        assignedEmployeeId: String(assignedEmployeeId),
        orderStatusTaglineId: orderStatusTaglineId
          ? String(orderStatusTaglineId)
          : null,
        orderSourceId: String(orderSourceId),
        phoneNumbers,
        name: name || customerName,
        districtId,
        cityId,
        wardId,
        customerWaist: customerWaist,
        customerWeight: customerWeight,
        customerHeight: customerHeight,
        discount: discount,
        discountValue: discountValue,
        birthDay: dayjs(birthDay),
      });

      // tính lại vì số đã đổi thành âm

      const isTotalAmount = calcTotalAmount({
        // applicableFee: rest.applicableFee,
        // shippingCost: rest.shippingCost,
        items: items || [],
      });
      recalculatePaymentFields();
      form.setFieldsValue({
        totalAmount: isTotalAmount,
      });
    } else {
      if (isDefaultOrderStatus) {
        form.setFieldsValue({
          orderStatusId: Number(isDefaultOrderStatus.orderStatusId),
        });
        setOrderProperties({
          selectedOrderStatusId: Number(isDefaultOrderStatus.orderStatusId),
        });
      }
    }
    firstRenderApiRef.current = false;
  }, [initialOrderDetail, isDefaultOrderStatus, promotionData]);

  const recalculatePaymentFields = useCallback(
    (payload?: {
      shippingCost?: number;
      applicableFee?: number;
      discountValue?: number;
      promotionValue?: number;
    }) => {
      const { totalAmount } = state.controlledProperties;

      const shippingCost =
        payload?.shippingCost ??
        (uncontrolledPropertiesRef.current.shippingCost || 0);
      const applicableFee =
        payload?.applicableFee ??
        (uncontrolledPropertiesRef.current.applicableFee || 0);

      const valueDiscount = payload?.discountValue ?? (discountValue || 0);
      const valuePromotion = payload?.promotionValue ?? (promotionValue || 0);

      uncontrolledPropertiesRef.current = {
        ...uncontrolledPropertiesRef.current,
        shippingCost,
        applicableFee,
        discountValue: valueDiscount,
        promotionValue: valuePromotion,
      };

      const paid = form.getFieldValue("paid") || 0;
      const pay =
        totalAmount +
        (shippingCost || 0) +
        (applicableFee || 0) -
        (valueDiscount || 0) -
        (valuePromotion || 0);

      const remainingAmount = pay - paid;

      // Cập nhật form
      form.setFieldsValue({
        pay,
        remainingAmount,
      });

      // Cập nhật state
      setState(
        produce((draft) => {
          draft.controlledProperties.pay = pay;
          draft.controlledProperties.remainingAmount = remainingAmount;
          draft.controlledProperties.paid = paid;
        })
      );
    },
    [state, form]
  );

  const handleOnShippingCostInputChange = useCallback(
    debounce(
      (event: ChangeEvent<HTMLInputElement>) =>
        recalculatePaymentFields({
          shippingCost: Number(event.target.value) || 0,
        }),
      200
    ),
    [recalculatePaymentFields]
  );

  const handleOnApplicableFeeInputChange = useCallback(
    debounce(
      (event: ChangeEvent<HTMLInputElement>) =>
        recalculatePaymentFields({
          applicableFee: Number(event.target.value) || 0,
        }),
      200
    ),
    [recalculatePaymentFields]
  );

  // tính tổng và xử lý customer
  useEffect(() => {
    const { totalAmount } = state.controlledProperties ?? {};
    if (totalAmount) {
      recalculatePaymentFields();
    }
  }, [state, form, promotionValue, discountValue]);

  useEffect(() => {
    if (state.controlledProperties.items.length > 0) {
      form.setFieldValue("totalAmount", state.controlledProperties.totalAmount);
      recalculatePaymentFields();
    } else {
      form.setFieldValue("totalAmount", 0);
      recalculatePaymentFields();
    }
  }, [
    state.controlledProperties.totalAmount,
    state.controlledProperties.items,
  ]);

  useEffect(() => {
    if (getSingleCustomerShippingAddressState?.data) {
      const { cityId, districtId } =
        getSingleCustomerShippingAddressState?.data || {};

      const shippingAddressDetail = displayFullAddress(
        getSingleCustomerShippingAddressState?.data
      );
      form.setFieldsValue({
        cityId,
        districtId,
        shippingAddressDetail,
      });
      setState(
        produce((draft) => {
          draft.customerShippingAddress =
            getSingleCustomerShippingAddressState.data;
        })
      );
    } else {
      setState(
        produce((draft) => {
          draft.customerShippingAddress = undefined;
        })
      );
      form.setFieldsValue({
        cityId: null,
        districtId: null,
      });
    }
  }, [getSingleCustomerShippingAddressState]);

  // useEffect(() => {
  //   console.log(state, "state in OrderDetail");
  // }, [state]);

  // useEffect(() => {
  //   if (state.controlledProperties.items.length > 0) {
  //     const amount = state.controlledProperties.items.reduce(
  //       (amount, { quantity, unitPrice }) => amount + quantity * unitPrice,
  //       0
  //     );
  //     form.setFieldValue("totalValue", amount);
  //   } else {
  //     form.setFieldValue("totalValue", 0);
  //   }
  // }, [state.controlledProperties.items]);

  return (
    <>
      <title>Thông tin đơn hàng</title>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleOnSubmit}
        initialValues={{
          channel: "online",
          // isShopSent: false,
          paid: 0,
          orderType: 1,
          shippingCost: 0,
          applicableFee: 0,
          discount: 0,
          discountValue: 0,
        }}
      >
        <div
          style={{
            marginTop: 16,
            marginLeft: "2.5%",
            display: "flex",
            justifyContent: "flex-start",
            gap: 16,
            alignItems: "center",
            flexWrap: "wrap",
          }}
        >
          {cancelButton}
          {viewMode ? <></> : <>{submitButton}</>}
          {/* {viewMode && initialOrderDetail?.orderStatusId === 21 ? (
            <BaseButton
              type="primary"
              bgColor={COLOR.BLUE[500]}
              hoverColor={COLOR.BLUE[700]}
              icon={<RetweetOutlined />}
              onClick={() => {
                navigateToExchangePage({
                  orderId: initialOrderDetail?.orderId,
                  orderStatusId: initialOrderDetail?.orderStatusId,
                });
              }}
            >
              Đổi hàng
            </BaseButton>
          ) : null} */}
        </div>

        <Section>
          <Card
            title={
              <Title level={4} style={{ margin: 0 }}>
                CHI TIẾT ĐƠN HÀNG: {initialOrderDetail?.code}
              </Title>
            }
            // style={{ marginLeft: "2.5%", marginRight: "2.5%" }}
            className="mx-[2.5%] my-4"
            extra={
              <Flex>
                <BaseButton
                  type="primary"
                  bgColor={COLOR.GRAY[200]}
                  hoverColor={COLOR.GRAY[300]}
                  className="text-black hover:!text-black"
                  onClick={() => {
                    handleOpenHistoryOrder();
                  }}
                >
                  Lịch sử chỉnh sửa đơn hàng
                </BaseButton>
              </Flex>
            }
          >
            <Card type="inner" title="Chi tiết đơn hàng">
              <AntRow gutter={[16, 16]}>
                {/** ĐƠN HÀNG */}
                <AntCol xs={24} lg={{ span: 12 }}>
                  <Collapse defaultActiveKey={["1"]}>
                    <Collapse.Panel key="1" header="Đơn hàng">
                      {/* <Form layout="vertical"> */}
                      <AntRow gutter={[16, 16]}>
                        <>
                          <Form.Item
                            hidden
                            label="orderIdRef"
                            name="orderIdRef"
                          >
                            <Input
                              disabled
                              placeholder="Nhập mã đơn hàng gốc"
                            />
                          </Form.Item>

                          <AntCol xs={24} md={24}>
                            <Space.Compact className="w-full">
                              <Form.Item
                                className="mb-0 w-full"
                                label="Mã Đơn hàng gốc"
                                name="codeRef"
                              >
                                <Input
                                  disabled
                                  placeholder="Nhập mã đơn hàng gốc"
                                />
                              </Form.Item>
                              <BaseButton
                                className={`!px-4 translate-y-[30px]`}
                                type="primary"
                                bgColor={COLOR.BLUE[500]}
                                hoverColor={COLOR.BLUE[700]}
                                onClick={() => {
                                  window.open(navigateLink, "_blank"); // Mở tab mới
                                }}
                              >
                                Mở trang đơn hàng gốc
                              </BaseButton>
                            </Space.Compact>
                          </AntCol>
                        </>

                        <AntCol xs={24} md={12}>
                          <Form.Item
                            label="Loại đơn hàng"
                            name="orderType"
                            rules={[
                              {
                                required: true,
                                message: "Vui lòng chọn loại đơn hàng",
                              },
                            ]}
                          >
                            <BaseSelect
                              disabled
                              placeholder="Chọn loại đơn hàng"
                              options={orderTypeOptions}
                            />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24} md={12}>
                          <Form.Item
                            label="Kênh bán hàng"
                            name="channel"
                            rules={[
                              {
                                required: true,
                                message: "Vui lòng chọn kênh bán hàng",
                              },
                            ]}
                          >
                            <BaseSelect
                              placeholder="Chọn kênh bán hàng"
                              disabled={viewMode}
                              options={[
                                { label: "Online", value: "online" },
                                { label: "Offline", value: "offline" },
                              ]}
                              style={{ width: "100%" }}
                            />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24} md={12}>
                          <Form.Item
                            label="Nguồn"
                            name="orderSourceId"
                            rules={[
                              {
                                required: true,
                                message: "Nguồn đơn là bắt buộc",
                              },
                            ]}
                          >
                            <BaseSelect
                              placeholder="Nguồn"
                              disabled={viewMode}
                              options={orderSourceOptions}
                            />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24} md={12}>
                          <Form.Item label="Ngày tạo" name="createdAt">
                            <DatePicker
                              // disabled={viewMode}
                              disabled
                              style={{ width: "100%" }}
                              placeholder={dayjs().format("DD/MM/YYYY")}
                              format="DD/MM/YYYY"
                            />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24} md={12}>
                          <Form.Item label="Vận chuyển" name="shipping">
                            <BaseSelect
                              key="shipping"
                              placeholder="Vận chuyển"
                              disabled={viewMode}
                              style={{ width: "100%" }}
                              options={deliveryOption}
                              showSearch
                            />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24} md={12}>
                          <Form.Item label="Mã bưu kiện" name="zipcode">
                            <Input disabled={viewMode} />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24} md={12}>
                          <Form.Item
                            label="Trạng thái"
                            name="orderStatusId"
                            // initialValue={selectedOrderStatus}
                            rules={[
                              {
                                required: true,
                                message: "Vui lòng chọn trạng thái đơn hàng",
                              },
                            ]}
                          >
                            <BaseSelectOrderStatus
                              placeholder="Chọn trạng thái đơn hàng"
                              disabled={viewMode}
                              currentValue={initialOrderDetail?.orderStatusId}
                              onChange={(value) => {
                                if (value) {
                                  setOrderProperties({
                                    selectedOrderStatusId: Number(value),
                                  });
                                  form.setFieldValue(
                                    "orderStatusTaglineId",
                                    null
                                  );
                                }
                              }}
                            />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24} md={12}>
                          <Form.Item
                            label="Tagline"
                            name="orderStatusTaglineId"
                            // initialValue={selectedStatusTagline}
                          >
                            <BaseSelect
                              options={orderStatusTaglineOptions}
                              disabled={viewMode}
                              onChange={(value) =>
                                value &&
                                setOrderProperties({
                                  selectedOrderStatusTaglineId: Number(value),
                                })
                              }
                              placeholder="Tagline"
                            />
                          </Form.Item>
                        </AntCol>

                        <Form.Item
                          noStyle
                          shouldUpdate={(prev, curr) =>
                            curr.orderStatusId === 18
                          }
                        >
                          {({ getFieldValue }) => {
                            const orderStatusId =
                              getFieldValue("orderStatusId");

                            const isCancelOrder = Number(orderStatusId) === 18;

                            return (
                              <AntCol
                                xs={24}
                                md={24}
                                style={{
                                  display: isCancelOrder ? "block" : "none",
                                }}
                              >
                                <Form.Item
                                  label="Lý do hủy"
                                  name="cancelReason"
                                  // style={{
                                  //   display: isCancelOrder ? "block" : "none",
                                  // }}
                                  rules={[
                                    {
                                      required: isCancelOrder,
                                      message: "Vui lòng nhập lý do hủy",
                                    },
                                  ]}
                                >
                                  <Input.TextArea
                                    allowClear
                                    rows={3}
                                    placeholder="Vui lòng nhập lý do hủy"
                                  />
                                </Form.Item>
                              </AntCol>
                            );
                          }}
                        </Form.Item>

                        <AntCol xs={24}>
                          <Form.Item
                            label="Phân công"
                            name="assignedEmployeeId"
                            rules={[
                              {
                                required: true,
                                message: "Vui lòng phân công cho một nhân viên",
                              },
                            ]}
                          >
                            <BaseSelectWithFeatures
                              triggerLoadMore={() => loadMoreEmployee()}
                              disabled={viewMode}
                              loading={loadMoreEmployeeState.loading}
                              showSearch
                              onSearch={(value) => {
                                onAssignEmployeePulldownInputChange(value);
                              }}
                              options={assignEmployeeOptions}
                              placeholder="Phân công"
                            />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24}>
                          <Form.Item
                            label="Ghi chú giao hàng"
                            name="deliveryNote"
                            // initialValue={initialOrderDetail?.deliveryNote}
                          >
                            <TextArea rows={3} disabled={viewMode} />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24} md={12}>
                          <Form.Item
                            label="Tổng khối lượng (kg)"
                            name="weight"
                            // initialValue={initialOrderDetail?.weight}
                          >
                            <InputNumber
                              style={{ width: "100%" }}
                              disabled={viewMode}
                            />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24} md={12}>
                          <Form.Item
                            label="Kích thước đóng gói (cm)"
                            name="boxCode"
                            // initialValue={initialOrderDetail?.size}
                          >
                            <Input disabled={viewMode} />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24}>
                          <Form.Item
                            label="Ghi chú đơn hàng"
                            name="orderNote"
                            // initialValue={initialOrderDetail?.orderNote}
                          >
                            <TextArea rows={3} disabled={viewMode} />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24}>
                          <Form.Item
                            label="Ghi chú API"
                            name="apiNote"
                            // initialValue={initialOrderDetail?.apiNote}
                          >
                            <TextArea rows={3} disabled={viewMode} />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24}>
                          <Form.Item
                            label="Chuyển đến shop"
                            name="shopSent"
                            rules={[
                              {
                                required: true,
                                message: "Vui lòng chọn shop",
                              },
                            ]}
                          >
                            <BaseSelect
                              placeholder="Chọn shop"
                              disabled={viewMode}
                              style={{ width: "100%" }}
                              options={listShopState}
                              fieldNames={{
                                label: "name",
                                value: "shopId",
                              }}
                            />
                          </Form.Item>
                        </AntCol>

                        <AntCol xs={24}>
                          <Form.Item
                            label="Ghi chú"
                            name="shopSentNote"
                            rules={[
                              {
                                max: 500,
                                message: "Tối đa 500 ký tự",
                              },
                            ]}
                          >
                            <Input.TextArea
                              rows={3}
                              placeholder="Nhập ghi chú (nếu có)..."
                              disabled={viewMode}
                            />
                          </Form.Item>
                        </AntCol>

                        {/* <AntCol xs={24}>
                          <Form.Item
                            name="isShopSent"
                            valuePropName="checked"
                            noStyle
                          >
                            <Checkbox disabled={viewMode}>Chi nhánh</Checkbox>
                          </Form.Item>
                        </AntCol>

                        <Form.Item
                          noStyle
                          shouldUpdate={(prev, curr) =>
                            prev.isShopSent !== curr.isShopSent
                          }
                        >
                          {({ getFieldValue }) => {
                            const isVisible = getFieldValue("isShopSent");

                            return (
                              <>
                                {isVisible && (
                                  <motion.div
                                    key="shopSentFields"
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: "auto" }}
                                    exit={{ opacity: 0, height: 0 }}
                                    transition={{ duration: 0.3 }}
                                    style={{
                                      overflow: "hidden",
                                      width: "100%",
                                    }}
                                  >
                                    <AntCol xs={24}>
                                      <Form.Item
                                        label="Chuyển đến shop"
                                        name="shopSent"
                                        rules={[
                                          {
                                            required: true,
                                            message: "Vui lòng chọn shop",
                                          },
                                        ]}
                                      >
                                        <BaseSelect
                                          placeholder="Chọn shop"
                                          disabled={viewMode}
                                          style={{ width: "100%" }}
                                          options={listShopState}
                                          fieldNames={{
                                            label: "name",
                                            value: "shopId",
                                          }}
                                        />
                                      </Form.Item>
                                    </AntCol>

                                    <AntCol xs={24}>
                                      <Form.Item
                                        label="Ghi chú"
                                        name="shopSentNote"
                                        rules={[
                                          {
                                            max: 500,
                                            message: "Tối đa 500 ký tự",
                                          },
                                        ]}
                                      >
                                        <Input.TextArea
                                          rows={3}
                                          placeholder="Nhập ghi chú (nếu có)..."
                                          disabled={viewMode}
                                        />
                                      </Form.Item>
                                    </AntCol>
                                  </motion.div>
                                )}
                              </>
                            );
                          }}
                        </Form.Item> */}
                      </AntRow>
                      {/* </Form> */}
                    </Collapse.Panel>
                  </Collapse>
                </AntCol>
                <AntCol xs={24} lg={{ span: 12 }}>
                  <AntRow gutter={[16, 16]}>
                    {/** KHÁCH HÀNG */}
                    <AntCol xs={24} xl={24}>
                      <Collapse defaultActiveKey={["1"]}>
                        <Collapse.Panel
                          collapsible="header"
                          className="[&_.ant-collapse-header]:!place-items-center"
                          key="1"
                          header={
                            <Space
                              align="center"
                              direction={isMobile ? "vertical" : "horizontal"}
                            >
                              <span>Khách hàng</span>
                              {state.controlledProperties.customer?.name && (
                                <Tag color="blue">
                                  {state.controlledProperties.customer?.name}
                                </Tag>
                              )}
                            </Space>
                          }
                          extra={
                            <BaseButton
                              type="primary"
                              bgColor={COLOR.BLUE[500]}
                              hoverColor={COLOR.BLUE[600]}
                              icon={<PlusOutlined />}
                              disabled={viewMode || editMode}
                              onClick={() => {
                                customerUpsertModalRef.current?.open();
                              }}
                            >
                              {isMobile ? null : "Thêm khách hàng"}
                            </BaseButton>
                          }
                        >
                          {/* <Form layout="vertical"> */}
                          <AntRow gutter={[16, 16]}>
                            <AntCol xs={24} md={12}>
                              <Form.Item label="Điện thoại" name="phoneNumber">
                                <Input
                                  disabled={viewMode || editMode}
                                  // value={
                                  //   primaryPhone(
                                  //     state.controlledProperties.customer
                                  //   ) || ""
                                  // }
                                  onFocus={handleOnFocusCustomerPhoneInput}
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} md={12}>
                              <Form.Item label="Email" name={"email"}>
                                <Input
                                  disabled
                                  value={
                                    primaryEmail(
                                      state.controlledProperties.customer
                                    ) || ""
                                  }
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} md={12}>
                              <Form.Item label="Họ Tên" name="name">
                                <Input
                                  disabled
                                  value={
                                    state.controlledProperties.customer?.name
                                  }
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} md={12}>
                              <Form.Item label="Ngày sinh" name="birthDay">
                                <DatePicker
                                  disabled
                                  format={"DD/MM/YYYY"}
                                  style={{ width: "100%" }}
                                  value={
                                    state.controlledProperties.customer
                                      ?.birthDay &&
                                    dayjs(
                                      state.controlledProperties.customer
                                        ?.birthDay
                                    )
                                  }
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24}>
                              <Form.Item label="Giới tính" name="sex">
                                <AntRadio.Group
                                  disabled
                                  value={
                                    state.controlledProperties.customer
                                      ?.gender === 1
                                      ? "GENDER_MALE"
                                      : "GENDER_FEMALE"
                                  }
                                >
                                  <AntRadio value="GENDER_MALE">Nam</AntRadio>
                                  <AntRadio value="GENDER_FEMALE">Nữ</AntRadio>
                                </AntRadio.Group>
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} md={8}>
                              <Form.Item
                                label="Chiều cao (cm)"
                                name="customerHeight"
                              >
                                <InputNumber
                                  style={{ width: "100%" }}
                                  disabled={viewMode}
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} md={8}>
                              <Form.Item
                                label="Cân nặng (kg)"
                                name="customerWeight"
                              >
                                <InputNumber
                                  style={{ width: "100%" }}
                                  disabled={viewMode}
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} md={8}>
                              <Form.Item
                                label="Vòng eo (cm)"
                                name="customerWaist"
                              >
                                <InputNumber
                                  style={{ width: "100%" }}
                                  disabled={viewMode}
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24}>
                              <Form.Item
                                label="Địa chỉ"
                                name="shippingAddressId"
                              >
                                <BaseSelect
                                  disabled={viewMode}
                                  options={customerShippingAddressOptions}
                                  value={
                                    state.customerShippingAddress &&
                                    formatShippingAddressOption(
                                      state.customerShippingAddress
                                    )
                                  }
                                  loading={fetchCustomerShippingAddressLoading}
                                  onChange={(
                                    __,
                                    option: { value: string; label: string }
                                  ) => {
                                    console.log(option, "option");
                                    if (option) {
                                      handleChangeShippingAddress(option);
                                    }
                                  }}
                                  placeholder="Địa chỉ"
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} lg={12}>
                              <Form.Item label="Tỉnh/Thành phố" name="cityId">
                                <BaseSelectCity
                                  disabled={viewMode}
                                  placeholder="Chọn tỉnh/thành phố"
                                />
                              </Form.Item>
                            </AntCol>
                            <AntCol xs={24} lg={12}>
                              <Form.Item label="Phường/Xã" name="districtId">
                                <BaseSelectDistrict
                                  placeholder="Chọn phường/xã"
                                  disabled={viewMode}
                                  cityId={cityIdValue}
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24}>
                              <Form.Item
                                label="Địa chỉ giao hàng"
                                name="shippingAddressDetail"
                                rules={[
                                  {
                                    required: true,
                                    message: "Vui lòng nhập địa chỉ",
                                  },
                                ]}
                              >
                                <TextArea
                                  disabled={viewMode}
                                  allowClear
                                  rows={3}
                                  placeholder="Nhập địa chỉ giao hàng"
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24}>
                              <Form.Item label="Ghi chú CSKH" name="cskhNote">
                                <TextArea
                                  disabled={viewMode}
                                  allowClear
                                  rows={3}
                                  placeholder="Nhập ghi chú chăm sóc khách hàng (nếu có)"
                                />
                              </Form.Item>
                            </AntCol>

                            {(editMode || createMode) &&
                              state.controlledProperties.customer && (
                                <AntCol xs={24} style={{ textAlign: "center" }}>
                                  <BaseButton
                                    type="primary"
                                    icon={<PlusOutlined rev={undefined} />}
                                    onClick={() =>
                                      handleOpenModalByType(
                                        "customerCreateShippingModal"
                                      )
                                    }
                                  >
                                    Thêm địa chỉ mới
                                  </BaseButton>
                                </AntCol>
                              )}
                          </AntRow>
                          {/* </Form> */}
                        </Collapse.Panel>
                      </Collapse>
                    </AntCol>

                    {/** THANH TOÁN */}
                    <AntCol xs={24} xl={24}>
                      <Collapse defaultActiveKey={["1"]}>
                        <Collapse.Panel key="1" header="Thanh toán">
                          {/* <Form layout="vertical"> */}
                          <AntRow gutter={[16, 8]}>
                            <AntCol xs={24}>
                              {/* <Form.Item label="Tổng" name="totalValue"> */}
                              <Form.Item
                                label="Tổng tiền hàng"
                                name="totalAmount"
                                // rules={[
                                //   {
                                //     validator: async (_, value) => {
                                //       if (value < 0) {
                                //         return Promise.reject(
                                //           new Error(
                                //             "Trường này không phép được âm"
                                //           )
                                //         );
                                //       }
                                //       return Promise.resolve();
                                //     },
                                //   },
                                // ]}
                              >
                                <InputNumber
                                  disabled
                                  className="w-full"
                                  suffix={<Typography>VND</Typography>}
                                  // min={0}
                                  value={state.controlledProperties.totalAmount}
                                  formatter={(value) =>
                                    value !== undefined && value !== null
                                      ? value
                                          .toString()
                                          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                                      : ""
                                  }
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24}>
                              <Form.Item label="Giảm giá">
                                <AntRow gutter={[16, 16]}>
                                  <AntCol xs={24} lg={12}>
                                    <Form.Item name="discount" noStyle>
                                      <Space.Compact className="w-full">
                                        <InputNumber
                                          className="w-full"
                                          // suffix={
                                          //   <PercentageOutlined rev={null} />
                                          // }
                                          formatter={(value) =>
                                            value !== undefined &&
                                            value !== null
                                              ? value
                                                  .toString()
                                                  .replace(
                                                    /\B(?=(\d{3})+(?!\d))/g,
                                                    ","
                                                  )
                                              : ""
                                          }
                                          // min={0}
                                          max={
                                            discountType === "percent"
                                              ? 100
                                              : Math.abs(
                                                  state.controlledProperties
                                                    .totalAmount
                                                )
                                          }
                                          disabled={viewMode}
                                          value={discountState}
                                          onChange={(value) => {
                                            recalculateDiscountValue(value);
                                          }}
                                        />
                                        <BaseButton
                                          type="primary"
                                          className="!px-6"
                                          bgColor={COLOR.BLUE[500]}
                                          hoverColor={COLOR.BLUE[700]}
                                          onClick={handleChangeDiscountType}
                                          icon={
                                            discountType === "VND" ? (
                                              <>VND</>
                                            ) : (
                                              <PercentageOutlined />
                                            )
                                          }
                                        />
                                      </Space.Compact>
                                    </Form.Item>
                                  </AntCol>
                                  <AntCol xs={24} lg={12}>
                                    <Form.Item name="discountValue" noStyle>
                                      <InputNumber
                                        className="w-full"
                                        suffix={<Typography>VND</Typography>}
                                        disabled
                                        min={0}
                                        formatter={(value) =>
                                          value !== undefined && value !== null
                                            ? value
                                                .toString()
                                                .replace(
                                                  /\B(?=(\d{3})+(?!\d))/g,
                                                  ","
                                                )
                                            : ""
                                        }
                                      />
                                    </Form.Item>
                                  </AntCol>
                                </AntRow>
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} lg={12}>
                              <Form.Item
                                label="Phí ship"
                                name="shippingCost"
                                // initialValue={initialOrderDetail?.shippingCost}
                              >
                                <InputNumber
                                  style={{ width: "100%" }}
                                  onChange={(value) =>
                                    handleOnShippingCostInputChange({
                                      target: { value },
                                    } as any)
                                  }
                                  suffix={<Typography>VND</Typography>}
                                  disabled={viewMode}
                                  min={0}
                                  formatter={(value) =>
                                    value !== undefined && value !== null
                                      ? value
                                          .toString()
                                          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                                      : ""
                                  }
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} lg={12}>
                              <Form.Item
                                label="Phụ thu"
                                name="applicableFee"
                                // initialValue={initialOrderDetail?.applicableFee}
                              >
                                <InputNumber
                                  style={{ width: "100%" }}
                                  disabled={viewMode}
                                  onChange={(value) =>
                                    handleOnApplicableFeeInputChange({
                                      target: { value },
                                    } as any)
                                  }
                                  suffix={<Typography>VND</Typography>}
                                  min={0}
                                  formatter={(value) =>
                                    value !== undefined && value !== null
                                      ? value
                                          .toString()
                                          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                                      : ""
                                  }
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24}>
                              <Form.Item label="Sử dụng khuyến mãi">
                                <AntRow gutter={[16, 16]}>
                                  <AntCol xs={24} lg={12}>
                                    <Form.Item name="voucherId" noStyle>
                                      <BaseSelect
                                        placeholder="Mã khuyến mãi"
                                        disabled={viewMode}
                                        style={{ width: "100%" }}
                                        options={promotionOptions}
                                        showSearch
                                        onClear={() => {
                                          setPromotionSelected(null);
                                        }}
                                        onSelect={(value, option) => {
                                          setPromotionSelected(
                                            option as PromotionDto
                                          );
                                        }}
                                      />
                                    </Form.Item>
                                  </AntCol>

                                  <AntCol xs={24} lg={12}>
                                    <Form.Item name="promotionValue" noStyle>
                                      <InputNumber
                                        className="w-full"
                                        disabled
                                        suffix={<Typography>VND</Typography>}
                                        min={0}
                                        formatter={(value) =>
                                          value !== undefined && value !== null
                                            ? value
                                                .toString()
                                                .replace(
                                                  /\B(?=(\d{3})+(?!\d))/g,
                                                  ","
                                                )
                                            : ""
                                        }
                                      />
                                    </Form.Item>
                                  </AntCol>
                                </AntRow>
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24}>
                              <Form.Item label="Áp dụng mã giới thiệu">
                                <AntRow gutter={[16, 16]}>
                                  <AntCol xs={24} lg={12}>
                                    <div className="grid grid-cols-[1fr_max-content] gap-1">
                                      <Form.Item
                                        name="applyreferralcode"
                                        noStyle
                                      >
                                        <Input
                                          disabled={viewMode}
                                          placeholder="Nhập áp dụng mã giới thiệu"
                                          allowClear
                                        />
                                      </Form.Item>
                                      <BaseButton
                                        type="primary"
                                        bgColor={COLOR.BLUE[500]}
                                        hoverColor={COLOR.BLUE[600]}
                                        icon={<CheckOutlined rev={undefined} />}
                                      ></BaseButton>
                                    </div>
                                  </AntCol>
                                  <AntCol xs={24} lg={12}>
                                    <Form.Item
                                      name="applyreferralValue"
                                      noStyle
                                    >
                                      <InputNumber
                                        className="w-full"
                                        disabled
                                        suffix={<Typography>VND</Typography>}
                                        min={0}
                                        formatter={(value) =>
                                          value !== undefined && value !== null
                                            ? value
                                                .toString()
                                                .replace(
                                                  /\B(?=(\d{3})+(?!\d))/g,
                                                  ","
                                                )
                                            : ""
                                        }
                                      />
                                    </Form.Item>
                                  </AntCol>
                                </AntRow>
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} lg={12}>
                              <Space.Compact className="w-full">
                                <Form.Item
                                  label="Thanh toán"
                                  name="pay"
                                  className="w-full"
                                  // rules={[
                                  //   {
                                  //     validator: async (_, value) => {
                                  //       if (value < 0) {
                                  //         return Promise.reject(
                                  //           new Error(
                                  //             "Trường này không được phép âm"
                                  //           )
                                  //         );
                                  //       }
                                  //       return Promise.resolve();
                                  //     },
                                  //   },
                                  // ]}
                                >
                                  <InputNumber
                                    disabled
                                    className="w-full"
                                    suffix={<Typography>VND</Typography>}
                                    // min={0}
                                    formatter={(value) =>
                                      value !== undefined && value !== null
                                        ? value
                                            .toString()
                                            .replace(
                                              /\B(?=(\d{3})+(?!\d))/g,
                                              ","
                                            )
                                        : ""
                                    }
                                  />
                                </Form.Item>
                                <BaseButton
                                  type="primary"
                                  className={`!px-2 text-black translate-y-[30px]`}
                                  bgColor={COLOR.BLUE[500]}
                                  hoverColor={COLOR.BLUE[700]}
                                  disabled={
                                    createMode || !initialOrderDetail?.orderId
                                  }
                                  onClick={() => {
                                    paymentOrderModalRef.current.open(
                                      initialOrderDetail?.orderId
                                    );
                                  }}
                                >
                                  <Text
                                    className={`text-sm ${
                                      createMode || !initialOrderDetail?.orderId
                                        ? "text-black opacity-60"
                                        : "text-white"
                                    }`}
                                  >
                                    Thanh toán
                                  </Text>
                                </BaseButton>
                              </Space.Compact>
                            </AntCol>

                            <AntCol xs={24} lg={12}>
                              <div className="grid grid-col-1 lg:grid-cols-2 gap-x-4">
                                <Form.Item
                                  label={
                                    <Tooltip title="Tổng điểm quy đổi">
                                      <Typography className="truncate">
                                        Tổng điểm quy đổi
                                      </Typography>
                                    </Tooltip>
                                  }
                                  name="totalApplyPoint"
                                >
                                  <InputNumber
                                    disabled
                                    className="w-full"
                                    placeholder="0"
                                    // min={0}
                                  />
                                </Form.Item>

                                <Form.Item
                                  label={
                                    <Tooltip title="Tổng tiền quy đổi từ điểm">
                                      <Typography className="truncate">
                                        Tổng tiền quy đổi
                                      </Typography>
                                    </Tooltip>
                                  }
                                  name="totalApplyPointValue"
                                >
                                  <InputNumber
                                    disabled
                                    className="w-full"
                                    placeholder="0"
                                    // min={0}
                                    addonAfter="VND"
                                    formatter={(value) =>
                                      `${value}`.replace(
                                        /\B(?=(\d{3})+(?!\d))/g,
                                        ","
                                      )
                                    }
                                  />
                                </Form.Item>
                              </div>
                            </AntCol>

                            <AntCol xs={24} lg={12}>
                              <Form.Item label="Đã thanh toán" name="paid">
                                <InputNumber
                                  disabled
                                  className="w-full"
                                  suffix={<Typography>VND</Typography>}
                                  // min={0}
                                  formatter={(value) =>
                                    value !== undefined && value !== null
                                      ? value
                                          .toString()
                                          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                                      : ""
                                  }
                                />
                              </Form.Item>
                            </AntCol>

                            <AntCol xs={24} lg={12}>
                              <Form.Item
                                label="Còn lại"
                                name="remainingAmount"
                                // rules={[
                                //   {
                                //     validator: async (_, value) => {
                                //       if (value < 0) {
                                //         return Promise.reject(
                                //           new Error(
                                //             "Trường này không được phép âm"
                                //           )
                                //         );
                                //       }
                                //       return Promise.resolve();
                                //     },
                                //   },
                                // ]}
                              >
                                <InputNumber
                                  disabled
                                  className="w-full"
                                  suffix={<Typography>VND</Typography>}
                                  // min={0}
                                  formatter={(value) =>
                                    value !== undefined && value !== null
                                      ? value
                                          .toString()
                                          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                                      : ""
                                  }
                                />
                              </Form.Item>
                            </AntCol>
                          </AntRow>
                          {/* </Form> */}
                        </Collapse.Panel>
                      </Collapse>
                    </AntCol>
                  </AntRow>
                </AntCol>
              </AntRow>
            </Card>

            <Card
              type="inner"
              title="Sản phẩm"
              style={{ marginTop: 16 }}
              extra={
                (editMode || createMode) && (
                  <BaseButton
                    // disabled={initialOrderDetail?.orderStatusId !== 15}
                    type="primary"
                    icon={<PlusOutlined rev={undefined} />}
                    onClick={() => handleOpenModalByType("productSkuPicker")}
                  >
                    Thêm sản phẩm mới
                  </BaseButton>
                )
              }
            >
              <div className="flex flex-col gap-2">
                {/* <Divider orientation="left" className="my-2 px-2" dashed>
                  Danh sách sản phẩm
                </Divider>
                <ListProduct
                  disabledSelectedButton={
                    viewMode || editMode
                      ? initialOrderDetail?.orderStatusId !== 15 &&
                        initialOrderDetail?.orderStatusId !== 2
                      : false
                  }
                  shopSentValue={shopSentValue}
                  onSubmit={handleOnSubmitProductSkuPickerForm}
                /> */}
                {/* <Divider orientation="left" className="my-2 px-2" dashed>
                  Danh sách sản phẩm đã chọn
                </Divider> */}
                <Table
                  columns={productColumns}
                  dataSource={state.controlledProperties?.items || []}
                  pagination={false}
                  rowKey="_id"
                  scroll={{ x: 1200 }}
                />
              </div>
            </Card>
          </Card>
        </Section>
      </Form>

      <CustomerPickerByPhone
        open={state.modalState.type === "customerPickerByPhone"}
        onClose={handleOnCloseModal}
        onChooseCustomer={useCallback(
          (customer) => {
            // console.log(customer, "customer");
            if (customer) {
              const { phoneNumber, name, gender, phoneNumber2, birthDay } =
                customer ?? {};
              const isPrimaryEmail = primaryEmail(customer) || "";
              form.setFieldsValue({
                phoneNumber: phoneNumber ?? phoneNumber2,
                email: isPrimaryEmail,
                name: name,
                sex: gender === 1 ? "GENDER_MALE" : "GENDER_FEMALE",
                birthDay: dayjs(birthDay),
                customerHeight: customer.height || undefined,
                customerWeight: customer.weight || undefined,
                customerWaist: customer.waist || undefined,
              });
              setOrderProperties({ customer });
              resetShippingAddress();
            }
          },
          [resetShippingAddress, setOrderProperties]
        )}
      />

      <ProductSkuPickerModal
        open={state.modalState.type === "productSkuPicker"}
        onClose={handleOnCloseModal}
        onSubmit={handleOnSubmitProductSkuPickerForm}
      />

      {state.controlledProperties.customer && (
        <CustomerShippingAddressModal
          onClose={handleOnCloseModal}
          open={
            modalTypeIsOpen("customerCreateShippingModal") ||
            modalTypeIsOpen("customerUpdateShippingModal")
          }
          customerShippingAddress={
            modalTypeIsOpen("customerUpdateShippingModal")
              ? state.customerShippingAddress
              : undefined
          }
          onSubmit={submitCustomerShippingAddressExec}
        />
      )}
      {isOpenModal && (
        <HistoryOrderModal
          isOpen={isOpenModal}
          setOpenModal={setIsOpenModal}
          orderId={
            initialOrderDetail?.orderId || initialOrderDetail?.orderIdRef
          }
          listMasterData={listMasterData}
        />
      )}
      <CustomerUpserModal
        ref={customerUpsertModalRef}
        onFinishForm={onFinishCustomerUpsert}
      />

      <PaymentOrderModal
        ref={paymentOrderModalRef}
        orderCode={initialOrderDetail?.code || ""}
        pay={state.controlledProperties?.pay || 0}
        remainingAmount={state.controlledProperties?.remainingAmount || 0}
        paid={state.controlledProperties?.paid || 0}
        handleRefetch={handleRefetch}
        customerId={state.controlledProperties?.customer?.customerId}
      />
    </>
  );
};
