import React from "react";

import { mapModifiers } from "helpers/component";

import { Icon } from "../icon";

export interface Props {
  hasContainer?: boolean;
  full?: boolean;
  fullContainer?: boolean;
  absolute?: boolean;
}

export const Spinner: React.FC<Props> = ({
  hasContainer,
  full,
  absolute,
  fullContainer,
}) => (
  <div
    className={mapModifiers(
      "a-spinner",
      hasContainer && "wrap",
      full && "full",
      absolute && "absolute",
      fullContainer && "fullcontainer"
    )}
  >
    <Icon iconName="loading-blue" />
  </div>
);
