/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-shadow */
import produce from "immer";

export type ActionSubscribeCallback<Payload> = (payload: Payload) => void;
type EffectRunnerParams<S, Payload> = {
  from: Event<Payload>;
  fromArgs: Payload;
  state: S;
};

const UNIT_TYPES = {
  source: Symbol("UNIT_SOURCE"),
  action: Symbol("UNIT_ACTION"),
  watcher: <PERSON><PERSON><PERSON>("UNIT_WATCHER"),
  asyncAction: Symbol("UNIT_ASYNC_ACTION"),
};

export interface Event<Payload> extends Function {
  type: typeof UNIT_TYPES["action"];
  subscribe: (callback: ActionSubscribeCallback<Payload>) => void;
  unsubscribe: (callback: ActionSubscribeCallback<Payload>) => void;
  unsubscribeAll: () => void;
  watch: (handler: (payload: Payload) => void) => () => void;
  once: (handler: (payload: Payload) => void) => () => void;
}

export interface Source<S> {
  type: typeof UNIT_TYPES.source;
  off: <Payload>(action: Event<Payload>) => void;
  offAll: () => void;
  on: <Payload>(
    action: Event<Payload>,
    callback: (state: S, payload: Payload) => void | S
  ) => void;
  effectRunner: <Payload>(payload: EffectRunnerParams<S, Payload>) => void;
  watch: <Payload>(...args: SourceWatcherParams<S, Payload>) => () => void;
  getValue: () => Readonly<S>;
}

export interface AsyncAction<E = any, P extends unknown[] = []> {
  pending: Event<unknown>;
  fulfilled: Event<P>;
  rejected: Event<E>;
}

export type SourceActionSubscriber<S, Payload> = (
  state: S,
  payload: Payload
) => void | S;

export type SourceWatcherParams<S, Payload> =
  // eslint-disable-next-line prettier/prettier
  | [watcher: (state: S) => void]
  | [
      event: Event<Payload> | Event<Payload>[],
      watcher: (sourceValue: S, eventPayload: Payload) => void
    ];

export const isEvent = (unit: any) => unit?.type === UNIT_TYPES.action;
export const isSource = (unit: any) => unit?.type === UNIT_TYPES.source;
export const isAsyncAction = (unit: any) =>
  unit?.type === UNIT_TYPES.asyncAction;
export const isWatcher = (unit: any) => unit?.type === UNIT_TYPES.watcher;

export const createEvent = <Payload = void>() => {
  const internal = {
    next: new Set<ActionSubscribeCallback<Payload>>(),
    watcher: new Set<(payload: Payload) => void>(),
    watcherOnce: new Set<(payload: Payload) => void>(),
  };
  const action = (payload: Payload) => {
    internal.next.forEach((cb) => {
      cb(payload);
    });
    internal.watcher.forEach((watcher) => watcher(payload));
    internal.watcherOnce.forEach((watcher) => watcher(payload));
    internal.watcherOnce.clear();
  };

  action.type = UNIT_TYPES.action;

  action.subscribe = (callback: ActionSubscribeCallback<Payload>) => {
    internal.next.add(callback);
  };

  action.unsubscribe = (callback: ActionSubscribeCallback<Payload>) => {
    internal.next.delete(callback);
  };

  action.unsubscribeAll = () => {
    internal.next.clear();
  };

  action.watch = (handler: (payload: Payload) => void) => {
    const subscriber = (payload: Payload) => handler(payload);
    internal.watcher.add(subscriber);

    return () => {
      internal.watcher.delete(subscriber);
    };
  };

  action.once = (handler: (payload: Payload) => void) => {
    const subscriber = (payload: Payload) => handler(payload);
    internal.watcherOnce.add(subscriber);

    return () => {
      internal.watcherOnce.delete(subscriber);
    };
  };

  return action;
};

export const createAsyncAction = <R, E = any, P extends unknown[] = []>(
  effect: (...args: P) => Promise<R>
) => {
  const internal = {
    pendingAction: createEvent<typeof effect>(),
    fulfillAction: createEvent<R>(),
    rejectAction: createEvent<E>(),
  };

  const executer = async (...args: P) => {
    internal.pendingAction(effect);
    try {
      const res = await effect(...args);
      internal.fulfillAction(res);
      return res;
    } catch (error: any) {
      internal.rejectAction(error as any);
      return Promise.reject(error);
    }
  };

  executer.type = UNIT_TYPES.asyncAction;
  executer.pending = internal.pendingAction;
  executer.fulfilled = internal.fulfillAction;
  executer.rejected = internal.rejectAction;

  executer.watch = (
    watcher: (action: Event<typeof effect> | Event<R> | Event<E>) => void
  ) => {
    const { pendingAction, fulfillAction, rejectAction } = internal;
    const unsubscribers = [
      pendingAction.watch(() => watcher(pendingAction)),
      fulfillAction.watch(() => watcher(fulfillAction)),
      rejectAction.watch(() => watcher(rejectAction)),
    ];
    return () => {
      unsubscribers.forEach((unsubscriber) => unsubscriber());
    };
  };

  executer.once = (
    watcher: (action: Event<typeof effect> | Event<R> | Event<E>) => void
  ) => {
    const { pendingAction, fulfillAction, rejectAction } = internal;
    const unsubscribers = [
      pendingAction.once(() => watcher(pendingAction)),
      fulfillAction.once(() => watcher(fulfillAction)),
      rejectAction.once(() => watcher(rejectAction)),
    ];
    return () => {
      unsubscribers.forEach((unsubscriber) => unsubscriber());
    };
  };

  return executer;
};

const unSubscriberAction = <K extends object, V>(
  subscriber: Map<K, V>,
  action: K
) => {
  subscriber.delete(action);
};

export const createSource = <S>(initialValue: S) => {
  let source: S = initialValue;
  const subscriber = new Map();
  const watcher = new Set<(payload: EffectRunnerParams<S, any>) => void>();
  const outPort = {
    type: UNIT_TYPES.source,
    off: <Payload>(action: Event<Payload>) => {
      const effectHandler = subscriber.get(action);
      if (effectHandler) {
        action.unsubscribe(effectHandler);
        unSubscriberAction(subscriber, action);
      }
    },
    offAll: () => {
      subscriber.forEach((effectHandler, action) =>
        unSubscriberAction(effectHandler, action)
      );
    },
    on: <Payload>(
      action: Event<Payload>,
      callback: SourceActionSubscriber<S, Payload>
    ) => {
      outPort.off(action);
      const effectHandler = (payload: Payload) => {
        const nextSource = produce(source, (draftSource: S) => {
          callback(draftSource, payload);
        });
        source = nextSource;
        outPort.effectRunner({
          from: action,
          state: source,
          fromArgs: payload,
        });
      };
      subscriber.set(action, effectHandler);
      action.subscribe(effectHandler);
      return outPort;
    },
    effectRunner: <Payload>(payload: EffectRunnerParams<S, Payload>) => {
      // eslint-disable-next-line @typescript-eslint/no-shadow
      watcher.forEach((subscriber) => {
        subscriber(payload);
      });
    },
    watch: <Payload>(...args: SourceWatcherParams<S, Payload>) => {
      // eslint-disable-next-line @typescript-eslint/no-shadow
      let subscriber: (payload: EffectRunnerParams<S, Payload>) => void;
      if (args.length === 1) {
        subscriber = (payload: { state: S }) => {
          args[0](payload.state);
        };
      } else {
        subscriber = (payload: {
          state: S;
          from: Event<Payload>;
          fromArgs: Payload;
        }) => {
          const fromEvents = [args[0]].flat();
          fromEvents.some((event) => {
            if (Object.is(event, payload.from)) {
              args[1](payload.state, payload.fromArgs);
              return true;
            }

            return false;
          });
        };
      }

      watcher.add(subscriber);

      return () => {
        watcher.delete(subscriber);
      };
    },
    getValue: () => Object.freeze(source),
  };

  return outPort;
};
