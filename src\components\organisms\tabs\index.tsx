import React from "react";

import { Tabs as ReactTabs, TabsProps } from "react-tabs";

import { mapModifiers } from "helpers/component";

export { TabList, Tab, TabPanel } from "react-tabs";

type Type = "main" | "editing";

export interface PropsTabs extends TabsProps {
  type?: Type;
  children: React.ReactNode;
}

export const Tabs: React.FC<PropsTabs> = ({
  type,
  children,
  ...innerProps
}) => (
  <div className={mapModifiers("o-tabs", type)}>
    <ReactTabs {...innerProps}>{children}</ReactTabs>
  </div>
);

Tabs.defaultProps = {
  type: "main",
};
