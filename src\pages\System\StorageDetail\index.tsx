import { useCallback, useEffect } from "react";
import {
  ArrowLeftOutlined,
  EditOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { Col, Form, Input, InputNumber, Row, Spin } from "antd";
import { useParams, useSearchParams, useNavigate } from "react-router";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { Heading } from "components/atoms/heading";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";

import { ChildrenPage } from "../types";
import { StorageDetailFormType } from "./constant";
import { StorageDetailPageVm } from "./vm";

const IndexPage = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [searchUrlPath] = useSearchParams();
  const { storageId } =
    useParams<PageParamsType<ChildrenPage["storageDetail"]>>();

  const pageActionType = searchUrlPath.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  const {
    storageData,
    loading,
    updateStorageDetailState,
    handleUpdateStorageDetail,
  } = StorageDetailPageVm({
    storageId: Number(storageId),
  });

  // Set form values when data is loaded
  useEffect(() => {
    if (storageData) {
      form.setFieldsValue({
        code: storageData.code,
        name: storageData.name,
        displayOrder: storageData.displayOrder,
      });
    }
  }, [storageData, form]);

  const onSubmitUpdateStorageDetail = useCallback(
    (formData: StorageDetailFormType) => {
      handleUpdateStorageDetail({
        ...formData,
      });
    },
    [handleUpdateStorageDetail]
  );

  const handleEditMode = () => {
    navigate(`${window.location.pathname}?action=edit`);
  };

  return (
    <Spin spinning={loading} tip="Đang tải...">
      <General>
        <title key="title">Chi tiết kho</title>
        <Section>
          {/* Header with title and action buttons */}
          <div className="flex justify-between items-center mb-6">
            <Heading>THÔNG TIN KHO: {storageData?.name || "..."}</Heading>

            <div className="flex gap-3">
              <BaseButton
                type="default"
                icon={<ArrowLeftOutlined rev={undefined} />}
                onClick={navigationHelper.goBack}
              >
                QUAY LẠI
              </BaseButton>

              {viewMode && (
                <BaseButton
                  type="primary"
                  bgColor={COLOR.ORANGE[500]}
                  hoverColor={COLOR.ORANGE[700]}
                  icon={<EditOutlined rev={undefined} />}
                  onClick={handleEditMode}
                >
                  CHỈNH SỬA
                </BaseButton>
              )}
            </div>
          </div>

          {/* Form Section */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <Form
              form={form}
              layout="vertical"
              onFinish={onSubmitUpdateStorageDetail}
              disabled={viewMode}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Mã kho"
                    name="code"
                    rules={
                      editMode
                        ? [{ required: true, message: "Vui lòng nhập mã kho" }]
                        : []
                    }
                  >
                    <Input
                      placeholder="Nhập mã kho"
                      readOnly={viewMode}
                      className={viewMode ? "bg-gray-50" : ""}
                    />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    label="Tên kho"
                    name="name"
                    rules={
                      editMode
                        ? [{ required: true, message: "Vui lòng nhập tên kho" }]
                        : []
                    }
                  >
                    <Input
                      placeholder="Nhập tên kho"
                      readOnly={viewMode}
                      className={viewMode ? "bg-gray-50" : ""}
                    />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    label="Thứ tự hiển thị"
                    name="displayOrder"
                    rules={
                      editMode
                        ? [
                            {
                              required: true,
                              message: "Vui lòng nhập thứ tự hiển thị",
                            },
                          ]
                        : []
                    }
                  >
                    <InputNumber
                      placeholder="Nhập thứ tự hiển thị"
                      min={0}
                      className={`w-full ${viewMode ? "bg-gray-50" : ""}`}
                      readOnly={viewMode}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Form Actions - Only show in edit mode */}
              {editMode && (
                <div className="flex justify-end gap-3 pt-6 border-t mt-6">
                  <BaseButton
                    type="default"
                    onClick={() => navigate(window.location.pathname)}
                  >
                    HỦY
                  </BaseButton>
                  <BaseButton
                    type="primary"
                    bgColor={COLOR.BLUE[500]}
                    hoverColor={COLOR.BLUE[700]}
                    icon={<SaveOutlined rev={undefined} />}
                    htmlType="submit"
                    loading={updateStorageDetailState.loading}
                  >
                    LƯU
                  </BaseButton>
                </div>
              )}
            </Form>
          </div>
        </Section>
      </General>
    </Spin>
  );
};

export default IndexPage;
