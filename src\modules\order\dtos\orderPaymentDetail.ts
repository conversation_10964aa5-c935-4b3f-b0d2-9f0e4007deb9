import { createMapper, fromSchema } from "libs/adapters/dto";
import { mergeSchema } from "libs/domain";
import { CustomerProfileSchema } from "modules/customer";

import { OrderItemSchema, OrderPaymentSchema } from "../entities";

export const orderPaymentDetailDto = createMapper(
  fromSchema(
    mergeSchema(OrderPaymentSchema, {
      order: mergeSchema(OrderItemSchema, { customer: CustomerProfileSchema }),
    })
  )
);

export type OrderPaymentDetailDtoType = ReturnType<
  typeof orderPaymentDetailDto
>;
