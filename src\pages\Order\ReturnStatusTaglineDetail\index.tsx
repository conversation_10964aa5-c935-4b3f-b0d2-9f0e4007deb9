import React, { use<PERSON>allback } from "react";

import { useParams, useSearchParams } from "react-router";

import { But<PERSON> } from "components/atoms/button";
import { ColorPicker } from "components/atoms/colorpicker";
import { Heading } from "components/atoms/heading";
import { NumberfieldHookForm } from "components/atoms/numberfield";
import { PulldownHookForm } from "components/atoms/pulldown";
import { TextareafieldHookForm } from "components/atoms/textareafield";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { SpinnerContainer } from "components/utils/spinnercontainer";
import { BasePageProps } from "helpers/component";
import { FormContainer } from "helpers/form";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";

import { ChildrenPage } from "../types";
import {
  ReturnOrderTaglineFormType,
  updateOrderReturnTaglineSchema,
} from "./constant";
import { OrderTaglineDetailPageVm } from "./vm";

const IndexPage: React.FC<BasePageProps> = () => {
  const { returnStatusId, taglineId } =
    useParams<PageParamsType<ChildrenPage["orderReturnStatusTaglineDetail"]>>();

  const {
    loading,
    orderTaglineDetail,
    orderReturnStatusOptions,
    selectedOrderStatus,
    updateReturnStatusTaglineState,
    onChangeColor,
    handleUpdateReturnStatusTagline,
    colorSelected,
  } = OrderTaglineDetailPageVm({
    orderStatusId: Number(returnStatusId),
    orderStatusTaglineId: Number(taglineId),
  });

  const updateReturnStatusTagline = useCallback(
    (formData: ReturnOrderTaglineFormType) =>
      handleUpdateReturnStatusTagline({
        ...formData,
        color: colorSelected,
      }),
    [handleUpdateReturnStatusTagline, colorSelected]
  );

  const [searchParams] = useSearchParams();
  const editMode = searchParams.get("action") === "edit";
  const viewMode = !editMode;

  return (
    <SpinnerContainer animating={loading}>
      <General>
        <title key="title">Sửa thông tin tagline đổi trả</title>
        <Section>
          <Heading type="h1" modifiers="primary">
            SỬA THÔNG TIN TAGLINE ĐỔI TRẢ
          </Heading>

          <Section>
            {orderTaglineDetail && (
              <FormContainer
                validationSchema={updateOrderReturnTaglineSchema}
                onSubmit={updateReturnStatusTagline}
              >
                <Row>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Tên tagline đổi trả" name="name">
                      <TextfieldHookForm
                        name="name"
                        placeholder="Tên tagline đổi trả"
                        defaultValue={orderTaglineDetail?.name}
                        disabled={viewMode}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Tên trạng thái đổi trả" name="stateName">
                      <PulldownHookForm
                        name="stateName"
                        options={orderReturnStatusOptions}
                        defaultValue={selectedOrderStatus}
                        value={selectedOrderStatus}
                        isDisabled
                        placeholder="Tên trạng thái đổi trả"
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Thứ tự hiển thị" name="displayOrder">
                      <NumberfieldHookForm
                        name="displayOrder"
                        placeholder="Thứ tự hiển thị"
                        defaultValue={orderTaglineDetail?.displayOrder}
                        disabled={viewMode}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6" className="u-mb-15">
                    <Formfield label="Màu sắc" name="color">
                      <ColorPicker
                        onChangeClolor={onChangeColor}
                        defaultColor={orderTaglineDetail?.color}
                        disabled={viewMode}
                      />
                    </Formfield>
                  </Col>
                  <Col lg="6">
                    <Formfield label="Mô tả" name="description">
                      <TextareafieldHookForm
                        name="description"
                        placeholder="Mô tả"
                        defaultValue={orderTaglineDetail?.description}
                        disabled={viewMode}
                      />
                    </Formfield>
                  </Col>
                </Row>
                <div className="d-flex justify-content-end u-mt-20">
                  <div className="u-mr-15">
                    <Button
                      buttonType="outline"
                      modifiers="secondary"
                      onClick={navigationHelper.goBack}
                    >
                      QUAY LẠI
                    </Button>
                  </div>
                  {editMode && (
                    <Button
                      type="submit"
                      isLoading={updateReturnStatusTaglineState.loading}
                      disabled={updateReturnStatusTaglineState.loading}
                    >
                      Lưu
                    </Button>
                  )}
                </div>
              </FormContainer>
            )}
          </Section>
        </Section>
      </General>
    </SpinnerContainer>
  );
};

export default IndexPage;
