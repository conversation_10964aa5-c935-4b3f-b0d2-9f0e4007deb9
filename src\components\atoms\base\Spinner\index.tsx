import { Spin } from "antd";
import store, { useAppSelector } from "store/index";
import { setLoading } from "store/system/systemSlice";

export const Spinner = () => {
  const loading = useAppSelector((state) => state.system.loading);
  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: "100%",
        height: "100%",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        zIndex: 999999,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        visibility: loading ? "visible" : "hidden",
      }}
    >
      <Spin
        className="text-white [&_.ant-spin-dot-item]:bg-white"
        spinning={loading}
      />
      ;
    </div>
  );
};

export const showLoading = (bool: boolean) => {
  store.dispatch(setLoading(bool));
};
