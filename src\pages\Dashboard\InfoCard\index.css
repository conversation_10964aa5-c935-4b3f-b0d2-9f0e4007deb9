.figure {
	text-shadow: 5px 5px 5px #555;
}

.main-card {
	display: flex;
	padding: 10px 10px 0;
	user-select: none;
}

.info-card {
	flex: 1;
}

.info-card__title {
	display: inline-block;
	position: relative;
	z-index: 1;
}

.info-card__title::before {
	position: absolute;
	content: "";
	bottom: 2px;
	z-index: -1;
	left: 0;
	border-radius: 2px;
	width: 150%;
	height: 2px;
	background-color: #ddd;
}

.more-card {
	user-select: none;
	position: absolute;
	bottom: 0;
	left: 0;
	height: 12px;
	text-align: right;
	width: 100%;
	padding: 0 10px;
	color: #fff;
	display: flex;
	justify-content: flex-end;
	align-items: center;
	font-size: 10px;
}
