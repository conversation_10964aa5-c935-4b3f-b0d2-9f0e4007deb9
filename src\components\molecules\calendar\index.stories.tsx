import { useState } from "react";

import { Story, Meta } from "@storybook/react/types-6-0";
import dayjs from "dayjs";

import { Calendar, Props } from ".";

// This default export determines where your story goes in the story list
export default {
  title: "Components|molecules/Calendar",
  component: Calendar,
} as Meta;

const DatePickerTemplate: Story<Props> = () => {
  const [dateTime, setDateTime] = useState<Date | undefined>(new Date());

  return (
    <>
      <Calendar defaultValue={new Date()} onDateChange={setDateTime} />
      <p style={{ textTransform: "uppercase", marginTop: 20 }}>
        Date-Time (for test): {dayjs(dateTime?.toString()).format("DD/MM/YYYY")}
      </p>
    </>
  );
};

const RangePickerTemplate: Story<Props> = ({ disabled }) => {
  const [dateRange, setDateRange] =
    useState<[Date, Date] | undefined>(undefined);

  return (
    <>
      <Calendar
        disabled={disabled}
        defaultValue={new Date()}
        onDateRangeChange={setDateRange}
        isRangePicker
      />
      <p style={{ textTransform: "uppercase", marginTop: 20 }}>
        Start Date:{" "}
        {dateRange && dateRange[0]
          ? dayjs(dateRange[0].toString()).format("DD/MM/YYYY")
          : "Chưa chọn ngày"}
      </p>
      <p style={{ textTransform: "uppercase" }}>
        End Date:{" "}
        {dateRange && dateRange[1]
          ? dayjs(dateRange[1].toString()).format("DD/MM/YYYY")
          : "Chưa chọn ngày"}
      </p>
    </>
  );
};

export const DatePicker = DatePickerTemplate.bind({});
export const RangePicker = RangePickerTemplate.bind({});
