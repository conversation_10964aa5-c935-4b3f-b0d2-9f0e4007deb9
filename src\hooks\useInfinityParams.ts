import { useCallback, useRef } from "react";

import useInfinity, { ResponseData } from "./useInfinity";

export const useInfinityParams = <
  ResponseItemData,
  AsyncActionParams extends { [key: string]: unknown } = {},
  ReturnAsyncAction extends Promise<ResponseData<ResponseItemData>> = Promise<
    ResponseData<ResponseItemData>
  >
>(
  asyncAction: (
    params: Parameters<Parameters<typeof useInfinity>[0]>[0] &
      Omit<AsyncActionParams, "pageSize" | "pageNum">
  ) => ReturnAsyncAction,
  otherArgs: Parameters<typeof useInfinity>[1]
) => {
  const paramsRef = useRef<Omit<AsyncActionParams, "pageSize" | "pageNum">>();
  const { goNextPage, gotoFirstPage, state, reset } = useInfinity(
    ({ pageSize, pageNum }) => {
      const params = paramsRef.current!;
      return asyncAction({ pageSize, pageNum, ...params });
    },
    otherArgs
  );

  const loadMore = useCallback(async () => {
    if (!paramsRef.current) return;
    goNextPage();
  }, [goNextPage]);

  const loadMoreWithParams = useCallback(
    (params: Omit<AsyncActionParams, "pageSize" | "pageNum">) => {
      paramsRef.current = params;
      gotoFirstPage();
    },
    [gotoFirstPage]
  );

  return {
    loadMore,
    loadMoreWithParams,
    state,
    reset,
  };
};
