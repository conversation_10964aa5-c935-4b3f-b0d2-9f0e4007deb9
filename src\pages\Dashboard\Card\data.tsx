import { DollarOutlined } from "@ant-design/icons";

export const CardsData = [
  {
    title: "Sales",
    color: {
      // backGround: "linear-gradient(180deg, #bb67ff 0%, #c484f3 100%)",
      backGround: "#26a843",
      boxShadow: "0px 10px 20px 0px #e0c6f5",
    },
    barValue: 70,
    value: "25,970",
    png: <DollarOutlined style={{ fontSize: "50px" }} rev={undefined} />,
    series: [
      {
        name: "Sales",
        data: [31, 40, 28, 51, 42, 109, 100],
      },
    ],
  },
  {
    title: "Revenue",
    color: {
      // backGround: "linear-gradient(180deg, #647e97 0%, #487eb0 100%)",
      backGround: "#dc3547",
      boxShadow: "0px 10px 20px 0px #647e97",
    },
    barValue: 80,
    value: "14,270",
    png: <DollarOutlined style={{ fontSize: "50px" }} rev={undefined} />,
    series: [
      {
        name: "Revenue",
        data: [10, 100, 50, 70, 80, 30, 40],
      },
    ],
  },
  {
    title: "Expenses",
    color: {
      backGround: "#17a3b8",
      boxShadow: "0px 10px 20px 0px #F9D59B",
    },
    barValue: 60,
    value: "4,270",
    png: <DollarOutlined style={{ fontSize: "50px" }} rev={undefined} />,
    series: [
      {
        name: "Expenses",
        data: [10, 25, 15, 30, 12, 15, 20],
      },
    ],
  },
  {
    title: "Expenses",
    color: {
      backGround: "#fec106",
      boxShadow: "0px 10px 20px 0px #F9D59B",
    },
    barValue: 60,
    value: "4,270",
    png: <DollarOutlined style={{ fontSize: "50px" }} rev={undefined} />,
    series: [
      {
        name: "Expenses",
        data: [10, 25, 15, 30, 12, 15, 20],
      },
    ],
  },
];
