import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import orderServices from "../order.service";

export const useGetOrderSourceWithoutPagination = () => {
  const [orderSourceRefetch, orderSourceData] = useAsync(
    useCallback(() => {
      return orderServices.getOrderSourceWithoutPagination();
    }, [])
  );
  return {
    orderSourceRefetch,
    orderSourceData: orderSourceData?.data?.data,
    loading: orderSourceData.loading,
  };
};
