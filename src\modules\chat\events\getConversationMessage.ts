import { createAsyncAction } from "libs/adapters/stores/core";
import { getConversationMessage } from "services/crm/chat";

import { MessageModel } from "../entities";

export const getConversationMessageEventCreator = () =>
  createAsyncAction(
    (options: {
      conversationId: string;
      beforeTime?: Date;
      pageSize: number;
    }) =>
      getConversationMessage({
        ...options,
        beforeTime: options.beforeTime?.toISOString(),
      }).then((res) => MessageModel.createMap(res.data?.data))
  );
