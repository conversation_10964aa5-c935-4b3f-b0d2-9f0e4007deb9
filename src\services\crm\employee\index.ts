import { useCallback } from "react";
import { toastSingleMode } from "components/atoms/toastify";
import { ERROR_MESSAGES } from "constants/errorMessages";
import { useAsync } from "hooks/useAsync";
import { CreateEmployeeGroupDtoType } from "modules/employee/dtos/createEmployeeGroup";
import { UpdateEmployeeGroupDtoType } from "modules/employee/dtos/updateEmployeeGroup";
import erpDriverV1 from "services/erp/erp-driver";

import crmDriverV1 from "../crm-driver-v1";
import { GetListEmployeeWithoutPaginationDto } from "./dto/employee.dto";

export type EmployeePasswordChangePayload = {
  oldPassword: string;
  newPassword: string;
};

export const refreshToken = (payload: { refreshToken: string }) =>
  crmDriverV1.post("/employees/external/auth/refresh-token", payload);

export const employeeLogin = (payload: {
  emailOrPhone: string;
  password: string;
  site: string;
}) => {
  return erpDriverV1.post(`/Users/<USER>
    No_: payload.emailOrPhone,
    Password: payload.password,
    site: payload.site,
  });
};
// export const employeeLogin = (payload: {
// 	emailOrPhone: string;
// 	password: string;
// }) =>
// 	crmDriverV1.post(`/employees/external/auth/login`, {
// 		emailOrPhoneNumber: payload.emailOrPhone,
// 		password: payload.password,
// 	});

export const employeeLogout = (payload: { refreshToken: string }) =>
  crmDriverV1.post("/employees/external/auth/logout", payload);

export const fetchEmployeeInfoSession = () => erpDriverV1.get("/Users/<USER>");

export const useGetListEmployeeWithoutPagination = () => {
  const [employeeRefetch, employeeData] = useAsync(
    useCallback(
      () =>
        crmDriverV1.get<GetListEmployeeWithoutPaginationDto>(
          "/employees/external/profile"
        ),
      []
    )
  );

  return {
    employeeRefetch,
    employeeData: employeeData?.data?.data,
  };
};

export const getListEmployee = (payload: {
  pageNum: number;
  pageSize: number;
  email?: string;
  phoneNumber?: string;
  name?: string;
}) => {
  return crmDriverV1.get(`/employees/external/profile`, {
    params: {
      ...payload,
    },
  });
};

export const getEmployeeDetail = (payload: { employeeId: number }) =>
  crmDriverV1.get(`/employees/external/profile/${payload.employeeId}`);

export const getEmployeeGroups = (payload?: {
  pageNum?: number;
  pageSize?: number;
}) => {
  return crmDriverV1.get("/employees/external/employee-groups", {
    params: {
      ...payload,
    },
  });
};

export const getEmployeeGroup = (payload: { groupId: number }) =>
  crmDriverV1.get(`/employees/external/employee-groups/${payload.groupId}`);

export type EmployeeUpdatePayload = {
  employeeId: number;
  name: string;
  phoneNumber: string;
  email: string;
  employeeGroupIds: Array<number>;
};

export const updateEmployee = ({
  employeeId,
  name,
  phoneNumber,
  email,
  employeeGroupIds,
}: EmployeeUpdatePayload) =>
  crmDriverV1.put(`/employees/external/profile/${employeeId}`, {
    name,
    phoneNumber,
    email,
    employeeGroupIds,
  });

export const updateEmployeeGroupById = (
  employeeGroupId: number,
  payload: UpdateEmployeeGroupDtoType
) =>
  crmDriverV1.put(`/employees/external/employee-groups/${employeeGroupId}`, {
    ...payload,
  });

export interface ForgetPasswordResponse {
  email: string;
}

export const employeeForgotPassword = (payload: ForgetPasswordResponse) => {
  return crmDriverV1.post(
    `/employees/external/profile/request-reset-password`,
    {
      email: payload.email,
    }
  );
};

export type EmployeeResetPasswordPayload = {
  token: string;
  newPassword: string;
};

export const employeeResetPassword = ({
  token,
  newPassword,
}: EmployeeResetPasswordPayload) =>
  crmDriverV1.put(`/employees/external/profile/reset-password`, {
    token,
    newPassword,
  });

export const changeEmployeePassword = (
  payload: EmployeePasswordChangePayload
) =>
  crmDriverV1.put("/employees/external/profile/change-password", {
    ...payload,
  });
export type EmployeeCreationPayload = {
  name: string;
  phoneNumber: string;
  email: string;
  password: string;
  employeeGroupIds: Array<number>;
};

export const createEmployee = (payload: EmployeeCreationPayload) =>
  crmDriverV1.post("/employees/external/profile", { ...payload });

export const createEmployeeGroup = (payload: CreateEmployeeGroupDtoType) =>
  crmDriverV1.post(`/employees/external/employee-groups`, payload);
