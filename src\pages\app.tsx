/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from "react";
import { SsoInitializer, TUserClaims } from "@dpt/react-pack";
import { BackTop, ConfigProvider, FloatButton, ThemeConfig } from "antd";
import i18n from "i18next";
import HttpBackend, { HttpBackendOptions } from "i18next-http-backend";
import { initReactI18next } from "react-i18next";

import { NotificationComponent } from "components/atoms/base/Notification";
import { useAppDispatch } from "store";
import authSlice from "store/auth/reducer";
import Routes from "./routes";

i18n
  .use(HttpBackend)
  .use(initReactI18next)
  .init<HttpBackendOptions>({
    lng: "vi",
    fallbackLng: "vi",
    interpolation: {
      escapeValue: false,
    },
    resources: {
      en: {
        translation: {
          home: "Home",
        },
      },
      vi: {
        translation: {
          home: "Trang chủ",
        },
      },
    },
  });

const antdToken: ThemeConfig = {
  components: {
    Form: {
      itemMarginBottom: 7,
    },
  },
};

const App = () => {
  const dispatch = useAppDispatch();

  const onSignInSuccess = useCallback((claims: TUserClaims) => {
    dispatch(
      authSlice.actions.updateEmployeeAuthState({
        employee: {
          userName: claims.username,
          name: claims.name,
          picture: claims.picture,
          site: claims.siteId,
        } as any,
        token: claims.access_token,
        refreshToken: "", // Logto does not provide refresh token by default
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ConfigProvider
      theme={antdToken}
      // locale={{
      //   locale: "vi_VN",
      //   Pagination: {
      //     jump_to: "Đi đến",
      //     page: "Trang",
      //   },
      // }}
    >
      <NotificationComponent />
      <SsoInitializer
        ssoCallbackPath="/sso-callback"
        onSignInSuccess={onSignInSuccess}
        listSites={[{ id: "KATA", name: "KATA" }]}
      >
        <Routes />
      </SsoInitializer>
    </ConfigProvider>
  );
};

export default App;
