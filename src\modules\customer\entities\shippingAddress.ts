import { emptyIfNull } from "helpers/string";
import { fromSchema } from "libs/adapters/dto";
import { Boolean, Enum, Model, String, Number, ModelValue } from "libs/domain";
import {
  CitySchema,
  CountrySchema,
  DistrictSchema,
  WardSchema,
} from "modules/location/entities";

export const ShippingAddressSchema = {
  _id: String(),
  shippingAddressId: Number(),
  name: String(),
  phoneNumber: String(),
  wardId: Number(),
  districtId: Number(),
  cityId: Number(),
  countryId: Number(),
  address: String(),
  detail: String(),
  isDefault: Boolean({ defaultValue: false }),
  type: Enum({ values: [0, 1] }),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  companyName: String(),
  country: fromSchema(CountrySchema),
  city: fromSchema(CitySchema),
  district: fromSchema(DistrictSchema),
  ward: fromSchema(WardSchema),
};

export const ShippingAddressModel = new Model(ShippingAddressSchema);
export type ShippingAddressModelType = ModelValue<typeof ShippingAddressModel>;

export const displayFullAddress = (
  shippingAddress: ShippingAddressModelType
) => {
  const { address, detail, city, district, ward } = shippingAddress || {};
  console.log(shippingAddress, "shippingAddress");
  return [
    emptyIfNull(address || detail),
    emptyIfNull(ward?.name),
    emptyIfNull(district?.name),
    emptyIfNull(city?.name),
  ].join(" ");
};
