import React from "react";

import { mapModifiers } from "helpers/component";

type Modifiers = "small" | "tiny";

export type IconName =
  | "angle-left"
  | "angle-right"
  | "browser"
  | "caret-down"
  | "cart"
  | "chart"
  | "chat"
  | "check-mark"
  | "circle-checked-green"
  | "circle-error-red"
  | "close-blue"
  | "close-modal"
  | "close-white"
  | "close"
  | "dashboard"
  | "dust-bin"
  | "emoji"
  | "employee"
  | "file-upload"
  | "filter"
  | "folder"
  | "human"
  | "image-upload"
  | "info"
  | "lead"
  | "line-dash"
  | "loading-blue"
  | "loading"
  | "menu"
  | "minus"
  | "news"
  | "note"
  | "pdf-file"
  | "pen"
  | "pencil"
  | "person-group"
  | "phone"
  | "plus-black"
  | "plus"
  | "recycle"
  | "remote-monitoring"
  | "send-message"
  | "setting"
  | "signout"
  | "slidebar"
  | "sort-ascending"
  | "store"
  | "system"
  | "triangle-warning-yellow"
  | "upload-file"
  | "upload-white"
  | "upload"
  | "plus-blue"
  | "search-blue"
  | "ticket"
  | "calendar-black"
  | "comment"
  | "employee-black"
  | "facebook"
  | "group"
  | "has-phone-number"
  | "inbox"
  | "not-has-phone-number"
  | "reply"
  | "report"
  | "seen"
  | "settings-black"
  | "tag"
  | "unreply"
  | "unseen";

export interface Props {
  modifiers?: Modifiers | Array<Modifiers>;
  iconName: IconName;
  onClick?: () => void;
  clickable?: boolean;
  style?: React.CSSProperties;
}

export const Icon: React.FC<Props> = ({
  iconName,
  modifiers,
  clickable,
  style,
  onClick,
}) => (
  <div
    className={mapModifiers(
      "a-icon",
      iconName,
      modifiers,
      clickable && "clickable",
      !!onClick && "clickable"
    )}
    aria-hidden="true"
    onClick={onClick}
    style={style}
  />
);
