import * as Yup from "yup";

export const validationSchemaOfCreation = Yup.object({
  bankName: Yup.string().required("Vui long nhập tên ngân hàng"),
  branchName: Yup.string().required("<PERSON>ui lòng nhập tên chi nh<PERSON>h"),
  owner: Yup.string().required("Vui lòng nhập tên chủ tài khoản"),
  accountNumber: Yup.string().required("Vui lòng nhập số tài khoản"),
});

export type CreateBankAccountFormPayload = {
  bankName: string;
  branchName: string;
  owner: string;
  accountNumber: string;
};
