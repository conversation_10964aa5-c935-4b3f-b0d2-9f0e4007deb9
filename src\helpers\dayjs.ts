import * as dayjsModule from "dayjs";
import toObject from "dayjs/plugin/toObject";

const dayjs = dayjsModule.default;
dayjs.extend(toObject);

/*
 * @see https://day.js.org/docs/en/display/difference
 * Input: Time string
 * Output: Diff time
 */
const UnitValues = {
  day: "ngày",
  hour: "giờ",
  minute: "phút",
  second: "giây",
} as const;
const units: Array<string> = ["day", "hour", "minute", "second"];
export const getDiffTime = (time: string) => {
  let timeDiff: number = 0;
  let unitDiff: string | null = null;

  // eslint-disable-next-line no-restricted-syntax
  for (const unit of units) {
    timeDiff = dayjs().diff(dayjs(time), unit as dayjsModule.QUnitType);
    if (timeDiff > 0) {
      unitDiff = unit;
      break;
    }
  }

  if (unitDiff) {
    return `${timeDiff} ${
      UnitValues[unitDiff as keyof typeof UnitValues]
    } trước`;
  }
  return "Vừa xong";
};

export default dayjs;
