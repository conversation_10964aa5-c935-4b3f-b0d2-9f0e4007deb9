import { fromSchema, createMapper, merge } from "libs/adapters/dto";

import { ConversationSchema, messageCreatorType } from "../entities";

export const assignConversationItemDto = createMapper(
  fromSchema(ConversationSchema),
  merge((data) => ({
    client: {
      name: `${data.client.firstName} ${data.client.lastName}`,
    },
    latestMessage: {
      creatorType: messageCreatorType(data, data.latestMessage.sender),
    },
  }))
);

export type AssignConversationDtoType = ReturnType<
  typeof assignConversationItemDto
>;
