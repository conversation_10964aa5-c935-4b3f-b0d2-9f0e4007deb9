/* eslint-disable @typescript-eslint/no-explicit-any */
export interface IObject {
  [key: string]: any;
}

export type ICallback = (...args: unknown[]) => unknown;

export type SchemaType<O extends IObject> = {
  [field in keyof O]: O[field];
};

export type SchemaTypeParser<R> = (value: any, extraConfig?: any) => R;

export type SchemaReturnValue<R = unknown> = R extends ICallback
  ? ReturnType<R>
  : R extends IObject
  ? { [key in keyof R]: SchemaReturnValue<R[key]> }
  : never;

export type SchemaCreator<R, O = undefined> = (
  options?: O
) => SchemaTypeParser<R>;
