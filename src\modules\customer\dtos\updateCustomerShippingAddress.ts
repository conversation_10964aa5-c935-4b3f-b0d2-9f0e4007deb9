import pick from "object.pick";

import { createMapper, fromSchema, merge } from "libs/adapters/dto";

import { ShippingAddressSchema } from "../entities";

export const updateCustomerShippingAddressDto = createMapper(
  fromSchema(
    pick(ShippingAddressSchema, [
      "name",
      "wardId",
      "districtId",
      "cityId",
      "countryId",
      "detail",
      "isDefault",
      "phoneNumber",
      "type",
      "companyName",
      "address",
    ])
  ),
  merge((data) => ({
    countryId: 1,
    companyName: data.companyName || undefined,
  }))
);

export type UpdateCustomerShippingAddressDtoType = ReturnType<
  typeof updateCustomerShippingAddressDto
>;
