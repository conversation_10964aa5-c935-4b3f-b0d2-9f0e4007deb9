import React, { useMemo } from "react";

import <PERSON>u, { SubMenu, Item as <PERSON><PERSON><PERSON><PERSON>, Divider, MenuProps } from "rc-menu";

import { Link } from "components/utils/link";
import { Tooltip } from "components/utils/tooltip";
import { mapModifiers } from "helpers/component";

export interface IGroupMenu {
  id: number;
  title: string;
  href: string;
  active?: boolean;
  icon?: React.ReactNode;
  subMenus?: IGroupMenu[];
}

export interface Props extends MenuProps {
  menu: IGroupMenu[];
  isCloseSideBar: boolean;
  onToggleSideBar?: () => void;
  expandIcon?: boolean;
}

export interface MenuTooltipProps {
  icon?: React.ReactNode;
  title: string;
  href: string;
}

export const MenuTooltip: React.FC<MenuTooltipProps> = ({
  title,
  href,
  icon,
}) => (
  <Tooltip description={title} id={title} place="right">
    <div className="o-sidebar_wrapicon">
      <Link to={href} className="o-sidebar_linktooltip">
        {icon}
      </Link>
    </div>
  </Tooltip>
);

export const SideBar: React.FC<Props> = ({
  onToggleSideBar,
  menu,
  isCloseSideBar,
}) => {
  const renderMenuData = useMemo(
    () =>
      menu.map((item) => (
        <React.Fragment key={item.id}>
          {item.subMenus && item.subMenus.length > 0 ? (
            <SubMenu
              className={mapModifiers(
                "o-sidebar_menu",
                item.active && "active"
              )}
              title={
                <div className="o-sidebar_wraptitle">
                  <Link to={item.href} className="o-sidebar_wrapicon">
                    {item.icon}
                  </Link>

                  {!isCloseSideBar && (
                    <Link to={item.href} className="o-sidebar_linkmenu">
                      {item.title}
                    </Link>
                  )}
                </div>
              }
            >
              {item.subMenus.map((itemMenu) => (
                <React.Fragment key={itemMenu.id}>
                  <MenuItem
                    className={mapModifiers(
                      "o-sidebar_submenu",
                      itemMenu.active && "active"
                    )}
                  >
                    <Link to={itemMenu.href} className="o-sidebar_linksubmenu">
                      {itemMenu.title}
                    </Link>
                  </MenuItem>

                  {item.subMenus && <Divider />}
                </React.Fragment>
              ))}
            </SubMenu>
          ) : (
            <MenuItem
              className={mapModifiers(
                "o-sidebar_menu",
                isCloseSideBar && "tooltip",
                item.active && "active"
              )}
            >
              <div className="o-sidebar_wraptitle">
                {isCloseSideBar ? (
                  <MenuTooltip
                    href={item.href}
                    title={item.title}
                    icon={item.icon}
                  />
                ) : (
                  <>
                    <div className="o-sidebar_wrapicon">{item.icon}</div>
                    <Link to={item.href} className="o-sidebar_linkmenu">
                      {item.title}
                    </Link>
                  </>
                )}
              </div>
            </MenuItem>
          )}

          {!isCloseSideBar && <Divider />}
        </React.Fragment>
      )),
    [isCloseSideBar, menu]
  );

  return (
    <div className={mapModifiers("o-sidebar", isCloseSideBar && "closed")}>
      <div
        className="o-sidebar_hamburger"
        aria-hidden
        onClick={onToggleSideBar}
      >
        <span />
        <span />
        <span />
      </div>

      <div className="o-sidebar_wrapmenu">
        {isCloseSideBar && <Menu selectable={false}>{renderMenuData}</Menu>}
        {!isCloseSideBar && (
          <Menu mode="inline" selectable={false}>
            {renderMenuData}
          </Menu>
        )}
      </div>
    </div>
  );
};
