import React, { useEffect, useMemo } from "react";
import { Form, Input, Flex, Row, Col } from "antd";
import { useSearchParams, useParams } from "react-router";

import { BaseButton } from "components/atoms/base/button/BaseButton";
import { showLoading } from "components/atoms/base/Spinner";
import { Heading } from "components/atoms/heading";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { COLOR } from "constants/color";
import * as navigationHelper from "helpers/navigation";
import { PageParamsType } from "libs/react";

import { ChildrenPage } from "../types";
import { OrderSourceFormType } from "./constant";
import { OrderSourceDetailPageVm } from "./vm";

type FormValueType = Partial<OrderSourceFormType>;

function OrderSourceDetailV2() {
  const [form] = Form.useForm<FormValueType>();
  const [searchParams] = useSearchParams();
  const { sourceId } =
    useParams<PageParamsType<ChildrenPage["orderSourceDetail"]>>();

  const {
    orderSourceData,
    loading,
    updateOrderSourceState,
    handleUpdateOrderSource,
  } = OrderSourceDetailPageVm({
    orderSourceId: Number(sourceId),
  });

  const pageActionType = searchParams.get("action") || "view";
  const editMode = pageActionType === "edit";
  const viewMode = !editMode;

  // Set form values when data is loaded
  useEffect(() => {
    if (orderSourceData) {
      form.setFieldsValue({
        name: orderSourceData.name,
      });
    }
  }, [orderSourceData, form]);

  // Show loading when updating
  useEffect(() => {
    if (updateOrderSourceState.loading) {
      showLoading(true);
    } else {
      showLoading(false);
    }
  }, [updateOrderSourceState.loading]);

  const onFinish = (values: FormValueType) => {
    handleUpdateOrderSource(values);
  };

  if (loading) {
    return (
      <General>
        <div className="flex justify-center items-center h-64">
          <div>Đang tải...</div>
        </div>
      </General>
    );
  }

  return (
    <General>
      <title key="title">Thông tin nguồn đơn</title>
      <Section>
        <div className="flex flex-col gap-6 p-6">
          <Heading type="h1" modifiers="primary">
            THÔNG TIN NGUỒN ĐƠN
          </Heading>

          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            // disabled={viewMode}
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="Nguồn đơn"
                  name="name"
                  rules={[
                    {
                      required: editMode,
                      message: "Vui lòng nhập tên nguồn đơn",
                    },
                  ]}
                >
                  <Input
                    placeholder="Tên nguồn đơn"
                    // readOnly={viewMode}
                    disabled={viewMode}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Flex align="center" justify="end" gap={16} className="mt-6">
              <BaseButton type="default" onClick={navigationHelper.goBack}>
                Quay lại
              </BaseButton>
              {editMode && (
                <BaseButton
                  htmlType="submit"
                  type="primary"
                  bgColor={COLOR.BLUE[500]}
                  hoverColor={COLOR.BLUE[700]}
                  disabled={updateOrderSourceState.loading}
                  loading={updateOrderSourceState.loading}
                >
                  Lưu
                </BaseButton>
              )}
            </Flex>
          </Form>
        </div>
      </Section>
    </General>
  );
}

export default OrderSourceDetailV2;
