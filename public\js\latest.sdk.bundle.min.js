!function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=372)}([function(e,t,n){var r=n(2),i=n(19),o=n(12),a=n(13),s=n(20),c=function(e,t,n){var l,u,f,d,h=e&c.F,v=e&c.G,p=e&c.S,g=e&c.P,m=e&c.B,y=v?r:p?r[t]||(r[t]={}):(r[t]||{}).prototype,_=v?i:i[t]||(i[t]={}),S=_.prototype||(_.prototype={});for(l in v&&(n=t),n)f=((u=!h&&y&&void 0!==y[l])?y:n)[l],d=m&&u?s(f,r):g&&"function"==typeof f?s(Function.call,f):f,y&&a(y,l,f,e&c.U),_[l]!=f&&o(_,l,d),g&&S[l]!=f&&(S[l]=f)};r.core=i,c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},function(e,t,n){var r=n(4);e.exports=function(e){if(!r(e))throw TypeError(e+" is not an object!");return e}},function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,n){var r=n(50)("wks"),i=n(34),o=n(2).Symbol,a="function"==typeof o;(e.exports=function(e){return r[e]||(r[e]=a&&o[e]||(a?o:i)("Symbol."+e))}).store=r},function(e,t,n){var r=n(22),i=Math.min;e.exports=function(e){return e>0?i(r(e),9007199254740991):0}},function(e,t,n){e.exports=!n(3)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},function(e,t,n){var r=n(1),i=n(101),o=n(24),a=Object.defineProperty;t.f=n(7)?Object.defineProperty:function(e,t,n){if(r(e),t=o(t,!0),r(n),i)try{return a(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t,n){var r=n(25);e.exports=function(e){return Object(r(e))}},function(e,t,n){"use strict";var r,i=n(137),o=Object.prototype.toString,a=(r=Object.create(null),function(e){var t=o.call(e);return r[t]||(r[t]=t.slice(8,-1).toLowerCase())});function s(e){return e=e.toLowerCase(),function(t){return a(t)===e}}function c(e){return Array.isArray(e)}function l(e){return void 0===e}var u=s("ArrayBuffer");function f(e){return null!==e&&"object"==typeof e}function d(e){if("object"!==a(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}var h=s("Date"),v=s("File"),p=s("Blob"),g=s("FileList");function m(e){return"[object Function]"===o.call(e)}var y=s("URLSearchParams");function _(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),c(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}var S,E=(S="undefined"!=typeof Uint8Array&&Object.getPrototypeOf(Uint8Array),function(e){return S&&e instanceof S});e.exports={isArray:c,isArrayBuffer:u,isBuffer:function(e){return null!==e&&!l(e)&&null!==e.constructor&&!l(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return e&&("function"==typeof FormData&&e instanceof FormData||"[object FormData]"===o.call(e)||m(e.toString)&&"[object FormData]"===e.toString())},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&u(e.buffer)},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:f,isPlainObject:d,isUndefined:l,isDate:h,isFile:v,isBlob:p,isFunction:m,isStream:function(e){return f(e)&&m(e.pipe)},isURLSearchParams:y,isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:_,merge:function e(){var t={};function n(n,r){d(t[r])&&d(n)?t[r]=e(t[r],n):d(n)?t[r]=e({},n):c(n)?t[r]=n.slice():t[r]=n}for(var r=0,i=arguments.length;r<i;r++)_(arguments[r],n);return t},extend:function(e,t,n){return _(t,(function(t,r){e[r]=n&&"function"==typeof t?i(t,n):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e},inherits:function(e,t,n,r){e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,n&&Object.assign(e.prototype,n)},toFlatObject:function(e,t,n){var r,i,o,a={};t=t||{};do{for(i=(r=Object.getOwnPropertyNames(e)).length;i-- >0;)a[o=r[i]]||(t[o]=e[o],a[o]=!0);e=Object.getPrototypeOf(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:a,kindOfTest:s,endsWith:function(e,t,n){e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;var r=e.indexOf(t,n);return-1!==r&&r===n},toArray:function(e){if(!e)return null;var t=e.length;if(l(t))return null;for(var n=new Array(t);t-- >0;)n[t]=e[t];return n},isTypedArray:E,isFileList:g}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t,n){var r=n(8),i=n(33);e.exports=n(7)?function(e,t,n){return r.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t,n){var r=n(2),i=n(12),o=n(15),a=n(34)("src"),s=n(149),c=(""+s).split("toString");n(19).inspectSource=function(e){return s.call(e)},(e.exports=function(e,t,n,s){var l="function"==typeof n;l&&(o(n,"name")||i(n,"name",t)),e[t]!==n&&(l&&(o(n,a)||i(n,a,e[t]?""+e[t]:c.join(String(t)))),e===r?e[t]=n:s?e[t]?e[t]=n:i(e,t,n):(delete e[t],i(e,t,n)))})(Function.prototype,"toString",(function(){return"function"==typeof this&&this[a]||s.call(this)}))},function(e,t,n){var r=n(0),i=n(3),o=n(25),a=/"/g,s=function(e,t,n,r){var i=String(o(e)),s="<"+t;return""!==n&&(s+=" "+n+'="'+String(r).replace(a,"&quot;")+'"'),s+">"+i+"</"+t+">"};e.exports=function(e,t){var n={};n[e]=t(s),r(r.P+r.F*i((function(){var t=""[e]('"');return t!==t.toLowerCase()||t.split('"').length>3})),"String",n)}},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,n){var r=n(51),i=n(25);e.exports=function(e){return r(i(e))}},function(e,t,n){var r=n(52),i=n(33),o=n(16),a=n(24),s=n(15),c=n(101),l=Object.getOwnPropertyDescriptor;t.f=n(7)?l:function(e,t){if(e=o(e),t=a(t,!0),c)try{return l(e,t)}catch(e){}if(s(e,t))return i(!r.f.call(e,t),e[t])}},function(e,t,n){var r=n(15),i=n(9),o=n(73)("IE_PROTO"),a=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=i(e),r(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?a:null}},function(e,t){var n=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=n)},function(e,t,n){var r=n(11);e.exports=function(e,t,n){if(r(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}}},function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t){var n=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:n)(e)}},function(e,t,n){"use strict";var r=n(3);e.exports=function(e,t){return!!e&&r((function(){t?e.call(null,(function(){}),1):e.call(null)}))}},function(e,t,n){var r=n(4);e.exports=function(e,t){if(!r(e))return e;var n,i;if(t&&"function"==typeof(n=e.toString)&&!r(i=n.call(e)))return i;if("function"==typeof(n=e.valueOf)&&!r(i=n.call(e)))return i;if(!t&&"function"==typeof(n=e.toString)&&!r(i=n.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},function(e,t){e.exports=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,n){var r=n(0),i=n(19),o=n(3);e.exports=function(e,t){var n=(i.Object||{})[e]||Object[e],a={};a[e]=t(n),r(r.S+r.F*o((function(){n(1)})),"Object",a)}},function(e,t,n){var r=n(20),i=n(51),o=n(9),a=n(6),s=n(89);e.exports=function(e,t){var n=1==e,c=2==e,l=3==e,u=4==e,f=6==e,d=5==e||f,h=t||s;return function(t,s,v){for(var p,g,m=o(t),y=i(m),_=r(s,v,3),S=a(y.length),E=0,C=n?h(t,S):c?h(t,0):void 0;S>E;E++)if((d||E in y)&&(g=_(p=y[E],E,m),e))if(n)C[E]=g;else if(g)switch(e){case 3:return!0;case 5:return p;case 6:return E;case 2:C.push(p)}else if(u)return!1;return f?-1:l||u?u:C}}},function(e,t,n){"use strict";if(n(7)){var r=n(30),i=n(2),o=n(3),a=n(0),s=n(65),c=n(97),l=n(20),u=n(40),f=n(33),d=n(12),h=n(42),v=n(22),p=n(6),g=n(129),m=n(36),y=n(24),_=n(15),S=n(46),E=n(4),C=n(9),I=n(86),T=n(37),b=n(18),R=n(38).f,O=n(88),w=n(34),A=n(5),k=n(27),M=n(55),P=n(54),D=n(91),x=n(48),N=n(60),L=n(39),F=n(90),j=n(118),U=n(8),V=n(17),G=U.f,B=V.f,H=i.RangeError,q=i.TypeError,W=i.Uint8Array,Y=Array.prototype,K=c.ArrayBuffer,z=c.DataView,J=k(0),Q=k(2),X=k(3),$=k(4),Z=k(5),ee=k(6),te=M(!0),ne=M(!1),re=D.values,ie=D.keys,oe=D.entries,ae=Y.lastIndexOf,se=Y.reduce,ce=Y.reduceRight,le=Y.join,ue=Y.sort,fe=Y.slice,de=Y.toString,he=Y.toLocaleString,ve=A("iterator"),pe=A("toStringTag"),ge=w("typed_constructor"),me=w("def_constructor"),ye=s.CONSTR,_e=s.TYPED,Se=s.VIEW,Ee=k(1,(function(e,t){return Re(P(e,e[me]),t)})),Ce=o((function(){return 1===new W(new Uint16Array([1]).buffer)[0]})),Ie=!!W&&!!W.prototype.set&&o((function(){new W(1).set({})})),Te=function(e,t){var n=v(e);if(n<0||n%t)throw H("Wrong offset!");return n},be=function(e){if(E(e)&&_e in e)return e;throw q(e+" is not a typed array!")},Re=function(e,t){if(!E(e)||!(ge in e))throw q("It is not a typed array constructor!");return new e(t)},Oe=function(e,t){return we(P(e,e[me]),t)},we=function(e,t){for(var n=0,r=t.length,i=Re(e,r);r>n;)i[n]=t[n++];return i},Ae=function(e,t,n){G(e,t,{get:function(){return this._d[n]}})},ke=function(e){var t,n,r,i,o,a,s=C(e),c=arguments.length,u=c>1?arguments[1]:void 0,f=void 0!==u,d=O(s);if(null!=d&&!I(d)){for(a=d.call(s),r=[],t=0;!(o=a.next()).done;t++)r.push(o.value);s=r}for(f&&c>2&&(u=l(u,arguments[2],2)),t=0,n=p(s.length),i=Re(this,n);n>t;t++)i[t]=f?u(s[t],t):s[t];return i},Me=function(){for(var e=0,t=arguments.length,n=Re(this,t);t>e;)n[e]=arguments[e++];return n},Pe=!!W&&o((function(){he.call(new W(1))})),De=function(){return he.apply(Pe?fe.call(be(this)):be(this),arguments)},xe={copyWithin:function(e,t){return j.call(be(this),e,t,arguments.length>2?arguments[2]:void 0)},every:function(e){return $(be(this),e,arguments.length>1?arguments[1]:void 0)},fill:function(e){return F.apply(be(this),arguments)},filter:function(e){return Oe(this,Q(be(this),e,arguments.length>1?arguments[1]:void 0))},find:function(e){return Z(be(this),e,arguments.length>1?arguments[1]:void 0)},findIndex:function(e){return ee(be(this),e,arguments.length>1?arguments[1]:void 0)},forEach:function(e){J(be(this),e,arguments.length>1?arguments[1]:void 0)},indexOf:function(e){return ne(be(this),e,arguments.length>1?arguments[1]:void 0)},includes:function(e){return te(be(this),e,arguments.length>1?arguments[1]:void 0)},join:function(e){return le.apply(be(this),arguments)},lastIndexOf:function(e){return ae.apply(be(this),arguments)},map:function(e){return Ee(be(this),e,arguments.length>1?arguments[1]:void 0)},reduce:function(e){return se.apply(be(this),arguments)},reduceRight:function(e){return ce.apply(be(this),arguments)},reverse:function(){for(var e,t=be(this).length,n=Math.floor(t/2),r=0;r<n;)e=this[r],this[r++]=this[--t],this[t]=e;return this},some:function(e){return X(be(this),e,arguments.length>1?arguments[1]:void 0)},sort:function(e){return ue.call(be(this),e)},subarray:function(e,t){var n=be(this),r=n.length,i=m(e,r);return new(P(n,n[me]))(n.buffer,n.byteOffset+i*n.BYTES_PER_ELEMENT,p((void 0===t?r:m(t,r))-i))}},Ne=function(e,t){return Oe(this,fe.call(be(this),e,t))},Le=function(e){be(this);var t=Te(arguments[1],1),n=this.length,r=C(e),i=p(r.length),o=0;if(i+t>n)throw H("Wrong length!");for(;o<i;)this[t+o]=r[o++]},Fe={entries:function(){return oe.call(be(this))},keys:function(){return ie.call(be(this))},values:function(){return re.call(be(this))}},je=function(e,t){return E(e)&&e[_e]&&"symbol"!=typeof t&&t in e&&String(+t)==String(t)},Ue=function(e,t){return je(e,t=y(t,!0))?f(2,e[t]):B(e,t)},Ve=function(e,t,n){return!(je(e,t=y(t,!0))&&E(n)&&_(n,"value"))||_(n,"get")||_(n,"set")||n.configurable||_(n,"writable")&&!n.writable||_(n,"enumerable")&&!n.enumerable?G(e,t,n):(e[t]=n.value,e)};ye||(V.f=Ue,U.f=Ve),a(a.S+a.F*!ye,"Object",{getOwnPropertyDescriptor:Ue,defineProperty:Ve}),o((function(){de.call({})}))&&(de=he=function(){return le.call(this)});var Ge=h({},xe);h(Ge,Fe),d(Ge,ve,Fe.values),h(Ge,{slice:Ne,set:Le,constructor:function(){},toString:de,toLocaleString:De}),Ae(Ge,"buffer","b"),Ae(Ge,"byteOffset","o"),Ae(Ge,"byteLength","l"),Ae(Ge,"length","e"),G(Ge,pe,{get:function(){return this[_e]}}),e.exports=function(e,t,n,c){var l=e+((c=!!c)?"Clamped":"")+"Array",f="get"+e,h="set"+e,v=i[l],m=v||{},y=v&&b(v),_=!v||!s.ABV,C={},I=v&&v.prototype,O=function(e,n){G(e,n,{get:function(){return function(e,n){var r=e._d;return r.v[f](n*t+r.o,Ce)}(this,n)},set:function(e){return function(e,n,r){var i=e._d;c&&(r=(r=Math.round(r))<0?0:r>255?255:255&r),i.v[h](n*t+i.o,r,Ce)}(this,n,e)},enumerable:!0})};_?(v=n((function(e,n,r,i){u(e,v,l,"_d");var o,a,s,c,f=0,h=0;if(E(n)){if(!(n instanceof K||"ArrayBuffer"==(c=S(n))||"SharedArrayBuffer"==c))return _e in n?we(v,n):ke.call(v,n);o=n,h=Te(r,t);var m=n.byteLength;if(void 0===i){if(m%t)throw H("Wrong length!");if((a=m-h)<0)throw H("Wrong length!")}else if((a=p(i)*t)+h>m)throw H("Wrong length!");s=a/t}else s=g(n),o=new K(a=s*t);for(d(e,"_d",{b:o,o:h,l:a,e:s,v:new z(o)});f<s;)O(e,f++)})),I=v.prototype=T(Ge),d(I,"constructor",v)):o((function(){v(1)}))&&o((function(){new v(-1)}))&&N((function(e){new v,new v(null),new v(1.5),new v(e)}),!0)||(v=n((function(e,n,r,i){var o;return u(e,v,l),E(n)?n instanceof K||"ArrayBuffer"==(o=S(n))||"SharedArrayBuffer"==o?void 0!==i?new m(n,Te(r,t),i):void 0!==r?new m(n,Te(r,t)):new m(n):_e in n?we(v,n):ke.call(v,n):new m(g(n))})),J(y!==Function.prototype?R(m).concat(R(y)):R(m),(function(e){e in v||d(v,e,m[e])})),v.prototype=I,r||(I.constructor=v));var w=I[ve],A=!!w&&("values"==w.name||null==w.name),k=Fe.values;d(v,ge,!0),d(I,_e,l),d(I,Se,!0),d(I,me,v),(c?new v(1)[pe]==l:pe in I)||G(I,pe,{get:function(){return l}}),C[l]=v,a(a.G+a.W+a.F*(v!=m),C),a(a.S,l,{BYTES_PER_ELEMENT:t}),a(a.S+a.F*o((function(){m.of.call(v,1)})),l,{from:ke,of:Me}),"BYTES_PER_ELEMENT"in I||d(I,"BYTES_PER_ELEMENT",t),a(a.P,l,xe),L(l),a(a.P+a.F*Ie,l,{set:Le}),a(a.P+a.F*!A,l,Fe),r||I.toString==de||(I.toString=de),a(a.P+a.F*o((function(){new v(1).slice()})),l,{slice:Ne}),a(a.P+a.F*(o((function(){return[1,2].toLocaleString()!=new v([1,2]).toLocaleString()}))||!o((function(){I.toLocaleString.call([1,2])}))),l,{toLocaleString:De}),x[l]=A?w:k,r||A||d(I,ve,k)}}else e.exports=function(){}},function(e,t,n){var r=n(124),i=n(0),o=n(50)("metadata"),a=o.store||(o.store=new(n(127))),s=function(e,t,n){var i=a.get(e);if(!i){if(!n)return;a.set(e,i=new r)}var o=i.get(t);if(!o){if(!n)return;i.set(t,o=new r)}return o};e.exports={store:a,map:s,has:function(e,t,n){var r=s(t,n,!1);return void 0!==r&&r.has(e)},get:function(e,t,n){var r=s(t,n,!1);return void 0===r?void 0:r.get(e)},set:function(e,t,n,r){s(n,r,!0).set(e,t)},keys:function(e,t){var n=s(e,t,!1),r=[];return n&&n.forEach((function(e,t){r.push(t)})),r},key:function(e){return void 0===e||"symbol"==typeof e?e:String(e)},exp:function(e){i(i.S,"Reflect",e)}}},function(e,t){e.exports=!1},function(e,t,n){var r=n(34)("meta"),i=n(4),o=n(15),a=n(8).f,s=0,c=Object.isExtensible||function(){return!0},l=!n(3)((function(){return c(Object.preventExtensions({}))})),u=function(e){a(e,r,{value:{i:"O"+ ++s,w:{}}})},f=e.exports={KEY:r,NEED:!1,fastKey:function(e,t){if(!i(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!o(e,r)){if(!c(e))return"F";if(!t)return"E";u(e)}return e[r].i},getWeak:function(e,t){if(!o(e,r)){if(!c(e))return!0;if(!t)return!1;u(e)}return e[r].w},onFreeze:function(e){return l&&f.NEED&&c(e)&&!o(e,r)&&u(e),e}}},function(e,t,n){var r=n(5)("unscopables"),i=Array.prototype;null==i[r]&&n(12)(i,r,{}),e.exports=function(e){i[r][e]=!0}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){var n=0,r=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+r).toString(36))}},function(e,t,n){var r=n(103),i=n(74);e.exports=Object.keys||function(e){return r(e,i)}},function(e,t,n){var r=n(22),i=Math.max,o=Math.min;e.exports=function(e,t){return(e=r(e))<0?i(e+t,0):o(e,t)}},function(e,t,n){var r=n(1),i=n(104),o=n(74),a=n(73)("IE_PROTO"),s=function(){},c=function(){var e,t=n(71)("iframe"),r=o.length;for(t.style.display="none",n(75).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),c=e.F;r--;)delete c.prototype[o[r]];return c()};e.exports=Object.create||function(e,t){var n;return null!==e?(s.prototype=r(e),n=new s,s.prototype=null,n[a]=e):n=c(),void 0===t?n:i(n,t)}},function(e,t,n){var r=n(103),i=n(74).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,i)}},function(e,t,n){"use strict";var r=n(2),i=n(8),o=n(7),a=n(5)("species");e.exports=function(e){var t=r[e];o&&t&&!t[a]&&i.f(t,a,{configurable:!0,get:function(){return this}})}},function(e,t){e.exports=function(e,t,n,r){if(!(e instanceof t)||void 0!==r&&r in e)throw TypeError(n+": incorrect invocation!");return e}},function(e,t,n){var r=n(20),i=n(116),o=n(86),a=n(1),s=n(6),c=n(88),l={},u={};(t=e.exports=function(e,t,n,f,d){var h,v,p,g,m=d?function(){return e}:c(e),y=r(n,f,t?2:1),_=0;if("function"!=typeof m)throw TypeError(e+" is not iterable!");if(o(m)){for(h=s(e.length);h>_;_++)if((g=t?y(a(v=e[_])[0],v[1]):y(e[_]))===l||g===u)return g}else for(p=m.call(e);!(v=p.next()).done;)if((g=i(p,y,v.value,t))===l||g===u)return g}).BREAK=l,t.RETURN=u},function(e,t,n){var r=n(13);e.exports=function(e,t,n){for(var i in t)r(e,i,t[i],n);return e}},function(e,t,n){var r=n(4);e.exports=function(e,t){if(!r(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},function(e,t,n){"use strict";const r={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};r.localCName=r.generateIdentifier(),r.splitLines=function(e){return e.trim().split("\n").map(e=>e.trim())},r.splitSections=function(e){return e.split("\nm=").map((e,t)=>(t>0?"m="+e:e).trim()+"\r\n")},r.getDescription=function(e){const t=r.splitSections(e);return t&&t[0]},r.getMediaSections=function(e){const t=r.splitSections(e);return t.shift(),t},r.matchPrefix=function(e,t){return r.splitLines(e).filter(e=>0===e.indexOf(t))},r.parseCandidate=function(e){let t;t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" ");const n={foundation:t[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]};for(let e=8;e<t.length;e+=2)switch(t[e]){case"raddr":n.relatedAddress=t[e+1];break;case"rport":n.relatedPort=parseInt(t[e+1],10);break;case"tcptype":n.tcpType=t[e+1];break;case"ufrag":n.ufrag=t[e+1],n.usernameFragment=t[e+1];break;default:void 0===n[t[e]]&&(n[t[e]]=t[e+1])}return n},r.writeCandidate=function(e){const t=[];t.push(e.foundation);const n=e.component;"rtp"===n?t.push(1):"rtcp"===n?t.push(2):t.push(n),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);const r=e.type;return t.push("typ"),t.push(r),"host"!==r&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},r.parseIceOptions=function(e){return e.substr(14).split(" ")},r.parseRtpMap=function(e){let t=e.substr(9).split(" ");const n={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),n.name=t[0],n.clockRate=parseInt(t[1],10),n.channels=3===t.length?parseInt(t[2],10):1,n.numChannels=n.channels,n},r.writeRtpMap=function(e){let t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);const n=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==n?"/"+n:"")+"\r\n"},r.parseExtmap=function(e){const t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},r.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},r.parseFmtp=function(e){const t={};let n;const r=e.substr(e.indexOf(" ")+1).split(";");for(let e=0;e<r.length;e++)n=r[e].trim().split("="),t[n[0].trim()]=n[1];return t},r.writeFmtp=function(e){let t="",n=e.payloadType;if(void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){const r=[];Object.keys(e.parameters).forEach(t=>{void 0!==e.parameters[t]?r.push(t+"="+e.parameters[t]):r.push(t)}),t+="a=fmtp:"+n+" "+r.join(";")+"\r\n"}return t},r.parseRtcpFb=function(e){const t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},r.writeRtcpFb=function(e){let t="",n=e.payloadType;return void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(e=>{t+="a=rtcp-fb:"+n+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"}),t},r.parseSsrcMedia=function(e){const t=e.indexOf(" "),n={ssrc:parseInt(e.substr(7,t-7),10)},r=e.indexOf(":",t);return r>-1?(n.attribute=e.substr(t+1,r-t-1),n.value=e.substr(r+1)):n.attribute=e.substr(t+1),n},r.parseSsrcGroup=function(e){const t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map(e=>parseInt(e,10))}},r.getMid=function(e){const t=r.matchPrefix(e,"a=mid:")[0];if(t)return t.substr(6)},r.parseFingerprint=function(e){const t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},r.getDtlsParameters=function(e,t){return{role:"auto",fingerprints:r.matchPrefix(e+t,"a=fingerprint:").map(r.parseFingerprint)}},r.writeDtlsParameters=function(e,t){let n="a=setup:"+t+"\r\n";return e.fingerprints.forEach(e=>{n+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"}),n},r.parseCryptoLine=function(e){const t=e.substr(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},r.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?r.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},r.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;const t=e.substr(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},r.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},r.getCryptoParameters=function(e,t){return r.matchPrefix(e+t,"a=crypto:").map(r.parseCryptoLine)},r.getIceParameters=function(e,t){const n=r.matchPrefix(e+t,"a=ice-ufrag:")[0],i=r.matchPrefix(e+t,"a=ice-pwd:")[0];return n&&i?{usernameFragment:n.substr(12),password:i.substr(10)}:null},r.writeIceParameters=function(e){let t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},r.parseRtpParameters=function(e){const t={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=r.splitLines(e)[0].split(" ");for(let i=3;i<n.length;i++){const o=n[i],a=r.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(a){const n=r.parseRtpMap(a),i=r.matchPrefix(e,"a=fmtp:"+o+" ");switch(n.parameters=i.length?r.parseFmtp(i[0]):{},n.rtcpFeedback=r.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(r.parseRtcpFb),t.codecs.push(n),n.name.toUpperCase()){case"RED":case"ULPFEC":t.fecMechanisms.push(n.name.toUpperCase())}}}return r.matchPrefix(e,"a=extmap:").forEach(e=>{t.headerExtensions.push(r.parseExtmap(e))}),t},r.writeRtpDescription=function(e,t){let n="";n+="m="+e+" ",n+=t.codecs.length>0?"9":"0",n+=" UDP/TLS/RTP/SAVPF ",n+=t.codecs.map(e=>void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType).join(" ")+"\r\n",n+="c=IN IP4 0.0.0.0\r\n",n+="a=rtcp:9 IN IP4 0.0.0.0\r\n",t.codecs.forEach(e=>{n+=r.writeRtpMap(e),n+=r.writeFmtp(e),n+=r.writeRtcpFb(e)});let i=0;return t.codecs.forEach(e=>{e.maxptime>i&&(i=e.maxptime)}),i>0&&(n+="a=maxptime:"+i+"\r\n"),t.headerExtensions&&t.headerExtensions.forEach(e=>{n+=r.writeExtmap(e)}),n},r.parseRtpEncodingParameters=function(e){const t=[],n=r.parseRtpParameters(e),i=-1!==n.fecMechanisms.indexOf("RED"),o=-1!==n.fecMechanisms.indexOf("ULPFEC"),a=r.matchPrefix(e,"a=ssrc:").map(e=>r.parseSsrcMedia(e)).filter(e=>"cname"===e.attribute),s=a.length>0&&a[0].ssrc;let c;const l=r.matchPrefix(e,"a=ssrc-group:FID").map(e=>e.substr(17).split(" ").map(e=>parseInt(e,10)));l.length>0&&l[0].length>1&&l[0][0]===s&&(c=l[0][1]),n.codecs.forEach(e=>{if("RTX"===e.name.toUpperCase()&&e.parameters.apt){let n={ssrc:s,codecPayloadType:parseInt(e.parameters.apt,10)};s&&c&&(n.rtx={ssrc:c}),t.push(n),i&&(n=JSON.parse(JSON.stringify(n)),n.fec={ssrc:s,mechanism:o?"red+ulpfec":"red"},t.push(n))}}),0===t.length&&s&&t.push({ssrc:s});let u=r.matchPrefix(e,"b=");return u.length&&(u=0===u[0].indexOf("b=TIAS:")?parseInt(u[0].substr(7),10):0===u[0].indexOf("b=AS:")?1e3*parseInt(u[0].substr(5),10)*.95-16e3:void 0,t.forEach(e=>{e.maxBitrate=u})),t},r.parseRtcpParameters=function(e){const t={},n=r.matchPrefix(e,"a=ssrc:").map(e=>r.parseSsrcMedia(e)).filter(e=>"cname"===e.attribute)[0];n&&(t.cname=n.value,t.ssrc=n.ssrc);const i=r.matchPrefix(e,"a=rtcp-rsize");t.reducedSize=i.length>0,t.compound=0===i.length;const o=r.matchPrefix(e,"a=rtcp-mux");return t.mux=o.length>0,t},r.writeRtcpParameters=function(e){let t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},r.parseMsid=function(e){let t;const n=r.matchPrefix(e,"a=msid:");if(1===n.length)return t=n[0].substr(7).split(" "),{stream:t[0],track:t[1]};const i=r.matchPrefix(e,"a=ssrc:").map(e=>r.parseSsrcMedia(e)).filter(e=>"msid"===e.attribute);return i.length>0?(t=i[0].value.split(" "),{stream:t[0],track:t[1]}):void 0},r.parseSctpDescription=function(e){const t=r.parseMLine(e),n=r.matchPrefix(e,"a=max-message-size:");let i;n.length>0&&(i=parseInt(n[0].substr(19),10)),isNaN(i)&&(i=65536);const o=r.matchPrefix(e,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substr(12),10),protocol:t.fmt,maxMessageSize:i};const a=r.matchPrefix(e,"a=sctpmap:");if(a.length>0){const e=a[0].substr(10).split(" ");return{port:parseInt(e[0],10),protocol:e[1],maxMessageSize:i}}},r.writeSctpDescription=function(e,t){let n=[];return n="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&n.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),n.join("")},r.generateSessionId=function(){return Math.random().toString().substr(2,21)},r.writeSessionBoilerplate=function(e,t,n){let i;const o=void 0!==t?t:2;i=e||r.generateSessionId();return"v=0\r\no="+(n||"thisisadapterortc")+" "+i+" "+o+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},r.getDirection=function(e,t){const n=r.splitLines(e);for(let e=0;e<n.length;e++)switch(n[e]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[e].substr(2)}return t?r.getDirection(t):"sendrecv"},r.getKind=function(e){return r.splitLines(e)[0].split(" ")[0].substr(2)},r.isRejected=function(e){return"0"===e.split(" ",2)[1]},r.parseMLine=function(e){const t=r.splitLines(e)[0].substr(2).split(" ");return{kind:t[0],port:parseInt(t[1],10),protocol:t[2],fmt:t.slice(3).join(" ")}},r.parseOLine=function(e){const t=r.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:t[0],sessionId:t[1],sessionVersion:parseInt(t[2],10),netType:t[3],addressType:t[4],address:t[5]}},r.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;const t=r.splitLines(e);for(let e=0;e<t.length;e++)if(t[e].length<2||"="!==t[e].charAt(1))return!1;return!0},e.exports=r},function(e,t,n){var r=n(8).f,i=n(15),o=n(5)("toStringTag");e.exports=function(e,t,n){e&&!i(e=n?e:e.prototype,o)&&r(e,o,{configurable:!0,value:t})}},function(e,t,n){var r=n(21),i=n(5)("toStringTag"),o="Arguments"==r(function(){return arguments}());e.exports=function(e){var t,n,a;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),i))?n:o?r(t):"Object"==(a=r(t))&&"function"==typeof t.callee?"Arguments":a}},function(e,t,n){var r=n(0),i=n(25),o=n(3),a=n(77),s="["+a+"]",c=RegExp("^"+s+s+"*"),l=RegExp(s+s+"*$"),u=function(e,t,n){var i={},s=o((function(){return!!a[e]()||"​"!="​"[e]()})),c=i[e]=s?t(f):a[e];n&&(i[n]=c),r(r.P+r.F*s,"String",i)},f=u.trim=function(e,t){return e=String(i(e)),1&t&&(e=e.replace(c,"")),2&t&&(e=e.replace(l,"")),e};e.exports=u},function(e,t){e.exports={}},function(e,t,n){"use strict";var r=n(10);function i(e,t,n,r,i){Error.call(this),this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i)}r.inherits(i,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var o=i.prototype,a={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED"].forEach((function(e){a[e]={value:e}})),Object.defineProperties(i,a),Object.defineProperty(o,"isAxiosError",{value:!0}),i.from=function(e,t,n,a,s,c){var l=Object.create(o);return r.toFlatObject(e,l,(function(e){return e!==Error.prototype})),i.call(l,e.message,t,n,a,s),l.name=e.name,c&&Object.assign(l,c),l},e.exports=i},function(e,t,n){var r=n(19),i=n(2),o=i["__core-js_shared__"]||(i["__core-js_shared__"]={});(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:r.version,mode:n(30)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},function(e,t,n){var r=n(21);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==r(e)?e.split(""):Object(e)}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,n){"use strict";var r=n(1);e.exports=function(){var e=r(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},function(e,t,n){var r=n(1),i=n(11),o=n(5)("species");e.exports=function(e,t){var n,a=r(e).constructor;return void 0===a||null==(n=r(a)[o])?t:i(n)}},function(e,t,n){var r=n(16),i=n(6),o=n(36);e.exports=function(e){return function(t,n,a){var s,c=r(t),l=i(c.length),u=o(a,l);if(e&&n!=n){for(;l>u;)if((s=c[u++])!=s)return!0}else for(;l>u;u++)if((e||u in c)&&c[u]===n)return e||u||0;return!e&&-1}}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,n){var r=n(21);e.exports=Array.isArray||function(e){return"Array"==r(e)}},function(e,t,n){var r=n(22),i=n(25);e.exports=function(e){return function(t,n){var o,a,s=String(i(t)),c=r(n),l=s.length;return c<0||c>=l?e?"":void 0:(o=s.charCodeAt(c))<55296||o>56319||c+1===l||(a=s.charCodeAt(c+1))<56320||a>57343?e?s.charAt(c):o:e?s.slice(c,c+2):a-56320+(o-55296<<10)+65536}}},function(e,t,n){var r=n(4),i=n(21),o=n(5)("match");e.exports=function(e){var t;return r(e)&&(void 0!==(t=e[o])?!!t:"RegExp"==i(e))}},function(e,t,n){var r=n(5)("iterator"),i=!1;try{var o=[7][r]();o.return=function(){i=!0},Array.from(o,(function(){throw 2}))}catch(e){}e.exports=function(e,t){if(!t&&!i)return!1;var n=!1;try{var o=[7],a=o[r]();a.next=function(){return{done:n=!0}},o[r]=function(){return a},e(o)}catch(e){}return n}},function(e,t,n){"use strict";var r=n(46),i=RegExp.prototype.exec;e.exports=function(e,t){var n=e.exec;if("function"==typeof n){var o=n.call(e,t);if("object"!=typeof o)throw new TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==r(e))throw new TypeError("RegExp#exec called on incompatible receiver");return i.call(e,t)}},function(e,t,n){"use strict";n(120);var r=n(13),i=n(12),o=n(3),a=n(25),s=n(5),c=n(92),l=s("species"),u=!o((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),f=function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2===n.length&&"a"===n[0]&&"b"===n[1]}();e.exports=function(e,t,n){var d=s(e),h=!o((function(){var t={};return t[d]=function(){return 7},7!=""[e](t)})),v=h?!o((function(){var t=!1,n=/a/;return n.exec=function(){return t=!0,null},"split"===e&&(n.constructor={},n.constructor[l]=function(){return n}),n[d](""),!t})):void 0;if(!h||!v||"replace"===e&&!u||"split"===e&&!f){var p=/./[d],g=n(a,d,""[e],(function(e,t,n,r,i){return t.exec===c?h&&!i?{done:!0,value:p.call(t,n,r)}:{done:!0,value:e.call(n,t,r)}:{done:!1}})),m=g[0],y=g[1];r(String.prototype,e,m),i(RegExp.prototype,d,2==t?function(e,t){return y.call(e,this,t)}:function(e){return y.call(e,this)})}}},function(e,t,n){var r=n(2).navigator;e.exports=r&&r.userAgent||""},function(e,t,n){"use strict";var r=n(2),i=n(0),o=n(13),a=n(42),s=n(31),c=n(41),l=n(40),u=n(4),f=n(3),d=n(60),h=n(45),v=n(78);e.exports=function(e,t,n,p,g,m){var y=r[e],_=y,S=g?"set":"add",E=_&&_.prototype,C={},I=function(e){var t=E[e];o(E,e,"delete"==e||"has"==e?function(e){return!(m&&!u(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return m&&!u(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,n){return t.call(this,0===e?0:e,n),this})};if("function"==typeof _&&(m||E.forEach&&!f((function(){(new _).entries().next()})))){var T=new _,b=T[S](m?{}:-0,1)!=T,R=f((function(){T.has(1)})),O=d((function(e){new _(e)})),w=!m&&f((function(){for(var e=new _,t=5;t--;)e[S](t,t);return!e.has(-0)}));O||((_=t((function(t,n){l(t,_,e);var r=v(new y,t,_);return null!=n&&c(n,g,r[S],r),r}))).prototype=E,E.constructor=_),(R||w)&&(I("delete"),I("has"),g&&I("get")),(w||b)&&I(S),m&&E.clear&&delete E.clear}else _=p.getConstructor(t,e,g,S),a(_.prototype,n),s.NEED=!0;return h(_,e),C[e]=_,i(i.G+i.W+i.F*(_!=y),C),m||p.setStrong(_,e,g),_}},function(e,t,n){for(var r,i=n(2),o=n(12),a=n(34),s=a("typed_array"),c=a("view"),l=!(!i.ArrayBuffer||!i.DataView),u=l,f=0,d="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");f<9;)(r=i[d[f++]])?(o(r.prototype,s,!0),o(r.prototype,c,!0)):u=!1;e.exports={ABV:l,CONSTR:u,TYPED:s,VIEW:c}},function(e,t,n){"use strict";e.exports=n(30)||!n(3)((function(){var e=Math.random();__defineSetter__.call(null,e,(function(){})),delete n(2)[e]}))},function(e,t,n){"use strict";var r=n(0);e.exports=function(e){r(r.S,e,{of:function(){for(var e=arguments.length,t=new Array(e);e--;)t[e]=arguments[e];return new this(t)}})}},function(e,t,n){"use strict";var r=n(0),i=n(11),o=n(20),a=n(41);e.exports=function(e){r(r.S,e,{from:function(e){var t,n,r,s,c=arguments[1];return i(this),(t=void 0!==c)&&i(c),null==e?new this:(n=[],t?(r=0,s=o(c,arguments[2],2),a(e,!1,(function(e){n.push(s(e,r++))}))):a(e,!1,n.push,n),new this(n))}})}},function(e,t,n){"use strict";var r=n(49);function i(e){r.call(this,null==e?"canceled":e,r.ERR_CANCELED),this.name="CanceledError"}n(10).inherits(i,r,{__CANCEL__:!0}),e.exports=i},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){var r=n(4),i=n(2).document,o=r(i)&&r(i.createElement);e.exports=function(e){return o?i.createElement(e):{}}},function(e,t,n){var r=n(2),i=n(19),o=n(30),a=n(102),s=n(8).f;e.exports=function(e){var t=i.Symbol||(i.Symbol=o?{}:r.Symbol||{});"_"==e.charAt(0)||e in t||s(t,e,{value:a.f(e)})}},function(e,t,n){var r=n(50)("keys"),i=n(34);e.exports=function(e){return r[e]||(r[e]=i(e))}},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,n){var r=n(2).document;e.exports=r&&r.documentElement},function(e,t,n){var r=n(4),i=n(1),o=function(e,t){if(i(e),!r(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,r){try{(r=n(20)(Function.call,n(17).f(Object.prototype,"__proto__").set,2))(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,n){return o(e,n),t?e.__proto__=n:r(e,n),e}}({},!1):void 0),check:o}},function(e,t){e.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"},function(e,t,n){var r=n(4),i=n(76).set;e.exports=function(e,t,n){var o,a=t.constructor;return a!==n&&"function"==typeof a&&(o=a.prototype)!==n.prototype&&r(o)&&i&&i(e,o),e}},function(e,t,n){"use strict";var r=n(22),i=n(25);e.exports=function(e){var t=String(i(this)),n="",o=r(e);if(o<0||o==1/0)throw RangeError("Count can't be negative");for(;o>0;(o>>>=1)&&(t+=t))1&o&&(n+=t);return n}},function(e,t){e.exports=Math.sign||function(e){return 0==(e=+e)||e!=e?e:e<0?-1:1}},function(e,t){var n=Math.expm1;e.exports=!n||n(10)>22025.465794806718||n(10)<22025.465794806718||-2e-17!=n(-2e-17)?function(e){return 0==(e=+e)?e:e>-1e-6&&e<1e-6?e+e*e/2:Math.exp(e)-1}:n},function(e,t,n){"use strict";var r=n(30),i=n(0),o=n(13),a=n(12),s=n(48),c=n(83),l=n(45),u=n(18),f=n(5)("iterator"),d=!([].keys&&"next"in[].keys()),h=function(){return this};e.exports=function(e,t,n,v,p,g,m){c(n,t,v);var y,_,S,E=function(e){if(!d&&e in b)return b[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},C=t+" Iterator",I="values"==p,T=!1,b=e.prototype,R=b[f]||b["@@iterator"]||p&&b[p],O=R||E(p),w=p?I?E("entries"):O:void 0,A="Array"==t&&b.entries||R;if(A&&(S=u(A.call(new e)))!==Object.prototype&&S.next&&(l(S,C,!0),r||"function"==typeof S[f]||a(S,f,h)),I&&R&&"values"!==R.name&&(T=!0,O=function(){return R.call(this)}),r&&!m||!d&&!T&&b[f]||a(b,f,O),s[t]=O,s[C]=h,p)if(y={values:I?O:E("values"),keys:g?O:E("keys"),entries:w},m)for(_ in y)_ in b||o(b,_,y[_]);else i(i.P+i.F*(d||T),t,y);return y}},function(e,t,n){"use strict";var r=n(37),i=n(33),o=n(45),a={};n(12)(a,n(5)("iterator"),(function(){return this})),e.exports=function(e,t,n){e.prototype=r(a,{next:i(1,n)}),o(e,t+" Iterator")}},function(e,t,n){var r=n(59),i=n(25);e.exports=function(e,t,n){if(r(t))throw TypeError("String#"+n+" doesn't accept regex!");return String(i(e))}},function(e,t,n){var r=n(5)("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[r]=!1,!"/./"[e](t)}catch(e){}}return!0}},function(e,t,n){var r=n(48),i=n(5)("iterator"),o=Array.prototype;e.exports=function(e){return void 0!==e&&(r.Array===e||o[i]===e)}},function(e,t,n){"use strict";var r=n(8),i=n(33);e.exports=function(e,t,n){t in e?r.f(e,t,i(0,n)):e[t]=n}},function(e,t,n){var r=n(46),i=n(5)("iterator"),o=n(48);e.exports=n(19).getIteratorMethod=function(e){if(null!=e)return e[i]||e["@@iterator"]||o[r(e)]}},function(e,t,n){var r=n(238);e.exports=function(e,t){return new(r(e))(t)}},function(e,t,n){"use strict";var r=n(9),i=n(36),o=n(6);e.exports=function(e){for(var t=r(this),n=o(t.length),a=arguments.length,s=i(a>1?arguments[1]:void 0,n),c=a>2?arguments[2]:void 0,l=void 0===c?n:i(c,n);l>s;)t[s++]=e;return t}},function(e,t,n){"use strict";var r=n(32),i=n(119),o=n(48),a=n(16);e.exports=n(82)(Array,"Array",(function(e,t){this._t=a(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,i(1)):i(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])}),"values"),o.Arguments=o.Array,r("keys"),r("values"),r("entries")},function(e,t,n){"use strict";var r,i,o=n(53),a=RegExp.prototype.exec,s=String.prototype.replace,c=a,l=(r=/a/,i=/b*/g,a.call(r,"a"),a.call(i,"a"),0!==r.lastIndex||0!==i.lastIndex),u=void 0!==/()??/.exec("")[1];(l||u)&&(c=function(e){var t,n,r,i,c=this;return u&&(n=new RegExp("^"+c.source+"$(?!\\s)",o.call(c))),l&&(t=c.lastIndex),r=a.call(c,e),l&&r&&(c.lastIndex=c.global?r.index+r[0].length:t),u&&r&&r.length>1&&s.call(r[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(r[i]=void 0)})),r}),e.exports=c},function(e,t,n){"use strict";var r=n(58)(!0);e.exports=function(e,t,n){return t+(n?r(e,t).length:1)}},function(e,t,n){var r,i,o,a=n(20),s=n(109),c=n(75),l=n(71),u=n(2),f=u.process,d=u.setImmediate,h=u.clearImmediate,v=u.MessageChannel,p=u.Dispatch,g=0,m={},y=function(){var e=+this;if(m.hasOwnProperty(e)){var t=m[e];delete m[e],t()}},_=function(e){y.call(e.data)};d&&h||(d=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return m[++g]=function(){s("function"==typeof e?e:Function(e),t)},r(g),g},h=function(e){delete m[e]},"process"==n(21)(f)?r=function(e){f.nextTick(a(y,e,1))}:p&&p.now?r=function(e){p.now(a(y,e,1))}:v?(o=(i=new v).port2,i.port1.onmessage=_,r=a(o.postMessage,o,1)):u.addEventListener&&"function"==typeof postMessage&&!u.importScripts?(r=function(e){u.postMessage(e+"","*")},u.addEventListener("message",_,!1)):r="onreadystatechange"in l("script")?function(e){c.appendChild(l("script")).onreadystatechange=function(){c.removeChild(this),y.call(e)}}:function(e){setTimeout(a(y,e,1),0)}),e.exports={set:d,clear:h}},function(e,t,n){var r=n(2),i=n(94).set,o=r.MutationObserver||r.WebKitMutationObserver,a=r.process,s=r.Promise,c="process"==n(21)(a);e.exports=function(){var e,t,n,l=function(){var r,i;for(c&&(r=a.domain)&&r.exit();e;){i=e.fn,e=e.next;try{i()}catch(r){throw e?n():t=void 0,r}}t=void 0,r&&r.enter()};if(c)n=function(){a.nextTick(l)};else if(!o||r.navigator&&r.navigator.standalone)if(s&&s.resolve){var u=s.resolve(void 0);n=function(){u.then(l)}}else n=function(){i.call(r,l)};else{var f=!0,d=document.createTextNode("");new o(l).observe(d,{characterData:!0}),n=function(){d.data=f=!f}}return function(r){var i={fn:r,next:void 0};t&&(t.next=i),e||(e=i,n()),t=i}}},function(e,t,n){"use strict";var r=n(11);function i(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r})),this.resolve=r(t),this.reject=r(n)}e.exports.f=function(e){return new i(e)}},function(e,t,n){"use strict";var r=n(2),i=n(7),o=n(30),a=n(65),s=n(12),c=n(42),l=n(3),u=n(40),f=n(22),d=n(6),h=n(129),v=n(38).f,p=n(8).f,g=n(90),m=n(45),y=r.ArrayBuffer,_=r.DataView,S=r.Math,E=r.RangeError,C=r.Infinity,I=y,T=S.abs,b=S.pow,R=S.floor,O=S.log,w=S.LN2,A=i?"_b":"buffer",k=i?"_l":"byteLength",M=i?"_o":"byteOffset";function P(e,t,n){var r,i,o,a=new Array(n),s=8*n-t-1,c=(1<<s)-1,l=c>>1,u=23===t?b(2,-24)-b(2,-77):0,f=0,d=e<0||0===e&&1/e<0?1:0;for((e=T(e))!=e||e===C?(i=e!=e?1:0,r=c):(r=R(O(e)/w),e*(o=b(2,-r))<1&&(r--,o*=2),(e+=r+l>=1?u/o:u*b(2,1-l))*o>=2&&(r++,o/=2),r+l>=c?(i=0,r=c):r+l>=1?(i=(e*o-1)*b(2,t),r+=l):(i=e*b(2,l-1)*b(2,t),r=0));t>=8;a[f++]=255&i,i/=256,t-=8);for(r=r<<t|i,s+=t;s>0;a[f++]=255&r,r/=256,s-=8);return a[--f]|=128*d,a}function D(e,t,n){var r,i=8*n-t-1,o=(1<<i)-1,a=o>>1,s=i-7,c=n-1,l=e[c--],u=127&l;for(l>>=7;s>0;u=256*u+e[c],c--,s-=8);for(r=u&(1<<-s)-1,u>>=-s,s+=t;s>0;r=256*r+e[c],c--,s-=8);if(0===u)u=1-a;else{if(u===o)return r?NaN:l?-C:C;r+=b(2,t),u-=a}return(l?-1:1)*r*b(2,u-t)}function x(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]}function N(e){return[255&e]}function L(e){return[255&e,e>>8&255]}function F(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]}function j(e){return P(e,52,8)}function U(e){return P(e,23,4)}function V(e,t,n){p(e.prototype,t,{get:function(){return this[n]}})}function G(e,t,n,r){var i=h(+n);if(i+t>e[k])throw E("Wrong index!");var o=e[A]._b,a=i+e[M],s=o.slice(a,a+t);return r?s:s.reverse()}function B(e,t,n,r,i,o){var a=h(+n);if(a+t>e[k])throw E("Wrong index!");for(var s=e[A]._b,c=a+e[M],l=r(+i),u=0;u<t;u++)s[c+u]=l[o?u:t-u-1]}if(a.ABV){if(!l((function(){y(1)}))||!l((function(){new y(-1)}))||l((function(){return new y,new y(1.5),new y(NaN),"ArrayBuffer"!=y.name}))){for(var H,q=(y=function(e){return u(this,y),new I(h(e))}).prototype=I.prototype,W=v(I),Y=0;W.length>Y;)(H=W[Y++])in y||s(y,H,I[H]);o||(q.constructor=y)}var K=new _(new y(2)),z=_.prototype.setInt8;K.setInt8(0,2147483648),K.setInt8(1,2147483649),!K.getInt8(0)&&K.getInt8(1)||c(_.prototype,{setInt8:function(e,t){z.call(this,e,t<<24>>24)},setUint8:function(e,t){z.call(this,e,t<<24>>24)}},!0)}else y=function(e){u(this,y,"ArrayBuffer");var t=h(e);this._b=g.call(new Array(t),0),this[k]=t},_=function(e,t,n){u(this,_,"DataView"),u(e,y,"DataView");var r=e[k],i=f(t);if(i<0||i>r)throw E("Wrong offset!");if(i+(n=void 0===n?r-i:d(n))>r)throw E("Wrong length!");this[A]=e,this[M]=i,this[k]=n},i&&(V(y,"byteLength","_l"),V(_,"buffer","_b"),V(_,"byteLength","_l"),V(_,"byteOffset","_o")),c(_.prototype,{getInt8:function(e){return G(this,1,e)[0]<<24>>24},getUint8:function(e){return G(this,1,e)[0]},getInt16:function(e){var t=G(this,2,e,arguments[1]);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=G(this,2,e,arguments[1]);return t[1]<<8|t[0]},getInt32:function(e){return x(G(this,4,e,arguments[1]))},getUint32:function(e){return x(G(this,4,e,arguments[1]))>>>0},getFloat32:function(e){return D(G(this,4,e,arguments[1]),23,4)},getFloat64:function(e){return D(G(this,8,e,arguments[1]),52,8)},setInt8:function(e,t){B(this,1,e,N,t)},setUint8:function(e,t){B(this,1,e,N,t)},setInt16:function(e,t){B(this,2,e,L,t,arguments[2])},setUint16:function(e,t){B(this,2,e,L,t,arguments[2])},setInt32:function(e,t){B(this,4,e,F,t,arguments[2])},setUint32:function(e,t){B(this,4,e,F,t,arguments[2])},setFloat32:function(e,t){B(this,4,e,U,t,arguments[2])},setFloat64:function(e,t){B(this,8,e,j,t,arguments[2])}});m(y,"ArrayBuffer"),m(_,"DataView"),s(_.prototype,a.VIEW,!0),t.ArrayBuffer=y,t.DataView=_},function(e,t,n){"use strict";(function(t){var r=n(10),i=n(355),o=n(49),a=n(139),s=n(140),c={"Content-Type":"application/x-www-form-urlencoded"};function l(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var u,f={transitional:a,adapter:(("undefined"!=typeof XMLHttpRequest||void 0!==t&&"[object process]"===Object.prototype.toString.call(t))&&(u=n(141)),u),transformRequest:[function(e,t){if(i(t,"Accept"),i(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e))return e;if(r.isArrayBufferView(e))return e.buffer;if(r.isURLSearchParams(e))return l(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString();var n,o=r.isObject(e),a=t&&t["Content-Type"];if((n=r.isFileList(e))||o&&"multipart/form-data"===a){var c=this.env&&this.env.FormData;return s(n?{"files[]":e}:e,c&&new c)}return o||"application/json"===a?(l(t,"application/json"),function(e,t,n){if(r.isString(e))try{return(t||JSON.parse)(e),r.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional||f.transitional,n=t&&t.silentJSONParsing,i=t&&t.forcedJSONParsing,a=!n&&"json"===this.responseType;if(a||i&&r.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(a){if("SyntaxError"===e.name)throw o.from(e,o.ERR_BAD_RESPONSE,this,null,this.response);throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:n(367)},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};r.forEach(["delete","get","head"],(function(e){f.headers[e]={}})),r.forEach(["post","put","patch"],(function(e){f.headers[e]=r.merge(c)})),e.exports=f}).call(this,n(354))},function(e,t,n){e.exports=n(146)},function(e,t,n){"use strict";(function(e){if(n(147),n(344),n(345),e._babelPolyfill)throw new Error("only one instance of babel-polyfill is allowed");e._babelPolyfill=!0;function t(e,t,n){e[t]||Object.defineProperty(e,t,{writable:!0,configurable:!0,value:n})}t(String.prototype,"padLeft","".padStart),t(String.prototype,"padRight","".padEnd),"pop,reverse,shift,keys,values,entries,indexOf,every,some,forEach,map,filter,find,findIndex,includes,join,slice,concat,push,splice,unshift,sort,lastIndexOf,reduce,reduceRight,copyWithin,fill".split(",").forEach((function(e){[][e]&&t(Array,e,Function.call.bind([][e]))}))}).call(this,n(70))},function(e,t,n){e.exports=!n(7)&&!n(3)((function(){return 7!=Object.defineProperty(n(71)("div"),"a",{get:function(){return 7}}).a}))},function(e,t,n){t.f=n(5)},function(e,t,n){var r=n(15),i=n(16),o=n(55)(!1),a=n(73)("IE_PROTO");e.exports=function(e,t){var n,s=i(e),c=0,l=[];for(n in s)n!=a&&r(s,n)&&l.push(n);for(;t.length>c;)r(s,n=t[c++])&&(~o(l,n)||l.push(n));return l}},function(e,t,n){var r=n(8),i=n(1),o=n(35);e.exports=n(7)?Object.defineProperties:function(e,t){i(e);for(var n,a=o(t),s=a.length,c=0;s>c;)r.f(e,n=a[c++],t[n]);return e}},function(e,t,n){var r=n(16),i=n(38).f,o={}.toString,a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return a&&"[object Window]"==o.call(e)?function(e){try{return i(e)}catch(e){return a.slice()}}(e):i(r(e))}},function(e,t,n){"use strict";var r=n(7),i=n(35),o=n(56),a=n(52),s=n(9),c=n(51),l=Object.assign;e.exports=!l||n(3)((function(){var e={},t={},n=Symbol(),r="abcdefghijklmnopqrst";return e[n]=7,r.split("").forEach((function(e){t[e]=e})),7!=l({},e)[n]||Object.keys(l({},t)).join("")!=r}))?function(e,t){for(var n=s(e),l=arguments.length,u=1,f=o.f,d=a.f;l>u;)for(var h,v=c(arguments[u++]),p=f?i(v).concat(f(v)):i(v),g=p.length,m=0;g>m;)h=p[m++],r&&!d.call(v,h)||(n[h]=v[h]);return n}:l},function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}},function(e,t,n){"use strict";var r=n(11),i=n(4),o=n(109),a=[].slice,s={},c=function(e,t,n){if(!(t in s)){for(var r=[],i=0;i<t;i++)r[i]="a["+i+"]";s[t]=Function("F,a","return new F("+r.join(",")+")")}return s[t](e,n)};e.exports=Function.bind||function(e){var t=r(this),n=a.call(arguments,1),s=function(){var r=n.concat(a.call(arguments));return this instanceof s?c(t,r.length,r):o(t,r,e)};return i(t.prototype)&&(s.prototype=t.prototype),s}},function(e,t){e.exports=function(e,t,n){var r=void 0===n;switch(t.length){case 0:return r?e():e.call(n);case 1:return r?e(t[0]):e.call(n,t[0]);case 2:return r?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return r?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return r?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)}},function(e,t,n){var r=n(2).parseInt,i=n(47).trim,o=n(77),a=/^[-+]?0[xX]/;e.exports=8!==r(o+"08")||22!==r(o+"0x16")?function(e,t){var n=i(String(e),3);return r(n,t>>>0||(a.test(n)?16:10))}:r},function(e,t,n){var r=n(2).parseFloat,i=n(47).trim;e.exports=1/r(n(77)+"-0")!=-1/0?function(e){var t=i(String(e),3),n=r(t);return 0===n&&"-"==t.charAt(0)?-0:n}:r},function(e,t,n){var r=n(21);e.exports=function(e,t){if("number"!=typeof e&&"Number"!=r(e))throw TypeError(t);return+e}},function(e,t,n){var r=n(4),i=Math.floor;e.exports=function(e){return!r(e)&&isFinite(e)&&i(e)===e}},function(e,t){e.exports=Math.log1p||function(e){return(e=+e)>-1e-8&&e<1e-8?e-e*e/2:Math.log(1+e)}},function(e,t,n){var r=n(80),i=Math.pow,o=i(2,-52),a=i(2,-23),s=i(2,127)*(2-a),c=i(2,-126);e.exports=Math.fround||function(e){var t,n,i=Math.abs(e),l=r(e);return i<c?l*(i/c/a+1/o-1/o)*c*a:(n=(t=(1+a/o)*i)-(t-i))>s||n!=n?l*(1/0):l*n}},function(e,t,n){var r=n(1);e.exports=function(e,t,n,i){try{return i?t(r(n)[0],n[1]):t(n)}catch(t){var o=e.return;throw void 0!==o&&r(o.call(e)),t}}},function(e,t,n){var r=n(11),i=n(9),o=n(51),a=n(6);e.exports=function(e,t,n,s,c){r(t);var l=i(e),u=o(l),f=a(l.length),d=c?f-1:0,h=c?-1:1;if(n<2)for(;;){if(d in u){s=u[d],d+=h;break}if(d+=h,c?d<0:f<=d)throw TypeError("Reduce of empty array with no initial value")}for(;c?d>=0:f>d;d+=h)d in u&&(s=t(s,u[d],d,l));return s}},function(e,t,n){"use strict";var r=n(9),i=n(36),o=n(6);e.exports=[].copyWithin||function(e,t){var n=r(this),a=o(n.length),s=i(e,a),c=i(t,a),l=arguments.length>2?arguments[2]:void 0,u=Math.min((void 0===l?a:i(l,a))-c,a-s),f=1;for(c<s&&s<c+u&&(f=-1,c+=u-1,s+=u-1);u-- >0;)c in n?n[s]=n[c]:delete n[s],s+=f,c+=f;return n}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,n){"use strict";var r=n(92);n(0)({target:"RegExp",proto:!0,forced:r!==/./.exec},{exec:r})},function(e,t,n){n(7)&&"g"!=/./g.flags&&n(8).f(RegExp.prototype,"flags",{configurable:!0,get:n(53)})},function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},function(e,t,n){var r=n(1),i=n(4),o=n(96);e.exports=function(e,t){if(r(e),i(t)&&t.constructor===e)return t;var n=o.f(e);return(0,n.resolve)(t),n.promise}},function(e,t,n){"use strict";var r=n(125),i=n(43);e.exports=n(64)("Map",(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{get:function(e){var t=r.getEntry(i(this,"Map"),e);return t&&t.v},set:function(e,t){return r.def(i(this,"Map"),0===e?0:e,t)}},r,!0)},function(e,t,n){"use strict";var r=n(8).f,i=n(37),o=n(42),a=n(20),s=n(40),c=n(41),l=n(82),u=n(119),f=n(39),d=n(7),h=n(31).fastKey,v=n(43),p=d?"_s":"size",g=function(e,t){var n,r=h(t);if("F"!==r)return e._i[r];for(n=e._f;n;n=n.n)if(n.k==t)return n};e.exports={getConstructor:function(e,t,n,l){var u=e((function(e,r){s(e,u,t,"_i"),e._t=t,e._i=i(null),e._f=void 0,e._l=void 0,e[p]=0,null!=r&&c(r,n,e[l],e)}));return o(u.prototype,{clear:function(){for(var e=v(this,t),n=e._i,r=e._f;r;r=r.n)r.r=!0,r.p&&(r.p=r.p.n=void 0),delete n[r.i];e._f=e._l=void 0,e[p]=0},delete:function(e){var n=v(this,t),r=g(n,e);if(r){var i=r.n,o=r.p;delete n._i[r.i],r.r=!0,o&&(o.n=i),i&&(i.p=o),n._f==r&&(n._f=i),n._l==r&&(n._l=o),n[p]--}return!!r},forEach:function(e){v(this,t);for(var n,r=a(e,arguments.length>1?arguments[1]:void 0,3);n=n?n.n:this._f;)for(r(n.v,n.k,this);n&&n.r;)n=n.p},has:function(e){return!!g(v(this,t),e)}}),d&&r(u.prototype,"size",{get:function(){return v(this,t)[p]}}),u},def:function(e,t,n){var r,i,o=g(e,t);return o?o.v=n:(e._l=o={i:i=h(t,!0),k:t,v:n,p:r=e._l,n:void 0,r:!1},e._f||(e._f=o),r&&(r.n=o),e[p]++,"F"!==i&&(e._i[i]=o)),e},getEntry:g,setStrong:function(e,t,n){l(e,t,(function(e,n){this._t=v(e,t),this._k=n,this._l=void 0}),(function(){for(var e=this._k,t=this._l;t&&t.r;)t=t.p;return this._t&&(this._l=t=t?t.n:this._t._f)?u(0,"keys"==e?t.k:"values"==e?t.v:[t.k,t.v]):(this._t=void 0,u(1))}),n?"entries":"values",!n,!0),f(t)}}},function(e,t,n){"use strict";var r=n(125),i=n(43);e.exports=n(64)("Set",(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(e){return r.def(i(this,"Set"),e=0===e?0:e,e)}},r)},function(e,t,n){"use strict";var r,i=n(2),o=n(27)(0),a=n(13),s=n(31),c=n(106),l=n(128),u=n(4),f=n(43),d=n(43),h=!i.ActiveXObject&&"ActiveXObject"in i,v=s.getWeak,p=Object.isExtensible,g=l.ufstore,m=function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},y={get:function(e){if(u(e)){var t=v(e);return!0===t?g(f(this,"WeakMap")).get(e):t?t[this._i]:void 0}},set:function(e,t){return l.def(f(this,"WeakMap"),e,t)}},_=e.exports=n(64)("WeakMap",m,y,l,!0,!0);d&&h&&(c((r=l.getConstructor(m,"WeakMap")).prototype,y),s.NEED=!0,o(["delete","has","get","set"],(function(e){var t=_.prototype,n=t[e];a(t,e,(function(t,i){if(u(t)&&!p(t)){this._f||(this._f=new r);var o=this._f[e](t,i);return"set"==e?this:o}return n.call(this,t,i)}))})))},function(e,t,n){"use strict";var r=n(42),i=n(31).getWeak,o=n(1),a=n(4),s=n(40),c=n(41),l=n(27),u=n(15),f=n(43),d=l(5),h=l(6),v=0,p=function(e){return e._l||(e._l=new g)},g=function(){this.a=[]},m=function(e,t){return d(e.a,(function(e){return e[0]===t}))};g.prototype={get:function(e){var t=m(this,e);if(t)return t[1]},has:function(e){return!!m(this,e)},set:function(e,t){var n=m(this,e);n?n[1]=t:this.a.push([e,t])},delete:function(e){var t=h(this.a,(function(t){return t[0]===e}));return~t&&this.a.splice(t,1),!!~t}},e.exports={getConstructor:function(e,t,n,o){var l=e((function(e,r){s(e,l,t,"_i"),e._t=t,e._i=v++,e._l=void 0,null!=r&&c(r,n,e[o],e)}));return r(l.prototype,{delete:function(e){if(!a(e))return!1;var n=i(e);return!0===n?p(f(this,t)).delete(e):n&&u(n,this._i)&&delete n[this._i]},has:function(e){if(!a(e))return!1;var n=i(e);return!0===n?p(f(this,t)).has(e):n&&u(n,this._i)}}),l},def:function(e,t,n){var r=i(o(t),!0);return!0===r?p(e).set(t,n):r[e._i]=n,e},ufstore:p}},function(e,t,n){var r=n(22),i=n(6);e.exports=function(e){if(void 0===e)return 0;var t=r(e),n=i(t);if(t!==n)throw RangeError("Wrong length!");return n}},function(e,t,n){var r=n(38),i=n(56),o=n(1),a=n(2).Reflect;e.exports=a&&a.ownKeys||function(e){var t=r.f(o(e)),n=i.f;return n?t.concat(n(e)):t}},function(e,t,n){"use strict";var r=n(57),i=n(4),o=n(6),a=n(20),s=n(5)("isConcatSpreadable");e.exports=function e(t,n,c,l,u,f,d,h){for(var v,p,g=u,m=0,y=!!d&&a(d,h,3);m<l;){if(m in c){if(v=y?y(c[m],m,n):c[m],p=!1,i(v)&&(p=void 0!==(p=v[s])?!!p:r(v)),p&&f>0)g=e(t,n,v,o(v.length),g,f-1)-1;else{if(g>=9007199254740991)throw TypeError();t[g]=v}g++}m++}return g}},function(e,t,n){var r=n(6),i=n(79),o=n(25);e.exports=function(e,t,n,a){var s=String(o(e)),c=s.length,l=void 0===n?" ":String(n),u=r(t);if(u<=c||""==l)return s;var f=u-c,d=i.call(l,Math.ceil(f/l.length));return d.length>f&&(d=d.slice(0,f)),a?d+s:s+d}},function(e,t,n){var r=n(7),i=n(35),o=n(16),a=n(52).f;e.exports=function(e){return function(t){for(var n,s=o(t),c=i(s),l=c.length,u=0,f=[];l>u;)n=c[u++],r&&!a.call(s,n)||f.push(e?[n,s[n]]:s[n]);return f}}},function(e,t,n){var r=n(46),i=n(135);e.exports=function(e){return function(){if(r(this)!=e)throw TypeError(e+"#toJSON isn't generic");return i(this)}}},function(e,t,n){var r=n(41);e.exports=function(e,t){var n=[];return r(e,!1,n.push,n,t),n}},function(e,t){e.exports=Math.scale||function(e,t,n,r,i){return 0===arguments.length||e!=e||t!=t||n!=n||r!=r||i!=i?NaN:e===1/0||e===-1/0?e:(e-t)*(i-r)/(n-t)+r}},function(e,t,n){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},function(e,t,n){"use strict";var r=n(10);function i(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var o;if(n)o=n(t);else if(r.isURLSearchParams(t))o=t.toString();else{var a=[];r.forEach(t,(function(e,t){null!=e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,(function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),a.push(i(t)+"="+i(e))})))})),o=a.join("&")}if(o){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}},function(e,t,n){"use strict";e.exports={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1}},function(e,t,n){"use strict";(function(t){var r=n(10);e.exports=function(e,n){n=n||new FormData;var i=[];function o(e){return null===e?"":r.isDate(e)?e.toISOString():r.isArrayBuffer(e)||r.isTypedArray(e)?"function"==typeof Blob?new Blob([e]):t.from(e):e}return function e(t,a){if(r.isPlainObject(t)||r.isArray(t)){if(-1!==i.indexOf(t))throw Error("Circular reference detected in "+a);i.push(t),r.forEach(t,(function(t,i){if(!r.isUndefined(t)){var s,c=a?a+"."+i:i;if(t&&!a&&"object"==typeof t)if(r.endsWith(i,"{}"))t=JSON.stringify(t);else if(r.endsWith(i,"[]")&&(s=r.toArray(t)))return void s.forEach((function(e){!r.isUndefined(e)&&n.append(c,o(e))}));e(t,c)}})),i.pop()}else n.append(a,o(t))}(e),n}}).call(this,n(356).Buffer)},function(e,t,n){"use strict";var r=n(10),i=n(360),o=n(361),a=n(138),s=n(142),c=n(364),l=n(365),u=n(139),f=n(49),d=n(69),h=n(366);e.exports=function(e){return new Promise((function(t,n){var v,p=e.data,g=e.headers,m=e.responseType;function y(){e.cancelToken&&e.cancelToken.unsubscribe(v),e.signal&&e.signal.removeEventListener("abort",v)}r.isFormData(p)&&r.isStandardBrowserEnv()&&delete g["Content-Type"];var _=new XMLHttpRequest;if(e.auth){var S=e.auth.username||"",E=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";g.Authorization="Basic "+btoa(S+":"+E)}var C=s(e.baseURL,e.url);function I(){if(_){var r="getAllResponseHeaders"in _?c(_.getAllResponseHeaders()):null,o={data:m&&"text"!==m&&"json"!==m?_.response:_.responseText,status:_.status,statusText:_.statusText,headers:r,config:e,request:_};i((function(e){t(e),y()}),(function(e){n(e),y()}),o),_=null}}if(_.open(e.method.toUpperCase(),a(C,e.params,e.paramsSerializer),!0),_.timeout=e.timeout,"onloadend"in _?_.onloadend=I:_.onreadystatechange=function(){_&&4===_.readyState&&(0!==_.status||_.responseURL&&0===_.responseURL.indexOf("file:"))&&setTimeout(I)},_.onabort=function(){_&&(n(new f("Request aborted",f.ECONNABORTED,e,_)),_=null)},_.onerror=function(){n(new f("Network Error",f.ERR_NETWORK,e,_,_)),_=null},_.ontimeout=function(){var t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",r=e.transitional||u;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(new f(t,r.clarifyTimeoutError?f.ETIMEDOUT:f.ECONNABORTED,e,_)),_=null},r.isStandardBrowserEnv()){var T=(e.withCredentials||l(C))&&e.xsrfCookieName?o.read(e.xsrfCookieName):void 0;T&&(g[e.xsrfHeaderName]=T)}"setRequestHeader"in _&&r.forEach(g,(function(e,t){void 0===p&&"content-type"===t.toLowerCase()?delete g[t]:_.setRequestHeader(t,e)})),r.isUndefined(e.withCredentials)||(_.withCredentials=!!e.withCredentials),m&&"json"!==m&&(_.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&_.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&_.upload&&_.upload.addEventListener("progress",e.onUploadProgress),(e.cancelToken||e.signal)&&(v=function(e){_&&(n(!e||e&&e.type?new d:e),_.abort(),_=null)},e.cancelToken&&e.cancelToken.subscribe(v),e.signal&&(e.signal.aborted?v():e.signal.addEventListener("abort",v))),p||(p=null);var b=h(C);b&&-1===["http","https","file"].indexOf(b)?n(new f("Unsupported protocol "+b+":",f.ERR_BAD_REQUEST,e)):_.send(p)}))}},function(e,t,n){"use strict";var r=n(362),i=n(363);e.exports=function(e,t){return e&&!r(t)?i(e,t):t}},function(e,t,n){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},function(e,t,n){"use strict";var r=n(10);e.exports=function(e,t){t=t||{};var n={};function i(e,t){return r.isPlainObject(e)&&r.isPlainObject(t)?r.merge(e,t):r.isPlainObject(t)?r.merge({},t):r.isArray(t)?t.slice():t}function o(n){return r.isUndefined(t[n])?r.isUndefined(e[n])?void 0:i(void 0,e[n]):i(e[n],t[n])}function a(e){if(!r.isUndefined(t[e]))return i(void 0,t[e])}function s(n){return r.isUndefined(t[n])?r.isUndefined(e[n])?void 0:i(void 0,e[n]):i(void 0,t[n])}function c(n){return n in t?i(e[n],t[n]):n in e?i(void 0,e[n]):void 0}var l={url:a,method:a,data:a,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:c};return r.forEach(Object.keys(e).concat(Object.keys(t)),(function(e){var t=l[e]||o,i=t(e);r.isUndefined(i)&&t!==c||(n[e]=i)})),n}},function(e,t){e.exports={version:"0.27.2"}},function(e,t,n){"use strict";t.__esModule=!0,t.default=function(){},e.exports=t.default},function(e,t,n){n(148),n(151),n(152),n(153),n(154),n(155),n(156),n(157),n(158),n(159),n(160),n(161),n(162),n(163),n(164),n(165),n(166),n(167),n(168),n(169),n(170),n(171),n(172),n(173),n(174),n(175),n(176),n(177),n(178),n(179),n(180),n(181),n(182),n(183),n(184),n(185),n(186),n(187),n(188),n(189),n(190),n(191),n(192),n(193),n(194),n(195),n(196),n(197),n(198),n(199),n(200),n(201),n(202),n(203),n(204),n(205),n(206),n(207),n(208),n(209),n(210),n(211),n(212),n(213),n(214),n(215),n(216),n(217),n(218),n(219),n(220),n(221),n(222),n(223),n(224),n(225),n(226),n(228),n(229),n(231),n(232),n(233),n(234),n(235),n(236),n(237),n(239),n(240),n(241),n(242),n(243),n(244),n(245),n(246),n(247),n(248),n(249),n(250),n(251),n(91),n(252),n(120),n(253),n(121),n(254),n(255),n(256),n(257),n(258),n(124),n(126),n(127),n(259),n(260),n(261),n(262),n(263),n(264),n(265),n(266),n(267),n(268),n(269),n(270),n(271),n(272),n(273),n(274),n(275),n(276),n(277),n(278),n(279),n(280),n(281),n(282),n(283),n(284),n(285),n(286),n(287),n(288),n(289),n(290),n(291),n(292),n(293),n(294),n(295),n(296),n(297),n(298),n(299),n(300),n(301),n(302),n(303),n(304),n(305),n(306),n(307),n(308),n(309),n(310),n(311),n(312),n(313),n(314),n(315),n(316),n(317),n(318),n(319),n(320),n(321),n(322),n(323),n(324),n(325),n(326),n(327),n(328),n(329),n(330),n(331),n(332),n(333),n(334),n(335),n(336),n(337),n(338),n(339),n(340),n(341),n(342),n(343),e.exports=n(19)},function(e,t,n){"use strict";var r=n(2),i=n(15),o=n(7),a=n(0),s=n(13),c=n(31).KEY,l=n(3),u=n(50),f=n(45),d=n(34),h=n(5),v=n(102),p=n(72),g=n(150),m=n(57),y=n(1),_=n(4),S=n(9),E=n(16),C=n(24),I=n(33),T=n(37),b=n(105),R=n(17),O=n(56),w=n(8),A=n(35),k=R.f,M=w.f,P=b.f,D=r.Symbol,x=r.JSON,N=x&&x.stringify,L=h("_hidden"),F=h("toPrimitive"),j={}.propertyIsEnumerable,U=u("symbol-registry"),V=u("symbols"),G=u("op-symbols"),B=Object.prototype,H="function"==typeof D&&!!O.f,q=r.QObject,W=!q||!q.prototype||!q.prototype.findChild,Y=o&&l((function(){return 7!=T(M({},"a",{get:function(){return M(this,"a",{value:7}).a}})).a}))?function(e,t,n){var r=k(B,t);r&&delete B[t],M(e,t,n),r&&e!==B&&M(B,t,r)}:M,K=function(e){var t=V[e]=T(D.prototype);return t._k=e,t},z=H&&"symbol"==typeof D.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof D},J=function(e,t,n){return e===B&&J(G,t,n),y(e),t=C(t,!0),y(n),i(V,t)?(n.enumerable?(i(e,L)&&e[L][t]&&(e[L][t]=!1),n=T(n,{enumerable:I(0,!1)})):(i(e,L)||M(e,L,I(1,{})),e[L][t]=!0),Y(e,t,n)):M(e,t,n)},Q=function(e,t){y(e);for(var n,r=g(t=E(t)),i=0,o=r.length;o>i;)J(e,n=r[i++],t[n]);return e},X=function(e){var t=j.call(this,e=C(e,!0));return!(this===B&&i(V,e)&&!i(G,e))&&(!(t||!i(this,e)||!i(V,e)||i(this,L)&&this[L][e])||t)},$=function(e,t){if(e=E(e),t=C(t,!0),e!==B||!i(V,t)||i(G,t)){var n=k(e,t);return!n||!i(V,t)||i(e,L)&&e[L][t]||(n.enumerable=!0),n}},Z=function(e){for(var t,n=P(E(e)),r=[],o=0;n.length>o;)i(V,t=n[o++])||t==L||t==c||r.push(t);return r},ee=function(e){for(var t,n=e===B,r=P(n?G:E(e)),o=[],a=0;r.length>a;)!i(V,t=r[a++])||n&&!i(B,t)||o.push(V[t]);return o};H||(s((D=function(){if(this instanceof D)throw TypeError("Symbol is not a constructor!");var e=d(arguments.length>0?arguments[0]:void 0),t=function(n){this===B&&t.call(G,n),i(this,L)&&i(this[L],e)&&(this[L][e]=!1),Y(this,e,I(1,n))};return o&&W&&Y(B,e,{configurable:!0,set:t}),K(e)}).prototype,"toString",(function(){return this._k})),R.f=$,w.f=J,n(38).f=b.f=Z,n(52).f=X,O.f=ee,o&&!n(30)&&s(B,"propertyIsEnumerable",X,!0),v.f=function(e){return K(h(e))}),a(a.G+a.W+a.F*!H,{Symbol:D});for(var te="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ne=0;te.length>ne;)h(te[ne++]);for(var re=A(h.store),ie=0;re.length>ie;)p(re[ie++]);a(a.S+a.F*!H,"Symbol",{for:function(e){return i(U,e+="")?U[e]:U[e]=D(e)},keyFor:function(e){if(!z(e))throw TypeError(e+" is not a symbol!");for(var t in U)if(U[t]===e)return t},useSetter:function(){W=!0},useSimple:function(){W=!1}}),a(a.S+a.F*!H,"Object",{create:function(e,t){return void 0===t?T(e):Q(T(e),t)},defineProperty:J,defineProperties:Q,getOwnPropertyDescriptor:$,getOwnPropertyNames:Z,getOwnPropertySymbols:ee});var oe=l((function(){O.f(1)}));a(a.S+a.F*oe,"Object",{getOwnPropertySymbols:function(e){return O.f(S(e))}}),x&&a(a.S+a.F*(!H||l((function(){var e=D();return"[null]"!=N([e])||"{}"!=N({a:e})||"{}"!=N(Object(e))}))),"JSON",{stringify:function(e){for(var t,n,r=[e],i=1;arguments.length>i;)r.push(arguments[i++]);if(n=t=r[1],(_(t)||void 0!==e)&&!z(e))return m(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!z(t))return t}),r[1]=t,N.apply(x,r)}}),D.prototype[F]||n(12)(D.prototype,F,D.prototype.valueOf),f(D,"Symbol"),f(Math,"Math",!0),f(r.JSON,"JSON",!0)},function(e,t,n){e.exports=n(50)("native-function-to-string",Function.toString)},function(e,t,n){var r=n(35),i=n(56),o=n(52);e.exports=function(e){var t=r(e),n=i.f;if(n)for(var a,s=n(e),c=o.f,l=0;s.length>l;)c.call(e,a=s[l++])&&t.push(a);return t}},function(e,t,n){var r=n(0);r(r.S,"Object",{create:n(37)})},function(e,t,n){var r=n(0);r(r.S+r.F*!n(7),"Object",{defineProperty:n(8).f})},function(e,t,n){var r=n(0);r(r.S+r.F*!n(7),"Object",{defineProperties:n(104)})},function(e,t,n){var r=n(16),i=n(17).f;n(26)("getOwnPropertyDescriptor",(function(){return function(e,t){return i(r(e),t)}}))},function(e,t,n){var r=n(9),i=n(18);n(26)("getPrototypeOf",(function(){return function(e){return i(r(e))}}))},function(e,t,n){var r=n(9),i=n(35);n(26)("keys",(function(){return function(e){return i(r(e))}}))},function(e,t,n){n(26)("getOwnPropertyNames",(function(){return n(105).f}))},function(e,t,n){var r=n(4),i=n(31).onFreeze;n(26)("freeze",(function(e){return function(t){return e&&r(t)?e(i(t)):t}}))},function(e,t,n){var r=n(4),i=n(31).onFreeze;n(26)("seal",(function(e){return function(t){return e&&r(t)?e(i(t)):t}}))},function(e,t,n){var r=n(4),i=n(31).onFreeze;n(26)("preventExtensions",(function(e){return function(t){return e&&r(t)?e(i(t)):t}}))},function(e,t,n){var r=n(4);n(26)("isFrozen",(function(e){return function(t){return!r(t)||!!e&&e(t)}}))},function(e,t,n){var r=n(4);n(26)("isSealed",(function(e){return function(t){return!r(t)||!!e&&e(t)}}))},function(e,t,n){var r=n(4);n(26)("isExtensible",(function(e){return function(t){return!!r(t)&&(!e||e(t))}}))},function(e,t,n){var r=n(0);r(r.S+r.F,"Object",{assign:n(106)})},function(e,t,n){var r=n(0);r(r.S,"Object",{is:n(107)})},function(e,t,n){var r=n(0);r(r.S,"Object",{setPrototypeOf:n(76).set})},function(e,t,n){"use strict";var r=n(46),i={};i[n(5)("toStringTag")]="z",i+""!="[object z]"&&n(13)(Object.prototype,"toString",(function(){return"[object "+r(this)+"]"}),!0)},function(e,t,n){var r=n(0);r(r.P,"Function",{bind:n(108)})},function(e,t,n){var r=n(8).f,i=Function.prototype,o=/^\s*function ([^ (]*)/;"name"in i||n(7)&&r(i,"name",{configurable:!0,get:function(){try{return(""+this).match(o)[1]}catch(e){return""}}})},function(e,t,n){"use strict";var r=n(4),i=n(18),o=n(5)("hasInstance"),a=Function.prototype;o in a||n(8).f(a,o,{value:function(e){if("function"!=typeof this||!r(e))return!1;if(!r(this.prototype))return e instanceof this;for(;e=i(e);)if(this.prototype===e)return!0;return!1}})},function(e,t,n){var r=n(0),i=n(110);r(r.G+r.F*(parseInt!=i),{parseInt:i})},function(e,t,n){var r=n(0),i=n(111);r(r.G+r.F*(parseFloat!=i),{parseFloat:i})},function(e,t,n){"use strict";var r=n(2),i=n(15),o=n(21),a=n(78),s=n(24),c=n(3),l=n(38).f,u=n(17).f,f=n(8).f,d=n(47).trim,h=r.Number,v=h,p=h.prototype,g="Number"==o(n(37)(p)),m="trim"in String.prototype,y=function(e){var t=s(e,!1);if("string"==typeof t&&t.length>2){var n,r,i,o=(t=m?t.trim():d(t,3)).charCodeAt(0);if(43===o||45===o){if(88===(n=t.charCodeAt(2))||120===n)return NaN}else if(48===o){switch(t.charCodeAt(1)){case 66:case 98:r=2,i=49;break;case 79:case 111:r=8,i=55;break;default:return+t}for(var a,c=t.slice(2),l=0,u=c.length;l<u;l++)if((a=c.charCodeAt(l))<48||a>i)return NaN;return parseInt(c,r)}}return+t};if(!h(" 0o1")||!h("0b1")||h("+0x1")){h=function(e){var t=arguments.length<1?0:e,n=this;return n instanceof h&&(g?c((function(){p.valueOf.call(n)})):"Number"!=o(n))?a(new v(y(t)),n,h):y(t)};for(var _,S=n(7)?l(v):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),E=0;S.length>E;E++)i(v,_=S[E])&&!i(h,_)&&f(h,_,u(v,_));h.prototype=p,p.constructor=h,n(13)(r,"Number",h)}},function(e,t,n){"use strict";var r=n(0),i=n(22),o=n(112),a=n(79),s=1..toFixed,c=Math.floor,l=[0,0,0,0,0,0],u="Number.toFixed: incorrect invocation!",f=function(e,t){for(var n=-1,r=t;++n<6;)r+=e*l[n],l[n]=r%1e7,r=c(r/1e7)},d=function(e){for(var t=6,n=0;--t>=0;)n+=l[t],l[t]=c(n/e),n=n%e*1e7},h=function(){for(var e=6,t="";--e>=0;)if(""!==t||0===e||0!==l[e]){var n=String(l[e]);t=""===t?n:t+a.call("0",7-n.length)+n}return t},v=function(e,t,n){return 0===t?n:t%2==1?v(e,t-1,n*e):v(e*e,t/2,n)};r(r.P+r.F*(!!s&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!n(3)((function(){s.call({})}))),"Number",{toFixed:function(e){var t,n,r,s,c=o(this,u),l=i(e),p="",g="0";if(l<0||l>20)throw RangeError(u);if(c!=c)return"NaN";if(c<=-1e21||c>=1e21)return String(c);if(c<0&&(p="-",c=-c),c>1e-21)if(n=(t=function(e){for(var t=0,n=e;n>=4096;)t+=12,n/=4096;for(;n>=2;)t+=1,n/=2;return t}(c*v(2,69,1))-69)<0?c*v(2,-t,1):c/v(2,t,1),n*=4503599627370496,(t=52-t)>0){for(f(0,n),r=l;r>=7;)f(1e7,0),r-=7;for(f(v(10,r,1),0),r=t-1;r>=23;)d(1<<23),r-=23;d(1<<r),f(1,1),d(2),g=h()}else f(0,n),f(1<<-t,0),g=h()+a.call("0",l);return g=l>0?p+((s=g.length)<=l?"0."+a.call("0",l-s)+g:g.slice(0,s-l)+"."+g.slice(s-l)):p+g}})},function(e,t,n){"use strict";var r=n(0),i=n(3),o=n(112),a=1..toPrecision;r(r.P+r.F*(i((function(){return"1"!==a.call(1,void 0)}))||!i((function(){a.call({})}))),"Number",{toPrecision:function(e){var t=o(this,"Number#toPrecision: incorrect invocation!");return void 0===e?a.call(t):a.call(t,e)}})},function(e,t,n){var r=n(0);r(r.S,"Number",{EPSILON:Math.pow(2,-52)})},function(e,t,n){var r=n(0),i=n(2).isFinite;r(r.S,"Number",{isFinite:function(e){return"number"==typeof e&&i(e)}})},function(e,t,n){var r=n(0);r(r.S,"Number",{isInteger:n(113)})},function(e,t,n){var r=n(0);r(r.S,"Number",{isNaN:function(e){return e!=e}})},function(e,t,n){var r=n(0),i=n(113),o=Math.abs;r(r.S,"Number",{isSafeInteger:function(e){return i(e)&&o(e)<=9007199254740991}})},function(e,t,n){var r=n(0);r(r.S,"Number",{MAX_SAFE_INTEGER:9007199254740991})},function(e,t,n){var r=n(0);r(r.S,"Number",{MIN_SAFE_INTEGER:-9007199254740991})},function(e,t,n){var r=n(0),i=n(111);r(r.S+r.F*(Number.parseFloat!=i),"Number",{parseFloat:i})},function(e,t,n){var r=n(0),i=n(110);r(r.S+r.F*(Number.parseInt!=i),"Number",{parseInt:i})},function(e,t,n){var r=n(0),i=n(114),o=Math.sqrt,a=Math.acosh;r(r.S+r.F*!(a&&710==Math.floor(a(Number.MAX_VALUE))&&a(1/0)==1/0),"Math",{acosh:function(e){return(e=+e)<1?NaN:e>94906265.62425156?Math.log(e)+Math.LN2:i(e-1+o(e-1)*o(e+1))}})},function(e,t,n){var r=n(0),i=Math.asinh;r(r.S+r.F*!(i&&1/i(0)>0),"Math",{asinh:function e(t){return isFinite(t=+t)&&0!=t?t<0?-e(-t):Math.log(t+Math.sqrt(t*t+1)):t}})},function(e,t,n){var r=n(0),i=Math.atanh;r(r.S+r.F*!(i&&1/i(-0)<0),"Math",{atanh:function(e){return 0==(e=+e)?e:Math.log((1+e)/(1-e))/2}})},function(e,t,n){var r=n(0),i=n(80);r(r.S,"Math",{cbrt:function(e){return i(e=+e)*Math.pow(Math.abs(e),1/3)}})},function(e,t,n){var r=n(0);r(r.S,"Math",{clz32:function(e){return(e>>>=0)?31-Math.floor(Math.log(e+.5)*Math.LOG2E):32}})},function(e,t,n){var r=n(0),i=Math.exp;r(r.S,"Math",{cosh:function(e){return(i(e=+e)+i(-e))/2}})},function(e,t,n){var r=n(0),i=n(81);r(r.S+r.F*(i!=Math.expm1),"Math",{expm1:i})},function(e,t,n){var r=n(0);r(r.S,"Math",{fround:n(115)})},function(e,t,n){var r=n(0),i=Math.abs;r(r.S,"Math",{hypot:function(e,t){for(var n,r,o=0,a=0,s=arguments.length,c=0;a<s;)c<(n=i(arguments[a++]))?(o=o*(r=c/n)*r+1,c=n):o+=n>0?(r=n/c)*r:n;return c===1/0?1/0:c*Math.sqrt(o)}})},function(e,t,n){var r=n(0),i=Math.imul;r(r.S+r.F*n(3)((function(){return-5!=i(4294967295,5)||2!=i.length})),"Math",{imul:function(e,t){var n=+e,r=+t,i=65535&n,o=65535&r;return 0|i*o+((65535&n>>>16)*o+i*(65535&r>>>16)<<16>>>0)}})},function(e,t,n){var r=n(0);r(r.S,"Math",{log10:function(e){return Math.log(e)*Math.LOG10E}})},function(e,t,n){var r=n(0);r(r.S,"Math",{log1p:n(114)})},function(e,t,n){var r=n(0);r(r.S,"Math",{log2:function(e){return Math.log(e)/Math.LN2}})},function(e,t,n){var r=n(0);r(r.S,"Math",{sign:n(80)})},function(e,t,n){var r=n(0),i=n(81),o=Math.exp;r(r.S+r.F*n(3)((function(){return-2e-17!=!Math.sinh(-2e-17)})),"Math",{sinh:function(e){return Math.abs(e=+e)<1?(i(e)-i(-e))/2:(o(e-1)-o(-e-1))*(Math.E/2)}})},function(e,t,n){var r=n(0),i=n(81),o=Math.exp;r(r.S,"Math",{tanh:function(e){var t=i(e=+e),n=i(-e);return t==1/0?1:n==1/0?-1:(t-n)/(o(e)+o(-e))}})},function(e,t,n){var r=n(0);r(r.S,"Math",{trunc:function(e){return(e>0?Math.floor:Math.ceil)(e)}})},function(e,t,n){var r=n(0),i=n(36),o=String.fromCharCode,a=String.fromCodePoint;r(r.S+r.F*(!!a&&1!=a.length),"String",{fromCodePoint:function(e){for(var t,n=[],r=arguments.length,a=0;r>a;){if(t=+arguments[a++],i(t,1114111)!==t)throw RangeError(t+" is not a valid code point");n.push(t<65536?o(t):o(55296+((t-=65536)>>10),t%1024+56320))}return n.join("")}})},function(e,t,n){var r=n(0),i=n(16),o=n(6);r(r.S,"String",{raw:function(e){for(var t=i(e.raw),n=o(t.length),r=arguments.length,a=[],s=0;n>s;)a.push(String(t[s++])),s<r&&a.push(String(arguments[s]));return a.join("")}})},function(e,t,n){"use strict";n(47)("trim",(function(e){return function(){return e(this,3)}}))},function(e,t,n){"use strict";var r=n(58)(!0);n(82)(String,"String",(function(e){this._t=String(e),this._i=0}),(function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=r(t,n),this._i+=e.length,{value:e,done:!1})}))},function(e,t,n){"use strict";var r=n(0),i=n(58)(!1);r(r.P,"String",{codePointAt:function(e){return i(this,e)}})},function(e,t,n){"use strict";var r=n(0),i=n(6),o=n(84),a="".endsWith;r(r.P+r.F*n(85)("endsWith"),"String",{endsWith:function(e){var t=o(this,e,"endsWith"),n=arguments.length>1?arguments[1]:void 0,r=i(t.length),s=void 0===n?r:Math.min(i(n),r),c=String(e);return a?a.call(t,c,s):t.slice(s-c.length,s)===c}})},function(e,t,n){"use strict";var r=n(0),i=n(84);r(r.P+r.F*n(85)("includes"),"String",{includes:function(e){return!!~i(this,e,"includes").indexOf(e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){var r=n(0);r(r.P,"String",{repeat:n(79)})},function(e,t,n){"use strict";var r=n(0),i=n(6),o=n(84),a="".startsWith;r(r.P+r.F*n(85)("startsWith"),"String",{startsWith:function(e){var t=o(this,e,"startsWith"),n=i(Math.min(arguments.length>1?arguments[1]:void 0,t.length)),r=String(e);return a?a.call(t,r,n):t.slice(n,n+r.length)===r}})},function(e,t,n){"use strict";n(14)("anchor",(function(e){return function(t){return e(this,"a","name",t)}}))},function(e,t,n){"use strict";n(14)("big",(function(e){return function(){return e(this,"big","","")}}))},function(e,t,n){"use strict";n(14)("blink",(function(e){return function(){return e(this,"blink","","")}}))},function(e,t,n){"use strict";n(14)("bold",(function(e){return function(){return e(this,"b","","")}}))},function(e,t,n){"use strict";n(14)("fixed",(function(e){return function(){return e(this,"tt","","")}}))},function(e,t,n){"use strict";n(14)("fontcolor",(function(e){return function(t){return e(this,"font","color",t)}}))},function(e,t,n){"use strict";n(14)("fontsize",(function(e){return function(t){return e(this,"font","size",t)}}))},function(e,t,n){"use strict";n(14)("italics",(function(e){return function(){return e(this,"i","","")}}))},function(e,t,n){"use strict";n(14)("link",(function(e){return function(t){return e(this,"a","href",t)}}))},function(e,t,n){"use strict";n(14)("small",(function(e){return function(){return e(this,"small","","")}}))},function(e,t,n){"use strict";n(14)("strike",(function(e){return function(){return e(this,"strike","","")}}))},function(e,t,n){"use strict";n(14)("sub",(function(e){return function(){return e(this,"sub","","")}}))},function(e,t,n){"use strict";n(14)("sup",(function(e){return function(){return e(this,"sup","","")}}))},function(e,t,n){var r=n(0);r(r.S,"Date",{now:function(){return(new Date).getTime()}})},function(e,t,n){"use strict";var r=n(0),i=n(9),o=n(24);r(r.P+r.F*n(3)((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})})),"Date",{toJSON:function(e){var t=i(this),n=o(t);return"number"!=typeof n||isFinite(n)?t.toISOString():null}})},function(e,t,n){var r=n(0),i=n(227);r(r.P+r.F*(Date.prototype.toISOString!==i),"Date",{toISOString:i})},function(e,t,n){"use strict";var r=n(3),i=Date.prototype.getTime,o=Date.prototype.toISOString,a=function(e){return e>9?e:"0"+e};e.exports=r((function(){return"0385-07-25T07:06:39.999Z"!=o.call(new Date(-50000000000001))}))||!r((function(){o.call(new Date(NaN))}))?function(){if(!isFinite(i.call(this)))throw RangeError("Invalid time value");var e=this,t=e.getUTCFullYear(),n=e.getUTCMilliseconds(),r=t<0?"-":t>9999?"+":"";return r+("00000"+Math.abs(t)).slice(r?-6:-4)+"-"+a(e.getUTCMonth()+1)+"-"+a(e.getUTCDate())+"T"+a(e.getUTCHours())+":"+a(e.getUTCMinutes())+":"+a(e.getUTCSeconds())+"."+(n>99?n:"0"+a(n))+"Z"}:o},function(e,t,n){var r=Date.prototype,i=r.toString,o=r.getTime;new Date(NaN)+""!="Invalid Date"&&n(13)(r,"toString",(function(){var e=o.call(this);return e==e?i.call(this):"Invalid Date"}))},function(e,t,n){var r=n(5)("toPrimitive"),i=Date.prototype;r in i||n(12)(i,r,n(230))},function(e,t,n){"use strict";var r=n(1),i=n(24);e.exports=function(e){if("string"!==e&&"number"!==e&&"default"!==e)throw TypeError("Incorrect hint");return i(r(this),"number"!=e)}},function(e,t,n){var r=n(0);r(r.S,"Array",{isArray:n(57)})},function(e,t,n){"use strict";var r=n(20),i=n(0),o=n(9),a=n(116),s=n(86),c=n(6),l=n(87),u=n(88);i(i.S+i.F*!n(60)((function(e){Array.from(e)})),"Array",{from:function(e){var t,n,i,f,d=o(e),h="function"==typeof this?this:Array,v=arguments.length,p=v>1?arguments[1]:void 0,g=void 0!==p,m=0,y=u(d);if(g&&(p=r(p,v>2?arguments[2]:void 0,2)),null==y||h==Array&&s(y))for(n=new h(t=c(d.length));t>m;m++)l(n,m,g?p(d[m],m):d[m]);else for(f=y.call(d),n=new h;!(i=f.next()).done;m++)l(n,m,g?a(f,p,[i.value,m],!0):i.value);return n.length=m,n}})},function(e,t,n){"use strict";var r=n(0),i=n(87);r(r.S+r.F*n(3)((function(){function e(){}return!(Array.of.call(e)instanceof e)})),"Array",{of:function(){for(var e=0,t=arguments.length,n=new("function"==typeof this?this:Array)(t);t>e;)i(n,e,arguments[e++]);return n.length=t,n}})},function(e,t,n){"use strict";var r=n(0),i=n(16),o=[].join;r(r.P+r.F*(n(51)!=Object||!n(23)(o)),"Array",{join:function(e){return o.call(i(this),void 0===e?",":e)}})},function(e,t,n){"use strict";var r=n(0),i=n(75),o=n(21),a=n(36),s=n(6),c=[].slice;r(r.P+r.F*n(3)((function(){i&&c.call(i)})),"Array",{slice:function(e,t){var n=s(this.length),r=o(this);if(t=void 0===t?n:t,"Array"==r)return c.call(this,e,t);for(var i=a(e,n),l=a(t,n),u=s(l-i),f=new Array(u),d=0;d<u;d++)f[d]="String"==r?this.charAt(i+d):this[i+d];return f}})},function(e,t,n){"use strict";var r=n(0),i=n(11),o=n(9),a=n(3),s=[].sort,c=[1,2,3];r(r.P+r.F*(a((function(){c.sort(void 0)}))||!a((function(){c.sort(null)}))||!n(23)(s)),"Array",{sort:function(e){return void 0===e?s.call(o(this)):s.call(o(this),i(e))}})},function(e,t,n){"use strict";var r=n(0),i=n(27)(0),o=n(23)([].forEach,!0);r(r.P+r.F*!o,"Array",{forEach:function(e){return i(this,e,arguments[1])}})},function(e,t,n){var r=n(4),i=n(57),o=n(5)("species");e.exports=function(e){var t;return i(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!i(t.prototype)||(t=void 0),r(t)&&null===(t=t[o])&&(t=void 0)),void 0===t?Array:t}},function(e,t,n){"use strict";var r=n(0),i=n(27)(1);r(r.P+r.F*!n(23)([].map,!0),"Array",{map:function(e){return i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(0),i=n(27)(2);r(r.P+r.F*!n(23)([].filter,!0),"Array",{filter:function(e){return i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(0),i=n(27)(3);r(r.P+r.F*!n(23)([].some,!0),"Array",{some:function(e){return i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(0),i=n(27)(4);r(r.P+r.F*!n(23)([].every,!0),"Array",{every:function(e){return i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(0),i=n(117);r(r.P+r.F*!n(23)([].reduce,!0),"Array",{reduce:function(e){return i(this,e,arguments.length,arguments[1],!1)}})},function(e,t,n){"use strict";var r=n(0),i=n(117);r(r.P+r.F*!n(23)([].reduceRight,!0),"Array",{reduceRight:function(e){return i(this,e,arguments.length,arguments[1],!0)}})},function(e,t,n){"use strict";var r=n(0),i=n(55)(!1),o=[].indexOf,a=!!o&&1/[1].indexOf(1,-0)<0;r(r.P+r.F*(a||!n(23)(o)),"Array",{indexOf:function(e){return a?o.apply(this,arguments)||0:i(this,e,arguments[1])}})},function(e,t,n){"use strict";var r=n(0),i=n(16),o=n(22),a=n(6),s=[].lastIndexOf,c=!!s&&1/[1].lastIndexOf(1,-0)<0;r(r.P+r.F*(c||!n(23)(s)),"Array",{lastIndexOf:function(e){if(c)return s.apply(this,arguments)||0;var t=i(this),n=a(t.length),r=n-1;for(arguments.length>1&&(r=Math.min(r,o(arguments[1]))),r<0&&(r=n+r);r>=0;r--)if(r in t&&t[r]===e)return r||0;return-1}})},function(e,t,n){var r=n(0);r(r.P,"Array",{copyWithin:n(118)}),n(32)("copyWithin")},function(e,t,n){var r=n(0);r(r.P,"Array",{fill:n(90)}),n(32)("fill")},function(e,t,n){"use strict";var r=n(0),i=n(27)(5),o=!0;"find"in[]&&Array(1).find((function(){o=!1})),r(r.P+r.F*o,"Array",{find:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),n(32)("find")},function(e,t,n){"use strict";var r=n(0),i=n(27)(6),o="findIndex",a=!0;o in[]&&Array(1)[o]((function(){a=!1})),r(r.P+r.F*a,"Array",{findIndex:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),n(32)(o)},function(e,t,n){n(39)("Array")},function(e,t,n){var r=n(2),i=n(78),o=n(8).f,a=n(38).f,s=n(59),c=n(53),l=r.RegExp,u=l,f=l.prototype,d=/a/g,h=/a/g,v=new l(d)!==d;if(n(7)&&(!v||n(3)((function(){return h[n(5)("match")]=!1,l(d)!=d||l(h)==h||"/a/i"!=l(d,"i")})))){l=function(e,t){var n=this instanceof l,r=s(e),o=void 0===t;return!n&&r&&e.constructor===l&&o?e:i(v?new u(r&&!o?e.source:e,t):u((r=e instanceof l)?e.source:e,r&&o?c.call(e):t),n?this:f,l)};for(var p=function(e){e in l||o(l,e,{configurable:!0,get:function(){return u[e]},set:function(t){u[e]=t}})},g=a(u),m=0;g.length>m;)p(g[m++]);f.constructor=l,l.prototype=f,n(13)(r,"RegExp",l)}n(39)("RegExp")},function(e,t,n){"use strict";n(121);var r=n(1),i=n(53),o=n(7),a=/./.toString,s=function(e){n(13)(RegExp.prototype,"toString",e,!0)};n(3)((function(){return"/a/b"!=a.call({source:"a",flags:"b"})}))?s((function(){var e=r(this);return"/".concat(e.source,"/","flags"in e?e.flags:!o&&e instanceof RegExp?i.call(e):void 0)})):"toString"!=a.name&&s((function(){return a.call(this)}))},function(e,t,n){"use strict";var r=n(1),i=n(6),o=n(93),a=n(61);n(62)("match",1,(function(e,t,n,s){return[function(n){var r=e(this),i=null==n?void 0:n[t];return void 0!==i?i.call(n,r):new RegExp(n)[t](String(r))},function(e){var t=s(n,e,this);if(t.done)return t.value;var c=r(e),l=String(this);if(!c.global)return a(c,l);var u=c.unicode;c.lastIndex=0;for(var f,d=[],h=0;null!==(f=a(c,l));){var v=String(f[0]);d[h]=v,""===v&&(c.lastIndex=o(l,i(c.lastIndex),u)),h++}return 0===h?null:d}]}))},function(e,t,n){"use strict";var r=n(1),i=n(9),o=n(6),a=n(22),s=n(93),c=n(61),l=Math.max,u=Math.min,f=Math.floor,d=/\$([$&`']|\d\d?|<[^>]*>)/g,h=/\$([$&`']|\d\d?)/g;n(62)("replace",2,(function(e,t,n,v){return[function(r,i){var o=e(this),a=null==r?void 0:r[t];return void 0!==a?a.call(r,o,i):n.call(String(o),r,i)},function(e,t){var i=v(n,e,this,t);if(i.done)return i.value;var f=r(e),d=String(this),h="function"==typeof t;h||(t=String(t));var g=f.global;if(g){var m=f.unicode;f.lastIndex=0}for(var y=[];;){var _=c(f,d);if(null===_)break;if(y.push(_),!g)break;""===String(_[0])&&(f.lastIndex=s(d,o(f.lastIndex),m))}for(var S,E="",C=0,I=0;I<y.length;I++){_=y[I];for(var T=String(_[0]),b=l(u(a(_.index),d.length),0),R=[],O=1;O<_.length;O++)R.push(void 0===(S=_[O])?S:String(S));var w=_.groups;if(h){var A=[T].concat(R,b,d);void 0!==w&&A.push(w);var k=String(t.apply(void 0,A))}else k=p(T,d,b,R,w,t);b>=C&&(E+=d.slice(C,b)+k,C=b+T.length)}return E+d.slice(C)}];function p(e,t,r,o,a,s){var c=r+e.length,l=o.length,u=h;return void 0!==a&&(a=i(a),u=d),n.call(s,u,(function(n,i){var s;switch(i.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,r);case"'":return t.slice(c);case"<":s=a[i.slice(1,-1)];break;default:var u=+i;if(0===u)return n;if(u>l){var d=f(u/10);return 0===d?n:d<=l?void 0===o[d-1]?i.charAt(1):o[d-1]+i.charAt(1):n}s=o[u-1]}return void 0===s?"":s}))}}))},function(e,t,n){"use strict";var r=n(1),i=n(107),o=n(61);n(62)("search",1,(function(e,t,n,a){return[function(n){var r=e(this),i=null==n?void 0:n[t];return void 0!==i?i.call(n,r):new RegExp(n)[t](String(r))},function(e){var t=a(n,e,this);if(t.done)return t.value;var s=r(e),c=String(this),l=s.lastIndex;i(l,0)||(s.lastIndex=0);var u=o(s,c);return i(s.lastIndex,l)||(s.lastIndex=l),null===u?-1:u.index}]}))},function(e,t,n){"use strict";var r=n(59),i=n(1),o=n(54),a=n(93),s=n(6),c=n(61),l=n(92),u=n(3),f=Math.min,d=[].push,h="length",v=!u((function(){RegExp(4294967295,"y")}));n(62)("split",2,(function(e,t,n,u){var p;return p="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1)[h]||2!="ab".split(/(?:ab)*/)[h]||4!=".".split(/(.?)(.?)/)[h]||".".split(/()()/)[h]>1||"".split(/.?/)[h]?function(e,t){var i=String(this);if(void 0===e&&0===t)return[];if(!r(e))return n.call(i,e,t);for(var o,a,s,c=[],u=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),f=0,v=void 0===t?4294967295:t>>>0,p=new RegExp(e.source,u+"g");(o=l.call(p,i))&&!((a=p.lastIndex)>f&&(c.push(i.slice(f,o.index)),o[h]>1&&o.index<i[h]&&d.apply(c,o.slice(1)),s=o[0][h],f=a,c[h]>=v));)p.lastIndex===o.index&&p.lastIndex++;return f===i[h]?!s&&p.test("")||c.push(""):c.push(i.slice(f)),c[h]>v?c.slice(0,v):c}:"0".split(void 0,0)[h]?function(e,t){return void 0===e&&0===t?[]:n.call(this,e,t)}:n,[function(n,r){var i=e(this),o=null==n?void 0:n[t];return void 0!==o?o.call(n,i,r):p.call(String(i),n,r)},function(e,t){var r=u(p,e,this,t,p!==n);if(r.done)return r.value;var l=i(e),d=String(this),h=o(l,RegExp),g=l.unicode,m=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(v?"y":"g"),y=new h(v?l:"^(?:"+l.source+")",m),_=void 0===t?4294967295:t>>>0;if(0===_)return[];if(0===d.length)return null===c(y,d)?[d]:[];for(var S=0,E=0,C=[];E<d.length;){y.lastIndex=v?E:0;var I,T=c(y,v?d:d.slice(E));if(null===T||(I=f(s(y.lastIndex+(v?0:E)),d.length))===S)E=a(d,E,g);else{if(C.push(d.slice(S,E)),C.length===_)return C;for(var b=1;b<=T.length-1;b++)if(C.push(T[b]),C.length===_)return C;E=S=I}}return C.push(d.slice(S)),C}]}))},function(e,t,n){"use strict";var r,i,o,a,s=n(30),c=n(2),l=n(20),u=n(46),f=n(0),d=n(4),h=n(11),v=n(40),p=n(41),g=n(54),m=n(94).set,y=n(95)(),_=n(96),S=n(122),E=n(63),C=n(123),I=c.TypeError,T=c.process,b=T&&T.versions,R=b&&b.v8||"",O=c.Promise,w="process"==u(T),A=function(){},k=i=_.f,M=!!function(){try{var e=O.resolve(1),t=(e.constructor={})[n(5)("species")]=function(e){e(A,A)};return(w||"function"==typeof PromiseRejectionEvent)&&e.then(A)instanceof t&&0!==R.indexOf("6.6")&&-1===E.indexOf("Chrome/66")}catch(e){}}(),P=function(e){var t;return!(!d(e)||"function"!=typeof(t=e.then))&&t},D=function(e,t){if(!e._n){e._n=!0;var n=e._c;y((function(){for(var r=e._v,i=1==e._s,o=0,a=function(t){var n,o,a,s=i?t.ok:t.fail,c=t.resolve,l=t.reject,u=t.domain;try{s?(i||(2==e._h&&L(e),e._h=1),!0===s?n=r:(u&&u.enter(),n=s(r),u&&(u.exit(),a=!0)),n===t.promise?l(I("Promise-chain cycle")):(o=P(n))?o.call(n,c,l):c(n)):l(r)}catch(e){u&&!a&&u.exit(),l(e)}};n.length>o;)a(n[o++]);e._c=[],e._n=!1,t&&!e._h&&x(e)}))}},x=function(e){m.call(c,(function(){var t,n,r,i=e._v,o=N(e);if(o&&(t=S((function(){w?T.emit("unhandledRejection",i,e):(n=c.onunhandledrejection)?n({promise:e,reason:i}):(r=c.console)&&r.error&&r.error("Unhandled promise rejection",i)})),e._h=w||N(e)?2:1),e._a=void 0,o&&t.e)throw t.v}))},N=function(e){return 1!==e._h&&0===(e._a||e._c).length},L=function(e){m.call(c,(function(){var t;w?T.emit("rejectionHandled",e):(t=c.onrejectionhandled)&&t({promise:e,reason:e._v})}))},F=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),D(t,!0))},j=function(e){var t,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===e)throw I("Promise can't be resolved itself");(t=P(e))?y((function(){var r={_w:n,_d:!1};try{t.call(e,l(j,r,1),l(F,r,1))}catch(e){F.call(r,e)}})):(n._v=e,n._s=1,D(n,!1))}catch(e){F.call({_w:n,_d:!1},e)}}};M||(O=function(e){v(this,O,"Promise","_h"),h(e),r.call(this);try{e(l(j,this,1),l(F,this,1))}catch(e){F.call(this,e)}},(r=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=n(42)(O.prototype,{then:function(e,t){var n=k(g(this,O));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=w?T.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&D(this,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),o=function(){var e=new r;this.promise=e,this.resolve=l(j,e,1),this.reject=l(F,e,1)},_.f=k=function(e){return e===O||e===a?new o(e):i(e)}),f(f.G+f.W+f.F*!M,{Promise:O}),n(45)(O,"Promise"),n(39)("Promise"),a=n(19).Promise,f(f.S+f.F*!M,"Promise",{reject:function(e){var t=k(this);return(0,t.reject)(e),t.promise}}),f(f.S+f.F*(s||!M),"Promise",{resolve:function(e){return C(s&&this===a?O:this,e)}}),f(f.S+f.F*!(M&&n(60)((function(e){O.all(e).catch(A)}))),"Promise",{all:function(e){var t=this,n=k(t),r=n.resolve,i=n.reject,o=S((function(){var n=[],o=0,a=1;p(e,!1,(function(e){var s=o++,c=!1;n.push(void 0),a++,t.resolve(e).then((function(e){c||(c=!0,n[s]=e,--a||r(n))}),i)})),--a||r(n)}));return o.e&&i(o.v),n.promise},race:function(e){var t=this,n=k(t),r=n.reject,i=S((function(){p(e,!1,(function(e){t.resolve(e).then(n.resolve,r)}))}));return i.e&&r(i.v),n.promise}})},function(e,t,n){"use strict";var r=n(128),i=n(43);n(64)("WeakSet",(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(e){return r.def(i(this,"WeakSet"),e,!0)}},r,!1,!0)},function(e,t,n){"use strict";var r=n(0),i=n(65),o=n(97),a=n(1),s=n(36),c=n(6),l=n(4),u=n(2).ArrayBuffer,f=n(54),d=o.ArrayBuffer,h=o.DataView,v=i.ABV&&u.isView,p=d.prototype.slice,g=i.VIEW;r(r.G+r.W+r.F*(u!==d),{ArrayBuffer:d}),r(r.S+r.F*!i.CONSTR,"ArrayBuffer",{isView:function(e){return v&&v(e)||l(e)&&g in e}}),r(r.P+r.U+r.F*n(3)((function(){return!new d(2).slice(1,void 0).byteLength})),"ArrayBuffer",{slice:function(e,t){if(void 0!==p&&void 0===t)return p.call(a(this),e);for(var n=a(this).byteLength,r=s(e,n),i=s(void 0===t?n:t,n),o=new(f(this,d))(c(i-r)),l=new h(this),u=new h(o),v=0;r<i;)u.setUint8(v++,l.getUint8(r++));return o}}),n(39)("ArrayBuffer")},function(e,t,n){var r=n(0);r(r.G+r.W+r.F*!n(65).ABV,{DataView:n(97).DataView})},function(e,t,n){n(28)("Int8",1,(function(e){return function(t,n,r){return e(this,t,n,r)}}))},function(e,t,n){n(28)("Uint8",1,(function(e){return function(t,n,r){return e(this,t,n,r)}}))},function(e,t,n){n(28)("Uint8",1,(function(e){return function(t,n,r){return e(this,t,n,r)}}),!0)},function(e,t,n){n(28)("Int16",2,(function(e){return function(t,n,r){return e(this,t,n,r)}}))},function(e,t,n){n(28)("Uint16",2,(function(e){return function(t,n,r){return e(this,t,n,r)}}))},function(e,t,n){n(28)("Int32",4,(function(e){return function(t,n,r){return e(this,t,n,r)}}))},function(e,t,n){n(28)("Uint32",4,(function(e){return function(t,n,r){return e(this,t,n,r)}}))},function(e,t,n){n(28)("Float32",4,(function(e){return function(t,n,r){return e(this,t,n,r)}}))},function(e,t,n){n(28)("Float64",8,(function(e){return function(t,n,r){return e(this,t,n,r)}}))},function(e,t,n){var r=n(0),i=n(11),o=n(1),a=(n(2).Reflect||{}).apply,s=Function.apply;r(r.S+r.F*!n(3)((function(){a((function(){}))})),"Reflect",{apply:function(e,t,n){var r=i(e),c=o(n);return a?a(r,t,c):s.call(r,t,c)}})},function(e,t,n){var r=n(0),i=n(37),o=n(11),a=n(1),s=n(4),c=n(3),l=n(108),u=(n(2).Reflect||{}).construct,f=c((function(){function e(){}return!(u((function(){}),[],e)instanceof e)})),d=!c((function(){u((function(){}))}));r(r.S+r.F*(f||d),"Reflect",{construct:function(e,t){o(e),a(t);var n=arguments.length<3?e:o(arguments[2]);if(d&&!f)return u(e,t,n);if(e==n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var r=[null];return r.push.apply(r,t),new(l.apply(e,r))}var c=n.prototype,h=i(s(c)?c:Object.prototype),v=Function.apply.call(e,h,t);return s(v)?v:h}})},function(e,t,n){var r=n(8),i=n(0),o=n(1),a=n(24);i(i.S+i.F*n(3)((function(){Reflect.defineProperty(r.f({},1,{value:1}),1,{value:2})})),"Reflect",{defineProperty:function(e,t,n){o(e),t=a(t,!0),o(n);try{return r.f(e,t,n),!0}catch(e){return!1}}})},function(e,t,n){var r=n(0),i=n(17).f,o=n(1);r(r.S,"Reflect",{deleteProperty:function(e,t){var n=i(o(e),t);return!(n&&!n.configurable)&&delete e[t]}})},function(e,t,n){"use strict";var r=n(0),i=n(1),o=function(e){this._t=i(e),this._i=0;var t,n=this._k=[];for(t in e)n.push(t)};n(83)(o,"Object",(function(){var e,t=this._k;do{if(this._i>=t.length)return{value:void 0,done:!0}}while(!((e=t[this._i++])in this._t));return{value:e,done:!1}})),r(r.S,"Reflect",{enumerate:function(e){return new o(e)}})},function(e,t,n){var r=n(17),i=n(18),o=n(15),a=n(0),s=n(4),c=n(1);a(a.S,"Reflect",{get:function e(t,n){var a,l,u=arguments.length<3?t:arguments[2];return c(t)===u?t[n]:(a=r.f(t,n))?o(a,"value")?a.value:void 0!==a.get?a.get.call(u):void 0:s(l=i(t))?e(l,n,u):void 0}})},function(e,t,n){var r=n(17),i=n(0),o=n(1);i(i.S,"Reflect",{getOwnPropertyDescriptor:function(e,t){return r.f(o(e),t)}})},function(e,t,n){var r=n(0),i=n(18),o=n(1);r(r.S,"Reflect",{getPrototypeOf:function(e){return i(o(e))}})},function(e,t,n){var r=n(0);r(r.S,"Reflect",{has:function(e,t){return t in e}})},function(e,t,n){var r=n(0),i=n(1),o=Object.isExtensible;r(r.S,"Reflect",{isExtensible:function(e){return i(e),!o||o(e)}})},function(e,t,n){var r=n(0);r(r.S,"Reflect",{ownKeys:n(130)})},function(e,t,n){var r=n(0),i=n(1),o=Object.preventExtensions;r(r.S,"Reflect",{preventExtensions:function(e){i(e);try{return o&&o(e),!0}catch(e){return!1}}})},function(e,t,n){var r=n(8),i=n(17),o=n(18),a=n(15),s=n(0),c=n(33),l=n(1),u=n(4);s(s.S,"Reflect",{set:function e(t,n,s){var f,d,h=arguments.length<4?t:arguments[3],v=i.f(l(t),n);if(!v){if(u(d=o(t)))return e(d,n,s,h);v=c(0)}if(a(v,"value")){if(!1===v.writable||!u(h))return!1;if(f=i.f(h,n)){if(f.get||f.set||!1===f.writable)return!1;f.value=s,r.f(h,n,f)}else r.f(h,n,c(0,s));return!0}return void 0!==v.set&&(v.set.call(h,s),!0)}})},function(e,t,n){var r=n(0),i=n(76);i&&r(r.S,"Reflect",{setPrototypeOf:function(e,t){i.check(e,t);try{return i.set(e,t),!0}catch(e){return!1}}})},function(e,t,n){"use strict";var r=n(0),i=n(55)(!0);r(r.P,"Array",{includes:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),n(32)("includes")},function(e,t,n){"use strict";var r=n(0),i=n(131),o=n(9),a=n(6),s=n(11),c=n(89);r(r.P,"Array",{flatMap:function(e){var t,n,r=o(this);return s(e),t=a(r.length),n=c(r,0),i(n,r,r,t,0,1,e,arguments[1]),n}}),n(32)("flatMap")},function(e,t,n){"use strict";var r=n(0),i=n(131),o=n(9),a=n(6),s=n(22),c=n(89);r(r.P,"Array",{flatten:function(){var e=arguments[0],t=o(this),n=a(t.length),r=c(t,0);return i(r,t,t,n,0,void 0===e?1:s(e)),r}}),n(32)("flatten")},function(e,t,n){"use strict";var r=n(0),i=n(58)(!0),o=n(3)((function(){return"𠮷"!=="𠮷".at(0)}));r(r.P+r.F*o,"String",{at:function(e){return i(this,e)}})},function(e,t,n){"use strict";var r=n(0),i=n(132),o=n(63),a=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(o);r(r.P+r.F*a,"String",{padStart:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0,!0)}})},function(e,t,n){"use strict";var r=n(0),i=n(132),o=n(63),a=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(o);r(r.P+r.F*a,"String",{padEnd:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0,!1)}})},function(e,t,n){"use strict";n(47)("trimLeft",(function(e){return function(){return e(this,1)}}),"trimStart")},function(e,t,n){"use strict";n(47)("trimRight",(function(e){return function(){return e(this,2)}}),"trimEnd")},function(e,t,n){"use strict";var r=n(0),i=n(25),o=n(6),a=n(59),s=n(53),c=RegExp.prototype,l=function(e,t){this._r=e,this._s=t};n(83)(l,"RegExp String",(function(){var e=this._r.exec(this._s);return{value:e,done:null===e}})),r(r.P,"String",{matchAll:function(e){if(i(this),!a(e))throw TypeError(e+" is not a regexp!");var t=String(this),n="flags"in c?String(e.flags):s.call(e),r=new RegExp(e.source,~n.indexOf("g")?n:"g"+n);return r.lastIndex=o(e.lastIndex),new l(r,t)}})},function(e,t,n){n(72)("asyncIterator")},function(e,t,n){n(72)("observable")},function(e,t,n){var r=n(0),i=n(130),o=n(16),a=n(17),s=n(87);r(r.S,"Object",{getOwnPropertyDescriptors:function(e){for(var t,n,r=o(e),c=a.f,l=i(r),u={},f=0;l.length>f;)void 0!==(n=c(r,t=l[f++]))&&s(u,t,n);return u}})},function(e,t,n){var r=n(0),i=n(133)(!1);r(r.S,"Object",{values:function(e){return i(e)}})},function(e,t,n){var r=n(0),i=n(133)(!0);r(r.S,"Object",{entries:function(e){return i(e)}})},function(e,t,n){"use strict";var r=n(0),i=n(9),o=n(11),a=n(8);n(7)&&r(r.P+n(66),"Object",{__defineGetter__:function(e,t){a.f(i(this),e,{get:o(t),enumerable:!0,configurable:!0})}})},function(e,t,n){"use strict";var r=n(0),i=n(9),o=n(11),a=n(8);n(7)&&r(r.P+n(66),"Object",{__defineSetter__:function(e,t){a.f(i(this),e,{set:o(t),enumerable:!0,configurable:!0})}})},function(e,t,n){"use strict";var r=n(0),i=n(9),o=n(24),a=n(18),s=n(17).f;n(7)&&r(r.P+n(66),"Object",{__lookupGetter__:function(e){var t,n=i(this),r=o(e,!0);do{if(t=s(n,r))return t.get}while(n=a(n))}})},function(e,t,n){"use strict";var r=n(0),i=n(9),o=n(24),a=n(18),s=n(17).f;n(7)&&r(r.P+n(66),"Object",{__lookupSetter__:function(e){var t,n=i(this),r=o(e,!0);do{if(t=s(n,r))return t.set}while(n=a(n))}})},function(e,t,n){var r=n(0);r(r.P+r.R,"Map",{toJSON:n(134)("Map")})},function(e,t,n){var r=n(0);r(r.P+r.R,"Set",{toJSON:n(134)("Set")})},function(e,t,n){n(67)("Map")},function(e,t,n){n(67)("Set")},function(e,t,n){n(67)("WeakMap")},function(e,t,n){n(67)("WeakSet")},function(e,t,n){n(68)("Map")},function(e,t,n){n(68)("Set")},function(e,t,n){n(68)("WeakMap")},function(e,t,n){n(68)("WeakSet")},function(e,t,n){var r=n(0);r(r.G,{global:n(2)})},function(e,t,n){var r=n(0);r(r.S,"System",{global:n(2)})},function(e,t,n){var r=n(0),i=n(21);r(r.S,"Error",{isError:function(e){return"Error"===i(e)}})},function(e,t,n){var r=n(0);r(r.S,"Math",{clamp:function(e,t,n){return Math.min(n,Math.max(t,e))}})},function(e,t,n){var r=n(0);r(r.S,"Math",{DEG_PER_RAD:Math.PI/180})},function(e,t,n){var r=n(0),i=180/Math.PI;r(r.S,"Math",{degrees:function(e){return e*i}})},function(e,t,n){var r=n(0),i=n(136),o=n(115);r(r.S,"Math",{fscale:function(e,t,n,r,a){return o(i(e,t,n,r,a))}})},function(e,t,n){var r=n(0);r(r.S,"Math",{iaddh:function(e,t,n,r){var i=e>>>0,o=n>>>0;return(t>>>0)+(r>>>0)+((i&o|(i|o)&~(i+o>>>0))>>>31)|0}})},function(e,t,n){var r=n(0);r(r.S,"Math",{isubh:function(e,t,n,r){var i=e>>>0,o=n>>>0;return(t>>>0)-(r>>>0)-((~i&o|~(i^o)&i-o>>>0)>>>31)|0}})},function(e,t,n){var r=n(0);r(r.S,"Math",{imulh:function(e,t){var n=+e,r=+t,i=65535&n,o=65535&r,a=n>>16,s=r>>16,c=(a*o>>>0)+(i*o>>>16);return a*s+(c>>16)+((i*s>>>0)+(65535&c)>>16)}})},function(e,t,n){var r=n(0);r(r.S,"Math",{RAD_PER_DEG:180/Math.PI})},function(e,t,n){var r=n(0),i=Math.PI/180;r(r.S,"Math",{radians:function(e){return e*i}})},function(e,t,n){var r=n(0);r(r.S,"Math",{scale:n(136)})},function(e,t,n){var r=n(0);r(r.S,"Math",{umulh:function(e,t){var n=+e,r=+t,i=65535&n,o=65535&r,a=n>>>16,s=r>>>16,c=(a*o>>>0)+(i*o>>>16);return a*s+(c>>>16)+((i*s>>>0)+(65535&c)>>>16)}})},function(e,t,n){var r=n(0);r(r.S,"Math",{signbit:function(e){return(e=+e)!=e?e:0==e?1/e==1/0:e>0}})},function(e,t,n){"use strict";var r=n(0),i=n(19),o=n(2),a=n(54),s=n(123);r(r.P+r.R,"Promise",{finally:function(e){var t=a(this,i.Promise||o.Promise),n="function"==typeof e;return this.then(n?function(n){return s(t,e()).then((function(){return n}))}:e,n?function(n){return s(t,e()).then((function(){throw n}))}:e)}})},function(e,t,n){"use strict";var r=n(0),i=n(96),o=n(122);r(r.S,"Promise",{try:function(e){var t=i.f(this),n=o(e);return(n.e?t.reject:t.resolve)(n.v),t.promise}})},function(e,t,n){var r=n(29),i=n(1),o=r.key,a=r.set;r.exp({defineMetadata:function(e,t,n,r){a(e,t,i(n),o(r))}})},function(e,t,n){var r=n(29),i=n(1),o=r.key,a=r.map,s=r.store;r.exp({deleteMetadata:function(e,t){var n=arguments.length<3?void 0:o(arguments[2]),r=a(i(t),n,!1);if(void 0===r||!r.delete(e))return!1;if(r.size)return!0;var c=s.get(t);return c.delete(n),!!c.size||s.delete(t)}})},function(e,t,n){var r=n(29),i=n(1),o=n(18),a=r.has,s=r.get,c=r.key,l=function(e,t,n){if(a(e,t,n))return s(e,t,n);var r=o(t);return null!==r?l(e,r,n):void 0};r.exp({getMetadata:function(e,t){return l(e,i(t),arguments.length<3?void 0:c(arguments[2]))}})},function(e,t,n){var r=n(126),i=n(135),o=n(29),a=n(1),s=n(18),c=o.keys,l=o.key,u=function(e,t){var n=c(e,t),o=s(e);if(null===o)return n;var a=u(o,t);return a.length?n.length?i(new r(n.concat(a))):a:n};o.exp({getMetadataKeys:function(e){return u(a(e),arguments.length<2?void 0:l(arguments[1]))}})},function(e,t,n){var r=n(29),i=n(1),o=r.get,a=r.key;r.exp({getOwnMetadata:function(e,t){return o(e,i(t),arguments.length<3?void 0:a(arguments[2]))}})},function(e,t,n){var r=n(29),i=n(1),o=r.keys,a=r.key;r.exp({getOwnMetadataKeys:function(e){return o(i(e),arguments.length<2?void 0:a(arguments[1]))}})},function(e,t,n){var r=n(29),i=n(1),o=n(18),a=r.has,s=r.key,c=function(e,t,n){if(a(e,t,n))return!0;var r=o(t);return null!==r&&c(e,r,n)};r.exp({hasMetadata:function(e,t){return c(e,i(t),arguments.length<3?void 0:s(arguments[2]))}})},function(e,t,n){var r=n(29),i=n(1),o=r.has,a=r.key;r.exp({hasOwnMetadata:function(e,t){return o(e,i(t),arguments.length<3?void 0:a(arguments[2]))}})},function(e,t,n){var r=n(29),i=n(1),o=n(11),a=r.key,s=r.set;r.exp({metadata:function(e,t){return function(n,r){s(e,t,(void 0!==r?i:o)(n),a(r))}}})},function(e,t,n){var r=n(0),i=n(95)(),o=n(2).process,a="process"==n(21)(o);r(r.G,{asap:function(e){var t=a&&o.domain;i(t?t.bind(e):e)}})},function(e,t,n){"use strict";var r=n(0),i=n(2),o=n(19),a=n(95)(),s=n(5)("observable"),c=n(11),l=n(1),u=n(40),f=n(42),d=n(12),h=n(41),v=h.RETURN,p=function(e){return null==e?void 0:c(e)},g=function(e){var t=e._c;t&&(e._c=void 0,t())},m=function(e){return void 0===e._o},y=function(e){m(e)||(e._o=void 0,g(e))},_=function(e,t){l(e),this._c=void 0,this._o=e,e=new S(this);try{var n=t(e),r=n;null!=n&&("function"==typeof n.unsubscribe?n=function(){r.unsubscribe()}:c(n),this._c=n)}catch(t){return void e.error(t)}m(this)&&g(this)};_.prototype=f({},{unsubscribe:function(){y(this)}});var S=function(e){this._s=e};S.prototype=f({},{next:function(e){var t=this._s;if(!m(t)){var n=t._o;try{var r=p(n.next);if(r)return r.call(n,e)}catch(e){try{y(t)}finally{throw e}}}},error:function(e){var t=this._s;if(m(t))throw e;var n=t._o;t._o=void 0;try{var r=p(n.error);if(!r)throw e;e=r.call(n,e)}catch(e){try{g(t)}finally{throw e}}return g(t),e},complete:function(e){var t=this._s;if(!m(t)){var n=t._o;t._o=void 0;try{var r=p(n.complete);e=r?r.call(n,e):void 0}catch(e){try{g(t)}finally{throw e}}return g(t),e}}});var E=function(e){u(this,E,"Observable","_f")._f=c(e)};f(E.prototype,{subscribe:function(e){return new _(e,this._f)},forEach:function(e){var t=this;return new(o.Promise||i.Promise)((function(n,r){c(e);var i=t.subscribe({next:function(t){try{return e(t)}catch(e){r(e),i.unsubscribe()}},error:r,complete:n})}))}}),f(E,{from:function(e){var t="function"==typeof this?this:E,n=p(l(e)[s]);if(n){var r=l(n.call(e));return r.constructor===t?r:new t((function(e){return r.subscribe(e)}))}return new t((function(t){var n=!1;return a((function(){if(!n){try{if(h(e,!1,(function(e){if(t.next(e),n)return v}))===v)return}catch(e){if(n)throw e;return void t.error(e)}t.complete()}})),function(){n=!0}}))},of:function(){for(var e=0,t=arguments.length,n=new Array(t);e<t;)n[e]=arguments[e++];return new("function"==typeof this?this:E)((function(e){var t=!1;return a((function(){if(!t){for(var r=0;r<n.length;++r)if(e.next(n[r]),t)return;e.complete()}})),function(){t=!0}}))}}),d(E.prototype,s,(function(){return this})),r(r.G,{Observable:E}),n(39)("Observable")},function(e,t,n){var r=n(2),i=n(0),o=n(63),a=[].slice,s=/MSIE .\./.test(o),c=function(e){return function(t,n){var r=arguments.length>2,i=!!r&&a.call(arguments,2);return e(r?function(){("function"==typeof t?t:Function(t)).apply(this,i)}:t,n)}};i(i.G+i.B+i.F*s,{setTimeout:c(r.setTimeout),setInterval:c(r.setInterval)})},function(e,t,n){var r=n(0),i=n(94);r(r.G+r.B,{setImmediate:i.set,clearImmediate:i.clear})},function(e,t,n){for(var r=n(91),i=n(35),o=n(13),a=n(2),s=n(12),c=n(48),l=n(5),u=l("iterator"),f=l("toStringTag"),d=c.Array,h={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},v=i(h),p=0;p<v.length;p++){var g,m=v[p],y=h[m],_=a[m],S=_&&_.prototype;if(S&&(S[u]||s(S,u,d),S[f]||s(S,f,m),c[m]=d,y))for(g in r)S[g]||o(S,g,r[g],!0)}},function(e,t,n){(function(t){!function(t){"use strict";var n=Object.prototype,r=n.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag",c="object"==typeof e,l=t.regeneratorRuntime;if(l)c&&(e.exports=l);else{(l=t.regeneratorRuntime=c?e.exports:{}).wrap=p;var u={},f={};f[o]=function(){return this};var d=Object.getPrototypeOf,h=d&&d(d(R([])));h&&h!==n&&r.call(h,o)&&(f=h);var v=_.prototype=m.prototype=Object.create(f);y.prototype=v.constructor=_,_.constructor=y,_[s]=y.displayName="GeneratorFunction",l.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},l.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,s in e||(e[s]="GeneratorFunction")),e.prototype=Object.create(v),e},l.awrap=function(e){return{__await:e}},S(E.prototype),E.prototype[a]=function(){return this},l.AsyncIterator=E,l.async=function(e,t,n,r){var i=new E(p(e,t,n,r));return l.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},S(v),v[s]="Generator",v[o]=function(){return this},v.toString=function(){return"[object Generator]"},l.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},l.values=R,b.prototype={constructor:b,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(T),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var s=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,u):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;T(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:R(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}}}function p(e,t,n,r){var i=t&&t.prototype instanceof m?t:m,o=Object.create(i.prototype),a=new b(r||[]);return o._invoke=function(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return O()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var s=C(a,n);if(s){if(s===u)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=g(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===u)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}(e,n,a),o}function g(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}function m(){}function y(){}function _(){}function S(e){["next","throw","return"].forEach((function(t){e[t]=function(e){return this._invoke(t,e)}}))}function E(e){function n(t,i,o,a){var s=g(e[t],e,i);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==typeof l&&r.call(l,"__await")?Promise.resolve(l.__await).then((function(e){n("next",e,o,a)}),(function(e){n("throw",e,o,a)})):Promise.resolve(l).then((function(e){c.value=e,o(c)}),a)}a(s.arg)}var i;"object"==typeof t.process&&t.process.domain&&(n=t.process.domain.bind(n)),this._invoke=function(e,t){function r(){return new Promise((function(r,i){n(e,t,r,i)}))}return i=i?i.then(r,r):r()}}function C(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,C(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var r=g(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function I(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function b(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(I,this),this.reset(!0)}function R(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:O}}function O(){return{value:void 0,done:!0}}}("object"==typeof t?t:"object"==typeof window?window:"object"==typeof self?self:this)}).call(this,n(70))},function(e,t,n){n(346),e.exports=n(19).RegExp.escape},function(e,t,n){var r=n(0),i=n(347)(/[\\^$*+?.()|[\]{}]/g,"\\$&");r(r.S,"RegExp",{escape:function(e){return i(e)}})},function(e,t){e.exports=function(e,t){var n=t===Object(t)?function(e){return t[e]}:t;return function(t){return String(t).replace(e,n)}}},function(e,t,n){e.exports=n(349)},function(e,t,n){"use strict";var r=n(10),i=n(137),o=n(350),a=n(144);var s=function e(t){var n=new o(t),s=i(o.prototype.request,n);return r.extend(s,o.prototype,n),r.extend(s,n),s.create=function(n){return e(a(t,n))},s}(n(98));s.Axios=o,s.CanceledError=n(69),s.CancelToken=n(369),s.isCancel=n(143),s.VERSION=n(145).version,s.toFormData=n(140),s.AxiosError=n(49),s.Cancel=s.CanceledError,s.all=function(e){return Promise.all(e)},s.spread=n(370),s.isAxiosError=n(371),e.exports=s,e.exports.default=s},function(e,t,n){"use strict";var r=n(10),i=n(138),o=n(351),a=n(352),s=n(144),c=n(142),l=n(368),u=l.validators;function f(e){this.defaults=e,this.interceptors={request:new o,response:new o}}f.prototype.request=function(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},(t=s(this.defaults,t)).method?t.method=t.method.toLowerCase():this.defaults.method?t.method=this.defaults.method.toLowerCase():t.method="get";var n=t.transitional;void 0!==n&&l.assertOptions(n,{silentJSONParsing:u.transitional(u.boolean),forcedJSONParsing:u.transitional(u.boolean),clarifyTimeoutError:u.transitional(u.boolean)},!1);var r=[],i=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,r.unshift(e.fulfilled,e.rejected))}));var o,c=[];if(this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)})),!i){var f=[a,void 0];for(Array.prototype.unshift.apply(f,r),f=f.concat(c),o=Promise.resolve(t);f.length;)o=o.then(f.shift(),f.shift());return o}for(var d=t;r.length;){var h=r.shift(),v=r.shift();try{d=h(d)}catch(e){v(e);break}}try{o=a(d)}catch(e){return Promise.reject(e)}for(;c.length;)o=o.then(c.shift(),c.shift());return o},f.prototype.getUri=function(e){e=s(this.defaults,e);var t=c(e.baseURL,e.url);return i(t,e.params,e.paramsSerializer)},r.forEach(["delete","get","head","options"],(function(e){f.prototype[e]=function(t,n){return this.request(s(n||{},{method:e,url:t,data:(n||{}).data}))}})),r.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,i){return this.request(s(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}f.prototype[e]=t(),f.prototype[e+"Form"]=t(!0)})),e.exports=f},function(e,t,n){"use strict";var r=n(10);function i(){this.handlers=[]}i.prototype.use=function(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1},i.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},i.prototype.forEach=function(e){r.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=i},function(e,t,n){"use strict";var r=n(10),i=n(353),o=n(143),a=n(98),s=n(69);function c(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new s}e.exports=function(e){return c(e),e.headers=e.headers||{},e.data=i.call(e,e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),r.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||a.adapter)(e).then((function(t){return c(e),t.data=i.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return o(t)||(c(e),t&&t.response&&(t.response.data=i.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},function(e,t,n){"use strict";var r=n(10),i=n(98);e.exports=function(e,t,n){var o=this||i;return r.forEach(n,(function(n){e=n.call(o,e,t)})),e}},function(e,t){var n,r,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(n===setTimeout)return setTimeout(e,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:o}catch(e){n=o}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(e){r=a}}();var c,l=[],u=!1,f=-1;function d(){u&&c&&(u=!1,c.length?l=c.concat(l):f=-1,l.length&&h())}function h(){if(!u){var e=s(d);u=!0;for(var t=l.length;t;){for(c=l,l=[];++f<t;)c&&c[f].run();f=-1,t=l.length}c=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function v(e,t){this.fun=e,this.array=t}function p(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new v(e,t)),1!==l.length||u||s(h)},v.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=p,i.addListener=p,i.once=p,i.off=p,i.removeListener=p,i.removeAllListeners=p,i.emit=p,i.prependListener=p,i.prependOnceListener=p,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,n){"use strict";var r=n(10);e.exports=function(e,t){r.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))}},function(e,t,n){"use strict";(function(e){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var r=n(357),i=n(358),o=n(359);function a(){return c.TYPED_ARRAY_SUPPORT?**********:**********}function s(e,t){if(a()<t)throw new RangeError("Invalid typed array length");return c.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=c.prototype:(null===e&&(e=new c(t)),e.length=t),e}function c(e,t,n){if(!(c.TYPED_ARRAY_SUPPORT||this instanceof c))return new c(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return f(this,e)}return l(this,e,t,n)}function l(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");t=void 0===n&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,n):new Uint8Array(t,n,r);c.TYPED_ARRAY_SUPPORT?(e=t).__proto__=c.prototype:e=d(e,t);return e}(e,t,n,r):"string"==typeof t?function(e,t,n){"string"==typeof n&&""!==n||(n="utf8");if(!c.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|v(t,n),i=(e=s(e,r)).write(t,n);i!==r&&(e=e.slice(0,i));return e}(e,t,n):function(e,t){if(c.isBuffer(t)){var n=0|h(t.length);return 0===(e=s(e,n)).length||t.copy(e,0,0,n),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(r=t.length)!=r?s(e,0):d(e,t);if("Buffer"===t.type&&o(t.data))return d(e,t.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function u(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function f(e,t){if(u(t),e=s(e,t<0?0:0|h(t)),!c.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function d(e,t){var n=t.length<0?0:0|h(t.length);e=s(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function h(e){if(e>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|e}function v(e,t){if(c.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return V(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return G(e).length;default:if(r)return V(e).length;t=(""+t).toLowerCase(),r=!0}}function p(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return A(this,t,n);case"utf8":case"utf-8":return R(this,t,n);case"ascii":return O(this,t,n);case"latin1":case"binary":return w(this,t,n);case"base64":return b(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return k(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function g(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function m(e,t,n,r,i){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(i)return-1;n=e.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof t&&(t=c.from(t,r)),c.isBuffer(t))return 0===t.length?-1:y(e,t,n,r,i);if("number"==typeof t)return t&=255,c.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):y(e,[t],n,r,i);throw new TypeError("val must be string, number or Buffer")}function y(e,t,n,r,i){var o,a=1,s=e.length,c=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;a=2,s/=2,c/=2,n/=2}function l(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(i){var u=-1;for(o=n;o<s;o++)if(l(e,o)===l(t,-1===u?0:o-u)){if(-1===u&&(u=o),o-u+1===c)return u*a}else-1!==u&&(o-=o-u),u=-1}else for(n+c>s&&(n=s-c),o=n;o>=0;o--){for(var f=!0,d=0;d<c;d++)if(l(e,o+d)!==l(t,d)){f=!1;break}if(f)return o}return-1}function _(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r))>i&&(r=i):r=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var a=0;a<r;++a){var s=parseInt(t.substr(2*a,2),16);if(isNaN(s))return a;e[n+a]=s}return a}function S(e,t,n,r){return B(V(t,e.length-n),e,n,r)}function E(e,t,n,r){return B(function(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,r)}function C(e,t,n,r){return E(e,t,n,r)}function I(e,t,n,r){return B(G(t),e,n,r)}function T(e,t,n,r){return B(function(e,t){for(var n,r,i,o=[],a=0;a<e.length&&!((t-=2)<0);++a)n=e.charCodeAt(a),r=n>>8,i=n%256,o.push(i),o.push(r);return o}(t,e.length-n),e,n,r)}function b(e,t,n){return 0===t&&n===e.length?r.fromByteArray(e):r.fromByteArray(e.slice(t,n))}function R(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;i<n;){var o,a,s,c,l=e[i],u=null,f=l>239?4:l>223?3:l>191?2:1;if(i+f<=n)switch(f){case 1:l<128&&(u=l);break;case 2:128==(192&(o=e[i+1]))&&(c=(31&l)<<6|63&o)>127&&(u=c);break;case 3:o=e[i+1],a=e[i+2],128==(192&o)&&128==(192&a)&&(c=(15&l)<<12|(63&o)<<6|63&a)>2047&&(c<55296||c>57343)&&(u=c);break;case 4:o=e[i+1],a=e[i+2],s=e[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&(c=(15&l)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&c<1114112&&(u=c)}null===u?(u=65533,f=1):u>65535&&(u-=65536,r.push(u>>>10&1023|55296),u=56320|1023&u),r.push(u),i+=f}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);var n="",r=0;for(;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=4096));return n}(r)}t.Buffer=c,t.SlowBuffer=function(e){+e!=e&&(e=0);return c.alloc(+e)},t.INSPECT_MAX_BYTES=50,c.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=a(),c.poolSize=8192,c._augment=function(e){return e.__proto__=c.prototype,e},c.from=function(e,t,n){return l(null,e,t,n)},c.TYPED_ARRAY_SUPPORT&&(c.prototype.__proto__=Uint8Array.prototype,c.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&c[Symbol.species]===c&&Object.defineProperty(c,Symbol.species,{value:null,configurable:!0})),c.alloc=function(e,t,n){return function(e,t,n,r){return u(t),t<=0?s(e,t):void 0!==n?"string"==typeof r?s(e,t).fill(n,r):s(e,t).fill(n):s(e,t)}(null,e,t,n)},c.allocUnsafe=function(e){return f(null,e)},c.allocUnsafeSlow=function(e){return f(null,e)},c.isBuffer=function(e){return!(null==e||!e._isBuffer)},c.compare=function(e,t){if(!c.isBuffer(e)||!c.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,i=0,o=Math.min(n,r);i<o;++i)if(e[i]!==t[i]){n=e[i],r=t[i];break}return n<r?-1:r<n?1:0},c.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},c.concat=function(e,t){if(!o(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return c.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=c.allocUnsafe(t),i=0;for(n=0;n<e.length;++n){var a=e[n];if(!c.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(r,i),i+=a.length}return r},c.byteLength=v,c.prototype._isBuffer=!0,c.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},c.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},c.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},c.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?R(this,0,e):p.apply(this,arguments)},c.prototype.equals=function(e){if(!c.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===c.compare(this,e)},c.prototype.inspect=function(){var e="",n=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},c.prototype.compare=function(e,t,n,r,i){if(!c.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),t<0||n>e.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&t>=n)return 0;if(r>=i)return-1;if(t>=n)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(r>>>=0),a=(n>>>=0)-(t>>>=0),s=Math.min(o,a),l=this.slice(r,i),u=e.slice(t,n),f=0;f<s;++f)if(l[f]!==u[f]){o=l[f],a=u[f];break}return o<a?-1:a<o?1:0},c.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},c.prototype.indexOf=function(e,t,n){return m(this,e,t,n,!0)},c.prototype.lastIndexOf=function(e,t,n){return m(this,e,t,n,!1)},c.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-t;if((void 0===n||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return _(this,e,t,n);case"utf8":case"utf-8":return S(this,e,t,n);case"ascii":return E(this,e,t,n);case"latin1":case"binary":return C(this,e,t,n);case"base64":return I(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return T(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},c.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function O(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}function w(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}function A(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=t;o<n;++o)i+=U(e[o]);return i}function k(e,t,n){for(var r=e.slice(t,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function M(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function P(e,t,n,r,i,o){if(!c.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function D(e,t,n,r){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-n,2);i<o;++i)e[n+i]=(t&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function x(e,t,n,r){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-n,4);i<o;++i)e[n+i]=t>>>8*(r?i:3-i)&255}function N(e,t,n,r,i,o){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function L(e,t,n,r,o){return o||N(e,0,n,4),i.write(e,t,n,r,23,4),n+4}function F(e,t,n,r,o){return o||N(e,0,n,8),i.write(e,t,n,r,52,8),n+8}c.prototype.slice=function(e,t){var n,r=this.length;if((e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e),c.TYPED_ARRAY_SUPPORT)(n=this.subarray(e,t)).__proto__=c.prototype;else{var i=t-e;n=new c(i,void 0);for(var o=0;o<i;++o)n[o]=this[o+e]}return n},c.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||M(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r},c.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||M(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},c.prototype.readUInt8=function(e,t){return t||M(e,1,this.length),this[e]},c.prototype.readUInt16LE=function(e,t){return t||M(e,2,this.length),this[e]|this[e+1]<<8},c.prototype.readUInt16BE=function(e,t){return t||M(e,2,this.length),this[e]<<8|this[e+1]},c.prototype.readUInt32LE=function(e,t){return t||M(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},c.prototype.readUInt32BE=function(e,t){return t||M(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},c.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||M(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*t)),r},c.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||M(e,t,this.length);for(var r=t,i=1,o=this[e+--r];r>0&&(i*=256);)o+=this[e+--r]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},c.prototype.readInt8=function(e,t){return t||M(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},c.prototype.readInt16LE=function(e,t){t||M(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},c.prototype.readInt16BE=function(e,t){t||M(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},c.prototype.readInt32LE=function(e,t){return t||M(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},c.prototype.readInt32BE=function(e,t){return t||M(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},c.prototype.readFloatLE=function(e,t){return t||M(e,4,this.length),i.read(this,e,!0,23,4)},c.prototype.readFloatBE=function(e,t){return t||M(e,4,this.length),i.read(this,e,!1,23,4)},c.prototype.readDoubleLE=function(e,t){return t||M(e,8,this.length),i.read(this,e,!0,52,8)},c.prototype.readDoubleBE=function(e,t){return t||M(e,8,this.length),i.read(this,e,!1,52,8)},c.prototype.writeUIntLE=function(e,t,n,r){(e=+e,t|=0,n|=0,r)||P(this,e,t,n,Math.pow(2,8*n)-1,0);var i=1,o=0;for(this[t]=255&e;++o<n&&(i*=256);)this[t+o]=e/i&255;return t+n},c.prototype.writeUIntBE=function(e,t,n,r){(e=+e,t|=0,n|=0,r)||P(this,e,t,n,Math.pow(2,8*n)-1,0);var i=n-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+n},c.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,1,255,0),c.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},c.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):D(this,e,t,!0),t+2},c.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):D(this,e,t,!1),t+2},c.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):x(this,e,t,!0),t+4},c.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):x(this,e,t,!1),t+4},c.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);P(this,e,t,n,i-1,-i)}var o=0,a=1,s=0;for(this[t]=255&e;++o<n&&(a*=256);)e<0&&0===s&&0!==this[t+o-1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+n},c.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);P(this,e,t,n,i-1,-i)}var o=n-1,a=1,s=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===s&&0!==this[t+o+1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+n},c.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,1,127,-128),c.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},c.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):D(this,e,t,!0),t+2},c.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):D(this,e,t,!1),t+2},c.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,4,**********,-2147483648),c.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):x(this,e,t,!0),t+4},c.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):x(this,e,t,!1),t+4},c.prototype.writeFloatLE=function(e,t,n){return L(this,e,t,!0,n)},c.prototype.writeFloatBE=function(e,t,n){return L(this,e,t,!1,n)},c.prototype.writeDoubleLE=function(e,t,n){return F(this,e,t,!0,n)},c.prototype.writeDoubleBE=function(e,t,n){return F(this,e,t,!1,n)},c.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var i,o=r-n;if(this===e&&n<t&&t<r)for(i=o-1;i>=0;--i)e[i+t]=this[i+n];else if(o<1e3||!c.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+o),t);return o},c.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!c.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;var o;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var a=c.isBuffer(e)?e:V(new c(e,r).toString()),s=a.length;for(o=0;o<n-t;++o)this[o+t]=a[o%s]}return this};var j=/[^+\/0-9A-Za-z-_]/g;function U(e){return e<16?"0"+e.toString(16):e.toString(16)}function V(e,t){var n;t=t||1/0;for(var r=e.length,i=null,o=[],a=0;a<r;++a){if((n=e.charCodeAt(a))>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(a+1===r){(t-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function G(e){return r.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(j,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function B(e,t,n,r){for(var i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}}).call(this,n(70))},function(e,t,n){"use strict";t.byteLength=function(e){var t=l(e),n=t[0],r=t[1];return 3*(n+r)/4-r},t.toByteArray=function(e){var t,n,r=l(e),a=r[0],s=r[1],c=new o(function(e,t,n){return 3*(t+n)/4-n}(0,a,s)),u=0,f=s>0?a-4:a;for(n=0;n<f;n+=4)t=i[e.charCodeAt(n)]<<18|i[e.charCodeAt(n+1)]<<12|i[e.charCodeAt(n+2)]<<6|i[e.charCodeAt(n+3)],c[u++]=t>>16&255,c[u++]=t>>8&255,c[u++]=255&t;2===s&&(t=i[e.charCodeAt(n)]<<2|i[e.charCodeAt(n+1)]>>4,c[u++]=255&t);1===s&&(t=i[e.charCodeAt(n)]<<10|i[e.charCodeAt(n+1)]<<4|i[e.charCodeAt(n+2)]>>2,c[u++]=t>>8&255,c[u++]=255&t);return c},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,o=[],a=0,s=n-i;a<s;a+=16383)o.push(u(e,a,a+16383>s?s:a+16383));1===i?(t=e[n-1],o.push(r[t>>2]+r[t<<4&63]+"==")):2===i&&(t=(e[n-2]<<8)+e[n-1],o.push(r[t>>10]+r[t>>4&63]+r[t<<2&63]+"="));return o.join("")};for(var r=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,c=a.length;s<c;++s)r[s]=a[s],i[a.charCodeAt(s)]=s;function l(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function u(e,t,n){for(var i,o,a=[],s=t;s<n;s+=3)i=(e[s]<<16&16711680)+(e[s+1]<<8&65280)+(255&e[s+2]),a.push(r[(o=i)>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return a.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},function(e,t){t.read=function(e,t,n,r,i){var o,a,s=8*i-r-1,c=(1<<s)-1,l=c>>1,u=-7,f=n?i-1:0,d=n?-1:1,h=e[t+f];for(f+=d,o=h&(1<<-u)-1,h>>=-u,u+=s;u>0;o=256*o+e[t+f],f+=d,u-=8);for(a=o&(1<<-u)-1,o>>=-u,u+=r;u>0;a=256*a+e[t+f],f+=d,u-=8);if(0===o)o=1-l;else{if(o===c)return a?NaN:1/0*(h?-1:1);a+=Math.pow(2,r),o-=l}return(h?-1:1)*a*Math.pow(2,o-r)},t.write=function(e,t,n,r,i,o){var a,s,c,l=8*o-i-1,u=(1<<l)-1,f=u>>1,d=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,h=r?0:o-1,v=r?1:-1,p=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=u):(a=Math.floor(Math.log(t)/Math.LN2),t*(c=Math.pow(2,-a))<1&&(a--,c*=2),(t+=a+f>=1?d/c:d*Math.pow(2,1-f))*c>=2&&(a++,c/=2),a+f>=u?(s=0,a=u):a+f>=1?(s=(t*c-1)*Math.pow(2,i),a+=f):(s=t*Math.pow(2,f-1)*Math.pow(2,i),a=0));i>=8;e[n+h]=255&s,h+=v,s/=256,i-=8);for(a=a<<i|s,l+=i;l>0;e[n+h]=255&a,h+=v,a/=256,l-=8);e[n+h-v]|=128*p}},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){"use strict";var r=n(49);e.exports=function(e,t,n){var i=n.config.validateStatus;n.status&&i&&!i(n.status)?t(new r("Request failed with status code "+n.status,[r.ERR_BAD_REQUEST,r.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}},function(e,t,n){"use strict";var r=n(10);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,i,o,a){var s=[];s.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),r.isString(i)&&s.push("path="+i),r.isString(o)&&s.push("domain="+o),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(e,t,n){"use strict";e.exports=function(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}},function(e,t,n){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},function(e,t,n){"use strict";var r=n(10),i=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,o,a={};return e?(r.forEach(e.split("\n"),(function(e){if(o=e.indexOf(":"),t=r.trim(e.substr(0,o)).toLowerCase(),n=r.trim(e.substr(o+1)),t){if(a[t]&&i.indexOf(t)>=0)return;a[t]="set-cookie"===t?(a[t]?a[t]:[]).concat([n]):a[t]?a[t]+", "+n:n}})),a):a}},function(e,t,n){"use strict";var r=n(10);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function i(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=i(window.location.href),function(t){var n=r.isString(t)?i(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},function(e,t,n){"use strict";e.exports=function(e){var t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}},function(e,t){e.exports=null},function(e,t,n){"use strict";var r=n(145).version,i=n(49),o={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){o[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));var a={};o.transitional=function(e,t,n){function o(e,t){return"[Axios v"+r+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return function(n,r,s){if(!1===e)throw new i(o(r," has been removed"+(t?" in "+t:"")),i.ERR_DEPRECATED);return t&&!a[r]&&(a[r]=!0,console.warn(o(r," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,r,s)}},e.exports={assertOptions:function(e,t,n){if("object"!=typeof e)throw new i("options must be an object",i.ERR_BAD_OPTION_VALUE);for(var r=Object.keys(e),o=r.length;o-- >0;){var a=r[o],s=t[a];if(s){var c=e[a],l=void 0===c||s(c,a,e);if(!0!==l)throw new i("option "+a+" must be "+l,i.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new i("Unknown option "+a,i.ERR_BAD_OPTION)}},validators:o}},function(e,t,n){"use strict";var r=n(69);function i(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;this.promise.then((function(e){if(n._listeners){var t,r=n._listeners.length;for(t=0;t<r;t++)n._listeners[t](e);n._listeners=null}})),this.promise.then=function(e){var t,r=new Promise((function(e){n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e){n.reason||(n.reason=new r(e),t(n.reason))}))}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.prototype.subscribe=function(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]},i.prototype.unsubscribe=function(e){if(this._listeners){var t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}},i.source=function(){var e;return{token:new i((function(t){e=t})),cancel:e}},e.exports=i},function(e,t,n){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},function(e,t,n){"use strict";var r=n(10);e.exports=function(e){return r.isObject(e)&&!0===e.isAxiosError}},function(e,t,n){"use strict";n.r(t);var r={};n.r(r),n.d(r,"shimGetUserMedia",(function(){return w})),n.d(r,"shimGetDisplayMedia",(function(){return A})),n.d(r,"shimMediaStream",(function(){return k})),n.d(r,"shimOnTrack",(function(){return M})),n.d(r,"shimGetSendersWithDtmf",(function(){return P})),n.d(r,"shimGetStats",(function(){return D})),n.d(r,"shimSenderReceiverGetStats",(function(){return x})),n.d(r,"shimAddTrackRemoveTrackWithNative",(function(){return N})),n.d(r,"shimAddTrackRemoveTrack",(function(){return L})),n.d(r,"shimPeerConnection",(function(){return F})),n.d(r,"fixNegotiationNeeded",(function(){return j}));var i={};n.r(i),n.d(i,"shimGetUserMedia",(function(){return U})),n.d(i,"shimGetDisplayMedia",(function(){return V})),n.d(i,"shimOnTrack",(function(){return G})),n.d(i,"shimPeerConnection",(function(){return B})),n.d(i,"shimSenderGetStats",(function(){return H})),n.d(i,"shimReceiverGetStats",(function(){return q})),n.d(i,"shimRemoveStream",(function(){return W})),n.d(i,"shimRTCDataChannel",(function(){return Y})),n.d(i,"shimAddTransceiver",(function(){return K})),n.d(i,"shimGetParameters",(function(){return z})),n.d(i,"shimCreateOffer",(function(){return J})),n.d(i,"shimCreateAnswer",(function(){return Q}));var o={};n.r(o),n.d(o,"shimLocalStreamsAPI",(function(){return X})),n.d(o,"shimRemoteStreamsAPI",(function(){return $})),n.d(o,"shimCallbacksAPI",(function(){return Z})),n.d(o,"shimGetUserMedia",(function(){return ee})),n.d(o,"shimConstraints",(function(){return te})),n.d(o,"shimRTCIceServerUrls",(function(){return ne})),n.d(o,"shimTrackEventTransceiver",(function(){return re})),n.d(o,"shimCreateOfferLegacy",(function(){return ie})),n.d(o,"shimAudioContext",(function(){return oe}));var a={};n.r(a),n.d(a,"shimRTCIceCandidate",(function(){return ce})),n.d(a,"shimMaxMessageSize",(function(){return le})),n.d(a,"shimSendThrowTypeError",(function(){return ue})),n.d(a,"shimConnectionState",(function(){return fe})),n.d(a,"removeExtmapAllowMixed",(function(){return de})),n.d(a,"shimAddIceCandidateNullOrEmpty",(function(){return he})),n.d(a,"shimParameterlessSetLocalDescription",(function(){return ve}));var s={PING:1,AUTHEN:2,MESSAGE_TYPE_RATING:14,CALL_START:26,CALL_SDP_CANDIDATE:27,CALL_STOP:28,CALL_SDP_CANDIDATE_FROM_SERVER:29,CALL_UPDATE:150,CALL_UPDATE_FROM_SERVER:151,CALL_STOP_FROM_SERVER:30,CALL_STATE:31,CALL_STATE_FROM_SERVER:32,CALL_START_FROM_SERVER:33,CALL_DTMF:34,CALL_DTMF_FROM_SERVER:35,CALL2_DTMF:145,CALL_INFO:36,CALL_INFO_FROM_SERVER:37,MSG_FROM_OTHER_DEVICE:38,PUSH_DEVICE_TOKEN_REGISTER:39,PUSH_DEVICE_TOKEN_UNREGISTER:40,CHAT_CREATE_CONVERSATION:41,CHAT_MESSAGE:42,CHAT_MESSAGE_FROM_SERVER:45,CHAT_MESSAGE_REPORT:46,CHAT_MESSAGE_REPORT_FROM_SERVER:47,CHAT_CONVERSATION_LOAD:48,CHAT_MESSAGES_LOAD:50,CHAT_CONVERSATION_CLEAR_HISTORY:51,CHAT_DELETE_CONVERSATION:52,CUSTOM_MESSAGE:54,CUSTOM_MESSAGE_FROM_SERVER:55,CHAT_GET_USERS_INFO:56,CHAT_GET_CONVERSATIONS_INFO:57,CHAT_ADD_PARTICIPANT:58,CHAT_ADD_PARTICIPANT_FROM_SERVER:59,CHAT_REMOVE_PARTICIPANT:60,CHAT_REMOVE_PARTICIPANT_FROM_SERVER:61,CHAT_DELETE_MESSAGE:62,CHAT_UPDATE_CONVERSATION:63,CHAT_ROUTE_TO_AGENT:64,CHAT_AGENT_RESPONSE:65,TIMEOUT_ROUTE_TO_AGENT:66,TIMEOUT_ROUTE_TO_QUEUE:67,END_CHAT:68,RATE_CHAT:69,UPDATE_USER_INFO:70,SEND_EMAIL_TRANSCRIPT:71,GET_CHAT_SERVICES:72,VIEW_CHAT:73,JOIN_CHAT_CUSTOMER_CARE:74,CHAT_CONVERSATION_FOR_USERS:75,CHAT_TRANSFER_TO_ANOTHER_AGENT:77,CHAT_UNREAD_CONVERSATION_COUNT:76,CHAT_CONFIRM_TRANSFER_REQUEST:78,CHAT_TRANSFER_REQUEST_FROM_ANOTHER_AGENT:79,GET_MESSAGES_INFO:80,UPDATE_USER_INFO_NOTIFICATION:81,CHAT_USER_BEGIN_TYPING:84,CHAT_USER_END_TYPING:85,CHAT_USER_BEGIN_TYPING_NOTIFICATION:86,CHAT_USER_END_TYPING_NOTIFIACTION:87,CHAT_PIN_MESSAGE:88,CHAT_AGENT_RESOLVE_CONVERSATION:94,CHAT_CONVERSATION_ATTACHMENT:95,CHAT_SET_ROLE:96,CHAT_LOAD_ALL_MESSAGES_FOR_CONVERSATIONS:97,CHAT_CONVERSATION_ENDED_NOTIFICATION:98,CHAT_REQUEST_LOAD:99,HOLD:100,TRANSFER:101,SUBSCRIBE_FROM_SERVER:102,CHANGE_ATTRIBUTE:103,OTHER_DEVICE_AUTHEN:104,CHAT_BLOCK_USER:105,CHAT_JOIN_OA_CONVERSATION:200,VIDEO_ENDPOINT_SDP:108,VIDEO_ENDPOINT_CANDIDATE:109,VIDEO_ENDPOINT_SDP_FROM_SERVER:110,VIDEO_ENDPOINT_CANDIDATE_FROM_SERVER:111,VIDEO_ENDPOINT_MAKE_ROOM:112,VIDEO_ENDPOINT_JOIN_ROOM:115,VIDEO_LEFT_ROOM:116,VIDEO_PUBLISH_TRACK:117,VIDEO_UNPUBLISH_TRACK:118,VIDEO_SUBSCRIBE_TRACK:119,VIDEO_UNSUBSCRIBE_TRACK:120,VIDEO_TRACK_ADDED_FROM_SERVER:121,VIDEO_TRACK_REMOVED_FROM_SERVER:122,VIDEO_ROOM_JOINED_FROM_SERVER:124,VIDEO_ROOM_LEFT_FROM_SERVER:125,VIDEO_ROOM_SEND_MSG:126,VIDEO_ROOM_MSG_FROM_SERVER:127,VIDEO_ROOM_MAKE_CALL:128,VIDEO_ROOM_CALL_INVITE:129,VIDEO_ROOM_CALL_INVITE_CANCEL:130,VIDEO_ROOM_CALL_STATE:131,VIDEO_ROOM_END_CALL:132,VIDEO_ROOM_END_CALL_FROM_SERVER:133,VIDEO_ROOM_CALL_STATE_FROM_SERVER:134,VIDEO_ROOM_TRANSFER_CALL:135,VIDEO_ROOM_HOLD:136,VIDEO_ROOM_ENABLE_DISABLE_AUDIO_VIDEO:138,VIDEO_ROOM_ENABLE_DISABLE_AUDIO_VIDEO_FROM_SERVER:139,VIDEO_ROOM_ENABLE_DISABLE_AUDIO_VIDEO_NOTIFICATION:140,VIDEO_ROOM_RENDER_LAYOUT_INFO:141,VIDEO_ROOM_JOIN_CALL2:142,VIDEO_ROOM_MEDIA_STATE:143,CHECK_EXIST_CALL:205};function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function l(e,t,n){return t&&c(e.prototype,t),n&&c(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var u=l((function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var t=[];return t.size=function(){return this.length},t.isEmpty=function(){return 0===this.length},t.containsKey=function(e){e+="";for(var t=0;t<this.length;t++)if(this[t].key===e)return t;return-1},t.get=function(e){e+="";var t=this.containsKey(e);if(t>-1)return this[t].value},t.put=function(e,t){if(e+="",-1!==this.containsKey(e))return this.get(e);this.push({key:e,value:t})},t.allKeys=function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t].key);return e},t.allValues=function(){return t.map((function(e){return e.value}))},t.allIntKeys=function(){for(var e=[],t=0;t<this.length;t++)e.push(parseInt(this[t].key));return e},t.remove=function(e){e+="";var t=this.containsKey(e);t>-1&&this.splice(t,1)},t.clear=function(){for(var e=this.allKeys(),t=0;t<e.length;t++){var n=e[t];this.remove(n)}},t})),f=1,d=2,h=3,v=5,p=6;let g=!0,m=!0;function y(e,t,n){const r=e.match(t);return r&&r.length>=n&&parseInt(r[n],10)}function _(e,t,n){if(!e.RTCPeerConnection)return;const r=e.RTCPeerConnection.prototype,i=r.addEventListener;r.addEventListener=function(e,r){if(e!==t)return i.apply(this,arguments);const o=e=>{const t=n(e);t&&(r.handleEvent?r.handleEvent(t):r(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(r,o),i.apply(this,[e,o])};const o=r.removeEventListener;r.removeEventListener=function(e,n){if(e!==t||!this._eventMap||!this._eventMap[t])return o.apply(this,arguments);if(!this._eventMap[t].has(n))return o.apply(this,arguments);const r=this._eventMap[t].get(n);return this._eventMap[t].delete(n),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[e,r])},Object.defineProperty(r,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function S(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(g=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function E(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(m=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function C(){if("object"==typeof window){if(g)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function I(e,t){m&&console.warn(e+" is deprecated, please use "+t+" instead.")}function T(e){return"[object Object]"===Object.prototype.toString.call(e)}function b(e){return T(e)?Object.keys(e).reduce((function(t,n){const r=T(e[n]),i=r?b(e[n]):e[n],o=r&&!Object.keys(i).length;return void 0===i||o?t:Object.assign(t,{[n]:i})}),{}):e}function R(e,t,n){const r=n?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;const o=[];return e.forEach(e=>{"track"===e.type&&e.trackIdentifier===t.id&&o.push(e)}),o.forEach(t=>{e.forEach(n=>{n.type===r&&n.trackId===t.id&&function e(t,n,r){n&&!r.has(n.id)&&(r.set(n.id,n),Object.keys(n).forEach(i=>{i.endsWith("Id")?e(t,t.get(n[i]),r):i.endsWith("Ids")&&n[i].forEach(n=>{e(t,t.get(n),r)})}))}(e,n,i)})}),i}const O=C;function w(e,t){const n=e&&e.navigator;if(!n.mediaDevices)return;const r=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach(n=>{if("require"===n||"advanced"===n||"mediaSource"===n)return;const r="object"==typeof e[n]?e[n]:{ideal:e[n]};void 0!==r.exact&&"number"==typeof r.exact&&(r.min=r.max=r.exact);const i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==r.ideal){t.optional=t.optional||[];let e={};"number"==typeof r.ideal?(e[i("min",n)]=r.ideal,t.optional.push(e),e={},e[i("max",n)]=r.ideal,t.optional.push(e)):(e[i("",n)]=r.ideal,t.optional.push(e))}void 0!==r.exact&&"number"!=typeof r.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",n)]=r.exact):["min","max"].forEach(e=>{void 0!==r[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,n)]=r[e])})}),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},i=function(e,i){if(t.version>=61)return i(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=r(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});const a=t.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&(!n.mediaDevices.getSupportedConstraints||!n.mediaDevices.getSupportedConstraints().facingMode||a)){let t;if(delete e.video.facingMode,"environment"===o.exact||"environment"===o.ideal?t=["back","rear"]:"user"!==o.exact&&"user"!==o.ideal||(t=["front"]),t)return n.mediaDevices.enumerateDevices().then(n=>{let a=(n=n.filter(e=>"videoinput"===e.kind)).find(e=>t.some(t=>e.label.toLowerCase().includes(t)));return!a&&n.length&&t.includes("back")&&(a=n[n.length-1]),a&&(e.video.deviceId=o.exact?{exact:a.deviceId}:{ideal:a.deviceId}),e.video=r(e.video),O("chrome: "+JSON.stringify(e)),i(e)})}e.video=r(e.video)}return O("chrome: "+JSON.stringify(e)),i(e)},o=function(e){return t.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(n.getUserMedia=function(e,t,r){i(e,e=>{n.webkitGetUserMedia(e,t,e=>{r&&r(o(e))})})}.bind(n),n.mediaDevices.getUserMedia){const e=n.mediaDevices.getUserMedia.bind(n.mediaDevices);n.mediaDevices.getUserMedia=function(t){return i(t,t=>e(t).then(e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach(e=>{e.stop()}),new DOMException("","NotFoundError");return e},e=>Promise.reject(o(e))))}}}function A(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(n){return t(n).then(t=>{const r=n.video&&n.video.width,i=n.video&&n.video.height,o=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:o||3}},r&&(n.video.mandatory.maxWidth=r),i&&(n.video.mandatory.maxHeight=i),e.navigator.mediaDevices.getUserMedia(n)})}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}function k(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function M(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",n=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===n.track.id):{track:n.track};const i=new Event("track");i.track=n.track,i.receiver=r,i.transceiver={receiver:r},i.streams=[t.stream],this.dispatchEvent(i)}),t.stream.getTracks().forEach(n=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===n.id):{track:n};const i=new Event("track");i.track=n,i.receiver=r,i.transceiver={receiver:r},i.streams=[t.stream],this.dispatchEvent(i)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else _(e,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e))}function P(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){let i=n.apply(this,arguments);return i||(i=t(this,e),this._senders.push(i)),i};const r=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){r.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach(e=>{this._senders.push(t(this,e))})};const r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach(e=>{const t=this._senders.find(t=>t.track===e);t&&this._senders.splice(this._senders.indexOf(t),1)})}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function D(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,n,r]=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const i=function(e){const t={};return e.result().forEach(e=>{const n={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach(t=>{n[t]=e.stat(t)}),t[n.id]=n}),t},o=function(e){return new Map(Object.keys(e).map(t=>[t,e[t]]))};if(arguments.length>=2){const r=function(e){n(o(i(e)))};return t.apply(this,[r,e])}return new Promise((e,n)=>{t.apply(this,[function(t){e(o(i(t)))},n])}).then(n,r)}}function x(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>R(t,e.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),_(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>R(t,e.track,!1))}}if(!("getStats"in e.RTCRtpSender.prototype)||!("getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,n,r;return this.getSenders().forEach(n=>{n.track===e&&(t?r=!0:t=n)}),this.getReceivers().forEach(t=>(t.track===e&&(n?r=!0:n=t),t.track===e)),r||t&&n?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():n?n.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function N(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(e=>this._shimmedLocalStreams[e][0])};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){if(!n)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const r=t.apply(this,arguments);return this._shimmedLocalStreams[n.id]?-1===this._shimmedLocalStreams[n.id].indexOf(r)&&this._shimmedLocalStreams[n.id].push(r):this._shimmedLocalStreams[n.id]=[n,r],r};const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")});const t=this.getSenders();n.apply(this,arguments);const r=this.getSenders().filter(e=>-1===t.indexOf(e));this._shimmedLocalStreams[e.id]=[e].concat(r)};const r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],r.apply(this,arguments)};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach(t=>{const n=this._shimmedLocalStreams[t].indexOf(e);-1!==n&&this._shimmedLocalStreams[t].splice(n,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]}),i.apply(this,arguments)}}function L(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return N(e);const n=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=n.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map(e=>this._reverseStreams[e.id])};const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[t.id]){const n=new e.MediaStream(t.getTracks());this._streams[t.id]=n,this._reverseStreams[n.id]=t,t=n}r.apply(this,[t])};const i=e.RTCPeerConnection.prototype.removeStream;function o(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{const r=e._reverseStreams[t],i=e._streams[r.id];n=n.replace(new RegExp(i.id,"g"),r.id)}),new RTCSessionDescription({type:t.type,sdp:n})}function a(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{const r=e._reverseStreams[t],i=e._streams[r.id];n=n.replace(new RegExp(r.id,"g"),i.id)}),new RTCSessionDescription({type:t.type,sdp:n})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,n){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const r=[].slice.call(arguments,1);if(1!==r.length||!r[0].getTracks().find(e=>e===t))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");const i=this.getSenders().find(e=>e.track===t);if(i)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const o=this._streams[n.id];if(o)o.addTrack(t),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const r=new e.MediaStream([t]);this._streams[n.id]=r,this._reverseStreams[r.id]=n,this.addStream(r)}return this.getSenders().find(e=>e.track===t)},["createOffer","createAnswer"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?n.apply(this,[t=>{const n=o(this,t);e[0].apply(null,[n])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):n.apply(this,arguments).then(e=>o(this,e))}};e.RTCPeerConnection.prototype[t]=r[t]}));const s=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=a(this,arguments[0]),s.apply(this,arguments)):s.apply(this,arguments)};const c=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=c.get.apply(this);return""===e.type?e:o(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(e._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach(n=>{this._streams[n].getTracks().find(t=>e.track===t)&&(t=this._streams[n])}),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function F(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=r[t]}))}function j(e,t){_(e,"negotiationneeded",e=>{const n=e.target;if(!(t.version<72||n.getConfiguration&&"plan-b"===n.getConfiguration().sdpSemantics)||"stable"===n.signalingState)return e})}function U(e,t){const n=e&&e.navigator,r=e&&e.MediaStreamTrack;if(n.getUserMedia=function(e,t,r){I("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),n.mediaDevices.getUserMedia(e).then(t,r)},!(t.version>55&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){const e=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])},t=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(n){return"object"==typeof n&&"object"==typeof n.audio&&(n=JSON.parse(JSON.stringify(n)),e(n.audio,"autoGainControl","mozAutoGainControl"),e(n.audio,"noiseSuppression","mozNoiseSuppression")),t(n)},r&&r.prototype.getSettings){const t=r.prototype.getSettings;r.prototype.getSettings=function(){const n=t.apply(this,arguments);return e(n,"mozAutoGainControl","autoGainControl"),e(n,"mozNoiseSuppression","noiseSuppression"),n}}if(r&&r.prototype.applyConstraints){const t=r.prototype.applyConstraints;r.prototype.applyConstraints=function(n){return"audio"===this.kind&&"object"==typeof n&&(n=JSON.parse(JSON.stringify(n)),e(n,"autoGainControl","mozAutoGainControl"),e(n,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[n])}}}}function V(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(n){if(!n||!n.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===n.video?n.video={mediaSource:t}:n.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(n)})}function G(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function B(e,t){if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=r[t]}));const n={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},r=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,i,o]=arguments;return r.apply(this,[e||null]).then(e=>{if(t.version<53&&!i)try{e.forEach(e=>{e.type=n[e.type]||e.type})}catch(t){if("TypeError"!==t.name)throw t;e.forEach((t,r)=>{e.set(r,Object.assign({},t,{type:n[t.type]||t.type}))})}return e}).then(i,o)}}function H(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function q(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),_(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function W(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function(e){I("removeStream","removeTrack"),this.getSenders().forEach(t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)})})}function Y(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function K(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];const e=arguments[1],n=e&&"sendEncodings"in e;n&&e.sendEncodings.forEach(e=>{if("rid"in e){if(!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});const r=t.apply(this,arguments);if(n){const{sender:t}=r,n=t.getParameters();(!("encodings"in n)||1===n.encodings.length&&0===Object.keys(n.encodings[0]).length)&&(n.encodings=e.sendEncodings,t.sendEncodings=e.sendEncodings,this.setParametersPromises.push(t.setParameters(n).then(()=>{delete t.sendEncodings}).catch(()=>{delete t.sendEncodings})))}return r})}function z(e){if("object"!=typeof e||!e.RTCRtpSender)return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function J(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}function Q(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}function X(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach(n=>t.call(this,n,e)),e.getVideoTracks().forEach(n=>t.call(this,n,e))},e.RTCPeerConnection.prototype.addTrack=function(e,...n){return n&&n.forEach(e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]}),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const n=e.getTracks();this.getSenders().forEach(e=>{n.includes(e.track)&&this.removeTrack(e)})})}}function $(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach(e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)})})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach(t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const n=new Event("addstream");n.stream=t,e.dispatchEvent(n)})}),t.apply(e,arguments)}}}function Z(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,n=t.createOffer,r=t.createAnswer,i=t.setLocalDescription,o=t.setRemoteDescription,a=t.addIceCandidate;t.createOffer=function(e,t){const r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){const n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i};let s=function(e,t,n){const r=i.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r};t.setLocalDescription=s,s=function(e,t,n){const r=o.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.setRemoteDescription=s,s=function(e,t,n){const r=a.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.addIceCandidate=s}function ee(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,n=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>n(te(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,n,r){t.mediaDevices.getUserMedia(e).then(n,r)}.bind(t))}function te(e){return e&&void 0!==e.video?Object.assign({},e,{video:b(e.video)}):e}function ne(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,n){if(e&&e.iceServers){const t=[];for(let n=0;n<e.iceServers.length;n++){let r=e.iceServers[n];!r.hasOwnProperty("urls")&&r.hasOwnProperty("url")?(I("RTCIceServer.url","RTCIceServer.urls"),r=JSON.parse(JSON.stringify(r)),r.urls=r.url,delete r.url,t.push(r)):t.push(e.iceServers[n])}e.iceServers=t}return new t(e,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function re(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function ie(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find(e=>"audio"===e.receiver.track.kind);!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio",{direction:"recvonly"}),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const n=this.getTransceivers().find(e=>"video"===e.receiver.track.kind);!1===e.offerToReceiveVideo&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==e.offerToReceiveVideo||n||this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function oe(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var ae=n(44),se=n.n(ae);function ce(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length){const n=new t(e),r=se.a.parseCandidate(e.candidate),i=Object.assign(n,r);return i.toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,_(e,"icecandidate",t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t))}function le(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const n=function(e){if(!e||!e.sdp)return!1;const t=se.a.splitSections(e.sdp);return t.shift(),t.some(e=>{const t=se.a.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")})},r=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const n=parseInt(t[1],10);return n!=n?-1:n},i=function(e){let n=65536;return"firefox"===t.browser&&(n=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),n},o=function(e,n){let r=65536;"firefox"===t.browser&&57===t.version&&(r=65535);const i=se.a.matchPrefix(e.sdp,"a=max-message-size:");return i.length>0?r=parseInt(i[0].substr(19),10):"firefox"===t.browser&&-1!==n&&(r=2147483637),r},a=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(n(arguments[0])){const e=r(arguments[0]),t=i(e),n=o(arguments[0],e);let a;a=0===t&&0===n?Number.POSITIVE_INFINITY:0===t||0===n?Math.max(t,n):Math.min(t,n);const s={};Object.defineProperty(s,"maxMessageSize",{get:()=>a}),this._sctp=s}return a.apply(this,arguments)}}function ue(e){if(!e.RTCPeerConnection||!("createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const n=e.send;e.send=function(){const r=arguments[0],i=r.length||r.size||r.byteLength;if("open"===e.readyState&&t.sctp&&i>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return n.apply(e,arguments)}}const n=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=n.apply(this,arguments);return t(e,this),e},_(e,"datachannel",e=>(t(e.channel,e.target),e))}function fe(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(e=>{const n=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const n=new Event("connectionstatechange",e);t.dispatchEvent(n)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}})}function de(e,t){if(!e.RTCPeerConnection)return;if("chrome"===t.browser&&t.version>=71)return;if("safari"===t.browser&&t.version>=605)return;const n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){const n=t.sdp.split("\n").filter(e=>"a=extmap-allow-mixed"!==e.trim()).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:n}):t.sdp=n}return n.apply(this,arguments)}}function he(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const n=e.RTCPeerConnection.prototype.addIceCandidate;n&&0!==n.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function ve(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const n=e.RTCPeerConnection.prototype.setLocalDescription;n&&0!==n.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){let e=arguments[0]||{};if("object"!=typeof e||e.type&&e.sdp)return n.apply(this,arguments);if(e={type:e.type,sdp:e.sdp},!e.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":e.type="offer";break;default:e.type="answer"}if(e.sdp||"offer"!==e.type&&"answer"!==e.type)return n.apply(this,[e]);const t="offer"===e.type?this.createOffer:this.createAnswer;return t.apply(this).then(e=>n.apply(this,[e]))})}!function({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const n=C,s=function(e){const t={browser:null,version:null};if(void 0===e||!e.navigator)return t.browser="Not a browser.",t;const{navigator:n}=e;if(n.mozGetUserMedia)t.browser="firefox",t.version=y(n.userAgent,/Firefox\/(\d+)\./,1);else if(n.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)t.browser="chrome",t.version=y(n.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!e.RTCPeerConnection||!n.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=y(n.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return t}(e),c={browserDetails:s,commonShim:a,extractVersion:y,disableLog:S,disableWarnings:E,sdp:ae};switch(s.browser){case"chrome":if(!r||!F||!t.shimChrome)return n("Chrome shim is not included in this adapter release."),c;if(null===s.version)return n("Chrome shim can not determine version, not shimming."),c;n("adapter.js shimming chrome."),c.browserShim=r,he(e,s),ve(e),w(e,s),k(e),F(e,s),M(e),L(e,s),P(e),D(e),x(e),j(e,s),ce(e),fe(e),le(e,s),ue(e),de(e,s);break;case"firefox":if(!i||!B||!t.shimFirefox)return n("Firefox shim is not included in this adapter release."),c;n("adapter.js shimming firefox."),c.browserShim=i,he(e,s),ve(e),U(e,s),B(e,s),G(e),W(e),H(e),q(e),Y(e),K(e),z(e),J(e),Q(e),ce(e),fe(e),le(e,s),ue(e);break;case"safari":if(!o||!t.shimSafari)return n("Safari shim is not included in this adapter release."),c;n("adapter.js shimming safari."),c.browserShim=o,he(e,s),ve(e),ne(e),ie(e),Z(e),X(e),$(e),re(e),ee(e),oe(e),ce(e),le(e,s),ue(e),de(e,s);break;default:n("Unsupported browser!")}}({window:"undefined"==typeof window?void 0:window});function pe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var ge=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,r=[{key:"isWebRTCSupported",value:function(){return!(!navigator||"undefined"==typeof navigator||!navigator.mediaDevices||void 0===navigator.mediaDevices||!navigator.mediaDevices.getUserMedia||void 0===navigator.mediaDevices.getUserMedia)}},{key:"getIceUfragFromSDP",value:function(e){if(!e)return null;var t=/a=ice-ufrag:(.*)(\r\n|\n)/g.exec(e);return null!=t&&t.length>=2?t[1]:null}},{key:"genFakeCandidate",value:function(t,n,r,i){var o=e.getIceUfragFromSDP(t);if(null!=o&&o.length>0){if("srflx"==i)return{sdpMLineIndex:n,candidate:"candidate:1289619645 1 udp 1686052607 115.78.95.208 47279 typ srflx raddr 10.151.102.246 rport 53870 generation 1 ufrag "+o+" network-id 1",sdpMid:r};if("relay"==i)return{sdpMLineIndex:n,candidate:"candidate:3052053315 1 udp 41688831 ************* 32028 typ relay raddr ************** rport 31251 generation 0 ufrag "+o+" network-id 2 network-cost 10",sdpMid:r}}return null}},{key:"getActiveClientId",value:function(){return localStorage.getItem("active_client_id")}},{key:"version",value:function(){return{version:"2.8.2",build:"1"}}},{key:"getIOSVersion",value:function(){var e=navigator.userAgent;if(/iPad|iPhone|iPod/.test(e)||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1){var t=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);return t||(t=navigator.appVersion.match(/Version\/(\d+).(\d+).?(\d+)?/)),[parseInt(t[1],10),parseInt(t[2],10),parseInt(t[3]||0,10)]}return!1}},{key:"removeCodec",value:function(e,t){return function e(n){var r=new RegExp("(a=rtpmap:(\\d*) "+t+"/90000\\r\\n)"),i=n.match(r);if(null==i||i.length<=2)return n;var o=i[2],a=n.replace(r,""),s=new RegExp("(a=rtcp-fb:"+o+".*\r\n)","g");a=a.replace(s,"");var c=new RegExp("(a=fmtp:"+o+".*\r\n)","g");a=a.replace(c,"");var l=new RegExp("(a=fmtp:(\\d*) apt="+o+"\\r\\n)"),u=a.match(l),f="";if(null!=u&&u.length>=3){f=u[2],a=a.replace(l,"");var d=new RegExp("(a=rtpmap:"+f+".*\r\n)","g");a=a.replace(d,"")}var h=/(m=video.*\r\n)/,v=a.match(h);if(null!=v){for(var p=v[0].substring(0,v[0].length-2).split(" "),g=p[0],m=1;m<p.length;m++)p[m]!=o&&p[m]!=f&&(g+=" "+p[m]);g+="\r\n",a=a.replace(h,g)}return e(a)}(e)}}],(n=null)&&pe(t.prototype,n),r&&pe(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function me(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ye(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var _e=function(){function e(t,n,r,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;me(this,e),this.client=t,this.fromNumber=n,this.toNumber=r,this.custom="",this.customDataFromYourServer="",this.fromAlias=n,this.toAlias=r,this.fromInternal=!0,this.answeredOnAnotherDevice=!1,this.isVideoCall=i,this.isIncomingCall=!1,this.isAnswered=!1,this.isOnHold=!1,this.ended=!1,this.callId="",this._iceServers=null,this.toType="",this.muted=!1,this.localVideoEnabled=i,this._onMethods=new u,this.client._stringeeCalls.push(this),this.videoResolution=null,o&&(console.log("create call object with encryptPhone:",o),this.encryptPhone=o),this._pc=null,this._localStream=null,this._remoteStream=null,this._localSdp=null,this._remoteSdps=new u,this._mapListCandidates=new u,this._answeredDeviceId=null,this._setRemoteSdpOk=!1,this._mediaConnected=!1,this.microphones=[],this.speakers=[],this.cameras=[],this.audioDeviceId=null,this.videoDeviceId=null,this._checkGenCandidateTimer=null,this._sentSrflxCandidate=!1,this._callCheckExistInterval=null,this.reasonEndcall=""}var t,n,r;return t=e,(n=[{key:"makeCall",value:function(e){if(this.isIncomingCall)console.log("could not make call, this is a incoming call");else{var t=this,n={fromNumber:this.fromNumber,toNumber:this.toNumber,video:this.isVideoCall,custom:t.custom};t.encryptPhone&&(console.log("passing custom data(encrypted phone number)",t.encryptPhone),n.encryptPhone=t.encryptPhone),this.client._sendMessage(s.CALL_START,n,(function(n){var r=n.r,i=n.iceServers;delete n.iceServers,0===r?(t.callId=n.callId,t._iceServers=i,t.toType=n.toType,t.customDataFromYourServer=n.customDataFromYourServer,t._initPeerConnection(!0,e,n),t._createCallCheckExistInterval()):(t.ended=!0,e.call(t,n))}))}}},{key:"answer",value:function(e){var t=this;if(this.isIncomingCall)if(this.isAnswered)console.log("Error: call has been answered");else if(this.answeredOnAnotherDevice)console.log("Error: call has been answered on other device");else{this.isAnswered=!0;var n={callId:this.callId,code:200};this.client._sendMessage(s.CALL_STATE,n,(function(n){e&&e.call(t,{r:n.r})})),t.fromInternal||t._onRemoteSDP(),t._checkAndAddCandidateFromQueue(),t._createCallCheckExistInterval()}else console.log("Error: could not answer call, this is a outgoing call")}},{key:"reject",value:function(e){var t=this;if(this.isIncomingCall){t.ended=!0;var n={callId:this.callId,code:486,reasonEndcall:this.reasonEndcall};this.client._sendMessage(s.CALL_STATE,n,(function(n){e&&e.call(t,{r:n.r})})),this._freeResource(),this.onRemove()}else console.log("could not reject call, this is a outgoing call")}},{key:"onRemove",value:function(){var e=this.client._stringeeCalls.indexOf(this);e>-1&&this.client._stringeeCalls.splice(e,1),this._clearCallCheckExistInterval()}},{key:"ringing",value:function(e){var t=this;if(this.isIncomingCall){var n={callId:this.callId,code:180};this.client._sendMessage(s.CALL_STATE,n,(function(n){e&&e.call(t,{r:n.r})}))}else console.log("could not send ringing signal, this is a outgoing call")}},{key:"hangup",value:function(e){var t=this;if(t.ended=!0,t._pc||!e){var n={callId:this.callId,reasonEndcall:this.reasonEndcall};this.client._sendMessage(s.CALL_STOP,n,(function(n){0===n.r&&(t._freeResource(),t.onRemove()),e&&e.call(t,{r:n.r})}))}else e.call(t,{r:-1})}},{key:"sendInfo",value:function(e,t){var n=this,r={callId:this.callId,info:e};this.client._sendMessage(s.CALL_INFO,r,(function(e){t&&t.call(n,{r:e.r})}))}},{key:"sendDtmf",value:function(e,t){var n=this,r={callId:this.callId,digits:e};this.client._sendMessage(s.CALL_DTMF,r,(function(e){t&&t.call(n,{r:e.r})}))}},{key:"sendHold",value:function(e,t){var n=this,r={callId:this.callId,hold:!0,musicOnHold:e};this.client._sendMessage(s.HOLD,r,(function(e){return t&&t.call(n,{r:e.r}),0===e.r&&(n.isOnHold=!0,!0)}))}},{key:"sendUnHold",value:function(e){var t=this,n={callId:this.callId,hold:!1};this.client._sendMessage(s.HOLD,n,(function(n){return e&&e.call(t,{r:n.r}),0===n.r&&(t.isOnHold=!1,!0)}))}},{key:"sendTransfer",value:function(e,t){var n=this,r={callId:this.callId,to:{number:e,type:"internal",alias:e}};this.client._sendMessage(s.TRANSFER,r,(function(e){t&&t.call(n,{r:e.r})}))}},{key:"_initPeerConnection",value:function(e,t,n){var r=this,i={audio:!0,video:r._buildVideoConstraints(!1)};navigator.mediaDevices.getUserMedia(i).then((function(i){t&&t.call(r,n);try{var o={iceServers:r._iceServers},a=new RTCPeerConnection(o);a.onicecandidate=function(e){r._onicecandidate(e)},a.oniceconnectionstatechange=function(e){"connected"===a.iceConnectionState?(r._mediaConnected=!0,r._callOnEvent("mediastate",{reason:"Connected",code:1})):"disconnected"===a.iceConnectionState&&r._callOnEvent("mediastate",{reason:"Disconnected",code:2})},a.ontrack=function(e){r._ontrack(e)},i.getTracks().forEach((function(e){a.addTrack(e,i)})),i.onremovetrack=function(e){console.log("=========localStream1.onremovetrack======",e)},r._pc=a,r._localStream=i,r._callOnEvent("addlocalstream",r._localStream),e?a.createOffer((function(e){r._onCreateLocalSdpSuccess(e)}),r._onCreateLocalSdpError):r._remoteSdps.size()>0&&r._onRemoteSDP(),r.ended&&(r._freeResource(),r.onRemove())}catch(e){console.log(e),r._freeResource(),r.onRemove();var c={callId:r.callId,reasonEndcall:"CREATE_PEER_CONNECTION_ERROR"};r.client._sendMessage(s.CALL_STOP,c,(function(e){})),r._callOnEvent("error",{reason:"CREATE_PEER_CONNECTION_ERROR",code:1001,moreInfo:e})}return navigator.mediaDevices.enumerateDevices()})).then((function(e){var t=1;r.microphones=[],r.speakers=[],r.cameras=[];for(var n=0;n<e.length;++n){var i=e[n];if(i.deviceId&&0!==i.deviceId.length)if("audioinput"===i.kind){var o=i.label||"Microphone ".concat(t++);r.microphones.push({label:o,deviceId:i.deviceId})}else"audiooutput"===i.kind?(o=i.label||"Speaker ".concat(t++),r.speakers.push({label:o,deviceId:i.deviceId})):"videoinput"===i.kind&&(o=i.label||"Camera ".concat(t++),r.cameras.push({label:o,deviceId:i.deviceId}))}})).catch((function(e){t&&(n.r=1e3,n.message="GET_USER_MEDIA_ERROR",n.moreInfo=e,t.call(r,n)),r._getUserMediaError(e)}))}},{key:"hold",value:function(){var e=this;return e._pc?e.isAnswered?e.isOnHold?(console.log("Call is on hold"),!1):(e._pc.createOffer((function(t){var n=t.sdp.replace("a=sendrecv","a=sendonly"),r={type:t.type,sdp:n},i=new RTCSessionDescription(r);e._onCreateLocalSdpSuccess(i)}),e._onCreateLocalSdpError),e.isOnHold=!0,!0):(console.log("Call not answered"),!1):(console.log("RTCPeerConnection not created"),!1)}},{key:"unhold",value:function(){var e=this;return e._pc?e.isAnswered?e.isOnHold?(e._pc.createOffer((function(t){var n=t.sdp.replace("a=sendonly","a=sendrecv");n=n.replace("a=inactive","a=sendrecv");var r={type:t.type,sdp:n},i=new RTCSessionDescription(r);e._onCreateLocalSdpSuccess(i)}),e._onCreateLocalSdpError),e.isOnHold=!1,!0):(console.log("Call is not on hold"),!1):(console.log("Call not answered"),!1):(console.log("RTCPeerConnection not created"),!1)}},{key:"_onRemoteSDP",value:function(){var e,t=this,n=!1;0!==t._remoteSdps.size()&&t._pc&&(e=t._remoteSdps.get(t._remoteSdps.allKeys()[0]),null!==t._answeredDeviceId&&void 0!==t._answeredDeviceId||(t._answeredDeviceId=""),"offer"===e.type&&t.fromInternal&&(n=!0),!t.fromInternal&&t.isAnswered&&(n=!0),"answer"===e.type&&t.isAnswered&&(n=!0,e=t._remoteSdps.get(t._answeredDeviceId)),t.isIncomingCall||"external"!==t.toType||(n=!0),n&&e&&(t._pc.setRemoteDescription(e,(function(){t._setRemoteSdpOk=!0,t._checkAndAddCandidateFromQueue(),"offer"===e.type&&t._pc.createAnswer().then((function(e){t._onCreateLocalSdpSuccess(e)}),t._onCreateLocalSdpError)}),(function(e){console.log("setRemoteDescription error",e)})),t._remoteSdps.clear()))}},{key:"_getUserMediaError",value:function(e){this.isIncomingCall?this._callOnEvent("error",{reason:"GET_USER_MEDIA_ERROR",code:1e3,moreInfo:e}):this.client._sendMessage(s.CALL_STOP,{callId:this.callId,reasonEndcall:"GET_USER_MEDIA_ERROR"},(function(e){}))}},{key:"upgradeToVideoCall",value:function(){this.isVideoCall||this._renegotiateForChangeDevice()}},{key:"switchCamera",value:function(){return!(!this.isVideoCall||this.cameras.length<2||(this.videoDeviceId?this.videoDeviceId=null:this.videoDeviceId=this.cameras[1].deviceId,this._renegotiateForChangeDevice(),0))}},{key:"_renegotiateForChangeDevice",value:function(){var e=this,t={audio:!0,video:e._buildVideoConstraints(!0)};navigator.mediaDevices.getUserMedia(t).then((function(t){e._localStream.getTracks().forEach((function(e){e.stop()})),t.getTracks().forEach((function(n){e._pc.addTrack(n,t)})),e._localStream=t,e._callOnEvent("addlocalstream",e._localStream),e._pc.createOffer((function(t){e._onCreateLocalSdpSuccess(t)}),e._onCreateLocalSdpError),e.isVideoCall=!0,e.localVideoEnabled=!0})).catch((function(t){e._getUserMediaError(t)}))}},{key:"_buildVideoConstraints",value:function(e){if(!e&&!this.isVideoCall)return!1;var t={deviceId:this.videoDeviceId?{exact:this.videoDeviceId}:void 0};return e?this.videoResolution?this.videoDeviceId?{width:{exact:this.videoResolution.width},height:{exact:this.videoResolution.height},deviceId:{exact:this.videoDeviceId}}:{width:{exact:this.videoResolution.width},height:{exact:this.videoResolution.height}}:t:this.isVideoCall&&this.videoResolution?this.videoDeviceId?{width:{exact:this.videoResolution.width},height:{exact:this.videoResolution.height},deviceId:{exact:this.videoDeviceId}}:{width:{exact:this.videoResolution.width},height:{exact:this.videoResolution.height}}:t}},{key:"_checkAndAddCandidateFromQueue",value:function(){if(this.isAnswered&&this._pc&&this._setRemoteSdpOk){this._answeredDeviceId||(this._answeredDeviceId=this._mapListCandidates.allKeys()[0]);var e=this._mapListCandidates.get(""+this._answeredDeviceId);if(e)for(;;){var t=e.pop();if(!t)break;this._pc.addIceCandidate(t)}}}},{key:"_onicecandidate",value:function(e){if(e.candidate){var t=e.candidate,n={sdpMLineIndex:t.sdpMLineIndex,candidate:t.candidate,sdpMid:t.sdpMid};(n.candidate.indexOf("srflx")>0||n.candidate.indexOf("relay")>0)&&this._checkGenCandidateTimer&&clearTimeout(this._checkGenCandidateTimer),this._sendCallSdpCandidate(this.callId,"candidate",n)}}},{key:"_checkGenCandidateTimeout",value:function(e){if(!e._sentSrflxCandidate){var t=!1;if(e.fromInternal&&"external"==e.toType&&(t=!0),!e.fromInternal&&e.isIncomingCall&&(t=!0),t){e._sentSrflxCandidate=!0,console.log("++++++++++++++++++ _checkGenCandidateTimeout run..., send FAKE CANDIDATE");var n=ge.genFakeCandidate(e._localSdp.sdp,0,"0","srflx");n&&e._sendCallSdpCandidate(e.callId,"candidate",n),(n=ge.genFakeCandidate(e._localSdp.sdp,0,"0","relay"))&&e._sendCallSdpCandidate(e.callId,"candidate",n)}}}},{key:"_ontrack",value:function(e){this._remoteStream=e.streams[0],this._callOnEvent("addremotestream",this._remoteStream)}},{key:"_onCreateLocalSdpSuccess",value:function(e){var t=this;this._localSdp=e,this._checkGenCandidateTimer||(this._checkGenCandidateTimer=setTimeout(t._checkGenCandidateTimeout,1200,t)),this._pc.setLocalDescription(e,(function(){t._sendCallSdpCandidate(t.callId,"sdp",e)}),(function(e){console.log("+++++++++ setLocalDescription error",e)}))}},{key:"_onCreateLocalSdpError",value:function(e){console.log(e)}},{key:"_sendCallSdpCandidate",value:function(e,t,n){var r={callId:e,type:t,data:n};this.client._sendMessage(s.CALL_SDP_CANDIDATE,r,(function(e){}))}},{key:"_freeResource",value:function(){if(this._pc){var e=this;this._pc.close(),this._pc=null,this._localStream&&this._localStream.getTracks().forEach((function(t){t.stop(),e._localStream.removeTrack(t)})),this._localStream=null,this._remoteStream=null,this._checkGenCandidateTimer&&clearTimeout(this._checkGenCandidateTimer)}}},{key:"mute",value:function(e){var t=this;t._localStream.getAudioTracks().forEach((function(n){e?(n.enabled=!1,t.muted=!0):(n.enabled=!0,t.muted=!1)}))}},{key:"enableLocalVideo",value:function(e){var t=this,n=!1;return t._localStream.getVideoTracks().forEach((function(r){n=!0,r.enabled=e,t.localVideoEnabled=e})),n}},{key:"on",value:function(e,t){this._onMethods.put(e,t)}},{key:"_callOnEvent",value:function(e,t){var n=this._onMethods.get(e);n?t?n.call(this,t):n.call(this):console.log("Please implement StringeeCall event: "+e)}},{key:"restartIce",value:function(){var e=this;if(!e._pc)return console.log("RTCPeerConnection not created"),!1;console.log("restartIce+++++");var t={offerToReceiveAudio:1,offerToReceiveVideo:1,iceRestart:!0};return e._setLocalSdpOk||!e.isIncomingCall?(console.log("=====create offer==="),e._pc.createOffer((function(t){e._onCreateLocalSdpSuccess(t)}),e._onCreateLocalSdpError,t)):(console.log("=====create Answer==="),e._pc.createAnswer().then((function(t){e._onCreateLocalSdpSuccess(t)}),e._onCreateLocalSdpError,t)),!0}},{key:"_createCallCheckExistInterval",value:function(){this._clearCallCheckExistInterval();var e=this.client,t=this,n={callId:t.callId};this._callCheckExistInterval=setInterval((function(){e._sendMessage(s.CHECK_EXIST_CALL,n,(function(n){if(0==n.r&&!1===n.exist){console.log("== checkExistCall == ",n,"call not exist => stop call");var r=e.findCallByCallId(n.callId);r&&r.hangup(),t._clearCallCheckExistInterval()}else console.log("== checkExistCall == ",n,"call OK => do nothing")}))}),18e4)}},{key:"_clearCallCheckExistInterval",value:function(){console.log("== clearCheckExistCall =="),this._callCheckExistInterval&&clearInterval(this._callCheckExistInterval)}}])&&ye(t.prototype,n),r&&ye(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Se(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Ee(e,t,n){return t&&Se(e.prototype,t),n&&Se(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var Ce=Ee((function e(t,n,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.code=t,this.msg=n,this.jsError=r}));function Ie(e){return(Ie="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Te(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Te=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",a=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var i=t&&t.prototype instanceof f?t:f,o=Object.create(i.prototype),a=new I(r||[]);return o._invoke=function(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return b()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===u)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===u)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}(e,n,a),o}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var u={};function f(){}function d(){}function h(){}var v={};s(v,i,(function(){return this}));var p=Object.getPrototypeOf,g=p&&p(p(T([])));g&&g!==t&&n.call(g,i)&&(v=g);var m=h.prototype=f.prototype=Object.create(v);function y(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var r;this._invoke=function(i,o){function a(){return new t((function(r,a){!function r(i,o,a,s){var c=l(e[i],e,o);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==Ie(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(f).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(i,o,r,a)}))}return r=r?r.then(a,a):a()}}function S(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var r=l(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function T(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:b}}function b(){return{value:void 0,done:!0}}return d.prototype=h,s(m,"constructor",h),s(h,"constructor",d),d.displayName=s(h,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,s(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e},e.awrap=function(e){return{__await:e}},y(_.prototype),s(_.prototype,o,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new _(c(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},y(m),s(m,a,"Generator"),s(m,i,(function(){return this})),s(m,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=T,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,u):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;C(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:T(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function be(e,t,n,r,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,i)}function Re(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){be(o,r,i,a,s,"next",e)}function s(e){be(o,r,i,a,s,"throw",e)}a(void 0)}))}}function Oe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}n(99),n(100);var we=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.localId=He.genLocalPeerConnectionId(),this.serverId="",this.pc=null,this.eventHandler=null,this.mediaConnected=!1,this.localStream=null,this.remoteStream=null,this.stopped=!1,this.iceServers=t,this.localSdp=null,this.localCandidates=new Array,this.muted=!1,this.localVideoEnabled=!1,this.localAudioEnabled=!1,this.localConstraints=null}var t,n,r,i,o,a,s;return t=e,(n=[{key:"initPeerConnection",value:(s=Re(Te().mark((function e(t,n,r,i){var o,a,s,c,l,u,f=arguments;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=f.length>4&&void 0!==f[4]?f[4]:null,a=!(f.length>5&&void 0!==f[5])||f[5],s=null,c=this,this.eventHandler=r,this.localConstraints=n,null!==o||!a){e.next=36;break}if(e.prev=7,!n.screen){e.next=26;break}if(!navigator.getDisplayMedia){e.next=15;break}return e.next=12,navigator.getDisplayMedia({video:!0,audio:n.screenAudio});case 12:s=e.sent,e.next=24;break;case 15:if(!navigator.mediaDevices.getDisplayMedia){e.next=21;break}return e.next=18,navigator.mediaDevices.getDisplayMedia({video:!0,audio:n.screenAudio});case 18:s=e.sent,e.next=24;break;case 21:return e.next=23,navigator.mediaDevices.getUserMedia({video:{mediaSource:"screen"}});case 23:s=e.sent;case 24:e.next=29;break;case 26:return e.next=28,navigator.mediaDevices.getUserMedia(n);case 28:s=e.sent;case 29:e.next=34;break;case 31:throw e.prev=31,e.t0=e.catch(7),new Ce(1,"getUserMediaError",e.t0);case 34:e.next=37;break;case 36:s=o;case 37:if(e.prev=37,(l=new RTCPeerConnection({iceServers:c.iceServers})).onicecandidate=function(e){e.candidate&&c.localCandidates.push(e.candidate),r.onCandidate(e.candidate)},l.oniceconnectionstatechange=function(e){"connected"===l.iceConnectionState?(c.mediaConnected=!0,r.onIceConnectionStateChange(c.mediaConnected)):"disconnected"===l.iceConnectionState&&(c.mediaConnected=!1,r.onIceConnectionStateChange(c.mediaConnected))},l.ontrack=function(e){if(e.streams.length>0){c.remoteStream=e.streams[0];var t="";e.track&&(t=e.track.kind),r.onTrack(e,t)}},s&&(s.getTracks().forEach((function(e){n.screen&&(e.onended=function(){r.onTrackEnded()}),l.addTrack(e,s)})),c.localVideoEnabled=s.getVideoTracks().length>0,c.localAudioEnabled=s.getAudioTracks().length>0,c.localAudioEnabled||(this.muted=!0),c.localStream=s,n.video||l.addTransceiver("video"),n.audio||l.addTransceiver("audio")),c.pc=l,!t){e.next=65;break}return e.prev=45,e.next=48,l.createOffer(i);case 48:return u=e.sent,c.localSdp=u,e.prev=50,e.next=53,c.pc.setLocalDescription(u);case 53:e.next=59;break;case 55:throw e.prev=55,e.t1=e.catch(50),c.freeResource(),new Ce(4,"setLocalDescriptionError",e.t1);case 59:e.next=65;break;case 61:throw e.prev=61,e.t2=e.catch(45),c.freeResource(),new Ce(3,"createOfferError",e.t2);case 65:return c.stopped&&c.freeResource(),e.abrupt("return",c);case 69:throw e.prev=69,e.t3=e.catch(37),c.freeResource(),new Ce(2,"peerConnectionError",e.t3);case 73:case"end":return e.stop()}}),e,this,[[7,31],[37,69],[45,61],[50,55]])}))),function(e,t,n,r){return s.apply(this,arguments)})},{key:"changeDevice",value:function(e,t){var n=this;return new Promise((function(r,i){"audio"==e?n.localConstraints.audio=t.audio:"video"==e?(t.video||(t.video=!0),n.localConstraints.video=t.video):"both"==e&&(n.localConstraints=t),navigator.mediaDevices.getUserMedia(t).then((function(t){var i=null,o=null;t.getTracks().forEach((function(e){"audio"===e.kind?i=e:"video"===e.kind&&(o=e)})),n.muted&&i&&(i.enabled=!1);var a=null,s=null,c=null;n.pc.getSenders().forEach((function(e){e.track&&"audio"===e.track.kind?a=e:e.track&&"video"===e.track.kind?s=e:null==e.track&&(c=e)})),i&&(a?a.replaceTrack(i):c&&c.replaceTrack(i)),o&&(s?s.replaceTrack(o):c&&c.replaceTrack(o)),n.localStream.getTracks().forEach((function(t){e!==t.kind&&"both"!==e||(t.stop(),n.localStream.removeTrack(t))})),i&&n.localStream.addTrack(i),o&&n.localStream.addTrack(o),r()})).catch((function(e){i(e)}))}))}},{key:"initPeerConnectionNoAccessDevice",value:(a=Re(Te().mark((function e(t,n,r){return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.initPeerConnection(t,null,r,n,void 0,!1));case 4:case"end":return e.stop()}}),e,this)}))),function(e,t,n){return a.apply(this,arguments)})},{key:"freeResource",value:function(){this.pc&&(this.pc.close(),this.pc=null);var e=this;this.localStream&&this.localStream.getTracks().forEach((function(t){t.stop(),e.localStream.removeTrack(t)})),this.localStream=null,this.remoteStream=null,this.eventHandler.onWebRtcClose()}},{key:"onReceiveSdp",value:function(e){var t=e,n=ge.getIOSVersion();if(Array.isArray(n)&&15==n[0]&&(1==n[1]||2==n[1])){var r={type:e.type,sdp:ge.removeCodec(e.sdp,"H264")};t=new RTCSessionDescription(r)}this.pc.setRemoteDescription(t,(function(){}),(function(e){console.log("setRemoteDescription error",e)}))}},{key:"onReceiveCandidate",value:function(e){this.pc.addIceCandidate(e)}},{key:"mute",value:(o=Re(Te().mark((function e(t){var n,r,i,o,a=arguments;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=a.length>1&&void 0!==a[1]?a[1]:null,!((r=this).localStream.getAudioTracks().length>0)){e.next=6;break}r.localStream.getAudioTracks().forEach((function(e){t?(e.enabled=!1,r.muted=!0):(e.enabled=!0,r.muted=!1)})),e.next=19;break;case 6:if(t){e.next=19;break}return i=n?{audio:{deviceId:{exact:n}},video:!1}:{audio:!0},o=r.muted,e.prev=9,r.muted=!1,e.next=13,r.changeDevice("audio",i);case 13:e.next=19;break;case 15:e.prev=15,e.t0=e.catch(9),r.muted=o,console.log("StringeeVideoTrack this.webrtc.changeDevice error ===========",e.t0);case 19:case"end":return e.stop()}}),e,this,[[9,15]])}))),function(e){return o.apply(this,arguments)})},{key:"enableLocalVideo",value:(i=Re(Te().mark((function e(t){var n,r,i;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=this,r=!1,t){e.next=6;break}n.localStream.getVideoTracks().forEach((function(e){r=!0,e.stop(),n.localStream.removeTrack(e),n.localVideoEnabled=!1})),e.next=18;break;case 6:return i={audio:!1,video:n.localConstraints.video},e.prev=7,e.next=10,n.changeDevice("video",i);case 10:r=!0,n.localVideoEnabled=!0,e.next=18;break;case 14:e.prev=14,e.t0=e.catch(7),console.log("StringeeVideoTrack this.webrtc.changeDevice error ===========",e.t0),r=!1;case 18:return e.abrupt("return",r);case 19:case"end":return e.stop()}}),e,this,[[7,14]])}))),function(e){return i.apply(this,arguments)})}])&&Oe(t.prototype,n),r&&Oe(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Ae(e){return(Ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ke(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ke=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",a=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var i=t&&t.prototype instanceof f?t:f,o=Object.create(i.prototype),a=new I(r||[]);return o._invoke=function(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return b()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===u)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===u)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}(e,n,a),o}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var u={};function f(){}function d(){}function h(){}var v={};s(v,i,(function(){return this}));var p=Object.getPrototypeOf,g=p&&p(p(T([])));g&&g!==t&&n.call(g,i)&&(v=g);var m=h.prototype=f.prototype=Object.create(v);function y(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){var r;this._invoke=function(i,o){function a(){return new t((function(r,a){!function r(i,o,a,s){var c=l(e[i],e,o);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==Ae(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(f).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}(i,o,r,a)}))}return r=r?r.then(a,a):a()}}function S(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var r=l(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function T(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:b}}function b(){return{value:void 0,done:!0}}return d.prototype=h,s(m,"constructor",h),s(h,"constructor",d),d.displayName=s(h,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,s(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e},e.awrap=function(e){return{__await:e}},y(_.prototype),s(_.prototype,o,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new _(c(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},y(m),s(m,a,"Generator"),s(m,i,(function(){return this})),s(m,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=T,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,u):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;C(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:T(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function Me(e,t,n,r,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,i)}function Pe(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){Me(o,r,i,a,s,"next",e)}function s(e){Me(o,r,i,a,s,"throw",e)}a(void 0)}))}}function De(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}n(99),n(100);var xe=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isLocal=!0,this.roomConnected=null,this.localSdpToSend=null,this.localCandidatesForSend=new Array,this.serverId="",this.serverPeerConnectionId="",this.stringeeClient=t,this.audio=!1,this.video=!1,this.screen=!1,this.screenAudio=!1,this.mobileCamera,this.userData=null,this.constraints={},n.audio?(this.constraints.audio=!0,this.audio=!0):n.audioDeviceId=null,n.screen&&(this.constraints.screen=!0,this.screen=!0,n.screenAudio?(this.screenAudio=!0,this.constraints.screenAudio=!0,this.constraints.audio=!0,this.audio=!0):(this.screenAudio=!1,this.constraints.screenAudio=!1),this.video=!1),n.video?(this.video=!0,this.constraints.video=!0):(n.videoDeviceId=null,n.videoDimensions=null),n.videoDimensions&&(this.constraints.video=n.videoDimensions),n.videoDeviceId?(this.videoDeviceId=n.videoDeviceId,n.videoDimensions?this.constraints.video.deviceId={exact:n.videoDeviceId}:this.constraints.video={deviceId:{exact:n.videoDeviceId}}):this.videoDeviceId=null,this.constraints.video&&n.mobileCamera&&(!0===this.constraints.video&&(this.constraints.video={}),"front"==n.mobileCamera?(this.constraints.video.facingMode="user",this.mobileCamera="front"):"back"==n.mobileCamera?(this.constraints.video.facingMode="environment",this.mobileCamera="back"):""!=n.mobileCamera&&null!=n.mobileCamera&&(this.mobileCamera=null,console.log('options.mobileCamera must be: "front" or "back"'))),n.audioDeviceId?(this.audioDeviceId=n.audioDeviceId,this.constraints.audio={deviceId:{exact:n.audioDeviceId}}):this.audioDeviceId=null,this.webrtc=new we(t.ice_servers),this.localId=this.webrtc.localId,this.onMethods=new u,this.elements=[],this.callOnReady=!1,this.muted=!1,this.localVideoEnabled=!1,this.localAudioEnabled=!1,this.audioDeviceId=null,this.speakerDeviceId=null,this.lastAudioBytesSent=-1,this.lastVideoBytesSent=-1,this.lastAudioBytesReceived=-1,this.lastVideoBytesReceived=-1,this.lastTimestampGetBytes=-1}var t,n,r,i,o,a;return t=e,(n=[{key:"getBW",value:function(){var e=this;return new Promise((function(t,n){var r={audioSent:0,videoSent:0,audioReceived:0,videoReceived:0};e.getStats((function(n){var i=(new Date).getTime(),o=i-e.lastTimestampGetBytes;for(var a in e.lastTimestampGetBytes=i,n){var s=n[a];"audio"===s.mediaType&&s.bytesSent&&(e.lastAudioBytesSent>-1&&(r.audioSent=Math.round(8*(s.bytesSent-e.lastAudioBytesSent)/o)),e.lastAudioBytesSent=s.bytesSent),"video"===s.mediaType&&s.bytesSent&&(e.lastVideoBytesSent>-1&&(r.videoSent=Math.round(8*(s.bytesSent-e.lastVideoBytesSent)/o)),e.lastVideoBytesSent=s.bytesSent),"audio"===s.mediaType&&s.bytesReceived&&(e.lastAudioBytesReceived>-1&&(r.audioReceived=Math.round(8*(s.bytesReceived-e.lastAudioBytesReceived)/o)),e.lastAudioBytesReceived=s.bytesReceived),"video"===s.mediaType&&s.bytesReceived&&(e.lastVideoBytesReceived>-1&&(r.videoReceived=Math.round(8*(s.bytesReceived-e.lastVideoBytesReceived)/o)),e.lastVideoBytesReceived=s.bytesReceived)}t(r)}))}))}},{key:"buildConstraints",value:function(e){var t={};return e.audio?(t.audio=!0,e.audioDeviceId&&(t.audio={deviceId:{exact:e.audioDeviceId}})):e.audioDeviceId=null,e.video?t.video=!0:(e.videoDeviceId=null,e.videoDimensions=null),e.videoDimensions&&(t.video=e.videoDimensions),e.videoDeviceId&&(e.videoDimensions?t.video.deviceId={exact:e.videoDeviceId}:t.video={deviceId:{exact:e.videoDeviceId}}),t.video&&e.mobileCamera&&(!0===t.video&&(t.video={}),"front"==e.mobileCamera?t.video.facingMode="user":"back"==e.mobileCamera?t.video.facingMode="environment":""!=e.mobileCamera&&null!=e.mobileCamera&&console.log('options.mobileCamera must be: "front" or "back"')),t}},{key:"init",value:(a=Pe(ke().mark((function e(){var t,n,r,i,o,a=arguments;return ke().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=a.length>0&&void 0!==a[0]?a[0]:null,r={onTrack:function(e,t){var r=e.streams[0];n.onRemoteStream(r,t)},onIceConnectionStateChange:function(e){var t={isConnected:e,localId:n.localId,roomId:n.roomConnected.roomId,serverPcId:n.webrtc.serverId,isLocal:n.isLocal,serverTrackId:n.serverId};n.stringeeClient._sendMessage(s.VIDEO_ROOM_MEDIA_STATE,t,(function(e){})),n.callOnEvent("mediastate",t)},onCandidate:function(e){n.localCandidatesForSend.push(e),n.processSendSdpCandidate()},onTrackEnded:function(){n.callOnEvent("trackended")},onWebRtcClose:function(){}},!(n=this).isLocal){e.next=9;break}return i={offerToReceiveAudio:1,offerToReceiveVideo:1},e.next=7,n.webrtc.initPeerConnection(!0,n.constraints,r,i,t,!0);case 7:e.next=14;break;case 9:return(i={}).offerToReceiveAudio=1,i.offerToReceiveVideo=1,e.next=14,n.webrtc.initPeerConnectionNoAccessDevice(!0,i,r);case 14:return n.localSdpToSend=n.webrtc.localSdp,n.processSendSdpCandidate(),e.prev=16,e.next=19,navigator.mediaDevices.enumerateDevices();case 19:o=e.sent,n._gotDevices(o),e.next=26;break;case 23:e.prev=23,e.t0=e.catch(16),console.log("++++++++ error enumerateDevices ++++++++",e.t0);case 26:return n.localVideoEnabled=n.webrtc.localVideoEnabled,n.localAudioEnabled=n.webrtc.localAudioEnabled,n.localAudioEnabled||(this.muted=!0),e.abrupt("return",this);case 30:case"end":return e.stop()}}),e,this,[[16,23]])}))),function(){return a.apply(this,arguments)})},{key:"switchCamera",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(!this.screen){var t=this;"front"==this.mobileCamera||""==this.mobileCamera||null==this.mobileCamera?this.mobileCamera="back":this.mobileCamera="front",this.videoDeviceId=null;var n=t.webrtc.localStream;n.getTracks().forEach((function(e){e.stop(),n.removeTrack(e)}));var r={audio:!0,video:!0,screen:!1,videoDimensions:e,videoDeviceId:null,mobileCamera:t.mobileCamera};t.changeDevice("both",null,r)}}},{key:"_gotDevices",value:function(e){var t=1;He.microphones=[],He.speakers=[],He.cameras=[];for(var n=0;n<e.length;++n){var r=e[n];if(r.deviceId&&0!==r.deviceId.length)if("audioinput"===r.kind){var i=r.label||"Microphone ".concat(t++);He.microphones.push({label:i,deviceId:r.deviceId,groupId:r.groupId})}else"audiooutput"===r.kind?(i=r.label||"Speaker ".concat(t++),He.speakers.push({label:i,deviceId:r.deviceId,groupId:r.groupId})):"videoinput"===r.kind&&(i=r.label||"Camera ".concat(t++),He.cameras.push({label:i,deviceId:r.deviceId,groupId:r.groupId}))}this.onMethods.get("gotDevicesInfo")&&this.callOnEvent("gotDevicesInfo",{microphones:He.microphones,speakers:He.speakers,cameras:He.cameras})}},{key:"onRemoteStream",value:function(e,t){this.callOnReady||(this.callOnReady=!0,this.callOnEvent("ready",t))}},{key:"processSendSdpCandidate",value:function(){if(this.roomConnected&&this.webrtc){if(this.localSdpToSend){var e={data:this.localSdpToSend,roomId:this.roomConnected.roomId,serverPcId:this.webrtc.serverId,isLocal:this.isLocal,serverTrackId:this.serverId};this.stringeeClient._sendMessage(s.VIDEO_ENDPOINT_SDP,e,(function(e){})),this.localSdpToSend=null}for(;this.localCandidatesForSend.length>0;){var t=this.localCandidatesForSend.pop();t&&(e={data:t,roomId:this.roomConnected.roomId,serverPcId:this.webrtc.serverId,isLocal:this.isLocal,serverTrackId:this.serverId},this.stringeeClient._sendMessage(s.VIDEO_ENDPOINT_CANDIDATE,e,(function(e){})))}}}},{key:"attach",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n="video";t&&(n="audio");var r=document.createElement(n);return r.classList.add("stringee-video-track-"+this.localId),r.srcObject=null,this.isLocal?r.srcObject=this.webrtc.localStream:r.srcObject=this.webrtc.remoteStream,r.setAttribute("autoplay","true"),r.setAttribute("playsinline","true"),this.isLocal&&(r.muted=!0),this.speakerDeviceId&&r.setSinkId(this.speakerDeviceId).then().catch((function(t){e.speakerDeviceId=null})),this.elements.push(r),r}},{key:"routeAudioToSpeaker",value:function(e){var t=!1,n=this;return this.speakerDeviceId=e,this.elements.forEach((function(r){void 0!==r.sinkId?r.setSinkId(e).then().catch((function(e){n.speakerDeviceId=null})):(this.speakerDeviceId=null,t=!1)})),t}},{key:"changeDevice",value:function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return new Promise((function(i,o){var a,s=n;if(a=r?n.buildConstraints(r):Object.assign({},n.constraints),"video"==e)a.audio=!1,a.video||(a.video={}),r||(!0===a.video&&(a.video={}),a.video.deviceId={exact:t});else if("audio"==e)a.video=!1,a.audio||(a.audio={}),r||(!0===a.audio&&(a.audio={}),a.audio.deviceId={exact:t});else if("both"!=e)return console.log('type must be "audio", "video" or "both"'),void o("type must be audio, video or both");n.webrtc.changeDevice(e,a).then((function(){i(n.webrtc.localStream),"video"==e?s.videoDeviceId=t:s.audioDeviceId=t,s.callOnEvent("changedevice",{type:e,newDeiceId:t,newOptions:r})})).catch((function(e){console.log("StringeeVideoTrack this.webrtc.changeDevice error ===========",e),o(e)}))}))}},{key:"detach",value:function(){return this.elements}},{key:"detachAndRemove",value:function(){this.elements.forEach((function(e){e.remove()}))}},{key:"getMediaStream",value:function(){return this.webrtc?this.isLocal?this.webrtc.localStream:this.webrtc.remoteStream:null}},{key:"onSdpFromServer",value:function(e){this.webrtc&&this.webrtc.onReceiveSdp(e)}},{key:"onCandidateFromServer",value:function(e){this.webrtc&&this.webrtc.onReceiveCandidate(e)}},{key:"close",value:function(){this.webrtc.freeResource()}},{key:"mute",value:(o=Pe(ke().mark((function e(t){var n,r,i,o=arguments;return ke().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:null,r=!(o.length>2&&void 0!==o[2])||o[2],e.next=4,this.webrtc.mute(t,n);case 4:if(this.muted=this.webrtc.muted,this.audio=!this.muted,r&&this.roomConnected){e.next=8;break}return e.abrupt("return");case 8:i={roomId:this.roomConnected.roomId,mediaType:1,enable:!t,serverTrackId:this.serverId},this.stringeeClient._sendMessage(s.VIDEO_ROOM_ENABLE_DISABLE_AUDIO_VIDEO,i,(function(e){}));case 10:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"enableLocalVideo",value:(i=Pe(ke().mark((function e(t){var n,r,i,o=arguments;return ke().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=!(o.length>1&&void 0!==o[1])||o[1],e.next=3,this.webrtc.enableLocalVideo(t);case 3:return r=e.sent,this.localVideoEnabled=this.webrtc.localVideoEnabled,this.video=this.localVideoEnabled,r&&n&&this.roomConnected&&(i={roomId:this.roomConnected.roomId,mediaType:2,enable:t,serverTrackId:this.serverId},this.stringeeClient._sendMessage(s.VIDEO_ROOM_ENABLE_DISABLE_AUDIO_VIDEO,i,(function(e){}))),e.abrupt("return",r);case 8:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"disableRemoteVideo",value:function(){var e=this;return new Promise((function(t,n){if(e.roomConnected&&e.stringeeClient){console.log("disableRemoteVideo111");var r={roomId:e.roomConnected.roomId,mediaType:2,enable:!1,serverTrackId:e.serverId,disableRemote:!0};e.stringeeClient._sendMessage(s.VIDEO_ROOM_ENABLE_DISABLE_AUDIO_VIDEO,r,(function(e){console.log("this1.stringeeClient._sendMessage(VIDEO_ROOM_ENABLE_DISABLE_AUDIO_VIDEO ++++++++ = ",e),0==e.r?t():n(e)}))}else n("not connected to room")}))}},{key:"disableRemoteAudio",value:function(){var e=this;return new Promise((function(t,n){if(e.roomConnected&&e.stringeeClient){console.log("disableRemoteAudio111");var r={roomId:e.roomConnected.roomId,mediaType:1,enable:!1,serverTrackId:e.serverId,disableRemote:!0};e.stringeeClient._sendMessage(s.VIDEO_ROOM_ENABLE_DISABLE_AUDIO_VIDEO,r,(function(e){console.log("this1.stringeeClient._sendMessage(VIDEO_ROOM_ENABLE_DISABLE_AUDIO_VIDEO ++++++++ = ",e),0==e.r?t():n(e)}))}else n("not connected to room")}))}},{key:"on",value:function(e,t){var n=this.onMethods.get(e);n||(n=new Array,this.onMethods.put(e,n)),n.push(t)}},{key:"callOnEvent",value:function(e,t){var n=this,r=this.onMethods.get(e);r?r.forEach((function(e){t?e.call(n,t):e.call(n)})):console.log("Please implement StringeeVideoTrack event: "+e)}},{key:"getStats",value:function(e){if(this.webrtc&&this.webrtc.pc)if(navigator.mozGetUserMedia){var t=this.webrtc.getReceivers().find((function(e){return"audio"==e.kind}));this.webrtc.getStats(t).then((function(t){var n={};t.forEach((function(e){if(("inboundrtp"==e.type||"outboundrtp"==e.type||"inbound-rtp"==e.type||"outbound-rtp"==e.type)&&(e.id.startsWith("inbound_rtp")||e.id.startsWith("outbound_rtp"))){var t={};for(var r in e)"bytesSent"!=r&&"packetsSent"!=r&&"mediaType"!=r&&"bytesReceived"!=r&&"packetsReceived"!=r&&"packetsLost"!=r||(t[r]=e[r]);n[e.id]=t}})),e(n)})).catch((function(e){console.log(e)}))}else this.webrtc.pc.getStats((function(t){var n={};t.result().forEach((function(e){if("ssrc"==e.type){var t={};e.names().forEach((function(n){"bytesSent"!=n&&"packetsSent"!=n&&"mediaType"!=n&&"bytesReceived"!=n&&"packetsReceived"!=n&&"packetsLost"!=n||(t[n]=e.stat(n))})),n[e.id]=t}})),e(n)}));else e("PC closed")}}])&&De(t.prototype,n),r&&De(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Ne(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Le(e,t,n){return t&&Ne(e.prototype,t),n&&Ne(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var Fe=Le((function e(t,n,r,i,o){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.trackId=t.serverId,this.displayVideo=n,this.isMainTrack=r,this.displayView={width:i,height:o}}));function je(e){return(je="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ue(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var Ve=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.stringeeClient=t,this.roomId=null,this.tracks=new u,this.mapServerIdTracks=new u,this.onMethods=new u,this.permissionSubscribe=!1,this.permissionPublish=!1,this.permissionControlRoom=!1}var t,n,r;return t=e,(n=[{key:"publish",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=this,r=new Promise((function(r,i){if(e.isLocal){var o={roomId:n.roomId,localPcId:e.webrtc.localId,audio:!!e.audio,video:!!e.video,screen:!!e.constraints.screen};t&&("object"===je(t)?(o.userData=t,e.userData=t):console.log("userData is not object, ignore it")),n.stringeeClient._sendMessage(s.VIDEO_PUBLISH_TRACK,o,(function(t){0==t.r?(e.serverId=t.serverTrackId,e.webrtc.serverId=t.serverPcId,n.tracks.put(e.localId,e),n.mapServerIdTracks.put(e.serverId,e),e.roomConnected=n,r(),e.processSendSdpCandidate(),e.on("trackended",(function(){n.unpublish(e)}))):i(t)}))}else i("Could not publish remote track")}));return r}},{key:"unpublish",value:function(e){if(e.isLocal){var t={roomId:e.roomConnected.roomId,serverTrackId:e.serverId};this.stringeeClient._sendMessage(s.VIDEO_UNPUBLISH_TRACK,t,(function(e){0==e.r&&console.info("VIDEO_UNPUBLISH_TRACK",e)}))}else console.info("Could not unpublish remote track")}},{key:"subscribe",value:function(e,t){var n=this;return t||(t={audio:!0,video:!0,screen:!1}),new Promise((function(r,i){var o=n.mapServerIdTracks.get(e);if(o)r(o);else{var a=He.genLocalPeerConnectionId(),c={roomId:n.roomId,localPcId:a,subscribeTrackId:e};n.stringeeClient._sendMessage(s.VIDEO_SUBSCRIBE_TRACK,c,(function(o){if(0==o.r){o.trackScreenSharing&&(t.video=!0);var s=new xe(n.stringeeClient,t);s.serverId=e,s.isLocal=!1,s.roomConnected=n,s.localId=a,s.webrtc.localId=a,s.webrtc.serverId=o.serverPcId,s.audio=o.trackAudioEnable&&t.audio,s.video=o.trackVideoEnable&&t.video,s.screen=o.trackScreenSharing&&t.video,s.userPublish=o.userPublish,s.stringeeClientPublish=o.stringeeClientPublish,n.tracks.put(s.localId,s),n.mapServerIdTracks.put(s.serverId,s),r(s),s.init(),s.processSendSdpCandidate()}else i(o)}))}}))}},{key:"unsubscribe",value:function(e){var t=this,n={roomId:t.roomId,serverTrackId:e.serverId,serverPcId:e.webrtc.serverId};t.stringeeClient._sendMessage(s.VIDEO_UNSUBSCRIBE_TRACK,n,(function(n){0==n.r&&(console.log("unsubscribe serverTrackId OK: "+e.serverId),e.close(),t.tracks.remove(e.localId),t.mapServerIdTracks.remove(e.serverId))}))}},{key:"leave",value:function(e){var t=this;return t.tracks.forEach((function(e){e.value.close(),t.tracks.remove(e.localId),t.mapServerIdTracks.remove(e.serverId)})),new Promise((function(n,r){var i={roomId:t.roomId,allStringeeClient:e};t.stringeeClient._sendMessage(s.VIDEO_LEFT_ROOM,i,(function(e){He.rooms.remove(t.roomId),0==e.r?n():r(e)}))}))}},{key:"sendMessage",value:function(e){var t=this;return new Promise((function(n,r){var i={roomId:t.roomId,msg:e};t.stringeeClient._sendMessage(s.VIDEO_ROOM_SEND_MSG,i,(function(e){0==e.r?n():r(e)}))}))}},{key:"onReceiveSdp1",value:function(e,t,n){var r=this.tracks.get(e);r?r.onSdpFromServer(n):console.log("StringeeVideoRoom onReceiveSdp1: Could not found localTrackId="+e)}},{key:"onReceiveCandidate1",value:function(e,t,n){var r=this.tracks.get(e);r?r.onCandidateFromServer(n):console.log("StringeeVideoRoom onReceiveCandidate1: Could not found localTrackId="+e)}},{key:"clearAllOnMethos",value:function(){this.onMethods.clear()}},{key:"on",value:function(e,t){var n=this.onMethods.get(e);n||(n=new Array,this.onMethods.put(e,n)),n.push(t)}},{key:"callOnEvent",value:function(e,t){var n=this,r=this.onMethods.get(e);r?r.forEach((function(e){t?e.call(n,t):e.call(n)})):console.log("Please implement StringeeVideoRoom event: "+e)}},{key:"onTrackAdded",value:function(e){var t={info:e};this.callOnEvent("addtrack",t)}},{key:"onTrackRemoved",value:function(e){var t=this.mapServerIdTracks.get(e.track.serverId),n={info:e};t&&(n.track=t,t.close(),this.mapServerIdTracks.remove(e.track.serverId)),this.callOnEvent("removetrack",n)}},{key:"onRoomJoined",value:function(e){var t={info:e};this.callOnEvent("joinroom",t)}},{key:"onRoomLeft",value:function(e){var t={info:e};this.callOnEvent("leaveroom",t)}},{key:"onRoomMsg",value:function(e){var t={info:e};this.callOnEvent("message",t)}},{key:"onTrackChangeAudioVideoEnableDisable",value:function(e){var t=this.mapServerIdTracks.get(e.track.serverId);if(t){1==e.mediaType?t.audio=e.enable:2==e.mediaType&&(t.video=e.enable),e.disableRemote&&t.isLocal&&(1!=e.mediaType||e.enable?2!=e.mediaType||e.enable||t.enableLocalVideo(!1,!1):t.mute(!0,null,!1));var n={track:t,enable:e.enable,mediaChange:1==e.mediaType?"audio":"video"};this.callOnEvent("trackmediachange",n)}}},{key:"sendRenderLayoutInfo",value:function(e,t){var n=this;return new Promise((function(r,i){var o={roomId:n.roomId,layout:e,subscribedTracks:t};n.stringeeClient._sendMessage(s.VIDEO_ROOM_RENDER_LAYOUT_INFO,o,(function(e){0==e.r?r(e):i(e)}))}))}}])&&Ue(t.prototype,n),r&&Ue(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Ge(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Be(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var He=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,r=[{key:"genLocalTrackId",value:function(){if(0==e.ID_GEN){var t=new Date;e.ID_GEN=t.getTime()}return e.ID_GEN++,"track-"+e.ID_GEN}},{key:"genLocalPeerConnectionId",value:function(){if(0==e.ID_GEN){var t=new Date;e.ID_GEN=t.getTime()}return e.ID_GEN++,"pc-"+e.ID_GEN}},{key:"makeCall",value:function(t,n,r,i,o){return new Promise((function(a,c){var l={fromNumber:n,toNumber:r,video:i,custom:o};t._sendMessage(s.VIDEO_ROOM_MAKE_CALL,l,(function(n){if(console.log("res",n),0==n.r){var r=new Ve(t);r.roomId=n.roomId,e.rooms.put(r.roomId,r);var i={room:r,roomToken:n.roomToken,callId:n.callId};a(i)}else c(n)}))}))}},{key:"createRoom",value:function(t){return new Promise((function(n,r){t._sendMessage(s.VIDEO_ENDPOINT_MAKE_ROOM,{},(function(i){if(0==i.r){var o=new Ve(t);o.roomId=i.roomId,e.rooms.put(o.roomId,o),n(o)}else r(i)}))}))}},{key:"joinRoom",value:function(t,n){return new Promise((function(r,i){var o={roomToken:n};t._sendMessage(s.VIDEO_ENDPOINT_JOIN_ROOM,o,(function(n){if(0==n.r){var o=e.rooms.get(n.roomId);o||(o=new Ve(t)),o.roomId=n.roomId,o.permissionSubscribe=n.permissionSubscribe,o.permissionPublish=n.permissionPublish,o.permissionControlRoom=n.permissionControlRoom,o.record=n.record,e.rooms.put(o.roomId,o);var a={room:o,listTracksInfo:n.tracks,listUsersInfo:n.users,listStringeeClients:n.listStringeeClients};r(a)}else i(n)}))}))}},{key:"createLocalVideoTrack",value:function(e,t){var n,r=new xe(e,t);return r.isLocal=!0,n=r.init(),r.on("changedevice",(function(e){console.log("++++++++++localTrack changedevice",e),("both"===e.type||"video"===e.type)&&r.detach().forEach((function(e){e.srcObject=null,e.srcObject=r.webrtc.localStream}))})),n}},{key:"getDevicesInfo",value:function(){return new Promise((function(t,n){navigator.mediaDevices.enumerateDevices().then((function(n){var r=1;e.microphones=[],e.speakers=[],e.cameras=[];for(var i=0;i<n.length;++i){var o=n[i];if(o.deviceId&&0!==o.deviceId.length)if("audioinput"===o.kind){var a=o.label||"Microphone ".concat(r++);e.microphones.push({label:a,deviceId:o.deviceId,groupId:o.groupId})}else"audiooutput"===o.kind?(a=o.label||"Speaker ".concat(r++),e.speakers.push({label:a,deviceId:o.deviceId,groupId:o.groupId})):"videoinput"===o.kind&&(a=o.label||"Camera ".concat(r++),e.cameras.push({label:a,deviceId:o.deviceId,groupId:o.groupId}))}t({microphones:e.microphones,speakers:e.speakers,cameras:e.cameras})})).catch((function(e){console.log("+++++++++++++++++++++++ error enumerateDevices +++++++++++++++++++++++",e),n()}))}))}}],(n=null)&&Ge(t.prototype,n),r&&Ge(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function qe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function We(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qe(Object(n),!0).forEach((function(t){Ye(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ye(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ke(e){return(Ke="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ze(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}Be(He,"rooms",new u),Be(He,"ID_GEN",0),Be(He,"microphones",[]),Be(He,"speakers",[]),Be(He,"cameras",[]);var Je=function(){function e(t,n,r,i){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.client=t,this.fromNumber=n,this.toNumber=r,this.custom="",this.customDataFromYourServer="",this.fromAlias=n,this.toAlias=r,this.fromInternal=!0,this.answeredOnAnotherDevice=!1,this.isVideoCall=i,this.isIncomingCall=!1,this.isAnswered=!1,this.isOnHold=!1,this._answeredDeviceId="",this.ended=!1,this.callId="",this.room=null,this.roomToken="",this.localTracks=[],this.subscribedTracks=[],this.toType="",this.muted=!1,this.localVideoEnabled=i,this._onMethods=new u,this.client._stringeeCalls2.push(this),this.videoResolution=null,this.audioDeviceId=null,this.videoDeviceId=null,this.mobileCamera=null,this._callCheckExistInterval=null,this.reasonEndcall=""}var t,n,r;return t=e,r=[{key:"joinCall",value:function(t,n){return new Promise((function(r,i){var o={callid:n};t._sendMessage(s.VIDEO_ROOM_JOIN_CALL2,o,(function(o){var a;0==o.r?((a=t.findCall2ByCallId(n))||((a=new e(t,o.fromNumber,o.toNumber,!0)).callId=n,a.isIncomingCall=!0,a.fromAlias=o.fromAlias,a.toAlias=o.toAlias,a.customDataFromYourServer=o.customDataFromYourServer,a.roomToken=o.roomToken),r(a)):i(o)}))}))}}],(n=[{key:"makeCall",value:function(e){if(this.isIncomingCall)console.log("could not make call, this is a incoming call");else{var t=this,n={fromNumber:this.fromNumber,toNumber:this.toNumber,video:this.isVideoCall,custom:t.custom};t.client._sendMessage(s.VIDEO_ROOM_MAKE_CALL,n,(function(n){0==n.r&&(t.roomToken=n.roomToken,delete n.roomToken,delete n.roomId,t.callId=n.callId,t.toType=n.toType,t.customDataFromYourServer=n.customDataFromYourServer,t._createLocalVideoTrack(),t._createCallCheckExistInterval()),e.call(t,n)}))}}},{key:"transferCall",value:function(e,t,n,r){var i=this,o={fromNumber:this.fromNumber,toNumber:e,toType:t,transferType:n,callId:i.callId};i.client._sendMessage(s.VIDEO_ROOM_TRANSFER_CALL,o,(function(e){r&&r.call(i,e)}))}},{key:"sendHold",value:function(){var e=arguments.length>1?arguments[1]:void 0,t=this,n={hold:!0,callId:t.callId};t.client._sendMessage(s.VIDEO_ROOM_HOLD,n,(function(n){return e.call(t,n),0===n.r&&(t.isOnHold=!0,!0)}))}},{key:"sendUnHold",value:function(e){var t=this,n={hold:!1,callId:t.callId};t.client._sendMessage(s.VIDEO_ROOM_HOLD,n,(function(n){return e.call(t,n),0===n.r&&(t.isOnHold=!1,!0)}))}},{key:"leaveRoom",value:function(){this.room.leave(!0)}},{key:"switchCameraOld",value:function(){var e=this;return He.cameras.length<2?(console.log("StringeeVideo.cameras.length: "+He.cameras.length),!1):("front"==this.mobileCamera||""==this.mobileCamera||null==this.mobileCamera?this.mobileCamera="back":this.mobileCamera="front",this.videoDeviceId=null,e.localTracks.forEach((function(t){t.screen||(e.room.unpublish(t),t.detachAndRemove())})),e.localTracks=e.localTracks.filter((function(e){return 1==e.screen})),e._createLocalVideoTrack(),!0)}},{key:"switchCamera",value:function(){var e=this;return new Promise((function(t,n){var r=e;return He.cameras.length<2?(console.log("StringeeVideo.cameras.length: "+He.cameras.length),n("StringeeVideo.cameras.length: "+He.cameras.length),!1):("front"==e.mobileCamera||""==e.mobileCamera||null==e.mobileCamera?e.mobileCamera="back":e.mobileCamera="front",e.videoDeviceId=null,r.localTracks.forEach((function(e){if(!e.screen){var t=e.webrtc.localStream;t.getTracks().forEach((function(e){e.stop(),t.removeTrack(e)}))}})),r.localTracks.forEach((function(e){if(!e.screen){var i=null;r.videoResolution&&(i={width:{min:r.videoResolution.width,max:r.videoResolution.width},height:{min:r.videoResolution.height,max:r.videoResolution.height}});var o={audio:!0,video:!0,screen:!1,videoDimensions:i,videoDeviceId:null,mobileCamera:r.mobileCamera};e.changeDevice("both",null,o).then((function(e){t(e)})).catch((function(e){n(e)}))}})),!0)}))}},{key:"answer",value:function(e){var t=this;if(this.isIncomingCall)if(this.isAnswered)console.log("Error: call has been answered");else if(this.answeredOnAnotherDevice)console.log("Error: call has been answered on other device");else{this.isAnswered=!0;var n={callId:this.callId,code:200};this.client._sendMessage(s.VIDEO_ROOM_CALL_STATE,n,(function(n){0==n.r&&(t._createLocalVideoTrack(),t._createCallCheckExistInterval()),e&&e.call(t,{r:n.r})}))}else console.log("Error: could not answer call, this is a outgoing call")}},{key:"reject",value:function(e){var t=this;if(this.isIncomingCall){t.ended=!0;var n={callId:this.callId,code:486,reasonEndcall:this.reasonEndcall};this.client._sendMessage(s.VIDEO_ROOM_CALL_STATE,n,(function(n){e&&e.call(t,{r:n.r})})),this.onRemove()}else console.log("could not reject call, this is a outgoing call")}},{key:"ringing",value:function(e){var t=this;if(this.isIncomingCall){var n={callId:this.callId,code:180};this.client._sendMessage(s.VIDEO_ROOM_CALL_STATE,n,(function(n){e&&e.call(t,{r:n.r})}))}else console.log("could not send ringing signal, this is a outgoing call")}},{key:"sendInfo",value:function(e,t){var n=this;("string"==typeof e||e instanceof String)&&(e={info:e}),n.room.sendMessage(e).then((function(){t.call(n,{r:0})})).catch((function(e){t.call(n,{r:1})}))}},{key:"hangup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=this;n.ended=!0;var r={callId:this.callId};t&&("string"==typeof t?r.reason=t:"object"===Ke(t)&&(r=We({callId:this.callId},t))),this.client._sendMessage(s.VIDEO_ROOM_END_CALL,r,(function(t){0===t.r&&n.onRemove(),e&&e.call(n,{r:t.r})}))}},{key:"_joinRoom",value:function(){var e=this;return new Promise((function(t,n){He.joinRoom(e.client,e.roomToken).then((function(n){var r=n.room;e.room=r,r.clearAllOnMethos(),r.on("joinroom",(function(e){})),r.on("leaveroom",(function(e){})),r.on("addtrack",(function(t){var n=!1;if(e.localTracks.forEach((function(e){e.serverId===t.info.track.serverId&&(n=!0)})),!n){for(var r=0;r<e.subscribedTracks.length;r++)if(e.subscribedTracks[r].serverId==t.info.track.serverId)return;e._subscribe(t.info.track)}})),r.on("removetrack",(function(t){var n=t.track;if(n){n.isLocal?(e.localTracks=e.localTracks.filter((function(e){return e.localId!==n.localId})),e._callOnEvent("removelocaltrack",n)):(e.subscribedTracks=e.subscribedTracks.filter((function(e){return e.localId!==n.localId})),e._callOnEvent("removeremotetrack",n)),n.detach().forEach((function(e){e.remove()}));for(var r=0;r<e.subscribedTracks.length;r++)e.subscribedTracks[r].serverId==n.serverId&&(e.subscribedTracks.splice(r,1),r--)}})),r.on("message",(function(t){e._callOnEvent("info",t.info)})),n.listTracksInfo.forEach((function(t){e._subscribe(t)})),t(r)})).catch((function(e){console.log("join room ERROR: ",e),n()}))}))}},{key:"_createLocalVideoTrack",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=null;this.videoResolution&&(t={width:{min:this.videoResolution.width,max:this.videoResolution.width},height:{min:this.videoResolution.height,max:this.videoResolution.height}});var n={audio:e,video:this.isVideoCall,screen:!1,videoDimensions:t,videoDeviceId:this.videoDeviceId,mobileCamera:this.mobileCamera};/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)&&(n.mobileCamera="front");var r=this;r.createLocalVideoTrack(n).catch((function(e){var t=e.name||e.jsError.name;console.log("create Local Video Track ERROR: ",t),"NotFoundError"===t?He.getDevicesInfo().then((function(e){0===e.cameras.length&&(n.video=!1),0===e.microphones.length&&(n.audio=!1),r.createLocalVideoTrack(n).catch((function(e){var t=e.name||e.jsError.name;console.log("create again Local Video Track ERROR: ",t),r._callOnEvent("signalingstate",{reason:"WebRTC_"+t,code:-1,sipCode:-1,sipReason:""}),r.hangup(null,{reason:t,pubOptions:n})}))})):"NotReadableError"===t?(n.videoDimensions=-1,r.createLocalVideoTrack(n).catch((function(e){var t=e.name||e.jsError.name;console.log("create again Local Video Track ERROR: ",t),r._callOnEvent("signalingstate",{reason:"WebRTC_"+t,code:-1,sipCode:-1,sipReason:""}),r.hangup(null,{reason:t,pubOptions:n})}))):"NotAllowedError"===t?(n.video=!1,r.createLocalVideoTrack(n).catch((function(e){var t=e.name||e.jsError.name;console.log("create again Local Video Track ERROR: ",t),r._callOnEvent("signalingstate",{reason:"WebRTC_"+t,code:-1,sipCode:-1,sipReason:""}),r.hangup(null,{reason:t,pubOptions:n})}))):("OverconstrainedError"===t&&console.log("Camera không hỗ trợ độ phân giải này, vui lòng chọn độ phân giải khác"),r._callOnEvent("signalingstate",{reason:"WebRTC_"+t,code:-1,sipCode:-1,sipReason:""}),r.hangup(null,{reason:t,pubOptions:n}))}))}},{key:"createLocalVideoTrack",value:function(e){var t=this;return new Promise((function(n,r){He.createLocalVideoTrack(t.client,e).then((function(e){t.localTracks.push(e),t._callOnEvent("addlocalstream",e.webrtc.localStream,e),t._callOnEvent("addlocaltrack",e),t.room?t.room.publish(e).then((function(){})).catch((function(e){console.log("publish Local Video Track ERROR: ",e)})):t._joinRoom().then((function(t){t.publish(e).then((function(){})).catch((function(e){console.log("publish Local Video Track ERROR: ",e)}))})).catch((function(){})),n()})).catch((function(e){r(e)}))}))}},{key:"startShareScreen",value:function(){var e=this;He.createLocalVideoTrack(e.client,{audio:!0,video:!0,screen:!0,videoDimensions:null,videoDeviceId:null}).then((function(t){e.localTracks.push(t),console.log("=====websdk addlocalstream share screen=====",t.webrtc.localStream),console.log("=====websdk addlocaltrack share screen=====",t),e._callOnEvent("addlocalstream",t.webrtc.localStream,t),e._callOnEvent("addlocaltrack",t),e.room.publish(t).then((function(){})).catch((function(e){console.log("publish Local Video Track ERROR: ",e)}))})).catch((function(e){console.log("create Local Video Track ERROR: ",e)}))}},{key:"_subscribe",value:function(e){var t=this;t.room.subscribe(e.serverId,{audio:!0,video:!0}).then((function(e){t.subscribedTracks.push(e),e.on("ready",(function(){t._callOnEvent("addremotestream",e.webrtc.remoteStream,e),t._callOnEvent("addremotetrack",e)})),e.on("mediastate",(function(e){t._callOnEvent("mediastate",e)}))})).catch((function(e){console.log("subscribe ERROR: ",e)}))}},{key:"_unsubscribe",value:function(e){this.room.unsubscribe(e)}},{key:"on",value:function(e,t){this._onMethods.put(e,t)}},{key:"_callOnEvent",value:function(e){var t=this._onMethods.get(e);if(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];r?t.call.apply(t,[this].concat(r)):t.call(this)}else console.log("Please implement StringeeCall event: "+e)}},{key:"onRemove",value:function(){var e=this.client._stringeeCalls2.indexOf(this);e>-1&&this.client._stringeeCalls2.splice(e,1),this._clearCallCheckExistInterval()}},{key:"mute",value:function(e){this.localTracks.forEach((function(t){t.mute(e)})),this.muted=e}},{key:"enableLocalVideo",value:function(e){var t=this;t.localTracks.forEach((function(n){n.screen||n.enableLocalVideo(e)&&(t.localVideoEnabled=e)}))}},{key:"sendDtmf",value:function(e,t){var n=this,r={callId:this.callId,digits:e};this.client._sendMessage(s.CALL2_DTMF,r,(function(e){t&&t.call(n,{r:e.r})}))}},{key:"upgradeToVideoCall",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=this;if(!r.isVideoCall){var i=!1;if(r.localTracks.forEach((function(e){e.screen||(i=e.enableLocalVideo(t))&&(r.isVideoCall=t,r.localVideoEnabled=t,r._callOnEvent("addlocaltrack",e))})),e&&i){var o={type:"voice-video-call",isRequest:e,isVideo:t},a={roomId:this.room.roomId,type:t?2:1,msg:o};this.client._sendMessage(s.CALL_UPDATE,a,(function(e){n&&n.call(r,{r:e.r})}))}}}},{key:"_createCallCheckExistInterval",value:function(){this._clearCallCheckExistInterval();var e=this.client,t=this,n={callId:t.callId};this._callCheckExistInterval=setInterval((function(){e._sendMessage(s.CHECK_EXIST_CALL,n,(function(n){if(0==n.r&&!1===n.exist){console.log("== checkExistCall == ",n,"call not exist => stop call");var r=e.findCallByCallId(n.callId);r&&r.hangup(),t._clearCallCheckExistInterval()}else console.log("== checkExistCall == ",n,"call OK => do nothing")}))}),18e4)}},{key:"_clearCallCheckExistInterval",value:function(){console.log("== clearCheckExistCall =="),this._callCheckExistInterval&&clearInterval(this._callCheckExistInterval)}}])&&ze(t.prototype,n),r&&ze(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Qe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var Xe=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,r=[{key:"_callSdpCandidateFromServer",value:function(e,t){var n=e.findCallByCallId(t.callId);if(n)if(n.answeredOnAnotherDevice)console.log("answeredOnAnotherDevice");else{var r=t.deviceId;if(r||(r=""),"sdp"===t.type){var i=t.data.sdp,o=i.match(/a=candidate([^\r\n]+)/g);o&&-1===i.indexOf("typ srflx")&&o.forEach((function(e){var t;t=(t=(t=(t=e.replace(" generation 0","")).replace("typ host","typ srflx")).replace("typ host","typ srflx")).replace("\n",""),t+=" raddr ********** rport 57527 generation 0";var n=i.indexOf(e);n>-1&&(i=[i.slice(0,n),t+"\n",i.slice(n)].join(""))}));var a={type:t.data.type,sdp:i},s=new RTCSessionDescription(a);n._remoteSdps.put(r+"",s),n._pc&&n._onRemoteSDP()}else if("candidate"===t.type){var c=new RTCIceCandidate(t.data),l=n._mapListCandidates.get(r+"");l||(l=new u,n._mapListCandidates.put(r+"",l)),l.push(c),n._checkAndAddCandidateFromQueue()}}else console.log("error could not found call for: "+t.callId)}},{key:"_callStateFromServer",value:function(e,t){var n=e.findCallByCallId(t.callId);if(n){var r=-1,i="",o=t.deviceId;o||(o=""),100===t.code?(r=f,i="Calling"):180===t.code||183===t.code?(r=d,i="Ringing"):486===t.code||603===t.code?(r=v,i="Busy here"):t.code>=400?(r=p,i="Ended"):200===t.code&&(r=h,i="Answered",n._answeredDeviceId=o),t.code>=400?n.ended=!0:200===t.code&&(n.isAnswered=!0),r>-1?n._callOnEvent("signalingstate",{reason:i,code:r,sipCode:t.code,sipReason:t.reason}):console.log("error: unknow code: "+t.code),t.code>=400?(n._freeResource(),n.onRemove()):200===t.code&&(n._pc&&n._onRemoteSDP(),n._checkAndAddCandidateFromQueue())}else console.log("error could not found call for: "+t.callId)}},{key:"_callStopFromServer",value:function(e,t){var n=e.findCallByCallId(t.callId);n?(n.ended=!0,n._freeResource(),n.onRemove(),n._callOnEvent("signalingstate",{reason:"Ended",code:p,sipCode:-1,sipReason:"Bye"})):console.log("error could not found call for: "+t.callId)}},{key:"_callStartFromServer",value:function(e,t){var n=new _e(e,t.fromNumber,t.toNumber,t.video,t.customData);n._iceServers=t.iceServers,n.callId=t.callId,n.isIncomingCall=!0,n.fromAlias=t.fromAlias,n.toAlias=t.toAlias,n.fromInternal=t.fromInternal,n.customDataFromYourServer=t.customDataFromYourServer,n._initPeerConnection(!1),e._callOnEvent("incomingcall",n)}},{key:"_callInfoFromServer",value:function(e,t){var n=e.findCallByCallId(t.callId);n?n._callOnEvent("info",t.info):console.log("error could not found call for: "+t.callId)}},{key:"_videoEndPointSdpFromServer",value:function(e,t){var n=new RTCSessionDescription({type:t.data.type,sdp:t.data.sdp}),r=t.localTrackId,i=t.serverTrackId,o=He.rooms.get(t.roomId);o?o.onReceiveSdp1(r,i,n):console.log("room not found: "+t.roomId)}},{key:"_videoEndPointCandidateFromServer",value:function(e,t){var n=new RTCIceCandidate(JSON.parse(t.data)),r=t.localTrackId,i=t.serverTrackId,o=He.rooms.get(t.roomId);o?o.onReceiveCandidate1(r,i,n):console.log("room not found: "+t.roomId)}},{key:"_videoTrackAddedFromServer",value:function(e,t){var n=He.rooms.get(t.roomId);n?n.onTrackAdded(t):console.log("room not found: "+t.roomId)}},{key:"_videoTrackRemovedFromServer",value:function(e,t){var n=He.rooms.get(t.roomId);n?n.onTrackRemoved(t):console.log("room not found: "+t.roomId)}},{key:"_videoRoomJoinedFromServer",value:function(e,t){var n=He.rooms.get(t.roomId);n?n.onRoomJoined(t):console.log("room not found: "+t.roomId)}},{key:"_videoRoomLeftFromServer",value:function(e,t){var n=He.rooms.get(t.roomId);n?n.onRoomLeft(t):console.log("room not found: "+t.roomId)}},{key:"_videoRoomMsgFromServer",value:function(e,t){var n=He.rooms.get(t.roomId);n?n.onRoomMsg(t):console.log("room not found: "+t.roomId)}},{key:"_videoRoomCallInviteFromServer",value:function(e,t){var n=new Je(e,t.fromNumber,t.toNumber,t.video);n.callId=t.callId,n.isIncomingCall=!0,n.fromAlias=t.fromAlias,n.toAlias=t.toAlias,n.customDataFromYourServer=t.customDataFromYourServer,n.roomToken=t.roomToken,e._callOnEvent("incomingcall2",n)}},{key:"_videoRoomCallInviteCancelFromServer",value:function(t,n){e._call2EndFromServer(t,n)}},{key:"_call2StateFromServer",value:function(e,t){var n=e.findCall2ByCallId(t.callId);if(n){var r=-1,i="",o=t.deviceId;o||(o=""),100===t.code?(r=f,i="Calling"):180===t.code||183===t.code?(r=d,i="Ringing"):486===t.code||603===t.code?(r=v,i="Busy here"):t.code>=400?(r=p,i="Ended"):200===t.code&&(r=h,i="Answered",n._answeredDeviceId=o),t.code>=400?n.ended=!0:200===t.code&&(n.isAnswered=!0),r>-1?n._callOnEvent("signalingstate",{reason:i,code:r,sipCode:t.code,sipReason:t.reason}):console.log("error: unknow code: "+t.code),t.code>=400&&n.onRemove()}else console.log("error could not found call for: "+t.callId)}},{key:"_call2EndFromServer",value:function(e,t){var n=e.findCall2ByCallId(t.callId);if(n){var r=t.deviceId;r||(r="");var i=p;n.ended=!0,n._callOnEvent("signalingstate",{reason:"Ended",code:i,sipCode:t.code,sipReason:t.reason}),n.onRemove(),n.subscribedTracks.forEach((function(e){n.room.unsubscribe(e),e.detachAndRemove()}))}else console.log("error could not found call for: "+t.callId)}},{key:"_videoRoomEnableDisableAudioVideoNotification",value:function(e,t){var n=He.rooms.get(t.roomId);n?n.onTrackChangeAudioVideoEnableDisable(t):console.log("room not found: "+t.roomId)}}],(n=null)&&Qe(t.prototype,n),r&&Qe(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function $e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var Ze=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,r=[{key:"_routeChatToAgent",value:function(e,t){console.log("_routeChatToAgent: "+JSON.stringify(t)),e._callOnEvent("incommingchat",t)}}],(n=null)&&$e(t.prototype,n),r&&$e(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function et(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var tt,nt,rt,it=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.serverAddr=t,this._onMethods=new u,this.userDisconnect=!1,this.socket=null,e.GEN_ID++,this.id=e.GEN_ID,this.alreadyTried=!1}var t,n,r;return t=e,(n=[{key:"clearHealthCheckInterval",value:function(){}},{key:"open",value:function(){this.connect()}},{key:"connect",value:function(){this.alreadyTried?console.log("SSWS: already tried, wait for 3 seconds"):(this.alreadyTried=!0,this.userDisconnect=!1,this.socket&&(this.socket.onopen=null,this.socket.onmessage=null,this.socket.onclose=null,this.socket.onerror=null,this.socket.close(),this.socket=null),this.socket=new WebSocket(this.serverAddr),this.bindSocketEvents(this.socket))}},{key:"bindSocketEvents",value:function(e){var t=this;e.onopen=function(e){var n=t._onMethods.get("connect");n&&n(t,e)},e.onmessage=function(e){var n=t._onMethods.get("EventPacket");n&&n(t,e.data)},e.onclose=function(e){var n=t._onMethods.get("disconnect");n&&n(t,e)},e.onerror=function(n){var r=t._onMethods.get("error");r?r(t,n):console.log("StringeeWebSocket on error",n),e.close()}}},{key:"clearAllOnMethods",value:function(){this._onMethods.clear()}},{key:"disconnect",value:function(){this.userDisconnect=!0,this.socket.close()}},{key:"close",value:function(){this.disconnect()}},{key:"on",value:function(e,t){this._onMethods.put(e,t)}},{key:"send",value:function(e){if(this.socket.readyState===WebSocket.OPEN){var t=JSON.stringify(e);this.socket.send(t)}else console.log("Socket is not open. not sending anything",this.socket.readyState)}},{key:"emit",value:function(e,t){this.send(t)}}])&&et(t.prototype,n),r&&et(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function ot(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function at(e,t,n){return t&&ot(e.prototype,t),n&&ot(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}rt=0,(nt="GEN_ID")in(tt=it)?Object.defineProperty(tt,nt,{value:rt,enumerable:!0,configurable:!0,writable:!0}):tt[nt]=rt;var st=at((function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.conv=t,this.role=n.role,this.user=n.user,this.lastMsgSeqReceived=n.lastMsgSeqReceived,this.lastMsgSeqSeen=n.lastMsgSeqSeen,this.name=n.displayName?n.displayName:"",this.avatar=n.avatarUrl?n.avatarUrl:""})),ct={TEXT:1,PHOTO:2,VIDEO:3,AUDIO:4,FILE:5,LINK:6,CONVERSATION_CREATION:7,CONVERSATION_RENAME:8,CONTACT:10,STICKER:11,NOTIFICATION:100};function lt(e){return(lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ut(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var ft=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.stringeeClient=t,this.messages=new u,this.id=n.convId,this.name=n.convName,this.isGroup=n.isGroup,this.lastUpdate=n.lastUpdate,this.created=n.created,this.unread=n.unread,this.creator=n.creator,this.pinMsgId=n.pinMsgId,this.ended=n.ended;var r=this;if(this.participants=n.participants.map((function(e){return new st(r,e)})),this.lastTimeNewMsg=n.lastTimeNewMsg,this.chatStatus=n.chatStatus,this.channelType=n.channelType,this.resolved=n.resolved,this.lastSeq=n.convLastSeq,n.lastMsg){var i=new vt(this,null);i.content=n.lastMsg,i.type=n.lastMsgType,i.id=n.lastMsgId,i.sender=n.lastMsgSender,i.channelType=n.channelType,i.seq=n.convLastSeq,this.lastMessage=i}"customData"in n&&(this.customData=n.customData),"oaId"in n&&(this.oaId=n.oaId)}var t,n,r;return t=e,(n=[{key:"getMessages",value:function(e,t,n,r){var i=this;return new Promise((function(o,a){if(!i.id||n<=0||t<0)a({r:-1,msg:"Params are invalid."});else if(i.stringeeClient.hasConnected){var s={seqGreater:e,seqSmaller:t,limit:n,sort:r,convId:i.id};i.stringeeClient._sendMessage(StringeeServiceType.CHAT_MESSAGES_LOAD,s,(function(e){var t=e.r;if(0==t){var n=e.msgs;null!=n&&(n=n.map((function(e){var t=new vt(i,e);return i.addMsg(t,!1),t}))),o(n)}else a({r:t,msg:e.message})}))}else a({r:-2,msg:"StringeeClient is not connected to Stringee server."})}))}},{key:"addMsg",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=this;r.messages.put(e.id,e),t&&e.sender!=r.stringeeClient.userId&&(n&&r.unread++,r.markParticipantRead(e)),(null==r.lastMessage||r.lastMessage.seq<e.seq)&&(r.lastMessage=e,r.lastSeq=e.seq)}},{key:"sendMessage",value:function(e,t){var n=this;return new Promise((function(r,i){if(n.stringeeClient.hasConnected&&null!=n.stringeeClient.userId)if("object"===lt(t)){var o=n.stringeeClient.userId,a=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15),s="web-"+o+"-"+a+"-"+Date.now().toString(),c={message:t,type:e,convId:n.id,localDbId:s};n.stringeeClient._sendMessage(StringeeServiceType.CHAT_MESSAGE,c,(function(a){var s=a.r;if(0==s){var c=new vt(n,null);c.created=a.created,c.id=a.msgId,c.channelType=a.channelType,c.localDbId=a.localDbId,c.seq=a.seq,c.content=t,c.type=e,c.sender=o,n.addMsg(c),r(c)}else i({r:s,msg:a.message})}))}else i({r:-2,msg:"info.message is not Object."});else i({r:-1,msg:"StringeeClient is not connected to Stringee server."})}))}},{key:"markReceived",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=this;return new Promise((function(r,i){if(n.stringeeClient.hasConnected&&null!=n.stringeeClient.userId){var o={lastMsgSeq:e.seq,lastMsgTimestamp:e.created,status:t,convId:n.id};n.stringeeClient._sendMessage(StringeeServiceType.CHAT_MESSAGE_REPORT,o,(function(e){var t=e.r;0==t?r(e):i({r:t,msg:e.message})}))}else i({r:-1,msg:"StringeeClient is not connected to Stringee server."})}))}},{key:"getMsgBySeq",value:function(e){for(var t=0;t<this.messages.size();t++){var n=this.messages[t].value;if(n.seq===e)return n}return null}},{key:"markParticipantRead",value:function(e){console.log("====markParticipantRead");var t={convId:e.conv.id,from:e.sender,status:2,lastMsgSeq:e.seq};this.stringeeClient._callOnEvent("chatmessagestate",t)}},{key:"markRead",value:function(e){var t=this;if(this.participants.forEach((function(n){var r=0;n.user==t.stringeeClient.userId&&e.seq>n.lastMsgSeqSeen&&(r=n.lastMsgSeqSeen,n.lastMsgSeqReceived=e.seq,n.lastMsgSeqSeen=e.seq,t.stringeeClient._callOnEvent("changemessagestate",{oldLastMsgSeqSeen:r,participant:n,status:"MsgSeen"}))})),e.sender!=t.stringeeClient.userId)return e.seq>=t.lastMessage.seq&&(t.unread=0),this.markReceived(e,2)}},{key:"endChat",value:function(){var e=this,t={convId:this.id};return new Promise((function(n,r){e.stringeeClient._sendMessage(StringeeServiceType.END_CHAT,t,(function(e){var t=e.r;0==t?n(e):r({r:t,msg:e.message})}))}))}},{key:"transfer",value:function(e,t){var n=this,r={convId:this.id,customerId:t.customerId,customerName:t.customerName,toUserId:e};return new Promise((function(e,t){n.stringeeClient._sendMessage(StringeeServiceType.CHAT_TRANSFER_TO_ANOTHER_AGENT,r,(function(n){var r=n.r;0==r?e(n):t({r:r,msg:n.message})}))}))}},{key:"sendChatTranscriptTo",value:function(e,t){var n=this,r={convId:this.id,email:e,domain:t};return new Promise((function(e,t){n.stringeeClient._sendMessage(StringeeServiceType.SEND_EMAIL_TRANSCRIPT,r,(function(n){var r=n.r;0==r?e(n):t({r:r,msg:n.message})}))}))}},{key:"beginTyping",value:function(){var e=this,t={userId:this.stringeeClient.userId,convId:this.id};return new Promise((function(n,r){e.stringeeClient._sendMessage(StringeeServiceType.CHAT_USER_BEGIN_TYPING,t,(function(e){var t=e.r;0==t?n(e):r({r:t,msg:e.message})}))}))}},{key:"endTyping",value:function(){var e=this,t={userId:this.stringeeClient.userId,convId:this.id};return new Promise((function(n,r){e.stringeeClient._sendMessage(StringeeServiceType.CHAT_USER_END_TYPING,t,(function(e){var t=e.r;0==t?n(e):r({r:t,msg:e.message})}))}))}},{key:"joinOaConversation",value:function(){var e=this,t={convId:this.id};return new Promise((function(n,r){e.stringeeClient._sendMessage(StringeeServiceType.CHAT_JOIN_OA_CONVERSATION,t,(function(e){var t=e.r;0==t?n(e):r({r:t,msg:e.message})}))}))}},{key:"addParticipants",value:function(e){var t=this;return new Promise((function(n,r){if(e&&e.length){var i={convId:t.id,userIds:e};t.stringeeClient._sendMessage(StringeeServiceType.CHAT_ADD_PARTICIPANT,i,(function(e){0===e.r?n(e):r(e)}))}else r({r:-1,message:"List of users is empty"})}))}},{key:"removeParticipants",value:function(e){var t=this;return new Promise((function(n,r){if(e&&e.length){var i={convId:t.id,userIds:e};t.stringeeClient._sendMessage(StringeeServiceType.CHAT_REMOVE_PARTICIPANT,i,(function(e){0===e.r?n(e):r(e)}))}else r({r:-1,message:"List of users is empty"})}))}},{key:"updateConversation",value:function(e){var t=this;return new Promise((function(n,r){if(!e||"object"===lt(e)&&0===Object.keys(e).length)r({r:-1,message:"params empty"});else{var i={convId:t.id};e.name&&(i.groupName=e.name),e.avatar&&(i.imageUrl=e.avatar),t.stringeeClient._sendMessage(StringeeServiceType.CHAT_UPDATE_CONVERSATION,i,(function(e){0===e.r?n(e):r(e)}))}}))}},{key:"deleteConversation",value:function(){var e=this;return new Promise((function(t,n){var r={convId:e.id,seq:e.lastSeq};e.stringeeClient._sendMessage(StringeeServiceType.CHAT_DELETE_CONVERSATION,r,(function(e){0===e.r?t(e):n(e)}))}))}},{key:"deleteMessage",value:function(e){var t=this;return new Promise((function(n,r){if(e){var i={convId:t.id,messageIds:[e]};t.stringeeClient._sendMessage(StringeeServiceType.CHAT_DELETE_MESSAGE,i,(function(e){0===e.r?n(e):r(e)}))}else r({r:-1,message:"params empty"})}))}},{key:"clearHistory",value:function(e){var t=this;return new Promise((function(n,r){if(e){var i={convId:t.id,seq:e};t.stringeeClient._sendMessage(StringeeServiceType.CHAT_CONVERSATION_CLEAR_HISTORY,i,(function(e){0===e.r?n(e):r(e)}))}else r({r:-1,message:"params empty"})}))}},{key:"getAttachmentMessages",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:20;return new Promise((function(i,o){if(t.id&&e&&[ct.PHOTO,ct.VIDEO,ct.AUDIO,ct.FILE,ct.LINK].includes(e)&&!(r<=0))if(t.stringeeClient.hasConnected){var a={convId:t.id,msgType:e,start:n,limit:r};t.stringeeClient._sendMessage(StringeeServiceType.CHAT_CONVERSATION_ATTACHMENT,a,(function(e){var n=e.r;if(0==n){var r=e.msgs;null!=r&&(r=r.map((function(e){return new vt(t,e)}))),i(r)}else o({r:n,msg:e.message})}))}else o({r:-2,msg:"StringeeClient is not connected to Stringee server."});else o({r:-1,msg:"Params are invalid."})}))}},{key:"setChatRole",value:function(e,t){var n=this;return new Promise((function(r,i){if(n.id&&e&&["member","admin"].includes(t))if(n.stringeeClient.hasConnected){var o={convId:n.id,userId:e,role:t};n.stringeeClient._sendMessage(StringeeServiceType.CHAT_SET_ROLE,o,(function(e){var t=e.r;0==t?r(e):i({r:t,msg:e.message})}))}else i({r:-2,msg:"StringeeClient is not connected to Stringee server."});else i({r:-1,msg:"Params are invalid."})}))}},{key:"pinMessage",value:function(e){var t=this,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return new Promise((function(r,i){if(t.id&&e)if(t.stringeeClient.hasConnected){var o={convId:t.id,msgId:e,isPin:n};t.stringeeClient._sendMessage(StringeeServiceType.CHAT_PIN_MESSAGE,o,(function(n){var o=n.r;0==o?(t.pinMsgId=e,r(n)):i({r:o,msg:n.message})}))}else i({r:-2,msg:"StringeeClient is not connected to Stringee server."});else i({r:-1,msg:"Params are invalid."})}))}},{key:"getMessagesByIds",value:function(e){var t=this;return new Promise((function(n,r){if(t.id&&Array.isArray(e)&&e.length)if(t.stringeeClient.hasConnected){var i={convId:t.id,msgIds:e.join(",")};t.stringeeClient._sendMessage(StringeeServiceType.GET_MESSAGES_INFO,i,(function(e){var i=e.r;if(0==i){var o=e.listMsgs;null!=o&&(o=o.map((function(e){var n=new vt(t,e);return t.addMsg(n,!1),n}))),n(o)}else r({r:i,msg:e.message})}))}else r({r:-2,msg:"StringeeClient is not connected to Stringee server."});else r({r:-1,message:"List of messages id is empty"})}))}}])&&ut(t.prototype,n),r&&ut(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function dt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function ht(e,t,n){return t&&dt(e.prototype,t),n&&dt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var vt=ht((function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.stringeeClient=t.stringeeClient,this.conv=t,n&&(this.created=n.created,this.localDbId=n.localDbId,this.channelType=n.channelType,this.id=n.id,this.type=n.type,this.sender=n.user,n.senderInfo&&(this.senderInfo=n.senderInfo),this.seq=n.seq,this.content=n.content,this.deletedStatus=n.deletedStatus)}));function pt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var gt=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.stringeeClient=t,this.convId=n.convId,this.customerId=n.customerId,this.customerName=n.customerName,this.channelType=n.channelType,this.type=n.type}var t,n,r;return t=e,(n=[{key:"acceptRequest",value:function(){var e=this;return new Promise((function(t,n){var r={answer:!0,channelType:e.channelType,convId:e.convId},i=s.CHAT_AGENT_RESPONSE;"transfer"===e.type&&(i=s.CHAT_CONFIRM_TRANSFER_REQUEST),e.stringeeClient._sendMessage(i,r,(function(e){var r=e.r;0==r?t(e):n({r:r,msg:e.message})}))}))}},{key:"rejectRequest",value:function(){var e=this;return new Promise((function(t,n){var r={answer:!1,channelType:e.channelType,convId:e.convId},i=s.CHAT_AGENT_RESPONSE;"transfer"===e.type&&(i=s.CHAT_CONFIRM_TRANSFER_REQUEST),e.stringeeClient._sendMessage(i,r,(function(e){var r=e.r;0==r?t(e):n({r:r,msg:e.message})}))}))}}])&&pt(t.prototype,n),r&&pt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function mt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function yt(e,t,n){return t&&mt(e.prototype,t),n&&mt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var _t=yt((function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.userId=t.user,this.name=t.displayName,this.avatar=t.avatarUrl,null!=t.lastMsgSeqReceived&&(this.lastMsgSeqReceived=t.lastMsgSeqReceived),null!=t.lastMsgSeqSeen&&(this.lastMsgSeqSeen=t.lastMsgSeqSeen),this.browser=t.browser,this.device=t.device,this.email=t.email,this.hostname=t.hostname,this.ipaddress=t.ipaddress,this.location=t.location,this.phone=t.phone,this.platform=t.platform,this.useragent=t.useragent}));function St(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Et(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ct=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,r=[{key:"getListConversations",value:function(t,n,r,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null;return new Promise((function(c,l){if(i<=0||r<=0)l({r:-2,msg:"Params are invalid."});else if(t.hasConnected){var u={lastUpdateGreater:n,lastUpdateSmaller:r,limit:i};o&&(u.oaId=o),a&&"ended"in a&&(u.ended=a.ended),a&&"channelTypes"in a&&Array.isArray(a.channelTypes)&&(u.channelTypes=a.channelTypes),t._sendMessage(s.CHAT_CONVERSATION_LOAD,u,(function(n){var r=n.r;if(0==r){var i=n.listConvs;null!=i&&(i=i.map((function(n){var r=new ft(t,n);return e.conversations.put(r.id,r),r}))),c(i)}else l({r:r,msg:n.message})}))}else l({r:-1,msg:"StringeeClient is not connected to Stringee server."})}))}},{key:"getConversationsByIds",value:function(t,n){return new Promise((function(r,i){if(n)if(t.hasConnected){var o={convIds:n.join(",")};t._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,o,(function(n){var o=n.r;if(0==o){var a=n.listConvs;null!=a&&(a=a.map((function(n){var r=new ft(t,n);return e.conversations.put(r.id,r),r}))),r(a)}else i({r:o,msg:n.message})}))}else i({r:-1,message:"StringeeClient is not connected to Stringee server."});else i({r:-1,message:"Conversaion's Id is invalid"})}))}},{key:"init",value:function(t){e.inited=!0,t.on("chatmessage2",(function(n){var r=e.conversations.get(n.convId);if(r){var i,o,a,s=new vt(r,null);s.created=n.createdTime,s.sender=n.from,s.id=n.msgId,s.channelType=n.channelType,s.type=n.type,s.seq=n.seq,s.content=n.message,s.senderInfo={avatar_url:null!==(i=n.avatarUrl)&&void 0!==i?i:"",display_name:null!==(o=n.displayName)&&void 0!==o?o:"",user:null!==(a=n.from)&&void 0!==a?a:""},r.addMsg(s),t._callOnEvent("addchatmessage",s)}else e.getConversationsByIds(t,[n.convId]).then((function(i){var o,a,s;console.log("StringeeChat2.getConversationsByIds",i),r=i[0],e.conversations.put(r.id,r);var c=new vt(r,null);c.created=n.createdTime,c.sender=n.from,c.id=n.msgId,c.channelType=n.channelType,c.type=n.type,c.seq=n.seq,c.content=n.message,c.senderInfo={avatar_url:null!==(o=n.avatarUrl)&&void 0!==o?o:"",display_name:null!==(a=n.displayName)&&void 0!==a?a:"",user:null!==(s=n.from)&&void 0!==s?s:""},r.addMsg(c,!0,!1),t._callOnEvent("addchatmessage",c)})).catch((function(e){console.log(e)}))})),t.on("chatmessagestate2",(function(n){var r=e.conversations.get(n.convId);if(r)for(var i=0;i<r.participants.length;i++){var o=r.participants[i];if(o.user===n.from){if(1==n.status&&n.lastMsgSeq>o.lastMsgSeqReceived){var a=o.lastMsgSeqReceived;o.lastMsgSeqReceived=n.lastMsgSeq,t._callOnEvent("changemessagestate",{oldLastMsgSeqReceived:a,participant:o,type:"MsgReceived"})}else if(2==n.status&&n.lastMsgSeq>o.lastMsgSeqSeen){var s=o.lastMsgSeqSeen;o.lastMsgSeqSeen=n.lastMsgSeq,t._callOnEvent("changemessagestate",{oldLastMsgSeqSeen:s,participant:o,status:"MsgSeen"})}break}}})),t.on("incommingchat2",(function(n){var r={convId:n.convId,customerId:n.customerId,customerName:n.customerName,channelType:n.channelType,type:"normal"},i=e.createChatRequest(t,r);i&&(t._callOnEvent("receiveChatRequest",i),e.chatRequests.put(n.convId,i))})),t.on("transferChatRequest2",(function(n){var r={convId:n.convId,customerId:n.customerId,customerName:n.customerName,channelType:n.channelType,type:"transfer"},i=e.createChatRequest(t,r);i&&(t._callOnEvent("receiveTransferChatRequest",i),e.chatRequests.put(n.convId,i))})),t.on("chatAgentResponse2",(function(t){0===t.r&&e.chatRequests.remove(t.convId)})),t.on("chatConfirmTransferResponse2",(function(t){0===t.r&&e.chatRequests.remove(t.convId)}))}},{key:"createConversation",value:function(e,t,n){var r=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);return new Promise((function(i,o){if(void 0!==t&&null!=t)if(e.hasConnected){var a;a=null==n.name||0===n.name.length?"":n.name;var c={distinct:n.isDistinct,isGroup:n.isGroup,name:a,participants:t,customField:{},localDbId:r};n.customData&&(c.customData=n.customData),n.oaId&&(c.oaId=n.oaId),n.channelType&&(c.channelType=n.channelType),e._sendMessage(s.CHAT_CREATE_CONVERSATION,c,(function(t){var n,r=t.r;if(0==r||2==r){t.convName=a,t.created=Date.now(),t.unread=0,t.creator=e.userId,t.convLastSeq=t.lastMsg.seq;var s=t.lastMsg;t.lastMsg=null;var c=new ft(e,t),l=new vt(c,null);l.content=s.content,l.type=s.type,l.id=s.id,l.seq=s.seq,l.created=s.created,l.sender=s.user,l.channelType=s.channelType,l.localDbId=s.localDbId,l.senderInfo=null!==(n=s.senderInfo)&&void 0!==n?n:{},c.lastMessage=l,i(c)}else o({r:r,msg:t.message})}))}else o({r:-2,message:"StringeeClient is not connected to Stringee server."});else o({r:-1,message:"UserIds are invalid"})}))}},{key:"createLiveChatConversation",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(!n)return new Promise((function(e,t){t()}));var o={isDistinct:!1,isGroup:!0,name:n,customData:r,oaId:i};return e.createConversation(t,[],o)}},{key:"updateUserInfo",value:function(e,t){return new Promise((function(n,r){e.hasConnected?e._sendMessage(s.UPDATE_USER_INFO,t,(function(e){var t=e.r;0==t?n(e):r({r:t,msg:e.message})})):r({r:-2,message:"StringeeClient is not connected to Stringee server."})}))}},{key:"getMessages",value:function(t,n,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"DESC",o=arguments.length>4?arguments[4]:void 0,a=arguments.length>5?arguments[5]:void 0;return new Promise((function(c,l){if(o<=0||r<=0)l({r:-2,msg:"Params are invalid."});else if(t.hasConnected){a=a.join(",");var u={createdGreater:n,createdSmaller:r,sort:i,limit:o,convIds:a};t._sendMessage(s.CHAT_LOAD_ALL_MESSAGES_FOR_CONVERSATIONS,u,(function(t){var n=t.r;if(0==n){var r=t.msgs,i=[];null!=r&&(i=r.map((function(t){var n=e.conversations.get(t.convId);return new vt(n,t)}))),c(i)}else l({r:n,msg:t.message})}))}else l({r:-1,msg:"StringeeClient is not connected to Stringee server."})}))}},{key:"createChatRequest",value:function(e,t){if(!t.convId||!t.channelType)return!1;var n=t.convId,r=t.customerId,i=t.customerName,o=t.channelType,a=t.type;return new gt(e,{convId:n,customerId:r,customerName:i,channelType:o,type:a})}},{key:"loadAllChatRequest",value:function(t){return new Promise((function(n,r){t._sendMessage(s.CHAT_REQUEST_LOAD,{},(function(i){var o=i.r;if(0==o){var a=i.chatRequests,s=[];null!=a&&a.forEach((function(n){if(-1===e.chatRequests.containsKey(n.convId)){var r=new gt(t,n);e.chatRequests.put(n.convId,r),s.push(r)}})),n(s)}else r({r:o,msg:i.message})}))}))}},{key:"getConversationWithUser",value:function(e,t){return new Promise((function(n,r){if(t)if(e.userId&&e.hasConnected){var i={participants:[t,e.userId]};e._sendMessage(s.CHAT_CONVERSATION_FOR_USERS,i,(function(t){if(0==t.r){var i=[];Array.isArray(t.conversations)&&t.conversations.length>0&&(i=t.conversations.map((function(t){return new ft(e,t)}))),n(i)}else r({r:t.r,msg:t.message})}))}else r({r:-1,message:"StringeeClient is not connected to Stringee server"});else r({r:-1,message:"UserId is invalid"})}))}},{key:"getUsersInfo",value:function(e,t,n){if(t&&t.length)if(e.userId&&e.hasConnected){var r="";t.forEach((function(e){0==r.length?r+=e:r+=", "+e}));var i={userIds:r};e._sendMessage(s.CHAT_GET_USERS_INFO,i,(function(t){if(n){var r=t.r,i=0==r,o=i?"Success":"Fail.",a=t.users,s=[];return a.forEach((function(e){var t=new _t(e);t.userId=e.userId,s.push(t)})),void n.call(e,i,r,o,s)}}))}else n.call(e,!1,-1,"StringeeClient is not connected to Stringee server",null);else n.call(e,!1,-2,"UserIds are invalid.",null)}}],(n=null)&&St(t.prototype,n),r&&St(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function It(e){return function(e){if(Array.isArray(e))return Tt(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Tt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Tt(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function bt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Rt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}Et(Ct,"conversations",new u),Et(Ct,"chatRequests",new u),Et(Ct,"inited",!1);var Ot=n(348).default,wt=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;bt(this,e),this.socket=null,this.hasConnected=!1,this.disconnectByUser=!1,this.timeoutToReconnect=0,this.lastTimeStampReceivedPacket=0,this.allClients=[],this.allClientsOfThisBrowser=[],this.apiStringeeBaseUrl="https://api.stringee.com/",n&&(this.apiStringeeBaseUrl=n),this.apiGetStringeeServerAddress=this.apiStringeeBaseUrl+"v2/getStringeeServerAddress",this.masterServer=null,this.stringeeServerAddrs=t,this.stringeeServerAddrIndex=-1,this._stringeeServerAddr=null,this.numberOfRetryConnect=0,this._currentRequestId=1,this._callbacks=new u,this.accessToken="",this._onMethods=new u,this._stringeeCalls=[],this._stringeeCalls2=[],this.ice_servers=[],this.deviceId="web-"+this._genUuid(),this.browserId=localStorage.getItem("stringee_browser_id"),this.browserId||(this.browserId="browser-"+this._genUuid(),localStorage.setItem("stringee_browser_id",this.browserId)),this.sessionId="session-"+this._genUuid(),this.alreadyTried=!1,this.healtcheckInterval=null}var t,n,r;return t=e,(n=[{key:"_cleanHouse",value:function(){this.healtcheckInterval&&clearInterval(this.healtcheckInterval)}},{key:"getClientId",value:function(){return this.deviceId}},{key:"getBrowserId",value:function(){return this.browserId}},{key:"setThisClientIsActive",value:function(e){e?localStorage.setItem("active_client_id",this.deviceId):localStorage.removeItem("active_client_id")}},{key:"isActiveClient",value:function(){var e=localStorage.getItem("active_client_id");return this.deviceId===e}},{key:"findCallByCallId",value:function(e){for(var t=0;t<this._stringeeCalls.length;t++){var n=this._stringeeCalls[t];if(n.callId===e)return n}return null}},{key:"findCall2ByCallId",value:function(e){for(var t=0;t<this._stringeeCalls2.length;t++){var n=this._stringeeCalls2[t];if(n.callId===e)return n}return null}},{key:"_pushCallback",value:function(e,t){if(t){var n=this._callbacks.get(e);n||(n=[]),n.push(t),this._callbacks.put(e,n)}}},{key:"_callCallback",value:function(e,t){var n=!1,r=this._callbacks.get(e);if(r){var i=r.pop();i&&(i.call(this,t),n=!0)}return n}},{key:"getURLToConnect",value:function(){if(null!==this.masterServer){console.log("Master server is set ",this.masterServer,". Stop alternating to other server");var e=this.masterServer;return this.masterServer=null,void(this._stringeeServerAddr=e)}if(this.tempWebsocketURL&&0!==this.tempWebsocketURL.length||(this.tempWebsocketURL=It(this.stringeeServerAddrs)),this._stringeeServerAddr&&this.numberOfRetryConnect<=2)return this._stringeeServerAddr;this._stringeeServerAddr=this.tempWebsocketURL.shift(),this.numberOfRetryConnect>2&&(this.numberOfRetryConnect=0)}},{key:"createNewStringeeWebSocket",value:function(e){this.getURLToConnect(),this.socket&&(this.socket.clearAllOnMethods(),this.socket.disconnect(),this.socket=null),this.socket=new it(this._stringeeServerAddr),this.bindSocketEvents(this.socket,e),this.accessToken=e,this.socket.open()}},{key:"createhealthCheckInterval",value:function(){var e=this;this._cleanHouse(),this.healtcheckInterval=setInterval((function(){if(e.disconnectByUser)return console.log("StringeeClient: Socket disconnected by user. Stop trying to connect"),void e._cleanHouse();if(null!=e.socket){if(Date.now()-e.lastTimeStampReceivedPacket>e.timeoutToReconnect){console.log("StringeeClient: Socket is die, reconnect...");var t=e.socket._onMethods.get("disconnect");t&&(clearInterval(e.healtcheckInterval),e.socket.clearAllOnMethods(),e.socket.disconnect(),t(e.socket,null))}}else console.log("StringeeClient: Socket is null")}),5e3)}},{key:"bindSocketEvents",value:function(e,t){var n=this;e.on("connect",(function(e,r){n._callOnEvent("connect"),n.numberOfRetryConnect=0;var i=screen.width,o=screen.height,a=localStorage.getItem("stringee_browser_id");a||(a="browser-"+n._genUuid(),localStorage.setItem("stringee_browser_id",a));var c={accesstoken:t,deviceId:n.deviceId,browserId:n.browserId,sessionId:n.sessionId,platform:3,platformVersion:"",deviceName:navigator.userAgent,screenSize:i+"x"+o,sdkVersion:"2.6.0"};n._sendMessage(s.AUTHEN,c,(function(e){0===e.r?(n.ice_servers=e.ice_servers,n.hasConnected=!0,e.clients.forEach((function(e){n.allClients.push(e.clientId),e.browserId===n.getBrowserId()&&n.allClientsOfThisBrowser.push(e.clientId),n.allClients.sort(),n.allClientsOfThisBrowser.sort()})),n.userId=e.userId,n.timeoutToReconnect=e.ping_after_ms+15e3,void 0!==e.chatAgent&&e.chatAgent&&Ct.loadAllChatRequest(n).then((function(e){e.forEach((function(e){"normal"===e.type?n._callOnEvent("receiveChatRequest",e):"transfer"===e.type&&n._callOnEvent("receiveTransferChatRequest",e)}))})),n.createhealthCheckInterval()):n.hasConnected=!1,15!==e.r?(n._callOnEvent("authen",e),6===e.r&&n._callOnEvent("requestnewtoken")):(console.log("CURRENT_SERVER_IS_SLAVE, please change to: "+e.change_to_server),e.change_to_server?(n.masterServer=e.change_to_server,n.socket.disconnect(),this.numberOfRetryConnect=0):setTimeout((function(){n.socket.disconnect()}),2e3)),0!==e.r&&15!==e.r&&n.disconnect()}))})),e.on("disconnect",(function(e,t){if(n.hasConnected=!1,!n.disconnectByUser){n.numberOfRetryConnect++,n.socket&&n.socket.disconnect();var r=500*n.numberOfRetryConnect;setTimeout((function(){this.disconnectByUser||n.createNewStringeeWebSocket(n.accessToken)}),r)}n._callOnEvent("disconnect")})),e.on("error",(function(e,t){})),e.on("EventPacket",(function(e,t){try{var r=JSON.parse(t);if(!r||!r.service)return void console.log("could not decode data, error=1, ",t);try{n._packetReceived(r.service,r.body)}catch(e){console.log("error when process packet",e)}}catch(e){console.log("could not decode data, error=2, ",t)}}))}},{key:"getStringeeServerAddrFromRestAPI",value:function(e){var t=this;Ot({method:"get",url:t.apiGetStringeeServerAddress,params:{access_token:e},responseType:"json"}).then((function(n){n.data&&0===n.data.r?(t.stringeeServerAddrs=[],n.data.data.stringeeServerAddrs.web.forEach((function(e){t.stringeeServerAddrs.push("wss://"+e.ip+":"+e.port)})),t.createNewStringeeWebSocket(e)):n.data&&Number.isInteger(n.data.r)?t._callOnEvent("authen",{r:-11,msg:"Access token invalid or Key SID not found"}):setTimeout((function(){t.disconnectByUser||t.getStringeeServerAddrFromRestAPI(e)}),2e3)})).catch((function(n){n.response.data?t._callOnEvent("authen",{r:-12,msg:"Access token invalid or Key SID not found"}):setTimeout((function(){t.disconnectByUser||t.getStringeeServerAddrFromRestAPI(e)}),2e3)}))}},{key:"connect",value:function(e){this.alreadyTried?console.log("StringeeClient: There is a recent attempt to connect to "+this._stringeeServerAddr+". Please wait"):(this.alreadyTried=!0,this.disconnectByUser=!1,0===this.stringeeServerAddrs.length?this.getStringeeServerAddrFromRestAPI(e):this.createNewStringeeWebSocket(e))}},{key:"disconnect",value:function(){this._cleanHouse(),this.alreadyTried=!1,this.tempWebsocketURL=null,this.disconnectByUser=!0,this.socket&&(this.socket.close(),this.socket=null)}},{key:"sendCustomMessage",value:function(e,t,n){var r=this,i={toUser:e,message:t};this._sendMessage(s.CUSTOM_MESSAGE,i,(function(e){n&&n.call(r,e)}))}},{key:"changeAttribute",value:function(e,t,n){var r=this,i={attribute:e,value:t};this._sendMessage(s.CHANGE_ATTRIBUTE,i,(function(e){n&&n.call(r,e)}))}},{key:"_sendMessage",value:function(e,t,n){if(t.requestId||(t.requestId=this._currentRequestId),n){var r="packet_"+e+"_"+t.requestId;this._pushCallback(r,n)}var i={service:e,body:t};this.socket.emit("EventPacket",i),this._currentRequestId++}},{key:"_packetReceived",value:function(e,t){var n,r=!1;if(this.lastTimeStampReceivedPacket=Date.now(),t.requestId){var i="packet_"+e+"_"+t.requestId;n=this._callCallback(i,t)}e===s.CALL_SDP_CANDIDATE_FROM_SERVER?Xe._callSdpCandidateFromServer(this,t):e===s.CALL_STATE_FROM_SERVER?Xe._callStateFromServer(this,t):e===s.CALL_STOP_FROM_SERVER?Xe._callStopFromServer(this,t):e===s.CALL_START_FROM_SERVER?Xe._callStartFromServer(this,t):e===s.PING?this._sendMessage(s.PING,{}):e===s.CALL_INFO_FROM_SERVER?Xe._callInfoFromServer(this,t):e===s.MSG_FROM_OTHER_DEVICE?this._msgFromOtherDevice(t):e===s.CUSTOM_MESSAGE_FROM_SERVER?this._callOnEvent("custommessage",t):e===s.OTHER_DEVICE_AUTHEN?this._otherDeviceAuthen(t):e===s.CHAT_MESSAGE_FROM_SERVER?(this._callOnEvent("chatmessage",t),Ct.inited&&this._callOnEvent("chatmessage2",t)):e===s.CHAT_MESSAGE_REPORT_FROM_SERVER?(this._callOnEvent("chatmessagestate",t),Ct.inited&&this._callOnEvent("chatmessagestate2",t)):e===s.CHAT_ROUTE_TO_AGENT?(Ze._routeChatToAgent(this,t),Ct.inited&&this._callOnEvent("incommingchat2",t)):e===s.SUBSCRIBE_FROM_SERVER?this._callOnEvent("messagefromtopic",t):e===s.TIMEOUT_ROUTE_TO_QUEUE?this._callOnEvent("timeoutInQueue",t):e===s.TIMEOUT_ROUTE_TO_AGENT?this._callOnEvent("timeoutAnswerChat",t):e===s.CHAT_PIN_MESSAGE_FROM_SERVER?this._callOnEvent("pinMsgFromServer",t):e===s.CHAT_EDIT_MESSAGE_FROM_SERVER?this._callOnEvent("editMsgFromServer",t):e===s.CHAT_REVOKE_MESSAGE_FROM_SERVER?this._callOnEvent("revokeMsgFromServer",t):e===s.CHAT_REMOVE_PARTICIPANT_FROM_SERVER?this._callOnEvent("removeParticipantFromServer",t):e===s.CHAT_ADD_PARTICIPANT_FROM_SERVER?this._callOnEvent("addParticipantFromServer",t):e===s.CHAT_USER_BEGIN_TYPING_NOTIFICATION?this._callOnEvent("userBeginTypingListener",t):e===s.CHAT_USER_END_TYPING_NOTIFIACTION?this._callOnEvent("userEndTypingListener",t):e===s.UPDATE_USER_INFO_NOTIFICATION?this._callOnEvent("updateUserInfoMsg",t):e===s.CHAT_CONVERSATION_ATTACHMENT?this._callOnEvent("chatConversationAttachment",t):e===s.CHAT_AGENT_RESPONSE?(this._callOnEvent("chatAgentResponse",t),Ct.inited&&this._callOnEvent("chatAgentResponse2",t)):e===s.CHAT_CONFIRM_TRANSFER_REQUEST?(this._callOnEvent("chatConfirmTransferResponse",t),Ct.inited&&this._callOnEvent("chatConfirmTransferResponse2",t)):e===s.END_CHAT?this._callOnEvent("endChat",t):e===s.CHAT_CONVERSATION_ENDED_NOTIFICATION?this._callOnEvent("conversationEnded",t):e===s.CHAT_TRANSFER_REQUEST_FROM_ANOTHER_AGENT?(this._callOnEvent("transferChatRequest",t),Ct.inited&&this._callOnEvent("transferChatRequest2",t)):e===s.CHAT_LOAD_ALL_MESSAGES_FOR_CONVERSATIONS?this._callOnEvent("loadAllChatMessages",t):e===s.VIDEO_ENDPOINT_SDP_FROM_SERVER?Xe._videoEndPointSdpFromServer(this,t):e===s.VIDEO_ENDPOINT_CANDIDATE_FROM_SERVER?Xe._videoEndPointCandidateFromServer(this,t):e===s.VIDEO_TRACK_ADDED_FROM_SERVER?Xe._videoTrackAddedFromServer(this,t):e===s.VIDEO_TRACK_REMOVED_FROM_SERVER?Xe._videoTrackRemovedFromServer(this,t):e===s.VIDEO_ROOM_JOINED_FROM_SERVER?Xe._videoRoomJoinedFromServer(this,t):e===s.VIDEO_ROOM_LEFT_FROM_SERVER?Xe._videoRoomLeftFromServer(this,t):e===s.VIDEO_ROOM_MSG_FROM_SERVER?Xe._videoRoomMsgFromServer(this,t):e===s.VIDEO_ROOM_CALL_INVITE?Xe._videoRoomCallInviteFromServer(this,t):e===s.VIDEO_ROOM_CALL_INVITE_CANCEL?Xe._videoRoomCallInviteCancelFromServer(this,t):e===s.VIDEO_ROOM_CALL_STATE_FROM_SERVER?Xe._call2StateFromServer(this,t):e===s.VIDEO_ROOM_END_CALL_FROM_SERVER?Xe._call2EndFromServer(this,t):e===s.VIDEO_ROOM_ENABLE_DISABLE_AUDIO_VIDEO_NOTIFICATION?Xe._videoRoomEnableDisableAudioVideoNotification(this,t):e===s.CALL_UPDATE_FROM_SERVER?this._callOnEvent("callUpdateFromServer",t):r=!0,n&&(r=!1),r&&console.log("===Packet received: service="+e+"; body="+JSON.stringify(t))}},{key:"_otherDeviceAuthen",value:function(e){if("disconnected"===e.status){for(var t=0;t<this.allClients.length;t++)this.allClients[t]===e.clientId&&this.allClients.splice(t,1);if(e.browserId===this.getBrowserId())for(t=0;t<this.allClientsOfThisBrowser.length;t++)this.allClientsOfThisBrowser[t]===e.clientId&&this.allClientsOfThisBrowser.splice(t,1)}else this.allClients.push(e.clientId),e.browserId===this.getBrowserId()&&this.allClientsOfThisBrowser.push(e.clientId),this.allClients.sort(),this.allClientsOfThisBrowser.sort();this._callOnEvent("otherdeviceauthen",e)}},{key:"_msgFromOtherDevice",value:function(e){var t=e.data;if("CALL_STATE"===e.type)(n=this.findCallByCallId(t.callId))&&(200===t.code&&(n.answeredOnAnotherDevice=!0),n._callOnEvent("otherdevice",{type:"CALL_STATE",code:t.code}),486===t.code||603===t.code?(n.ended=!0,n._freeResource(),n.onRemove()):200===t.code&&n._freeResource());else if("CALL_END"===e.type)(n=this.findCallByCallId(t.callId))&&(n.ended=!0,n._callOnEvent("otherdevice",{type:"CALL_END"}));else if("CALL2_STATE"===e.type){var n;(n=this.findCall2ByCallId(t.callId))&&(200===t.code&&(n.answeredOnAnotherDevice=!0),n._callOnEvent("otherdevice",{type:"CALL2_STATE",code:t.code}),486===t.code||603===t.code?(n.ended=!0,n.onRemove()):200===t.code&&(n.isAnswered=!0,n.onRemove()))}else console.log("===_msgFromOtherDevice: body="+JSON.stringify(e))}},{key:"sendChatOneToOneMsg",value:function(){}},{key:"on",value:function(e,t){this._onMethods.put(e,t)}},{key:"_callOnEvent",value:function(e,t){var n=this._onMethods.get(e);n?t?n.call(this,t):n.call(this):console.log("Please implement StringeeClient event: "+e)}},{key:"_genUuid",value:function(){function e(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()}},{key:"setApiStringeeBaseUrl",value:function(e){this.apiStringeeBaseUrl=e}},{key:"getApiStringeeBaseUrl",value:function(){return this.apiStringeeBaseUrl}}])&&Rt(t.prototype,n),r&&Rt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),At=0,kt=1;function Mt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Pt(e,t,n){return t&&Mt(e.prototype,t),n&&Mt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var Dt=Pt((function e(t,n,r,i){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.id=t,this.userId=n,this.body=r,this.callback=i,this.expireTime=Date.now()+36e5})),xt=0,Nt=1,Lt=2,Ft=0,jt=1,Ut=2,Vt=3,Gt=4;function Bt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Ht(e,t,n){return t&&Bt(e.prototype,t),n&&Bt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var qt=Ht((function e(t,n,r,i,o){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),0===n?(this.id=t.id,this.localId=t.localDbId,this.conversationId=t.convId,this.convId=t.convId,this.sender=t.user,this.createdAt=t.created,this.created=t.created,this.sequence=t.seq,this.seq=t.seq,this.type=t.type,this.content=t.content,i>=t.seq?this.state=Gt:r>=t.seq?this.state=Vt:this.state=Ut):1===n?(this.id=t.msgId,this.localId=t.localDbId,this.conversationId=t.convId,this.convId=t.convId,this.sender=t.from,this.createdAt=t.createdTime,this.sequence=t.seq,this.created=t.createdTime,this.seq=t.seq,this.type=t.type,this.content=t.message,this.state=Vt):2===n?(this.id=t.msgId,this.conversationId=t.convId,this.createdAt=t.created,this.sequence=t.seq,this.convId=t.convId,this.created=t.created,this.seq=t.seq,this.state=Ut):3==n&&(0==o?(this.id=t.lastMsg.id,this.localId="",this.conversationId=t.convId,this.convId=t.convId,this.sender=t.lastMsg.user,this.createdAt=t.lastMsg.created,this.created=t.lastMsg.created,this.sequence=1,this.seq=1,this.type=t.lastMsg.type,this.content=t.lastMsg.content,this.state=Gt):1==o?(this.id=null!=t.lastMsgId?t.lastMsgId:"",this.localId="",this.conversationId=t.convId,this.convId=t.convId,this.sender=t.lastMsgSender,this.createdAt=t.lastTimeNewMsg,this.created=t.lastTimeNewMsg,this.sequence=t.lastMsgSeqReceived,this.seq=t.lastMsgSeqReceived,this.type=t.lastMsgType,this.content=t.lastMsg,t.lastMsgSeqReceived>t.lastMsgSeqSeen?this.state=Vt:this.state=Gt):2==o&&(this.id=null!=t.lastMsgId?t.lastMsgId:"",this.localId="",this.conversationId=t.convId,this.convId=t.convId,this.sender=null!=t.lastMsgSender?t.lastMsgSender:"",this.createdAt=t.lastTimeNewMsg,this.created=t.lastTimeNewMsg,this.sequence=t.lastMsgSeqReceived,this.seq=t.lastMsgSeqReceived,this.type=t.lastMsgType,this.content=t.lastMsg,t.lastMsgSeqReceived>t.lastMsgSeqSeen?this.state=Vt:this.state=Gt))}));function Wt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Yt(e,t,n){return t&&Wt(e.prototype,t),n&&Wt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var Kt=Yt((function e(t,n){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),0===n){this.id=t.convId,this.name=t.lastMsg.content.groupName,this.isGroup=t.isGroup,this.updatedAt=t.lastUpdate,this.unreadCount=0,this.creator=t.lastMsg.content.creator,this.created=t.lastTimeNewMsg,this.pinMsgId=t.pinMsgId;var r=[];t.participants.forEach((function(e){var t=new _t(e);r.push(t)})),this.participants=r,this.lastMessage=new qt(t,3,0,0,n)}else if(1===n){this.id=t.convId,this.name=t.convName,this.isGroup=t.isGroup,this.updatedAt=t.lastUpdate,this.unreadCount=t.unread,this.creator=t.creator,this.created=t.created,this.pinMsgId=t.pinMsgId;r=[];t.participants.forEach((function(e){var t=new _t(e);r.push(t)})),this.participants=r,this.lastMessage=new qt(t,3,0,0,n)}else{this.id=t.convId,this.name=t.info.name,this.isGroup=t.info.isGroup,this.updatedAt=t.lastUpdate,this.unreadCount=0,this.creator=t.info.creator,this.created=t.info.created,this.pinMsgId=t.pinMsgId;r=[];t.participants.forEach((function(e){var t=new _t(e);r.push(t)})),this.participants=r,this.lastMessage=new qt(t,3,0,0,n)}}));function zt(e){return(zt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Jt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Qt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Jt(Object(n),!0).forEach((function(t){Xt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Jt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Xt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var Zt=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=this;n.realTimeEvent="onObjectChange",n.pinMsgFromServerEvent="pinMsgFromServer",n.editMsgFromServerEvent="editMsgFromServer",n.userBeginTypingEvent="userBeginTypingListener",n.userEndTypingEvent="userEndTypingListener",n.revokeMsgFromServerEvent="revokeMsgFromServer",n.removeParticipantFromServerEvent="removeParticipantFromServer",n.addParticipantFromServerEvent="addParticipantFromServer",n.created=!1,n.client=t,n._onMethods=new u,n.trackingMap=new u,n.msgQueue=[],setInterval((function(){var e=Date.now();n.msgQueue=n.msgQueue.filter((function(t){return t.expireTime>e}))}),1e3),n.setupChatClient(n)}var t,n,r;return t=e,(n=[{key:"setupChatClient",value:function(e){e.client.on("authensuccess",(function(t){e.sendMessageQueue()})),e.client.on("chatmessage",(function(t){var n=new qt(t,1),r={lastMsgSeq:n.sequence,lastMsgTimestamp:n.createdAt,status:1,convId:n.conversationId};e.client._sendMessage(s.CHAT_MESSAGE_REPORT,r,(function(e){})),e.trackMsg(n),e.fireObjectChangeEvent(kt,[n],xt),null!=n.conversationId&&n.conversationId.length>0&&e.getConversationById(n.conversationId,(function(t,n,r,i){t&&null!=i&&e.fireObjectChangeEvent(At,[i],Nt)}))})),e.client.on("chatmessagestate",(function(t){var n=t.lastMsgSeq,r=t.status,i=(t.convId,e.trackingMap.allValues()),o=[];i.forEach((function(t){if(n>=t.sequence)if(2===r){t.state=Gt,o.push(t);var i=e.keyForMsg(t);e.trackingMap.remove(i)}else 1===r&&t.state!==Gt&&t.state!==Vt&&(t.state=Vt,o.push(t))})),o.length>0&&e.fireObjectChangeEvent(kt,o,Nt)})),e.client.on("pinMsgFromServer",(function(t){var n=t.convId,r=t.msgId,i=t.isPin;e._callOnEvent(e.pinMsgFromServerEvent,{convId:n,msgId:r,isPin:i})})),e.client.on("editMsgFromServer",(function(t){var n=t.convId,r=t.msgId;e._callOnEvent(e.editMsgFromServerEvent,{convId:n,msgId:r})})),e.client.on("revokeMsgFromServer",(function(t){var n=t.convId,r=t.msgIds;e._callOnEvent(e.revokeMsgFromServerEvent,{convId:n,msgIds:r})})),e.client.on("userBeginTypingListener",(function(t){e._callOnEvent(e.userBeginTypingEvent,t)})),e.client.on("userEndTypingListener",(function(t){e._callOnEvent(e.userEndTypingEvent,t)})),e.client.on(e.removeParticipantFromServerEvent,(function(t){e._callOnEvent(e.removeParticipantFromServerEvent,t)})),e.client.on(e.addParticipantFromServerEvent,(function(t){e._callOnEvent(e.addParticipantFromServerEvent,t)}))}},{key:"createConversation",value:function(e,t,n){var r=this;return new Promise((function(i,o){if(r.created)o({r:-1,message:"Could not create conversation, conversation is created"});else{if(void 0===e||null==e||null==e.length||0===e.length)return o({r:-1,message:"UserIds are invalid"}),void n.call(r.client,!1,-2,"UserIds are invalid",null);if(!r.client.hasConnected)return o({r:-1,message:"StringeeClient is not connected to Stringee server."}),void n.call(r.client,!1,-1,"StringeeClient is not connected to Stringee server.",null);var a;a=null==t.name||0===t.name.length?"":t.name;var c={distinct:t.isDistinct,isGroup:t.isGroup,name:a,participants:e};r.client._sendMessage(s.CHAT_CREATE_CONVERSATION,c,(function(e){var t=e.r,a=0===t||2===t;a&&(r.created=!0);var s=a?"Success":null!=e.message&&e.message.length>0?e.message:"Generic error.",c=a?new Kt(e,t):null,l=0===t||2===t?0:e.r;n&&n.call(r.client,a,l,s,c),a||o(t),0===e.r?(e.objectType=At,e.objectChanges=[c],e.changeType=xt,i(e),r.fireObjectChangeEvent(At,[c],xt)):2===e.r&&(e.objectType=At,e.objectChanges=[c],e.changeType=Nt,i(e),r.fireObjectChangeEvent(At,[c],Nt))}))}}))}},{key:"getLastConversations",value:function(e,t,n){var r=this;if(e<=0)n.call(r.client,!1,-2,"Params are invalid.",null);else if(r.client.hasConnected){var i={lastUpdateGreater:0,lastUpdateSmaller:Number.MAX_SAFE_INTEGER,limit:e};r.client._sendMessage(s.CHAT_CONVERSATION_LOAD,i,(function(e){if(n){var i=0===e.r,o=e.r,a=i?"Success":null!=e.message&&e.message.length>0?e.message:"Generic error.",s=e.listConvs;return null!=s&&(s=t?s.reverse().map((function(e){return new Kt(e,1)})):s.map((function(e){return new Kt(e,1)}))),void n.call(r.client,i,o,a,s)}}))}else n.call(r.client,!1,-1,"StringeeClient is not connected to Stringee server.",null)}},{key:"getConversationsAfter",value:function(e,t,n,r){var i=this;if(t<=0||e<=0)r.call(i.client,!1,-2,"Params are invalid.",null);else if(i.client.hasConnected){var o={lastUpdateGreater:e,lastUpdateSmaller:Number.MAX_SAFE_INTEGER,limit:t};i.client._sendMessage(s.CHAT_CONVERSATION_LOAD,o,(function(e){if(r){var t=0===e.r,o=e.r,a=t?"Success":null!=e.message&&e.message.length>0?e.message:"Generic error.",s=e.listConvs;return null!=s&&(s=n?s.reverse().map((function(e){return new Kt(e,1)})):s.map((function(e){return new Kt(e,1)}))),void r.call(i.client,t,o,a,s)}}))}else r.call(i.client,!1,-1,"StringeeClient is not connected to Stringee server.",null)}},{key:"getConversationsBefore",value:function(e,t,n,r){var i=this;if(t<=0||e<=0)r.call(i.client,!1,-2,"Params are invalid.",null);else if(i.client.hasConnected){var o={lastUpdateGreater:0,lastUpdateSmaller:e,limit:t};i.client._sendMessage(s.CHAT_CONVERSATION_LOAD,o,(function(e){if(r){var t=0===e.r,o=e.r,a=t?"Success":null!=e.message&&e.message.length>0?e.message:"Generic error.",s=e.listConvs;return null!=s&&(s=n?s.reverse().map((function(e){return new Kt(e,1)})):s.map((function(e){return new Kt(e,1)}))),void r.call(i.client,t,o,a,s)}}))}else r.call(i.client,!1,-1,"StringeeClient is not connected to Stringee server.",null)}},{key:"getListConversations",value:function(e,t,n){var r=this;return new Promise((function(i,o){if(n<=0||t<=0)o({r:-2,msg:"Params are invalid."});else if(r.client.hasConnected){var a={lastUpdateGreater:e,lastUpdateSmaller:t,limit:n};r.client._sendMessage(s.CHAT_CONVERSATION_LOAD,a,(function(e){var t=e.r;if(0==t){var n=e.listConvs;null!=n&&(n=n.map((function(e){return new Kt(e,1)}))),i(n)}else o({r:t,msg:e.message})}))}else o({r:-1,msg:"StringeeClient is not connected to Stringee server."})}))}},{key:"getConversationById",value:function(e,t){var n=this;return new Promise((function(r,i){if(!e)return i({r:-1,message:"Conversaion's Id is invalid"}),void t.call(n.client,!1,-2,"Conversaion's Id is invalid",conv);if(!n.client.hasConnected)return i({r:-1,message:"StringeeClient is not connected to Stringee server."}),void t.call(n.client,!1,-2,"StringeeClient is not connected to Stringee server.",conv);var o={convIds:e};n.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,o,(function(e){var o,a=e.r,s=0===a,c=e.r,l=s?"Success":null!=e.message&&e.message.length>0?e.message:"Generic error.",u=e.listConvs;null!=u&&u.length>0&&(o=new Kt(u[0],1)),t&&t.call(n.client,s,c,l,o),0===a?r(Qt(Qt({},e),{},{conversation:o})):i(Qt(Qt({},e),{},{conversation:o}))}))}))}},{key:"deleteConversation",value:function(e,t){var n=this;if(e)if(n.client.hasConnected){var r={convIds:e};n.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,r,(function(r){if(t){var i=r.listConvs;if(null!=i&&i.length>0){var o=i[0],a={seq:o.convLastSeq,convId:e};n.client._sendMessage(s.CHAT_DELETE_CONVERSATION,a,(function(e){var r=e.r,i=0===r,a=r,s=i?"Success":null!=e.message&&e.message.length>0?e.message:"Generic error.";if(t.call(n.client,i,a,s),0===e.r){var c=new Kt(o,1);n.fireObjectChangeEvent(At,[c],Lt)}}))}else t.call(n.client,!1,-3,"Can not get conversation's info")}}))}else t.call(n.client,!1,-1,"StringeeClient is not connected to Stringee server.");else t.call(n.client,!1,-2,"Conversaion's Id is invalid")}},{key:"updateConversation",value:function(e,t,n){var r=this;if(e)if(r.client.hasConnected){var i={convId:e,groupName:t.name,imageUrl:t.avatar};r.client._sendMessage(s.CHAT_UPDATE_CONVERSATION,i,(function(t){if(n){var i=0===t.r,o=t.r,a=i?"Success":null!=t.message&&t.message.length>0?t.message:"Generic error.";n.call(r.client,i,o,a)}if(0===t.r){var c={convIds:e};r.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,c,(function(e){var t=e.listConvs;if(null!=t&&t.length>0){var n=t[0],i=new Kt(n,1);r.fireObjectChangeEvent(At,[i],Nt)}}))}}))}else n.call(r.client,!1,-1,"StringeeClient is not connected to Stringee server.");else n.call(r.client,!1,-2,"Conversation's Id is invalid")}},{key:"addParticipants",value:function(e,t,n){var r=this;return new Promise((function(i,o){if(!e)return o({r:-1,message:"Conversation ID is invalid"}),void n.call(r.client,!1,-2,"Params are invalid.",null);if(!t||!t.length)return o({r:-1,message:"List of users is empty"}),void n.call(r.client,!1,-2,"Params are invalid.",null);if(!r.client.hasConnected)return o({r:-1,message:"StringeeClient is not connected to Stringee server."}),void n.call(r.client,!1,-1,"StringeeClient is not connected to Stringee server.",null);var a={convId:e,userIds:t};r.client._sendMessage(s.CHAT_ADD_PARTICIPANT,a,(function(t){if(n){var a=t.r,c=0===a,l=a,u=c?"Success":null!=t.message&&t.message.length>0?t.message:"Generic error.",f=t.added;null!=f&&(f=f.map((function(e){return new _t(e)}))),n.call(r.client,c,l,u,f)}if(0===t.r){var d={convIds:e};r.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,d,(function(e){var t=e.listConvs;if(null!=t&&t.length>0){var n=t[0],i=new Kt(n,1);r.fireObjectChangeEvent(At,[i],Nt)}})),i(t)}else o(t)}))}))}},{key:"removeParticipants",value:function(e,t,n){var r=this;return new Promise((function(i,o){if(!e)return n.call(r.client,!1,-2,"Params are invalid.",null),void o({r:-1,message:"Conversation ID is not valid"});if(!t||!t.length)return n.call(r.client,!1,-2,"Params are invalid.",null),void o({r:-1,message:"list of user is empty"});if(!r.client.hasConnected)return n.call(r.client,!1,-1,"StringeeClient is not connected to Stringee server.",null),void o({r:-1,message:"StringeeClient is not connected to Stringee server."});var a={convId:e,userIds:t};r.client._sendMessage(s.CHAT_REMOVE_PARTICIPANT,a,(function(t){if(n){var a=t.r,c=0===a,l=a,u=c?"Success":null!=t.message&&t.message.length>0?t.message:"Generic error.",f=t.removed;null!=f&&(f=f.map((function(e){return new _t(e)}))),n.call(r.client,c,l,u,f)}if(0===t.r){var d={convIds:e};r.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,d,(function(e){var t=e.listConvs;if(null!=t&&t.length>0){var n=t[0],i=new Kt(n,1);r.fireObjectChangeEvent(At,[i],Nt)}})),0===t.r?i(t):o(t)}}))}))}},{key:"sendMessage",value:function(e,t){var n=this;if(e.convId)if(n.client.hasConnected&&null!=n.client.userId)if("object"===zt(e.message)){var r=n.client.userId,i=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15),o="web-"+r+"-"+i+"-"+Date.now().toString(),a={message:e.message,type:e.type,convId:e.convId,localDbId:o},c=new Dt(o,r,a,t);n.msgQueue.push(c),console.log("start sending message");var l=new qt({},3);l.id=null,l.localId=o,l.conversationId=e.convId,l.sender=r,l.createdAt=Date.now(),l.type=e.type,l.content=e.message,l.state=Ft,n.trackMsg(l),n.fireObjectChangeEvent(kt,[l],xt),l.state=jt,n.fireObjectChangeEvent(kt,[l],Nt),console.log("requesting send message"),n.client._sendMessage(s.CHAT_MESSAGE,a,(function(i){console.log("on chat message sent",i);var a=i.r,s=0===a,c=null;if(t&&9!==a){var l=i.r,u=s?"Success":null!=i.message&&i.message.length>0?i.message:"Generic error.";(c=new qt(i,2)).localId=o,c.type=e.type,c.content=e.message,c.sender=r,t.call(n.client,s,l,u,c)}var f=i.localDbId;if(null!=f&&s&&(n.msgQueue=n.msgQueue.filter((function(e){return e.id!==f}))),null!=c){var d=n.keyForMsg(c);n.trackingMap.remove(d),n.trackingMap.put(d,c),n.fireObjectChangeEvent(kt,[c],Nt)}}))}else t.call(n.client,!1,-100,"info.message is not Object.");else t.call(n.client,!1,-1,"StringeeClient is not connected to Stringee server.");else t.call(n.client,!1,-2,"Conversation's Id is invalid")}},{key:"sendMsg",value:function(e){var t=this;return new Promise((function(n,r){if(e.convId)if(t.client.hasConnected&&null!=t.client.userId)if("object"===zt(e.message)){var i=t.client.userId,o="web-"+i+"-"+(Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15))+"-"+Date.now().toString(),a={message:e.message,type:e.type,convId:e.convId,localDbId:o},c=new qt({},3);c.id=null,c.localId=o,c.convId=e.convId,c.sender=i,c.created=Date.now(),c.type=e.type,c.content=e.message,t.client._sendMessage(s.CHAT_MESSAGE,a,(function(e){var t=e.r;console.log("res from server: "+JSON.stringify(e)),0==t?(c.id=e.msgId,c.seq=e.seq,c.created=e.created,n(c)):r({r:t,msg:e.message})}))}else r({r:-3,msg:"info.message is not Object."});else r({r:-2,msg:"StringeeClient is not connected to Stringee server."});else r({r:-1,msg:"Conversation's Id is invalid"})}))}},{key:"sendMessageQueue",value:function(){var e=this;if(e.msgQueue.length){var t=e.msgQueue[0];if(t.userId!=e.client.userId&&null!=e.client.userId)return t.callback&&t.callback.call(e.client,!1,-3,"Generic error.",null),void e.msgQueue.shift();e.client._sendMessage(s.CHAT_MESSAGE,t.body,(function(n){var r=n.r,i=0==r,o=n.r,a=i?"Success":null!=n.message&&n.message.length>0?n.message:"Generic error.",s=n.localDbId,c=null;if(i&&((c=new qt(n,2)).localId=s,c.type=t.body.type,c.content=t.body.message,c.sender=t.userId),t.callback&&9!=r&&t.callback.call(e.client,i,o,a,c),null!=s&&i&&(e.msgQueue=e.msgQueue.filter((function(e){return e.id!=s})),e.sendMessageQueue()),null!=c){var l=stringeeChat.keyForMsg(c);e.trackingMap.remove(l),e.trackingMap.put(l,c),e.fireObjectChangeEvent(kt,[c],Nt)}}))}}},{key:"loadChatMessages",value:function(e,t){var n=this,r={seqGreater:e.seqGreater,limit:e.limit,sort:e.sort,convId:e.convId};n.client._sendMessage(s.CHAT_MESSAGES_LOAD,r,(function(e){t&&t.call(n.client,e)}))}},{key:"loadAllChatMessages",value:function(e,t){var n=this,r=e.createdGreater,i=e.createdSmaller,o=e.limit,a=e.sort,c=e.convIds;if(Array.isArray(c)){var l={createdGreater:r,createdSmaller:i,sort:a,limit:o,convIds:c=c.join(",")};return new Promise((function(e,r){n.client._sendMessage(s.CHAT_LOAD_ALL_MESSAGES_FOR_CONVERSATIONS,l,(function(r){console.log("got data from server, ",r),t&&(console.log("got a valid callback"),t.call(n.client,r)),e(r)}))}))}console.log("must be an array")}},{key:"getListMessages",value:function(e,t,n,r,i){var o=this;return new Promise((function(a,c){if(!e||r<=0||n<0)c({r:-1,msg:"Params are invalid."});else if(o.client.hasConnected){var l={seqGreater:t,seqSmaller:n,limit:r,sort:i,convId:e};o.client._sendMessage(s.CHAT_MESSAGES_LOAD,l,(function(t){var n=t.r;if(0==n){var r=t.msgs;if(null!=r){var i={convIds:e};o.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,i,(function(e){var t=e.listConvs,n=0,i=0;if(null!=t&&t.length>0){var s=t[0];n=s.lastMsgSeqReceived,i=s.lastMsgSeqSeen}r=r.reverse().map((function(e){var t=new qt(e,0,n,i);return o.trackMsg(t),t})),a(r)}))}}else c({r:n,msg:t.message})}))}else c({r:-2,msg:"StringeeClient is not connected to Stringee server."})}))}},{key:"getLastMessages",value:function(e,t,n,r){var i=this;if(!e||t<0)r.call(i.client,!1,-2,"Params are invalid.",null);else if(i.client.hasConnected){var o=null!=t?t:50,a={seqGreater:0,seqSmaller:Number.MAX_SAFE_INTEGER,limit:o,sort:"DESC",convId:e};i.client._sendMessage(s.CHAT_MESSAGES_LOAD,a,(function(t){if(r){var o=0===t.r,a=t.r,c=o?"Success":null!=t.message&&t.message.length>0?t.message:"Generic error.",l=t.msgs;if(null!=l){var u={convIds:e};i.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,u,(function(e){var t=e.listConvs,s=0,u=0;if(null!=t&&t.length>0){var f=t[0];s=f.lastMsgSeqReceived,u=f.lastMsgSeqSeen}l=n?l.reverse().map((function(e){var t=new qt(e,0,s,u);return i.trackMsg(t),t})):l.map((function(e){var t=new qt(e,0,s,u);return i.trackMsg(t),t})),r.call(i.client,o,a,c,l)}))}}}))}else r.call(i.client,!1,-1,"StringeeClient is not connected to Stringee server.",null)}},{key:"getMessagesAfter",value:function(e,t,n,r,i){var o=this;if(!e||t<0||n<0)i.call(o.client,!1,-2,"Params are invalid.",null);else if(o.client.hasConnected){var a=null!=n?n:50,c={seqGreater:null!=t?t:0,seqSmaller:Number.MAX_SAFE_INTEGER,limit:a,sort:"ASC",convId:e};o.client._sendMessage(s.CHAT_MESSAGES_LOAD,c,(function(t){if(i){var n=0==t.r,a=t.r,c=n?"Success":null!=t.message&&t.message.length>0?t.message:"Generic error.",l=t.msgs;if(null!=l){var u={convIds:e};o.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,u,(function(e){var t=e.listConvs,s=0,u=0;if(null!=t&&t.length>0){var f=t[0];s=f.lastMsgSeqReceived,u=f.lastMsgSeqSeen}l=r?l.map((function(e){var t=new qt(e,0,s,u);return o.trackMsg(t),t})):l.reverse().map((function(e){var t=new qt(e,0,s,u);return o.trackMsg(t),t})),i.call(o.client,n,a,c,l)}))}}}))}else i.call(o.client,!1,-1,"StringeeClient is not connected to Stringee server.",null)}},{key:"getMessagesBefore",value:function(e,t,n,r,i){var o=this;if(!e||t<0||n<0)i.call(o.client,!1,-2,"Params are invalid.",null);else if(o.client.hasConnected){var a={seqGreater:0,seqSmaller:t,limit:null!=n?n:50,sort:"DESC",convId:e};o.client._sendMessage(s.CHAT_MESSAGES_LOAD,a,(function(t){if(i){var n=0==t.r,a=t.r,c=n?"Success":null!=t.message&&t.message.length>0?t.message:"Generic error.",l=t.msgs;if(null!=l){var u={convIds:e};o.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,u,(function(e){var t=e.listConvs,s=0,u=0;if(null!=t&&t.length>0){var f=t[0];s=f.lastMsgSeqReceived,u=f.lastMsgSeqSeen}l=r?l.reverse().map((function(e){var t=new qt(e,0,s,u);return o.trackMsg(t),t})):l.map((function(e){var t=new qt(e,0,s,u);return o.trackMsg(t),t})),i.call(o.client,n,a,c,l)}))}}}))}else i.call(o.client,!1,-1,"StringeeClient is not connected to Stringee server.",null)}},{key:"deleteMessage",value:function(e,t,n){var r=this;if(e)if(t)if(r.client.hasConnected){var i={msgIds:t,convId:e};r.client._sendMessage(s.CHAT_GET_MESSAGES_INFO,i,(function(i){if(0===i.r){var o=i.listMsgs;if(null!=o&&o.length>0){var a=o[0],c=new qt(a,0),l={convId:e,messageIds:[t]};r.client._sendMessage(s.CHAT_DELETE_MESSAGE,l,(function(e){if(n){var t=e.r,i=0==t,o=t,a=i?"Success":null!=e.message&&e.message.length>0?e.message:"Generic error.";n.call(r.client,i,o,a),r.fireObjectChangeEvent(kt,[c],Lt)}}))}}else n.call(r.client,!1,-3,"Message is not found.")}))}else n.call(r.client,!1,-1,"StringeeClient is not connected to Stringee server.");else n.call(r.client,!1,-2,"Params are invalid.");else n.call(r.client,!1,-2,"Params are invalid.")}},{key:"clearHistory",value:function(e,t){var n=this;if(e.convId)if(n.client.hasConnected){var r={seq:e.sequence,convId:e.convId};n.client._sendMessage(s.CHAT_CONVERSATION_CLEAR_HISTORY,r,(function(e){if(t){var r=e.r,i=0===r,o=i?"Success":"Fail";t.call(n.client,i,r,o)}}))}else t.call(n.client,!1,-1,"StringeeClient is not connected to Stringee server.");else t.call(n.client,!1,-2,"Params are invalid.")}},{key:"markMessageSeen",value:function(e,t){var n=this;if(e.convId)if(n.client.hasConnected){var r={lastMsgSeq:e.sequence,lastMsgTimestamp:e.createdAt,status:2,convId:e.convId};n.client._sendMessage(s.CHAT_MESSAGE_REPORT,r,(function(e){if(t){var r=0==e.r,i=r?0:-3,o=r?"Success":"Generic error.";t.call(n.client,r,i,o)}}))}else t.call(n.client,!1,-1,"StringeeClient is not connected to Stringee server.");else t.call(n.client,!1,-2,"Params are invalid.")}},{key:"markConversationAsRead",value:function(e,t){var n=this;if(e)if(n.client.hasConnected){var r={convIds:e};n.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,r,(function(r){var i=r.listConvs;if(null!=i&&i.length>0){var o=i[0],a={lastMsgSeq:o.convLastSeq,lastMsgTimestamp:o.lastTimeNewMsg,status:2,convId:e};n.client._sendMessage(s.CHAT_MESSAGE_REPORT,a,(function(r){if(t){var i=0==r.r,o=i?0:-4,a=i?"Success":"Generic error.";t.call(n.client,i,o,a)}if(0===r.r){var s=n.trackingMap.allValues(),c=[];s.forEach((function(t){if(t.conversationId===e){t.state=Gt,c.push(t);var r=n.keyForMsg(t);n.trackingMap.remove(r)}})),c.length>0&&n.fireObjectChangeEvent(kt,c,Nt),n.getConversationById(e,(function(e,t,r,i){e&&null!=i&&n.fireObjectChangeEvent(At,[i],Nt)}))}}))}else t&&t.call(n.client,!1,-3,"Can not get conversation's info")}))}else t.call(n.client,!1,-1,"StringeeClient is not connected to Stringee server.");else t.call(n.client,!1,-2,"Conversation's Id is invalid")}},{key:"getUnreadConversationCount",value:function(e){var t=this;t.client.hasConnected?t.client._sendMessage(s.CHAT_UNREAD_CONVERSATION_COUNT,{},(function(n){if(e){var r=0==n.r,i=r?0:-3,o=r?"Success":"Generic error.",a=n.count;e.call(t.client,r,i,o,a)}})):e.call(t.client,!1,-1,"StringeeClient is not connected to Stringee server.",null)}},{key:"getConversationWithUser",value:function(e,t){var n=this;if(e)if(n.client.userId&&n.client.hasConnected){var r={participants:[e,n.client.userId]};n.client._sendMessage(s.CHAT_CONVERSATION_FOR_USERS,r,(function(e){if(t){var r,i=0==e.r,o=i?0:-3,a=i?"Success":"Generic error.",s=e.conversations;if(s.length>0){var c=s[0];r=new Kt(c,1),t.call(n.client,i,o,a,r)}else t.call(n.client,!1,-4,"Conversation is not found.",r)}}))}else t.call(n.client,!1,-1,"StringeeClient is not connected to Stringee server");else t.call(n.client,!1,-2,"UserId is invalid")}},{key:"blockUser",value:function(e,t){var n=this;if(e){var r={userId:e};n.client._sendMessage(s.CHAT_BLOCK_USER,r,(function(e){if(t){var r=e.r,i=0==r,o=i?"Success":"Fail";t.call(n.client,i,r,o)}}))}else t.call(n.client,!1,-1,"UserId is invalid")}},{key:"blockInviteToGroup",value:function(e,t){var n=this;if(e){var r={block_group_invite:e};n.client._sendMessage(s.CHAT_BLOCK_USER,r,(function(e){if(t){var r=e.r,i=0==r,o=i?"Success":"Fail";t.call(n.client,i,r,o)}}))}else t.call(n.client,!1,-1,"Conversation's Id is invalid")}},{key:"rateChat",value:function(e,t,n,r){var i=this,o={convId:e,rating:t,comment:n};i.client._sendMessage(s.RATE_CHAT,o,(function(e){r&&r.call(i.client,e)}))}},{key:"updateUserInfo",value:function(e,t){var n=this;n.client._sendMessage(s.UPDATE_USER_INFO,e,(function(e){t&&t.call(n.client,e)}))}},{key:"getUsersInfo",value:function(e,t){var n=this;if(e&&e.length)if(n.client.userId&&n.client.hasConnected){var r="";e.forEach((function(e){0==r.length?r+=e:r+=", "+e}));var i={userIds:r};n.client._sendMessage(s.CHAT_GET_USERS_INFO,i,(function(e){if(t){var r=e.r,i=0==r,o=i?"Success":"Fail.",a=e.users,s=[];return a.forEach((function(e){var t=new _t(e);t.userId=e.userId,s.push(t)})),void t.call(n.client,i,r,o,s)}}))}else t.call(n.client,!1,-1,"StringeeClient is not connected to Stringee server",null);else t.call(n.client,!1,-2,"UserIds are invalid.",null)}},{key:"sendEmailTranscript",value:function(e,t){var n=this,r={convId:e.convId,email:e.email,domain:e.domain};n.client._sendMessage(s.SEND_EMAIL_TRANSCRIPT,r,(function(e){t&&t.call(n.client,e)}))}},{key:"getChatServices",value:function(e){var t=this;t.client._sendMessage(s.GET_CHAT_SERVICES,{},(function(n){e&&e.call(t.client,n)}))}},{key:"viewChat",value:function(e,t){var n=this,r={convId:e};n.client._sendMessage(s.VIEW_CHAT,r,(function(e){t&&t.call(n.client,e)}))}},{key:"joinChat",value:function(e,t){var n=this,r={convId:e};n.client._sendMessage(s.JOIN_CHAT_CUSTOMER_CARE,r,(function(e){t&&t.call(n.client,e)}))}},{key:"transferChat",value:function(e,t,n){var r=this,i={convId:e,customerId:t.customerId,customerName:t.customerName,toUserId:t.toUserId};r.client._sendMessage(s.CHAT_TRANSFER_TO_ANOTHER_AGENT,i,(function(e){n&&n.call(r.client,e)}))}},{key:"confirmTransferChat",value:function(e,t){var n=this,r={convId:e,answer:1};n.client._sendMessage(s.CHAT_CONFIRM_TRANSFER_REQUEST,r,(function(e){t&&t.call(n.client,e)}))}},{key:"pinMessage",value:function(e,t,n,r){var i=this;if(i.isString(e)&&i.isString(t)&&""!==e&&""!==t&&null!=n)if(i.client.userId&&i.client.hasConnected){var o={convId:e,msgId:t,isPin:n};i.client._sendMessage(s.CHAT_PIN_MESSAGE,o,(function(e){if(r){var t=e.r,n=0==t,o=e.message;r.call(i.client,n,t,o)}}))}else r.call(i.client,!1,-1,"StringeeClient is not connected to Stringee server");else r.call(i.client,!1,-2,"Params are invalid")}},{key:"editMessage",value:function(e,t,n,r){var i=this;if(i.isString(e)&&i.isString(t)&&i.isString(n)&&""!==e&&""!==t&&""!==n)if(i.client.userId&&i.client.hasConnected){var o={content:n},a={convId:e,msgId:t,newContent:JSON.stringify(o)};i.client._sendMessage(s.CHAT_EDIT_MESSAGE,a,(function(e){if(r){var t=e.r,n=0==t,o=e.message;r.call(i.client,n,t,o)}}))}else r.call(i.client,!1,-1,"StringeeClient is not connected to Stringee server");else r.call(i.client,!1,-2,"Params are invalid")}},{key:"revokeMessage",value:function(e,t,n){var r=this;if(r.isString(e)&&r.isString(t)&&""!==e&&""!==t)if(r.client.userId&&r.client.hasConnected){var i={convId:e,msgIds:[t]};r.client._sendMessage(s.CHAT_REVOKE_MESSAGE,i,(function(e){if(n){var t=e.r,i=0==t,o=e.message;n.call(r.client,i,t,o)}}))}else n.call(r.client,!1,-1,"StringeeClient is not connected to Stringee server");else n.call(r.client,!1,-2,"Params are invalid")}},{key:"userBeginTyping",value:function(e,t){var n=this,r={userId:e.userId,convId:e.convId};n.client._sendMessage(s.CHAT_USER_BEGIN_TYPING,r,(function(e){t&&t.call(n.client,e)}))}},{key:"userEndTyping",value:function(e,t){var n=this,r={userId:e.userId,convId:e.convId};n.client._sendMessage(s.CHAT_USER_END_TYPING,r,(function(e){t&&t.call(n.client,e)}))}},{key:"getAttachmentMessages",value:function(e,t,n,r,i){var o=this;if(o.isString(e)&&""!==e&&null!=t&&0!==t&&null!=r&&0!==r)if(o.client.userId&&o.client.hasConnected){var a={convId:e,msgType:t,limit:r,start:n};o.client._sendMessage(s.CHAT_CONVERSATION_ATTACHMENT,a,(function(e){if(i){var t=e.r,n=0==t,r=e.message,a=n?e.msgs:null;i.call(o.client,n,t,r,a)}}))}else i.call(o.client,!1,-1,"StringeeClient is not connected to Stringee server",null);else i.call(o.client,!1,-2,"Params are invalid",null)}},{key:"on",value:function(e,t){this._onMethods.put(e,t)}},{key:"_callOnEvent",value:function(e,t){var n=this._onMethods.get(e);n?t?n.call(this,t):n.call(this):console.log("Please implement StringeeChat event: "+e)}},{key:"fireObjectChangeEvent",value:function(e,t,n){this._callOnEvent(this.realTimeEvent,{objectType:e,objectChanges:t,changeType:n})}},{key:"trackMsg",value:function(e){if(e.state!==Gt){var t=this.keyForMsg(e);this.trackingMap.put(t,e)}}},{key:"keyForMsg",value:function(e){var t=null!=e.conversationId&&e.conversationId.length>0?e.conversationId:"",n=null!=e.id&&e.id.length>0?e.id:"",r=null!=e.localId&&e.localId.length>0?e.localId:"";return t+"_"+(r.length>0?r:n)}},{key:"isString",value:function(e){var t=zt(e);return"string"===t||"object"===t&&null!=e&&!Array.isArray(e)&&"[object String]"==getTag(e)}}])&&$t(t.prototype,n),r&&$t(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function en(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var tn=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.client=t}var t,n,r;return t=e,(n=[{key:"createConversation",value:function(e,t){this.created?console.log("Could not create conversation, conversation is created"):this.client._sendMessage(s.CHAT_CREATE_CONVERSATION,e,(function(e){t&&t.call(this.client,e)}))}},{key:"sendChatMessage",value:function(e,t){var n={message:e.message,type:e.type,convId:e.convId};this.client._sendMessage(s.CHAT_MESSAGE,n,(function(e){t&&t.call(this.client,e)}))}},{key:"loadChatMessages",value:function(e,t){var n={seqGreater:e.seqGreater,limit:e.limit,sort:e.sort,convId:e.convId};this.client._sendMessage(s.CHAT_MESSAGES_LOAD,n,(function(e){t&&t.call(this.client,e)}))}},{key:"loadAllChatMessages",value:function(e,t){var n=this,r=e.createdGreater,i=e.createdSmaller,o=e.limit,a=e.sort,c=e.convIds;if(Array.isArray(c)){var l={createdGreater:r,createdSmaller:i,sort:a,limit:o,convIds:c=c.join(",")};return new Promise((function(e,r){n.client._sendMessage(s.CHAT_LOAD_ALL_MESSAGES_FOR_CONVERSATIONS,l,(function(r){console.log("got data from server, ",r),t&&(console.log("got a valid callback"),t.call(n.client,r)),e(r)}))}))}console.log("must be an array")}},{key:"addParticipant",value:function(e,t){var n={convId:e.convId,userIds:e.userIds};this.client._sendMessage(s.CHAT_ADD_PARTICIPANT,n,(function(e){t&&t.call(this.client,e)}))}},{key:"removeParticipant",value:function(e,t){var n={convId:e.convId,userIds:e.userIds};this.client._sendMessage(s.CHAT_REMOVE_PARTICIPANT,n,(function(e){t&&t.call(this.client,e)}))}},{key:"deleteMessage",value:function(e,t){var n={convId:e.convId,messageIds:e.messageIds};this.client._sendMessage(s.CHAT_DELETE_MESSAGE,n,(function(e){t&&t.call(this.client,e)}))}},{key:"updateConversation",value:function(e,t){var n={convId:e.convId,groupName:e.name,imageUrl:e.imageUrl};this.client._sendMessage(s.CHAT_UPDATE_CONVERSATION,n,(function(e){t&&t.call(this.client,e)}))}},{key:"deleteConversation",value:function(e,t){var n={seq:e.seq,convId:e.convId};this.client._sendMessage(s.CHAT_DELETE_CONVERSATION,n,(function(e){t&&t.call(this.client,e)}))}},{key:"clearHistory",value:function(e,t){var n={seq:e.seq,convId:e.convId};this.client._sendMessage(s.CHAT_CONVERSATION_CLEAR_HISTORY,n,(function(e){t&&t.call(this.client,e)}))}},{key:"loadConversations",value:function(e,t){var n={lastUpdateGreater:e.lastUpdateGreater,lastUpdateSmaller:e.lastUpdateSmaller,limit:e.limit};this.client._sendMessage(s.CHAT_CONVERSATION_LOAD,n,(function(e){t&&t.call(this.client,e)}))}},{key:"getInfo",value:function(e,t){var n={convIds:e};this.client._sendMessage(s.CHAT_GET_CONVERSATIONS_INFO,n,(function(e){t&&t.call(this.client,e)}))}},{key:"markMessageReceived",value:function(e,t){var n={lastMsgSeq:e.seq,lastMsgTimestamp:e.createdTime,status:1,convId:e.convId};this.client._sendMessage(s.CHAT_MESSAGE_REPORT,n,(function(e){t&&t.call(this.client,e)}))}},{key:"markMessageSeen",value:function(e,t){var n={lastMsgSeq:e.seq,lastMsgTimestamp:e.createdTime,status:2,convId:e.convId};this.client._sendMessage(s.CHAT_MESSAGE_REPORT,n,(function(e){t&&t.call(this.client,e)}))}},{key:"blockUser",value:function(e,t){var n={userId:e};this.client._sendMessage(s.CHAT_BLOCK_USER,n,(function(e){t&&t.call(this.client,e)}))}},{key:"blockInviteToGroup",value:function(e,t){var n={block_group_invite:e};this.client._sendMessage(s.CHAT_BLOCK_USER,n,(function(e){t&&t.call(this.client,e)}))}},{key:"rateChat",value:function(e,t,n,r){var i={convId:e,rating:t,comment:n};this.client._sendMessage(s.RATE_CHAT,i,(function(e){r&&r.call(this.client,e)}))}},{key:"updateUserInfo",value:function(e,t){this.client._sendMessage(s.UPDATE_USER_INFO,e,(function(e){t&&t.call(this.client,e)}))}},{key:"userBeginTyping",value:function(e,t){var n={userId:e.userId,convId:e.convId};this.client._sendMessage(s.CHAT_USER_BEGIN_TYPING,n,(function(e){t&&t.call(this.client,e)}))}},{key:"userEndTyping",value:function(e,t){var n={userId:e.userId,convId:e.convId};this.client._sendMessage(s.CHAT_USER_END_TYPING,n,(function(e){t&&t.call(this.client,e)}))}},{key:"getUsersInfo",value:function(e,t){var n={userIds:e};this.client._sendMessage(s.CHAT_GET_USERS_INFO,n,(function(e){t&&t.call(this.client,e)}))}},{key:"sendEmailTranscript",value:function(e,t){var n={convId:e.convId,email:e.email,domain:e.domain};this.client._sendMessage(s.SEND_EMAIL_TRANSCRIPT,n,(function(e){t&&t.call(this.client,e)}))}},{key:"getChatServices",value:function(e){this.client._sendMessage(s.GET_CHAT_SERVICES,{},(function(t){e&&e.call(this.client,t)}))}},{key:"viewChat",value:function(e,t){var n={convId:e};this.client._sendMessage(s.VIEW_CHAT,n,(function(e){t&&t.call(this.client,e)}))}},{key:"joinChat",value:function(e,t){var n={convId:e};this.client._sendMessage(s.JOIN_CHAT_CUSTOMER_CARE,n,(function(e){t&&t.call(this.client,e)}))}},{key:"transferChat",value:function(e,t){var n={convId:e.conversationId,customerId:e.customerId,customerName:e.customerName,toUserId:e.toUserId};this.client._sendMessage(s.CHAT_TRANSFER_TO_ANOTHER_AGENT,n,(function(e){t&&t.call(this.client,e)}))}},{key:"confirmTransferChat",value:function(e,t){var n={convId:e.convId,answer:e.answer};console.log("+++ confirmTransferChat",n),this.client._sendMessage(s.CHAT_CONFIRM_TRANSFER_REQUEST,n,(function(e){t&&t.call(this.client,e)}))}},{key:"unreadConversationCount",value:function(e){this.client._sendMessage(s.CHAT_UNREAD_CONVERSATION_COUNT,{},(function(t){e&&e.call(this.client,t)}))}},{key:"resolveConversation",value:function(e,t){var n={convId:e.convId,resolved:e.resolved};console.log("+++ resolveConversation",n),this.client._sendMessage(s.CHAT_AGENT_RESOLVE_CONVERSATION,n,(function(e){t&&t.call(this.client,e)}))}}])&&en(t.prototype,n),r&&en(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();window.StringeeChat2=Ct,window.StringeeCall=_e,window.StringeeCall2=Je,window.StringeeChat=Zt,window.StringeeHashMap=u,window.DeprecatedStringeeChat=tn,window.StringeeClient=wt,window.StringeeUtil=ge,window.StringeeServiceType=s,window.StringeeWebRtc=we,window.StringeeVideo=He,window.StringeeVideoRoom=Ve,window.StringeeVideoTrack=xe,window.SubscribedTrackDisplay=Fe}]);