import { PayloadAction, createSlice } from "@reduxjs/toolkit";

import { EmployeeDetailDtoType } from "modules/employee";
import { clearEmployeeSession, initialEmployeeSession } from "services/session";

export interface AuthReducerState {
  status: "AUTH" | "UNAUTH";
  loading: boolean;
  employeeInfo?: EmployeeDetailDtoType;
  accessToken?: string;
  refreshToken?: string;
}

const initialState: AuthReducerState = {
  status: "UNAUTH",
  loading: true,
  employeeInfo: undefined,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    employeeLogout: (state) => {
      state.loading = false;
      state.status = "UNAUTH";
      state.employeeInfo = undefined;
      clearEmployeeSession();
    },
    updateEmployeeAuthState: (
      state,
      action: PayloadAction<{
        employee: EmployeeDetailDtoType;
        token: string;
        refreshToken: string;
      }>
    ) => {
      state.loading = false;
      state.status = "AUTH";
      state.employeeInfo = action.payload.employee;
      initialEmployeeSession({
        accessToken: action.payload.token,
        refreshToken: action.payload.refreshToken,
      });
    },
  },
});

export default authSlice;
