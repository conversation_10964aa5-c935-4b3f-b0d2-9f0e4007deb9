export interface GetListItemLocationStockBySkyIdDto {
  itemId: number;
  skuId: number;
}

export interface GetListItemLocationStockBySkuIdResponseDto {
  data: ItemStockLocationDto[];
}

export interface ItemStockLocationDto {
  id: number;
  itemCode: string;
  skuCode: string;
  whCode: string | null;
  storeCode: string;
  onhandQty: number;
  bookingQty: number;
  orderQty: number;
  stockQty: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  itemId: number;
  skuId: number;
  storage: Storage;
}

export interface Storage {
  name: string;
}
