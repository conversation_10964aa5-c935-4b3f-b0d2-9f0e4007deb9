import { Button } from "components/atoms/button";
import { ColorPicker } from "components/atoms/colorpicker";
import { Heading } from "components/atoms/heading";
import { NumberfieldHookForm } from "components/atoms/numberfield";
import { TextfieldHookForm } from "components/atoms/textfield";
import { Formfield } from "components/molecules/formfield";
import { Col, Row } from "components/organisms/grid";
import { Section } from "components/organisms/section";
import { General } from "components/pages/general";
import { FormContainer } from "helpers/form";
import * as navigationHelper from "helpers/navigation";

const StatusTicketDetails = () => {
  return (
    <General>
      <title key="title">Chi tiết Trạng thái ticket</title>
      <Section>
        <Heading type="h1" modifiers="primary">
          SỬA THÔNG TIN TRẠNG THÁI TICKET
        </Heading>
        <Section>
          <FormContainer validationSchema={{}}>
            <Row>
              <Col lg="12" className="u-mb-15">
                <Formfield label="Tên trạng thái" name="statusName">
                  <TextfieldHookForm
                    placeholder="Tên trạng thái..."
                    name="statusName"
                  />
                </Formfield>
              </Col>
              <Col lg="6" className="u-mb-15">
                <Formfield label="Màu sắc" name="color">
                  <ColorPicker />
                </Formfield>
              </Col>
              <Col lg="6" className="u-mb-15">
                <Formfield label="Thứ tự hiển thị" name="displayOrder">
                  <NumberfieldHookForm name="displayOrder" />
                </Formfield>
              </Col>
              <Col lg="12" className="d-flex justify-content-end u-mt-20">
                <Button
                  buttonType="outline"
                  modifiers="secondary"
                  onClick={navigationHelper.goBack}
                >
                  Quay lại
                </Button>
                <div className="u-ml-20">
                  <Button type="submit">Lưu</Button>
                </div>
              </Col>
            </Row>
          </FormContainer>
        </Section>
      </Section>
    </General>
  );
};

export default StatusTicketDetails;
