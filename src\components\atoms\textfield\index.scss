.a-textfield {
	$root: &;
	position: relative;

	&_input {
		width: 100%;
		height: rem(48);
		padding: 0 rem(12);
		font-size: rem(14);
		line-height: rem(16);
		color: $COLOR-QUARTZ;
		background-color: $COLOR-WHITE;
		border: 1px solid $COLOR-PLATINUM-4;
		border-radius: 2px;
		outline: none;
		transition: all 0.3s ease-in-out;
		appearance: none;

		#{$root}-withicon & {
			padding-right: rem(48);
		}

		&::placeholder {
			color: $COLOR-LIGHT-GRAY;
		}

		&:hover,
		&:focus {
			color: $COLOR-QUARTZ;
			border-color: $COLOR-QUARTZ;
		}

		#{$root}-disabled & {
			color: $COLOR-QUARTZ;
			background-color: $COLOR-WHITE-SMOKE-2;
		}

		#{$root}-error & {
			color: $COLOR-CINNABAR;
			border: 1px solid $COLOR-CINNABAR;
			box-shadow: none;

			&:focus {
				box-shadow: 0px 0px 4px $COLOR-CINNABAR;
			}
		}
	}

	&_wrapicon {
		position: absolute;
		top: 50%;
		right: rem(14);
		width: rem(20);
		height: rem(20);
		cursor: pointer;
		transition: opacity 0.3s ease-in-out;
		transform: translateY(-50%);

		&:hover {
			opacity: 0.7;
		}
	}

	&_errormessage {
		margin-top: rem(5);
		margin-left: rem(10);
		font-size: rem(13);
		color: $COLOR-CINNABAR;
		@include u-fw-light;
	}

	&-disabled {
		pointer-events: none;
	}
}
