import { PaginationDTOType } from "modules/common/pagination";
import { OrderDetailDtoType } from "modules/order";
import { MetaTotalRecords } from "pages/TickerManagement/StatusTicket/dto/get-status-ticket.dto";

export interface GetDetailOrderDto {
  orderId: number;
}

export interface GetDetailOrderResponseDto {
  data: OrderDetailDtoType;
}

export interface GetAllOrderByCustomerIdDto {
  customerId: number;
  pageNum?: number;
  pageSize?: number;
}

export interface GetAllOrderByCustomerIdResponseDto extends MetaTotalRecords {
  data: OrderDetailDtoType[];
  links: PaginationDTOType;
}

export interface UpdateOrderExchangeDto extends CreateExchangeOrderDto {
  orderId: number;
}

export interface CreateExchangeOrderDto {
  orderIdRef: number;
  codeRef: string;
  orderType: number;
  channel: string;
  orderSourceId: number;
  shipping?: number;
  zipcode?: string | null;
  orderStatusId: number;
  orderStatusTaglineId?: number;
  cancelReason?: string | null;
  assignedEmployeeId: number;
  deliveryNote?: string | null;
  weight?: string | null;
  boxCode?: string | null;
  orderNote?: string | null;
  apiNote?: string | null;
  // isShopSent: boolean;
  phoneNumber: string;
  email?: string;
  name: string;
  birthDay: string;
  sex: string;
  customerHeight?: string;
  customerWeight?: string;
  customerWaist?: string;
  cityId?: number;
  districtId?: number;
  shippingAddressDetail: string;
  cskhNote?: string | null;
  totalAmount: number;
  discount?: number;
  discountValue?: number;
  shippingCost?: number;
  applicableFee?: number;
  promotionValue?: number | null;
  applyreferralcode?: number | null;
  applyreferralValue?: number | null;
  pay: number;
  paid: number;
  remainingAmount: number;
  shopSent?: number;
  shopSentNote?: string;
  // personalInfo?: PersonalInfo;
  discountType: string;
  customerId: number;
  items: Item[];
}

export interface Item {
  skuId: number;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  productId: number;
  discountAmount: number;
  discountPercent: number;
  listedPrice: number;
  orderItemIdRef?: number;
}

export interface CreateExchangeOrderResponseDto {
  data: ExchangeOrderDto;
}

export interface ExchangeOrderDto extends OrderDetailDtoType {
  orderIdRef: number;
  codeRef: string;
  items: OrderDetailDtoType["items"];
}
