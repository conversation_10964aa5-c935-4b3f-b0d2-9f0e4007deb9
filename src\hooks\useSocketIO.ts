import { useCallback, useEffect, useState } from "react";

import { io, Socket } from "socket.io-client";

import { getEmployeeSessionInfo } from "services/session";
import { useAppSelector } from "store";

export enum EventNames {
  PUSH_FB_MESSAGE = "push_fb_message",
  RECEIVE_FB_MESSAGE = "receive_fb_message",
  CUSTOM_ERROR = "custom_error",
  MEW_CONVERSATION = "new_conversation",
  ASSIGNED_CONVERSATION = "assigned_conversation",
  LEFT_CONVERSATION = "left_conversation",
}

interface SocketIoParams {
  uri: string;
  options?: {
    onConnectSuccess?: () => void;
    onConnectError?: (error: Error) => void;
  };
}

const useSocketIO = (paramsConfig: SocketIoParams) => {
  const { uri, options } = paramsConfig;
  const { onConnectSuccess, onConnectError } = options || {};
  const [socketIoInstance, setSocket] = useState<null | Socket>(null);
  const authStatus = useAppSelector((state) => state.auth.status);

  const handlePushFbMessage = useCallback(
    async (params: {
      conversationId: string;
      content: {
        text?: string;
        mediaIds?: string[];
        metadata?: {
          [key: string]: string;
        };
      };
    }) => {
      if (!socketIoInstance) return;

      const { conversationId, content } = params;

      socketIoInstance.emit(EventNames.PUSH_FB_MESSAGE, {
        conversationId,
        content,
      });
    },
    [socketIoInstance]
  );

  const disconnect = useCallback(() => {
    if (socketIoInstance) {
      socketIoInstance.disconnect();
      setSocket(null);
    }
  }, [socketIoInstance]);

  useEffect(() => {
    let socketIO: Socket;
    const employeeSesstion = getEmployeeSessionInfo();

    if (authStatus === "AUTH" && employeeSesstion) {
      socketIO = io(uri, {
        transports: ["websocket"],
        auth: {
          accessToken: `Bearer ${employeeSesstion.accessToken}`,
        },
      });

      socketIO.on("connect", () => {
        setSocket(socketIO);

        if (onConnectSuccess) {
          onConnectSuccess();
        }
      });

      if (onConnectError) {
        socketIO.on("connect_error", (err: Error) => {
          onConnectError(JSON.parse(err.message));
        });
      }
    }
    return () => {
      if (socketIO) {
        socketIO.disconnect();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authStatus]);

  return {
    socketIoInstance,
    handlePushFbMessage,
    disconnect,
  };
};

export default useSocketIO;
