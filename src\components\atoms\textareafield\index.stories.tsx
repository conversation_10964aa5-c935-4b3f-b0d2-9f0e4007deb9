import { <PERSON>, <PERSON>a } from "@storybook/react/types-6-0";

import { Textareafield, Props } from ".";

export default {
  title: "Components|atoms/Textareafield",
  component: Textareafield,
} as Meta;

const Template: Story<Props> = ({ disabled, placeholder, errorMessage }) => (
  <Textareafield
    disabled={disabled}
    placeholder={placeholder}
    errorMessage={errorMessage}
  />
);

export const Normal = Template.bind({});

Normal.args = {
  placeholder: "Placeholder",
  disabled: false,
  errorMessage: "",
};
