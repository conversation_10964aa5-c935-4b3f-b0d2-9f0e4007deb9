import { Model, String, Number, Boolean, Array, ModelValue } from "libs/domain";

export const OrderReturnStatusSchema = {
  _id: String(),
  name: String(),
  description: String(),
  returnOrderStatusId: Number(),
  isDefault: Boolean({ defaultValue: false }),
  displayOrder: Number(),
  // eslint-disable-next-line @typescript-eslint/no-array-constructor, no-array-constructor
  prevReturnOrderStatusIds: Array(Number(), { defaultValue: [] }),
  color: String(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatedAt: (raw: any) => new Date(raw),
};

export const OrderReturnStatusModel = new Model(OrderReturnStatusSchema);
export type OrderReturnStatusEntityType = ModelValue<
  typeof OrderReturnStatusModel
>;
