/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useMemo } from "react";
import { useGetDistricts } from "services/crm/location/hooks/location.hooks";
import { BaseSelect, BaseSelectProps } from "../BaseSelect";

export type BaseSelectDistrictProps = {
  cityId?: number;
} & BaseSelectProps;

export default function BaseSelectDistrict(props: BaseSelectDistrictProps) {
  const { cityId, ...rest } = props;
  const { districtData, districtRefetch, loading } = useGetDistricts({
    cityId,
  });
  const districtOptions = useMemo(() => {
    if (cityId && districtData?.data?.length > 0) {
      return districtData.data.map((district) => ({
        label: district.name,
        value: district.districtId,
      }));
    }
    return [];
  }, [districtData, cityId]);

  useEffect(() => {
    if (cityId) {
      districtRefetch();
    }
  }, [cityId]);

  return <BaseSelect loading={loading} options={districtOptions} {...rest} />;
}
