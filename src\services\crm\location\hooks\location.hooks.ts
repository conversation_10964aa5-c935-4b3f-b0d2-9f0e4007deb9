import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import { GetDistrictsDto } from "../dtos/location.dto";
import locationService from "../location.service";

/**
 * @returns {Object<GetCitiesResponseDto>}  - Contains cityRefetch, cityData, loading
 * @data {Array<CityDto>} - Contains cityId, name
 * @description Hook to fetch cities
 */

export const useGetCities = () => {
  const [cityRefetch, cityData] = useAsync(
    useCallback(() => {
      return locationService.getCities();
    }, [])
  );

  return {
    cityRefetch,
    cityData: cityData?.data?.data,
    loading: cityData.loading,
  };
};

/**
 * @description Hook to fetch districts based on cityId
 * @param {GetDistrictsDto} dto - Contains cityId
 * @returns {Object<GetDistrictsResponseDto>} - Contains districtRefetch, districtData, loading
 * @data {Array<DistrictDto>} - Contains districtId, name, cityId
 */
export const useGetDistricts = (dto: GetDistrictsDto) => {
  const [districtRefetch, districtData] = useAsync(
    useCallback(() => {
      return locationService.getDistricts(dto);
    }, [dto])
  );

  return {
    districtRefetch,
    districtData: districtData?.data?.data,
    loading: districtData.loading,
  };
};

/**
 * @description Hook to fetch all districts
 * @returns {Object<GetDistrictResponseDto>} - Contains allDistrictsRefetch, allDistrictsData, loading
 * @data {Array<DistrictDto>} - Contains districtId,
 */
export const useGetAllDistricts = () => {
  const [allDistrictsRefetch, allDistrictsData] = useAsync(
    useCallback(() => {
      return locationService.getAllDistricts();
    }, [])
  );

  return {
    allDistrictsRefetch,
    allDistrictsData: allDistrictsData?.data?.data,
    loading: allDistrictsData.loading,
  };
};
