import { PaginationDTOType } from "modules/common/pagination";
import { MetaTotalRecords } from "pages/TickerManagement/StatusTicket/dto/get-status-ticket.dto";

export interface InvertoryDto {
  searchText?: string;
  storeCode: string;
  pageNum: number;
  pageSize: number;
}
export interface InventoryResponse extends MetaTotalRecords {
  data: InventoryItem[];
  links: PaginationDTOType;
}
export interface InventoryItem {
  id: number;
  itemCode: string;
  skuCode: string;
  whCode: string | null;
  storeCode: string;
  onhandQty: number;
  bookingQty: number;
  orderQty: number;
  stockQty: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  productId: number;
  skuId: number;
  sku: SkuDto;
  product: ProductDto;
  storeName: string;
}

export interface ProductDto {
  productId: number;
  categoryId: number | null;
  code: string;
  name: string;
  sold: number;
  unitName: string;
  unitPrice: number;
  productCollection: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export type SkuDto = {
  skuId: number;
  productId: number;
  colorId: number;
  sizeId: number;
  code: string;
  name: string;
  price: number;
  salePrice: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  color: ColorDto;
  size: SizeDto;
};

export interface ColorDto {
  colorId: number;
  code: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
export interface SizeDto {
  sizeId: number;
  code: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
