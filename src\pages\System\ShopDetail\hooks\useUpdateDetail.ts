/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from "react";
import { useAsync } from "hooks/useAsync";
import crmDriverV1 from "services/crm/crm-driver-v1";

export const UseUpdateShopDetail = () => {
  const [updateShopDetailExe] = useAsync(
    useCallback(
      (shopId: number, payload: any) =>
        crmDriverV1.put(`/locations/external/shops/${shopId}`, payload),
      []
    )
  );
  return {
    updateShopDetailExe,
  };
};
